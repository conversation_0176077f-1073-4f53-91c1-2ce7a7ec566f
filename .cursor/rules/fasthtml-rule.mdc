---
description: Developing apps in fasthtml
globs: main.py
---

# Your rule content

- You can @ files here
- You can use markdown but dont have to

We are using FastHTML for this project, which is a Python framework for developing web apps. It provides full access to HTTP, HTML, JS, and CSS, bringing the foundations of the web to you. There's no limits to what you can build.
FastHTML applications are fast and scalable. They're also easy to deploy, since you can use any hosting service that supports Python.

Please ensure to keep this in mind when writing code in [main.py](mdc:main.py) or any other file that uses fasthtml imports.

Here is an example of a table created with FastHTML:

	async def weather_table():

	    """Dynamically generated python content

	    directly incorporated into the HTML"""

	    # These are actual real-time weather.gov observations

	    try: results = await all_weather()

	    except: return P('Weather not available')

	    rows = [Tr(Td(city), *map(Td, d.values()), cls="even:bg-purple/5")

	            for city,d in results.items()]

	    flds = 'City', 'Temp (C)', 'Wind (kmh)', 'Humidity'

	    head = Thead(*map(Th, flds), cls="bg-purple/10")

	    return Table(head, *rows, cls="w-full")

As you can see, it's utilizing HTML/HTMX and CSS - while coding in pure Python.
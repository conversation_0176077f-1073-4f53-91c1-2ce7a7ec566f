# Python
__pycache__/
*.py[cod]
*$py.class
.pytest_cache/
.coverage
htmlcov/

# Virtual Environment
.venv/
venv/
env/

# IDE
.vscode/
.idea/

# Project specific
test-results/
playwright-report/

# Node.js (keeping these for reference)
node_modules/
package-lock.json
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# UV specific
.uv/
.venv.uv/

# Testing
.pytest_cache/
__pycache__/
*.pyc
.coverage
htmlcov/
test-results/
playwright-report/
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

# Data
data/
scratch/
logs/
paper_texts/
neo4j_import/

.sesskey
.env
workflow:
  rules:
    - when: never

stages:
  - test

variables:
  PIP_CACHE_DIR: "$CI_PROJECT_DIR/.pip-cache"
  npm_config_cache: "$CI_PROJECT_DIR/.npm-cache"

# Cache dependencies between jobs
cache:
  paths:
    - .pip-cache/
    - .npm-cache/
    - node_modules/

playwright-tests:
  stage: test
  image: mcr.microsoft.com/playwright:v1.51.0-jammy
  script:
    - npm ci
    - npx playwright install --with-deps
    - npx playwright test
  artifacts:
    when: always
    paths:
      - playwright-report/
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "develop"
    - if: $CI_PIPELINE_SOURCE == "schedule"

python-tests:
  stage: test
  image: python:3.12
  before_script:
    - pip install --cache-dir .pip-cache -r requirements.txt
    - pip install --cache-dir .pip-cache -r requirements-test.txt
  script:
    - python -m pytest tests/e2e/
  artifacts:
    when: always
    paths:
      - .pytest_cache/
      - pytest-report.xml
    expire_in: 1 week
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "develop"
    - if: $CI_PIPELINE_SOURCE == "schedule"

# Schedule daily tests
daily-tests:
  extends: python-tests
  variables:
    SCHEDULE_TYPE: "daily"
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"

# Weekly test updates
test-updates:
  stage: test
  image: python:3.12
  script:
    - pip install --cache-dir .pip-cache -r requirements.txt
    - pip install --cache-dir .pip-cache -r requirements-test.txt
    - python scripts/update_tests.py || true  # Don't fail the pipeline if updates fail
    - python -m pytest tests/e2e/  # Run tests after updates
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule" && $RUN_TEST_UPDATES == "true"

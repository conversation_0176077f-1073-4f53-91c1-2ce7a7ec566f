# FastMoatless Project Guidelines

## Commands

### Core Application
- Run app: `python main.py`
- Run app in local mode: `python main.py --local`
- Deploy application: `./deploy.sh`

### Testing
- Run single test: `python -m tests.test_name` (e.g., `python -m tests.test_daily_stats`)
- Run all tests: `python -m unittest discover tests`
- Run TypeScript Playwright tests: `python tests/run_tests.py --typescript` or `npx playwright test`
- Run Python Playwright tests: `python tests/run_tests.py --playwright`
- Run specific test file: `python tests/run_tests.py --test-file tests/e2e/test_navigation.py`
- Run specific TypeScript test: `python tests/run_tests.py --test-file tests/e2e-ts/navigation.spec.ts`
- Run Playwright in UI mode: `npx playwright test --ui`
- Test entity resolution with sample data: `python scripts/testing/test_local.py`
- Set up GitLab CI/CD schedules: `./scripts/**********************.sh`

### Knowledge Graph Operations
- Extract knowledge graph: `python scripts/knowledge_graph/extraction/extract_kg.py`
- Run knowledge graph extraction: `python scripts/knowledge_graph/extraction/run_kg_extraction.py`
- Run KG extraction locally: `python scripts/knowledge_graph/extraction/run_kg_extraction.py --local`
- Export KG to Neo4j: `python scripts/knowledge_graph/extraction/run_kg_extraction.py --export`
- Check knowledge graph: `python scripts/knowledge_graph/testing/check_kg.py`
- Test KG with mock DB: `python scripts/knowledge_graph/testing/check_kg.py --mock`

### Entity Management
- Run entity resolution: `python scripts/entity_management/resolution/run_entity_resolution.py`
- Run entity resolution locally: `python scripts/entity_management/resolution/run_entity_resolution.py --local`
- Force full entity resolution: `python scripts/entity_management/resolution/run_entity_resolution.py --force`
- Update entity dictionary: `python scripts/entity_management/resolution/update_entity_dictionary.py`
- Force rebuild entity dictionary: `python scripts/entity_management/resolution/update_entity_dictionary.py --force`
- Look up entity in dictionary: `python scripts/entity_management/resolution/lookup_entity.py "entity name"`

### Vector Embeddings and Data Processing
- Create embeddings: `python scripts/data_processing/create_embeddings.py`
- Check arXiv HTML: `python scripts/data_processing/arxiv_html_checker.py`
- Test Gemini OCR: `python scripts/data_processing/gemini_ocr.py`
- Test vector database: `python test_pgvector.py`

## Docker
- Build image: `docker build -t fastmoatless .`
- Run container: `docker run -p 8000:8000 fastmoatless`

## Code Style
- **Imports**: Order imports by standard lib, third-party, local modules
- **Formatting**: 4-space indentation, 100-char line limit
- **Types**: Use type hints for function params and returns
- **Naming**: snake_case for vars/functions, PascalCase for classes
- **Error handling**: Use try/except blocks with specific exceptions
- **Documentation**: Docstrings for functions, classes using triple quotes
- **FastHTML**: Build HTML with Python functions (e.g., Div, P, Table)
- **MongoDB**: Use collection methods (find_one, update_one) with proper queries

## Project Structure
- **Route Handlers**: API routes live in dedicated modules in `src/api/`
  - `stats.py`: Paper statistics routes (`/stats`)
  - `saved_papers.py`: Saved papers routes (`/save_paper`, `/saved`, `/save_to_notion`)
  - `search.py`: Search routes (`/search`, `/semantic-search`, etc.)
  - `knowledge_graph.py`: Knowledge graph routes (`/graph`, `/interactive_graph`, `/entities`, etc.)
  - `entity_resolution.py`: Entity resolution routes (`/entities/resolve`, `/entities/resolved`, `/entities/dashboard`)
- **Services**: Business logic in `src/services/`
  - `daily_stats.py`: Generate daily statistics about papers
  - `nlp.py`: Natural language processing utilities
  - `papers.py`: Paper-related services
  - `reviews.py`: Paper review services
  - `saved_papers.py`: Bookmarking and saving papers
  - `entity_resolution.py`: Entity resolution and normalization
- **Models**: Data models in `src/models/`
  - `kg.py`: Knowledge graph models
  - `paper.py`: Paper data models
- **UI Components**: Reusable UI components in `src/ui/`
  - `buttons.py`: Button components (SaveButton, MermaidButton)
  - `common.py`: Common UI utilities and functions
  - `components.py`: General HTML component builders
  - `entities.py`: Entity-related components
  - `metadata.py`: Paper metadata display components
  - `navigation.py`: Navigation bar and buttons
  - `paper.py`: Paper display components
  - `search.py`: Search form components
  - `stats.py`: Statistics display components
  - `tags.py`: Tag display components and tag mappings
- **Database**: Database connections in `src/db/`
  - `mongo.py`: MongoDB connection and helpers
- **Utils**: Helper functions in `src/utils/`
  - `date.py`: Date formatting and manipulation
  - `entity_dictionary.py`: Entity management
  - `json.py`: JSON processing utilities
  - `poppler.py`: PDF processing with Poppler
  - `text.py`: Text processing utilities

## Additional Tools
- `scripts/data_processing/arxiv_html_checker.py`: Extract text from arXiv HTML versions
- `scripts/knowledge_graph/testing/check_kg.py`: Knowledge graph verification
- `scripts/knowledge_graph/neo4j/mongodb_to_neo4j.py`: Export MongoDB KG to Neo4j
- `scripts/knowledge_graph/neo4j/neo4j_ontology_kg.py`: Neo4j schema and ontology setup
- `scripts/knowledge_graph/extraction/run_kg_extraction.py`: Knowledge graph extraction runner
- `scripts/entity_management/resolution/run_entity_resolution.py`: Entity resolution and normalization

## Project Notes

### Core Functionality
- Web app built with FastHTML framework
- Uses MongoDB for data storage with in-memory fallbacks
- Knowledge graph extraction with NLP tools
- Entity resolution with persistent dictionary cache
- Named Entity Recognition (NER) for extracting key concepts from text
- Entity normalization and deduplication with LLM-based resolution
- Neo4j integration for storing and querying knowledge graphs

### AI Integration
- Google Gemini API support for summarization and entity extraction
- OpenAI API support as an alternative AI backend
- OCR capabilities for extracting text from PDFs
- LLM-based entity resolution and normalization
- Semantic search using vector embeddings

### Architecture Features
- In-memory database implementations for local testing
- Modular service-based architecture
- Docker support for containerized deployment
- HTML content extraction from arXiv papers
- Vector search for semantic similarity
- Support for multiple vector database backends

### Testing & Deployment
- Comprehensive test suite with Playwright and unittest
- Support for CI/CD with GitLab schedules
- Local testing mode with in-memory databases
- Docker containerization for easy deployment

## Database Fallbacks

### MongoDB
- When running in `--local` mode without a MongoDB connection, the app falls back to an in-memory MongoDB implementation
- Primary in-memory implementation uses `mongomock`
- Falls back to `pymongo-inmemory` if mongomock is not available
- Final fallback to a simple dictionary-based mock implementation
- All collections are simulated with basic CRUD operations
- MongoDB methods like `find_one`, `update_one`, `count_documents` are supported

### Neo4j
- When running in `--local` mode without a Neo4j connection, the app falls back to an in-memory Neo4j implementation
- Uses `NetworkX` to simulate the graph database
- Supports core Neo4j operations like node creation, relationships, and queries
- Cypher queries are parsed and executed against the in-memory graph
- MATCH, CREATE, MERGE, and WHERE clauses are supported

### Vector Databases
- Vector search falls back to in-memory implementations when external databases are unavailable
- Simple in-memory vector storage with approximate nearest neighbor search
- ChromaDB can be used as a local alternative to PGVector for testing

## System Architecture
```mermaid
flowchart TD
    User([User]) --> Web[Web Browser]
    Web --> FastHTML[FastHTML Framework]

    FastHTML --> API[API Layer]

    API --> StatsRoutes[Stats Routes]
    API --> SearchRoutes[Search Routes]
    API --> KGRoutes[Knowledge Graph Routes]
    API --> EntityRoutes[Entity Resolution Routes]
    API --> SavedRoutes[Saved Papers Routes]
    API --> AuthRoutes[Auth Routes]

    StatsRoutes --> DailyStats[Daily Stats Service]
    SearchRoutes --> SearchService[Search Service]
    SearchRoutes --> EmbeddingService[Embeddings Service]
    KGRoutes --> KGService[Knowledge Graph Service]
    EntityRoutes --> EntityService[Entity Resolution Service]
    SavedRoutes --> SavedService[Saved Papers Service]

    DailyStats --> MongoDB[(MongoDB)]
    SearchService --> MongoDB
    KGService --> MongoDB
    KGService --> Neo4j[(Neo4j)]
    EntityService --> MongoDB
    SavedService --> MongoDB

    EmbeddingService --> OpenAI[OpenAI API]
    EmbeddingService --> Gemini[Google Gemini]
    SearchService --> PGVector[(Vector DB)]

    MongoDB -.-> InMemoryMongo[(In-Memory MongoDB)]
    Neo4j -.-> NetworkX[(NetworkX Graph)]

    %% Services group with icon
    subgraph Services ["🔧 Services"]
        DailyStats
        SearchService
        KGService
        EntityService
        SavedService
        EmbeddingService
    end

    %% External APIs group with icon
    subgraph External APIs ["🌐 External APIs"]
        OpenAI
        Gemini
    end

    %% Databases group with icon
    subgraph Databases ["💾 Databases"]
        MongoDB
        Neo4j
        PGVector
        InMemoryMongo
        NetworkX
    end
```
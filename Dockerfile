FROM python:3.11-slim

WORKDIR /app

# Install system dependencies including Poppler for PDF processing
RUN apt-get update && apt-get install -y \
    poppler-utils \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the rest of the application
COPY . .

# Default command - this can be overridden at runtime
CMD ["python", "main.py"]
# Moatless

Moatless is your gateway to AI research. It provides a sleek web interface to browse, search, and explore the latest AI research papers from arXiv with automatically generated summaries, classifications, and knowledge graphs.

## Features

- Automatic processing of arXiv papers with summarization and categorization
- PDF text extraction using OCR (Optical Character Recognition)
- Full text search capabilities with vector embeddings
- Tag-based categorization of papers
- Named Entity Recognition (NER) for extracting key concepts from papers
- Entity resolution and normalization with persistent dictionary
- Knowledge graph visualization and exploration
- Neo4j integration for graph database capabilities
- Multiple vector database options (ChromaDB, PGVector)
- Integration with Notion for saving interesting papers
- Support for Google Gemini and OpenAI for AI processing
- In-memory database fallbacks for local testing
- Responsive web UI for desktop and mobile

## Requirements

- Python 3.8+
- MongoDB (optional - falls back to in-memory implementation)
- Neo4j (optional - falls back to NetworkX in-memory implementation)
- Poppler (for PDF processing)
- Google API credentials (for auth)
- LLM API access (Google Gemini or OpenAI)
- PostgreSQL with pgvector extension (optional - for vector search)

## Installation

### Dependencies

1. Install Python requirements:
   ```bash
   pip install -r requirements.txt
   ```

2. Install Poppler for PDF processing:
   - **Ubuntu/Debian**: `apt-get install poppler-utils`
   - **macOS**: `brew install poppler`
   - **CentOS/RHEL**: `yum install poppler-utils`

### Environment Configuration

Create a `.env` file with the following variables:

```env
# API Configuration (Google Gemini or OpenAI)
GEMINI_API_KEY=your_gemini_api_key
GEMINI_BASE_URL=your_gemini_base_url
GEMINI_MODEL=gemini-1.5-pro

# Alternative API Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4o

# Database Configuration
MONGO_PRIVATE_URL=your_mongodb_connection_string
NEO4J_URL_PRIVATE=your_neo4j_bolt_url
NEO4J_USERNAME=your_neo4j_username
NEO4J_PASSWORD=your_neo4j_password

# Vector Database (Optional)
PGVECTOR_CONNECTION_STRING=your_postgres_connection_string

# Auth Configuration (optional)
AUTH_CLIENT_ID=your_google_client_id
AUTH_CLIENT_SECRET=your_google_client_secret

# Notion Integration (optional)
NOTION_API_KEY=your_notion_api_key
NOTION_DATABASE_ID=your_notion_database_id
```

Note: The application will automatically use in-memory implementations for MongoDB and Neo4j when running in local mode or when connection strings are not provided.

## Usage

### Local Testing Mode

Run the application in local testing mode with in-memory databases:

```bash
python main.py --local
```


### Entity Resolution Testing

For testing entity resolution specifically with test data:

```bash
python test_local.py
```

This script runs a standalone test environment with in-memory databases and sample data to verify entity extraction and resolution.

### Production Mode

For production deployment with real databases:

```bash
python main.py
```

### Entity Resolution Tools

Moatless provides several utilities for working with entity resolution:

1. **Run Entity Resolution**:
   ```bash
   python scripts/entity_management/resolution/run_entity_resolution.py
   ```
   Options:
   - `--local`: Run with local/in-memory database
   - `--force`: Force a full update of all entities

2. **Update Entity Dictionary**:
   ```bash
   python scripts/entity_management/resolution/update_entity_dictionary.py
   ```
   Options:
   - `--force`: Force a full rebuild of the dictionary
   - `--path`: Specify a custom path for the dictionary file

3. **Entity Lookup**:
   ```bash
   python scripts/entity_management/resolution/lookup_entity.py "BERT"
   ```
   Quickly lookup normalized forms of entities in the dictionary

## Docker Deployment

Build and run using Docker:

```bash
docker build -t moatless .
docker run -p 8000:8000 --env-file .env moatless
```

## VM Deployment

For deployment on cloud VMs, we provide a deployment script:

```bash
# Clone the repository
git clone https://gitlab.com/xaiguy/fastmoatless.git
cd fastmoatless

# Run the deployment script (requires sudo)
sudo ./deploy.sh
```

This script will:
1. Install all required dependencies including Poppler
2. Set up a Python virtual environment
3. Install the application
4. Create a systemd service for automatic startup

## Technical Notes

### Poppler Installation

Poppler is required for PDF processing. The application will automatically check for Poppler and display appropriate error messages if it's missing. On cloud VMs, you may need to install it manually if the automated installation fails:

```bash
# Debian/Ubuntu
sudo apt-get update
sudo apt-get install -y poppler-utils

# CentOS/RHEL
sudo yum install -y poppler-utils

# Alpine
apk add poppler-utils
```

The application is now built to handle Poppler installation issues gracefully, providing clear guidance if the dependency is missing.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Testing

Moatless includes a comprehensive test suite using Playwright for end-to-end testing. The project uses both TypeScript and Python Playwright tests.

### Running Tests

To run the tests manually:

```bash
# Run all tests (TypeScript, Python Playwright, and pytest)
python tests/run_tests.py

# Run only TypeScript Playwright tests (recommended)
python tests/run_tests.py --typescript

# Run only Python Playwright tests
python tests/run_tests.py --playwright

# Run only pytest tests
python tests/run_tests.py --pytest

# Run a specific test file
python tests/run_tests.py --test-file tests/e2e/test_navigation.py
python tests/run_tests.py --test-file tests/e2e-ts/navigation.spec.ts
```

### Running TypeScript Tests Directly

```bash
# Run all TypeScript Playwright tests
npx playwright test

# Run with UI mode (interactive)
npx playwright test --ui

# Run with headed browsers
npx playwright test --headed

# Run only in Chrome
npx playwright test --project=chromium
```

### VS Code Integration

This project includes VS Code integration for Playwright tests:

1. Open the Testing panel in VS Code (flask icon in the sidebar)
2. Run tests directly from the Testing panel
3. Use keyboard shortcuts:
   - `Ctrl+Shift+T`: Run current test file
   - `Ctrl+Shift+A`: Run all tests
   - `Ctrl+Shift+D`: Debug current test file
   - `Ctrl+Shift+R`: Open Playwright UI mode



For more details, see [VS Code Integration](tests/README.md#vs-code-integration) in the tests README.

### Setting Up Automated Testing

To set up GitLab CI/CD schedules for automated testing:

```bash
# Set up GitLab CI/CD schedules (requires GitLab API token)
export GITLAB_TOKEN=your_personal_access_token
export GITLAB_PROJECT_ID=your_project_id
./scripts/**********************.sh
```

For more details about testing, see the [tests/README.md](tests/README.md) file.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

When contributing, please ensure that:

1. All tests pass before submitting your PR
2. New features include appropriate tests
3. Code follows the project's style guidelines

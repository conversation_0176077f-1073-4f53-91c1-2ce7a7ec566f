body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', 'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    background-color: #282a36;
    color: white;
    padding: 20px;
}

.tag-container {
    margin-bottom: 10px;
}

.tag-container .tag {
    margin-bottom: 0.75rem; /* Adjusted vertical space */
}

.tag {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px; /* Increased padding */
    border-radius: 9999px; /* Fully rounded corners */
    font-size: 12px; /* Slightly smaller font size */
    font-weight: 600; /* Bold text */
    letter-spacing: 0.5px; /* Better letter spacing */
    color: white;
    margin-right: 8px; /* Space between tags */
    text-transform: uppercase;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.25); /* Enhanced shadow for depth */
    transition: all 0.2s ease; /* Smooth transition on hover */
}

.tag:hover {
    transform: translateY(-1px); /* Subtle lift effect */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); /* Enhanced shadow on hover */
}

.tag-security { background-color: #ff4757; } /* Red for security */
.tag-safety { background-color: #ff4757; } /* Red for security */
.tag-agents { background-color: #5352ed; } /* Blue for AI agents */
.tag-finance { background-color: #20bf6b; } /* Green for finance */
.tag-health { background-color: #eb3b5a; } /* Red for health */
.tag-tabular { background-color: #3867d6; } /* Blue for structured data */
.tag-audio { background-color: #8854d0; } /* Purple for audio */
.tag-vision { background-color: #4b7bec; } /* Blue for computer vision */
.tag-language { background-color: #4b7bec; } /* Blue for computer vision */
.tag-time-series { background-color: #a5b1c2; } /* Gray for time series */
.tag-education { background-color: #fed330; } /* Yellow for education */
.tag-chemistry { background-color: #26de81; } /* Green for chemistry */
.tag-physics { background-color: #4b7bec; } /* Blue for physics */
.tag-biology { background-color: #2ecc71; } /* Green for biology */
.tag-psychology { background-color: #e67e22; } /* Orange for psychology */
.tag-sociology { background-color: #9b59b6; } /* Purple for sociology */
.tag-law { background-color: #34495e; } /* Dark blue for law */
.tag-reinforcement-learning { background-color: #3498db; } /* Blue for RL */
.tag-reasoning { background-color: #f39c12; } /* Orange for reasoning */
.tag-tool-use { background-color: #4b7bec; } /* Gray for tool use */
.tag-ai-ethics { background-color: #1abc9c; } /* Teal for AI ethics */
.tag-coding { background-color: #2c3e50; } /* Dark blue for coding */
.tag-efficiency { background-color: #27ae60; } /* Green for efficiency */
.tag-alignment { background-color: #8e44ad; } /* Purple for alignment */
.tag-prompt-engineering { background-color: #d35400; } /* Orange for prompt engineering */
.tag-robotics { background-color: #7f8c8d; } /* Gray for robotics */
.tag-graphs { background-color: #16a085; } /* Teal for graphs */
.tag-explainable-ai { background-color: #f1c40f; } /* Yellow for explainable AI */
.tag-rag { background-color: #e74c3c; } /* Red for RAG */
.tag-entities { background-color: #4b7bec; } /* Blue for entities */

/* Entity highlight and tooltip styles */
.entity-highlight {
  background-color: rgba(117, 117, 240, 0.15);
  border-bottom: 1px dashed #7575F0;
  cursor: pointer;
  position: relative;
  padding: 0 2px;
}

.entity-highlight:hover {
  background-color: rgba(117, 117, 240, 0.25);
}

.entity-tooltip {
  position: absolute;
  z-index: 10;
  background-color: #1e2532;
  color: white;
  border: 1px solid #3a3f52;
  border-radius: 8px;
  padding: 10px 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  max-width: 300px;
  min-width: 200px;
  transition: opacity 0.3s;
  font-size: 14px;
}

.entity-tooltip.hidden {
  display: none;
}

.entity-tooltip::after {
  content: "";
  position: absolute;
  top: -10px;
  left: 20px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent transparent #1e2532 transparent;
}

.entity-name {
  font-weight: bold;
  margin-bottom: 5px;
  font-size: 16px;
  color: #7575F0;
}

.entity-description {
  margin-bottom: 8px;
  line-height: 1.4;
}

.entity-category {
  font-size: 12px;
  color: #a0aec0;
  border-top: 1px solid #3a3f52;
  padding-top: 5px;
  margin-top: 5px;
}

.entity-category span {
  background-color: #2d3748;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
  margin-left: 4px;
}

/* Mobile styles */
@media (max-width: 640px) {
  .tag {
    font-size: 10px !important; /* Smaller font for mobile */
    padding: 3px 8px !important; /* Smaller padding for mobile */
    border: 1px solid white !important; /* White outline for mobile */
    margin-right: 5px !important; /* Less space between tags */
    margin-bottom: 5px !important; /* Add bottom margin */
  }
  
  /* Save button container on mobile */
  [id^="save-button-"] {
    min-width: 50px !important;
    min-height: 50px !important;
    width: 50px !important;
    height: 50px !important;
    padding: 3px !important;
  }
}
#!/bin/bash
# Deployment script for fastmoatless

# Ensure script exits on error
set -e

echo "Starting deployment of fastmoatless..."

# Check if running with sudo
if [ "$EUID" -ne 0 ]; then
  echo "Please run as root or with sudo"
  exit 1
fi

# Update system and install dependencies
echo "Updating system and installing dependencies..."
apt-get update
apt-get install -y \
  git \
  python3 \
  python3-pip \
  python3-venv \
  poppler-utils

# Create directory if it doesn't exist
DEPLOY_DIR="/opt/fastmoatless"
if [ ! -d "$DEPLOY_DIR" ]; then
  echo "Creating deployment directory $DEPLOY_DIR..."
  mkdir -p "$DEPLOY_DIR"
fi

# Clone or update repository
if [ ! -d "$DEPLOY_DIR/.git" ]; then
  echo "Cloning repository to $DEPLOY_DIR..."
  # Clone first to temporary location to avoid permission issues
  git clone https://github.com/yourusername/fastmoatless.git /tmp/fastmoatless
  mv /tmp/fastmoatless/* "$DEPLOY_DIR"
  mv /tmp/fastmoatless/.git* "$DEPLOY_DIR" 2>/dev/null || true
  rmdir /tmp/fastmoatless
else
  echo "Updating existing repository..."
  cd "$DEPLOY_DIR"
  git pull
fi

# Set up virtual environment if it doesn't exist
VENV_DIR="$DEPLOY_DIR/venv"
if [ ! -d "$VENV_DIR" ]; then
  echo "Creating virtual environment..."
  python3 -m venv "$VENV_DIR"
fi

# Activate virtual environment and install dependencies
echo "Installing Python dependencies..."
source "$VENV_DIR/bin/activate"
pip install --upgrade pip
pip install -r "$DEPLOY_DIR/requirements.txt"

# Create directories for data storage if needed
mkdir -p "$DEPLOY_DIR/paper_texts"

# Set correct permissions
echo "Setting permissions..."
chown -R $(logname):$(logname) "$DEPLOY_DIR"

# Create systemd service file for automatic startup
SERVICE_FILE="/etc/systemd/system/fastmoatless.service"
echo "Creating systemd service..."
cat > "$SERVICE_FILE" << EOF
[Unit]
Description=Fastmoatless AI Research Service
After=network.target

[Service]
User=$(logname)
WorkingDirectory=$DEPLOY_DIR
ExecStart=$VENV_DIR/bin/python $DEPLOY_DIR/main.py
Restart=always
RestartSec=5
Environment=PATH=$VENV_DIR/bin:/usr/local/bin:/usr/bin:/bin

[Install]
WantedBy=multi-user.target
EOF

# Reload systemd, enable and start service
echo "Configuring and starting service..."
systemctl daemon-reload
systemctl enable fastmoatless
systemctl restart fastmoatless

echo "Deployment completed successfully!"
echo "Service status:"
systemctl status fastmoatless --no-pager

echo "To view logs: journalctl -u fastmoatless -f"
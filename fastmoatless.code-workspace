{
  "folders": [
    {
      "path": "."
    }
  ],
  "settings": {
    "geminicodeassist.enableTelemetry": false,
    "mdb.presetConnections": [
      {
        "name": "Preset Connection",
        "connectionString": "mongodb://localhost:27017"
      }
    ],
    "python.testing.pytestArgs": [
      "tests"
    ],
    "python.testing.unittestEnabled": false,
    "python.testing.pytestEnabled": true,
    // Playwright configuration
    "playwright.env": {
      "BASE_URL": "http://localhost:8000"
    },
    "playwright.reuseBrowser": true,
    "playwright.showBrowser": false,
    "playwright.testDir": "${workspaceFolder}/tests/e2e-ts",
    "playwright.testMatch": "**/*.spec.ts",
    "playwright.browsers": [
      "chromium"
    ],
    "playwright.headed": false,
    // Editor configuration for TypeScript/JavaScript
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.fixAll": true
    },
    "[typescript]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    // Test Explorer configuration
    "testExplorer.useNativeTesting": true,
    "testExplorer.addToEditorContextMenu": true,
    "testExplorer.mergeSuites": true,
    "testExplorer.hideEmptyLog": true,
    "testExplorer.showCollapseButton": true,
    "testExplorer.codeLens": true,
    "testExplorer.gutterDecoration": true,
    "testExplorer.errorDecoration": true,
    "testExplorer.errorDecorationHover": true,
    "testExplorer.sort": "byLocation",
    "testExplorer.showOnStart": true
  },
  "extensions": {
    "recommendations": [
      "ms-playwright.playwright",
      "ms-python.python",
      "ms-python.vscode-pylance",
      "esbenp.prettier-vscode",
      "dbaeumer.vscode-eslint",
      "ms-vscode.test-adapter-converter",
      "hbenl.vscode-test-explorer"
    ]
  },
  "tasks": {
    "version": "2.0.0",
    "tasks": []
  },
  "launch": {
    "version": "0.2.0",
    "configurations": [
      {
        "name": "Debug Playwright Tests",
        "type": "python",
        "request": "launch",
        "module": "pytest",
        "args": [
          "${file}",
          "-v",
          "--no-header",
          "--showlocals"
        ],
        "cwd": "${workspaceFolder}",
        "console": "integratedTerminal",
        "justMyCode": false
      },
      {
        "name": "Debug Current Test File",
        "type": "python",
        "request": "launch",
        "module": "pytest",
        "args": [
          "${relativeFile}",
          "-v",
          "--no-header",
          "--showlocals"
        ],
        "cwd": "${workspaceFolder}",
        "console": "integratedTerminal",
        "justMyCode": false
      },
      {
        "name": "Debug All E2E Tests",
        "type": "python",
        "request": "launch",
        "module": "pytest",
        "args": [
          "tests/e2e",
          "-v",
          "--no-header"
        ],
        "cwd": "${workspaceFolder}",
        "console": "integratedTerminal",
        "justMyCode": false
      },
      {
        "name": "Debug Python Tests",
        "type": "python",
        "request": "launch",
        "module": "pytest",
        "args": [
          "${file}"
        ],
        "console": "integratedTerminal",
        "justMyCode": false
      }
    ]
  }
}
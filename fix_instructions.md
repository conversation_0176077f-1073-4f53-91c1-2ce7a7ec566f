# Instructions for Fixing Entity Resolution

We've created a single script that fixes entity format inconsistencies and runs entity resolution in one process. This ensures all data is processed consistently using the same MongoDB connection.

## How to Run

1. Copy or download the `fix_and_resolve_entities.py` script to your environment
2. Make sure you have the required environment variables set:
   - `GEMINI_API_KEY` - Your API key
   - `GEMINI_BASE_URL` - The base URL for the API
   - `GEMINI_MODEL` - The model to use

3. Run the script on your VM:
   ```bash
   python fix_and_resolve_entities.py --force
   ```

   If you want to test locally first:
   ```bash
   python fix_and_resolve_entities.py --local --force --test-data
   ```

## What the Script Does

1. Finds all summaries with missing or empty entities field
2. Creates default entities based on tags or title words for these summaries
3. Updates the summaries with these default entities
4. Runs entity resolution on all summaries to create mappings and resolved entities

## Troubleshooting

- Check the logs in `logs/entity_fix_resolution.log` for detailed information
- If you're having connection issues, ensure your MongoDB connection variables are set correctly
- Entity resolution should work with all common entity formats:
  - List format: `[["entity_name", "category_name"]]`
  - Dictionary format: `[{"value": "entity_name", "category": "category_name"}]`

## Important Notes

- This is a one-time fix to ensure all summaries have entities
- The script is designed to be idempotent (can be run multiple times safely)

#!/usr/bin/env python3
"""
FastMoatless - Paper Summarization and Analysis App
"""
import asyncio
import os
import sys
import argparse
import json
import uuid
import time
import random
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import numpy as np

# Import dependencies
from fasthtml.common import *
from monsterui.all import *
from fasthtml.fastapp import fast_app
from fasthtml import Beforeware
from fasthtml.oauth import OAuth, GoogleAppClient, redir_url
from starlette.responses import HTMLResponse, JSONResponse, RedirectResponse
from pydantic import BaseModel
from neo4j import GraphDatabase
import psycopg2
from psycopg2.extras import execute_values
from pgvector.psycopg2 import register_vector

# Parse command line arguments
parser = argparse.ArgumentParser(description='Run the Moatless app')
parser.add_argument('--local', action='store_true', help='Use local MongoDB and Neo4j for testing with faster startup')
args = parser.parse_args()

# Initialize environment and database connections
from src.startup.config import setup_environment
from src.startup.db import setup_database_connections, is_neo4j_available
from src.startup.utilities import get_knowledge_graph_data as get_kg_data
from src.startup.tasks import (
    start_update_task,
    run_kg_extraction,
    sync_kg_to_neo4j,
    start_entity_resolution
)

# Setup environment and clients
openai_client, genai_client, arxiv_url = setup_environment(args.local)

# Setup database connections
collections, neo4j_driver = setup_database_connections(args.local)

# Extract collections for ease of use
summaries_collection = collections['summaries']
full_texts_collection = collections['full_texts']
daily_stats_collection = collections['daily_stats']
sota_collection = collections['sota_results']
reviews_collection = collections['reviews']
knowledge_graphs_collection = collections['knowledge_graphs']
saved_papers_collection = collections['saved_papers']
entity_resolution_collection = collections['entity_resolution']
mermaid_diagrams_collection = collections['mermaid_diagrams']

# Flag for local mode
local_mode = args.local

from monsterui.all import Theme
# Choose a theme color (blue, slate, zinc, gray etc)
base_hdrs = Theme.slate.headers()

# Add favicon links to the headers
def custom_headers():
    # Start with the base headers from the theme
    headers = base_hdrs.copy()

    # Add favicon links
    favicon_links = [
        Link(rel="icon", href="/assets/favicon/favicon.ico", type="image/x-icon"),
        Link(rel="shortcut icon", href="/assets/favicon/favicon.ico", type="image/x-icon"),
        Link(rel="apple-touch-icon", href="/assets/favicon/apple-touch-icon.png"),
        # Add dark mode favicon support
        Link(rel="icon", href="/assets/favicon/favicon-dark.ico", media="(prefers-color-scheme: dark)"),
    ]

    # Add the favicon links to the headers
    headers.extend(favicon_links)

    return headers

hdrs = custom_headers()

# Create beforeware for auth
auth_callback_path = "/auth_redirect"

def before(request, session):
    # if not logged in, we send them to our login page
    # logged in means:
    # - 'user_id' in the session object,
    # - 'auth' in the request object

    # Skip authentication check if we're in local mode
    if local_mode:
        # Set a dummy auth value for local testing
        request.scope['auth'] = 'local_test_user'
        return

    auth = request.scope['auth'] = session.get('user_id', None)
    user_email = session.get('user_email', None)

    if not auth or user_email != "<EMAIL>": return RedirectResponse('/login', status_code=303)
    # Don't need xtra right now because all users will see the same base feed
    #summaries.xtra(name=auth)

# Always assume FastHTML is available
# Get app and router components from fasthtml
from fasthtml import Beforeware
from fasthtml.fastapp import fast_app

# Add robust path exclusions for auth
excluded_paths = [
        r'/assets/favicon/.*',
        r'/static/.*',
        r'.*\.css',
        r'.*\.js',
        r'.*\.svg',
        r'.*\.png',
        r'.*\.jpg',
        r'.*\.jpeg',
        r'.*\.gif',
        '/login',
        auth_callback_path
    ]
bware = Beforeware(before, skip=excluded_paths)

# Import directly from fasthtml
from fasthtml.oauth import OAuth, GoogleAppClient

# Setup OAuth client
google_client = GoogleAppClient(os.getenv("AUTH_CLIENT_ID"),
                        os.getenv("AUTH_CLIENT_SECRET"))

# We will use this in our `exception_handlers` dict
def _not_found(request, exc): return Titled('Oh no!', Div('We could not find that page :('))

app, rt = fast_app(before=bware,
                      exception_handlers={404: _not_found},
                      hdrs=hdrs,
                      default_hdrs=False) # use the default_hdrs for the search bar

# Add entity tooltip JavaScript to all pages
entity_tooltip_script = """
document.addEventListener('click', function(event) {
  // Check if the clicked element is an entity highlight
  if (event.target.classList.contains('entity-highlight')) {
    // Remove any existing tooltips
    document.querySelectorAll('.entity-popup').forEach(el => el.remove());

    const entity = event.target.getAttribute('data-entity');
    const absUrl = event.target.getAttribute('data-abs-url');

    // Fetch entity description
    fetch(`/get_entity_description?entity=${encodeURIComponent(entity)}&abs_url=${encodeURIComponent(absUrl)}`)
      .then(response => response.json())
      .then(data => {
        // Create tooltip element
        const tooltip = document.createElement('div');
        tooltip.className = 'entity-popup';
        tooltip.style.position = 'absolute';
        tooltip.style.zIndex = '1000';
        tooltip.style.backgroundColor = '#1e2532';
        tooltip.style.color = 'white';
        tooltip.style.border = '1px solid #3a3f52';
        tooltip.style.borderRadius = '8px';
        tooltip.style.padding = '12px';
        tooltip.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.25)';
        tooltip.style.maxWidth = '300px';
        tooltip.style.minWidth = '200px';

        // Position tooltip near the entity highlight
        const rect = event.target.getBoundingClientRect();
        tooltip.style.top = `${rect.bottom + window.scrollY + 5}px`;
        tooltip.style.left = `${rect.left + window.scrollX}px`;

        // Add content to tooltip
        tooltip.innerHTML = `
          <h4 style="margin-top: 0; font-weight: bold;">${data.entity || entity}</h4>
          <p style="margin-bottom: 0;">${data.description || 'No description available'}</p>
        `;

        // Add tooltip to the document
        document.body.appendChild(tooltip);

        // Close tooltip when clicking outside
        document.addEventListener('click', function closeTooltip(e) {
          if (!tooltip.contains(e.target) && e.target !== event.target) {
            tooltip.remove();
            document.removeEventListener('click', closeTooltip);
          }
        });
      })
      .catch(error => {
        console.error('Error fetching entity description:', error);
      });
  }
});
"""

@app.middleware("http")
async def add_entity_tooltip_script(request, call_next):
    """Add entity tooltip script to all HTML responses"""
    response = await call_next(request)

    # Only add script to HTML responses and check if it's a regular response with a body attribute
    if (response.headers.get("content-type", "").startswith("text/html") and
        hasattr(response, "body")):
        content = response.body.decode()

        # Check if the content ends with </body></html>
        if "</body></html>" in content:
            # Insert script before closing body tag
            content = content.replace("</body>", f"<script>{entity_tooltip_script}</script></body>")
            response.body = content.encode()

            # Update content-length header
            if "content-length" in response.headers:
                response.headers["content-length"] = str(len(response.body))

    return response

# Register all API routes from src/api
from src.api import register_all_routes
register_all_routes(app, rt, collections)

# Handle tag routes
@rt("/tag/{tag_name}")
async def tag_page(request, tag_name: str):
    """Redirect to tag search"""
    return RedirectResponse(url=f"/?tag_name={tag_name}", status_code=303)

# Serve favicon files from assets/favicon directory
@app.get('/favicon.ico')
async def favicon():
    """Redirect to favicon in assets folder"""
    return RedirectResponse(url='/assets/favicon/favicon.ico')

# Serve static assets
@app.get('/assets/{path:path}')
async def serve_assets(path: str):
    """Serve static assets from the assets directory"""
    from starlette.responses import FileResponse
    return FileResponse(f'assets/{path}')

app.state.summaries_collection = collections['summaries']

# Define startup event handlers

@app.on_event("startup")
async def startup_update_task():
    """Start the update task for fetching and processing papers"""
    if not local_mode:
        await start_update_task(arxiv_url, collections, openai_client)
    else:
        print("Local mode: Using pre-generated data for faster startup")

@app.on_event("startup")
async def startup_neo4j_sync_task():
    """Start a background task for MongoDB to Neo4j synchronization"""
    asyncio.create_task(sync_kg_to_neo4j(knowledge_graphs_collection, neo4j_driver, local_mode))

@app.on_event("startup")
async def startup_entity_resolution():
    """Start entity resolution process at startup"""
    await start_entity_resolution(collections, local_mode)

@app.on_event("startup")
def startup_add_embeddings():
    """Add synthetic embeddings for local mode"""
    from src.startup.tasks import add_synthetic_embeddings
    add_synthetic_embeddings(collections, local_mode=local_mode)

# Flag to enable/disable ratings functionality
ratings_enabled = False

# Helper function for knowledge graph data
def get_knowledge_graph_data(limit=100):
    """Wrapper for the KG data function from utilities module"""
    return get_kg_data(neo4j_driver, limit)

@app.get('/login')
def login(request):
    redir = redir_url(request, auth_callback_path)
    login_link = google_client.login_link(redir)
    # Return a more styled login page
    return Title("Login to Moatless"), Body(
        Div(
            Div(
                H1("moatless", cls="text-4xl font-bold text-white mb-6 text-center"),
                Div(
                    P("Access your personalized AI research dashboard", cls="text-lg text-gray-400 mb-8 text-center"),
                    A('Sign in with Google',
                      href=login_link,
                      cls="inline-flex items-center px-6 py-3 bg-gradient-to-r from-slate-500 to-slate-700 text-white rounded-lg font-medium text-base transition-all duration-200 hover:shadow-lg hover:from-slate-400 hover:to-slate-600 transform hover:-translate-y-1"),
                    cls="flex flex-col items-center"
                ),
                cls="bg-[#273142] p-6 sm:p-10 rounded-xl shadow-lg max-w-md w-full mx-4"
            ),
            cls="min-h-screen flex items-center justify-center bg-[#1e2532] p-4"
        )
    )

# User login route is handled above at the auth_callback_path
@app.get(auth_callback_path)
def auth_redirect(code: str, request, session):
    print("Redirection successful!")
    redir = redir_url(request, auth_callback_path)
    user_info = google_client.retr_info(code, redir)
    user_id = str(user_info[google_client.id_key])
    user_name = user_info['name']

    session['user_id'] = user_id
    session["user_name"] = user_name
    session["user_email"] = user_info['email']

    # Update or insert user in MongoDB
    users_collection = collections.get('users')
    if users_collection is not None:
        users_collection.update_one(
            {'user_id': user_id},
            {'$set': {'user_id': user_id, 'name': user_name}},
            upsert=True
        )

    print("Updated MongoDB with user_id")

    return RedirectResponse('/', status_code=303)

@app.get('/logout')
def logout(session):
    session.pop('user_id', None)
    # Show a success message before redirecting
    return Title("Logged Out"), Body(
        Div(
            Div(
                H2("You have been logged out", cls="text-2xl font-semibold text-white mb-6"),
                P("Redirecting you to the homepage...", cls="text-gray-400 mb-6"),
                A("Return to Home", href="/", cls="px-5 py-2.5 bg-gradient-to-r from-slate-500 to-slate-700 text-white rounded-lg hover:from-slate-400 hover:to-slate-600 transition-all duration-200 inline-block"),
                Script("setTimeout(function() { window.location.href = '/'; }, 2000);"),
                cls="bg-[#273142] p-8 rounded-xl shadow-lg max-w-md w-full text-center"
            ),
            cls="min-h-screen flex items-center justify-center bg-[#1e2532] p-4"
        )
    )

@rt("/")
async def get(request: Request, session, entity_name: str = None, tag_name: str = None, feed: str = "normal"):
    from src.ui import Paper, SearchForm, Navigation
    from src.services.recommendations import recommend_papers

    session = request.session  # Access the session from the request

    # Initialize session ID if it doesn't exist
    if 'session_id' not in session:
        session['session_id'] = str(uuid.uuid4())

    # Initialize selected_entity if it doesn't exist
    if 'selected_entity' not in session:
        session['selected_entity'] = None

    session["last_search"] = None

    # Get the selected entity from the session
    selected_entity = session.get('selected_entity', None)

    # Get the selected tag from the session
    selected_tag = session.get('selected_tag', None)

    # Get the previously selected entity from the session
    previously_selected_entity = session.get('previously_selected_entity', None)

    # Get user ID from session (default if not authenticated)
    user_id = request.session.get('user_id', "default")

    # Determine which feed to show based on the feed parameter
    latest_summaries = []
    feed_title = "the latest AI research at your fingertips."
    feed_description = ""

    if feed == "recommended":
        # Fetch recommended papers for the user
        recommendations = recommend_papers(user_id, collections, top_n=30)

        # If no recommendations available but user requested recommended feed, show message and fallback to normal feed
        if not recommendations:
            feed_title = "personalized recommendations"
            feed_description = "Save some papers to get recommendations based on your interests."

            # Fetch latest papers as fallback
            latest_summaries = list(summaries_collection.find(
                {},
                sort=[('published', -1)],
                limit=100
            ))
        else:
            feed_title = "papers recommended for you"
            feed_description = "Based on your saved papers and reading history."

            # Convert recommendation results to paper objects
            recommended_urls = [paper_url for paper_url, _, _ in recommendations]

            # Fetch full paper details for each recommendation
            for paper_url, similarity_score, details in recommendations:
                paper = summaries_collection.find_one({"abs_url": paper_url})
                if paper:
                    # Add recommendation metadata to the summary
                    paper['recommendation_score'] = similarity_score
                    paper['recommendation_details'] = details
                    latest_summaries.append(paper)
    else:
        # Build query based on filters for normal feed
        query = {}
        if entity_name and tag_name:
            query = {
                'abstract': {'$regex': entity_name, '$options': 'i'},
                'tags': tag_name
            }
        elif entity_name:
            query = {'abstract': {'$regex': entity_name, '$options': 'i'}}
        elif tag_name:
            query = {'tags': tag_name}

        # Fetch latest summaries for normal feed
        latest_summaries = list(summaries_collection.find(
            query,
            sort=[('published', -1)],
            limit=100
        ))

    # Check if each paper has a mermaid diagram and include it directly
    for paper in latest_summaries:
        # Check for mermaid diagrams
        mermaid_doc = mermaid_diagrams_collection.find_one({'abs_url': paper['abs_url']})
        if mermaid_doc:
            paper['has_mermaid'] = True
            paper['mermaid_diagram'] = mermaid_doc.get('mermaid_diagram')
        else:
            paper['has_mermaid'] = False
        
        # Check for tables
        paper_id = paper['abs_url'].split('/')[-1]
        tables = collections['tables'].find({'paper_id': paper_id}).count()
        if tables > 0:
            paper['has_tables'] = True
            paper['table_count'] = tables

    # Create components for papers with recommendation score display if available
    paper_components = []
    for paper in latest_summaries:
        if 'recommendation_score' in paper:
            # Add a recommendation badge component
            paper_component = Div(
                # Add recommendation badge
                Div(
                    Span(f"Match: {paper['recommendation_score']}%",
                         cls="inline-block px-3 py-1 text-sm font-medium rounded-md bg-blue-600 text-white"),
                    cls="absolute top-4 right-4 z-10"
                ),
                # Standard Paper component
                Paper(paper, session, entity_name, selected_entity, selected_tag),
                cls="relative paper-wrapper"
            )
            paper_components.append(paper_component)
        else:
            # Just use standard Paper component for normal feed
            paper_components.append(Paper(paper, session, entity_name, selected_entity, selected_tag))

    return Navigation(request), Title("moatless"), Body(
        Div(  # Outer container
            Div(  # Hero section with simplified header content
                Div(  # Content wrapper with max width
                    H1("moatless",
                       cls="text-5xl sm:text-5xl md:text-7xl 2xl:text-7xl font-bold text-white mb-4 sm:mb-6 2xl:mb-4 text-center"),
                    H3(feed_title,
                       cls="text-2xl sm:text-2xl md:text-3xl 2xl:text-3xl text-center text-gray-400 font-light tracking-wide mb-2 sm:mb-4 2xl:mb-2"),
                    P(feed_description,
                      cls="text-lg text-center text-gray-500 mb-5 sm:mb-8 2xl:mb-6") if feed_description else None,
                    SearchForm(feed=feed),
                    cls="flex flex-col items-center w-full max-w-5xl mx-auto px-4 sm:px-6"
                ),
                cls="py-8 sm:py-10 md:py-12 2xl:py-8 w-full bg-[#1e2532] border-b-0"
            ),
            Div(  # Content container
                *paper_components,
                cls="w-full max-w-4xl 2xl:max-w-[1200px] mx-auto px-4 sm:px-6 py-6 sm:py-8 2xl:py-6"
            ),
            cls="min-h-screen bg-[#1e2532] flex flex-col items-center w-full border-t-0"
        )
    )

@app.get('/paper/{paper_id}')
def paper_detail(paper_id: str, request):
    """View paper details"""
    from src.ui import Navigation, TablesSection

    # Extract the full abs_url from the paper ID
    abs_url = f"https://arxiv.org/abs/{paper_id}"

    # Find the paper
    paper = summaries_collection.find_one({'abs_url': abs_url})

    if not paper:
        return Navigation(request), Title("Paper Not Found"), Body(
            Div(
                Div(
                    H3("Paper Not Found", cls="text-3xl font-semibold text-white mb-4"),
                    P("The requested paper doesn't exist in our database.",
                      cls="text-2xl text-gray-400 mb-6"),
                    A("Back to Home", href="/",
                      cls="mt-6 inline-block px-6 py-4 text-xl bg-slate-700 hover:bg-slate-600 text-white rounded-md transition-all duration-200"),
                    cls="text-center py-16"
                ),
                cls="w-full max-w-4xl mx-auto px-6 sm:px-8 py-8 sm:py-12"
            )
        )

    # Check if a mermaid diagram exists for this paper
    mermaid_doc = mermaid_diagrams_collection.find_one({'abs_url': abs_url})
    has_mermaid = mermaid_doc is not None

    # Check if the paper has tables
    has_tables = paper.get('has_tables', False)
    table_count = paper.get('table_count', 0)

    # Build the page with paper details
    return Navigation(request), Title(f"Paper: {paper.get('title', 'Unknown')}"), Body(
        Div(
            Div(
                Div(
                    H2(paper.get('title', 'Unknown'), cls="text-4xl sm:text-5xl font-bold text-white mb-6"),
                    P(f"Authors: <AUTHORS>
                    P(f"Published: {paper.get('published', 'Unknown')}", cls="text-lg text-gray-400 mb-6"),
                    A("View on arXiv", href=paper.get('abs_url', '#'), target="_blank",
                      cls="inline-block px-4 py-2 mb-8 mr-4 text-xl bg-blue-700 hover:bg-blue-600 text-white rounded-md transition-all duration-200"),
                    A("View Knowledge Graph", href=f"/mermaid/{paper_id}",
                      cls="inline-block px-4 py-2 mb-8 text-xl bg-slate-700 hover:bg-slate-600 text-white rounded-md transition-all duration-200") if has_mermaid else None,
                    cls="w-full max-w-4xl mx-auto px-4 sm:px-6"
                ),
                cls="py-12 sm:py-14 w-full bg-[#1e2532] border-b border-gray-800"
            ),
            Div(
                Div(
                    H3("Abstract", cls="text-3xl font-semibold text-white mb-6"),
                    P(paper.get('abstract', 'No abstract available'), cls="text-lg text-gray-300 mb-6"),
                    H3("Summary", cls="text-3xl font-semibold text-white mb-6"),
                    P(paper.get('summary', 'No summary available'), cls="text-lg text-gray-300 mb-6"),
                    H3("Tags", cls="text-3xl font-semibold text-white mb-6"),
                    Div(
                        *[Span(tag, cls="inline-block px-3 py-1 mr-2 mb-2 bg-blue-800 text-white rounded-full") for tag in paper.get('tags', [])],
                        cls="mb-6"
                    ) if paper.get('tags') else P("No tags available", cls="text-lg text-gray-300 mb-6"),

                    # Tables section
                    H3("Tables", cls="text-3xl font-semibold text-white mb-6"),
                    *TablesSection(paper_id, has_tables, table_count),
                    cls="w-full"
                ),
                cls="w-full max-w-4xl mx-auto px-6 sm:px-8 py-8 sm:py-12"
            ),
            cls="min-h-screen bg-[#1e2532] flex flex-col items-center w-full"
        )
    )

@app.get('/mermaid/{paper_id}')
def mermaid_diagram(paper_id: str, request):
    """View mermaid diagram for a specific paper"""
    from src.ui import Navigation

    # Extract the full abs_url from the paper ID
    abs_url = f"https://arxiv.org/abs/{paper_id}"

    # Find the mermaid diagram
    mermaid_doc = mermaid_diagrams_collection.find_one({'abs_url': abs_url})

    if not mermaid_doc or not mermaid_doc.get('mermaid_diagram'):
        # Check if we need to generate a mermaid diagram for this paper
        paper = summaries_collection.find_one({'abs_url': abs_url})
        if paper and paper.get('summary'):
            # Set up a message that diagram will be generated soon
            return Navigation(request), Title("Mermaid Diagram Coming Soon"), Body(
                Div(
                    Div(
                        H3("Mermaid Diagram Coming Soon", cls="text-3xl font-semibold text-white mb-4"),
                        P("We're generating a knowledge graph visualization for this paper. Please check back in a few minutes.",
                          cls="text-2xl text-gray-400 mb-6"),
                        A("Back to Paper", href=f"/paper/{paper_id}",
                          cls="mt-6 inline-block px-6 py-4 text-xl bg-slate-700 hover:bg-slate-600 text-white rounded-md transition-all duration-200 mr-4"),
                        A("Back to Home", href="/",
                          cls="mt-6 inline-block px-6 py-4 text-xl bg-slate-700 hover:bg-slate-600 text-white rounded-md transition-all duration-200"),
                        cls="text-center py-16"
                    ),
                    cls="w-full max-w-4xl mx-auto px-6 sm:px-8 py-8 sm:py-12"
                )
            )
        else:
            # No paper found or no summary available
            return Navigation(request), Title("Mermaid Diagram Not Found"), Body(
                Div(
                    Div(
                        H3("Mermaid Diagram Not Found", cls="text-3xl font-semibold text-white mb-4"),
                        P("The requested diagram hasn't been generated yet or doesn't exist.",
                          cls="text-2xl text-gray-400 mb-6"),
                        A("Back to Home", href="/",
                          cls="mt-6 inline-block px-6 py-4 text-xl bg-slate-700 hover:bg-slate-600 text-white rounded-md transition-all duration-200"),
                        cls="text-center py-16"
                    ),
                    cls="w-full max-w-4xl mx-auto px-6 sm:px-8 py-8 sm:py-12"
                )
            )

    # Get associated paper details
    paper = summaries_collection.find_one({'abs_url': abs_url})
    title = paper.get('title', 'Unknown Paper') if paper else 'Unknown Paper'

    # Build the page with the mermaid diagram
    return Navigation(request), Title(f"Mermaid Diagram: {title}"), Body(
        Div(
            Div(
                Div(
                    H2(title, cls="text-4xl sm:text-5xl font-bold text-white mb-6"),
                    A("Back to Paper", href=f"/paper/{paper_id}",
                      cls="inline-block px-4 py-2 mb-8 text-xl bg-slate-700 hover:bg-slate-600 text-white rounded-md transition-all duration-200"),
                    cls="w-full max-w-4xl mx-auto px-4 sm:px-6"
                ),
                cls="py-12 sm:py-14 w-full bg-[#1e2532] border-b border-gray-800"
            ),
            Div(
                Div(
                    H3("Knowledge Graph Visualization", cls="text-3xl font-semibold text-white mb-6"),
                    Div(
                        f"""<div class="mermaid">
{mermaid_doc.get('mermaid_diagram', '')}
</div>
<script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {{
        mermaid.initialize({{
            startOnLoad: false,
            theme: 'dark',
            securityLevel: 'loose',
            logLevel: 5, // Silence logging (1=debug, 2=info, 3=warn, 4=error, 5=fatal)
            fontFamily: "Geist, sans-serif"
        }});
        setTimeout(function() {{
            mermaid.init(undefined, document.querySelectorAll('.mermaid'));
        }}, 500);
    }});
</script>""",
                        cls="bg-gray-900 p-4 rounded-lg overflow-auto"
                    ),
                    cls="w-full"
                ),
                cls="w-full max-w-4xl mx-auto px-6 sm:px-8 py-8 sm:py-12"
            ),
            cls="min-h-screen bg-[#1e2532] flex flex-col items-center w-full"
        )
    )

# Mermaid API endpoint moved to src/api/mermaid.py

serve()

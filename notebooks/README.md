# Notebooks

This directory contains Ju<PERSON><PERSON> notebooks for various exploratory and development tasks.

## Notebooks

### benchmark_extraction.ipynb
A notebook for extracting text from PDF files using spaCy and spaCy_layout. This notebook demonstrates how to process scientific papers and extract structured information.

### analyzing_entities.ipynb
A notebook for analyzing entities in the MongoDB database. It connects to the database and explores the entity data.

### paper_review.ipynb
A notebook for reviewing and analyzing research papers. It fetches papers from arXiv and processes them.

### testing_canvas.ipynb
A notebook for testing various NLP functionalities, including Named Entity Recognition (NER) and State-of-the-Art (SOTA) extraction.

## Usage

To run these notebooks, make sure you have the required dependencies installed:

```bash
pip install jupyter spacy spacy-layout pymongo python-dotenv openai
```

You may also need to install specific models for spaCy:

```bash
python -m spacy download en_core_web_sm
```

## Environment Variables

These notebooks require certain environment variables to be set. Create a `.env` file in the root directory with the following variables:

```
GEMINI_API_KEY=your_api_key
GEMINI_BASE_URL=your_base_url
GEMINI_MODEL=gemini-2.0-flash-001
MONGO_PUBLIC_URL=your_mongo_url
```

{"cells": [{"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["from pymongo import MongoClient\n", "import os\n", "\n", "from dotenv import load_dotenv, find_dotenv\n", "\n", "load_dotenv(find_dotenv())\n", "\n", "# MongoDB setup (as above)\n", "client = MongoClient(os.getenv(\"MONGO_PUBLIC_URL\"))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["An error occurred during iteration: operation cancelled\n"]}], "source": ["# Start an explicit session\n", "with client.start_session() as session:\n", "    # Use no_cursor_timeout and pass the session explicitly\n", "    cursor = client[\"papers\"][\"summaries\"].find(\n", "        {}, \n", "        no_cursor_timeout=True,\n", "        session=session\n", "    )\n", "    \n", "    try:\n", "        for document in cursor:\n", "            print(document)\n", "    except Exception as e:\n", "        print(f\"An error occurred during iteration: {e}\")\n", "    finally:\n", "        # Always close the cursor manually when not using a 'with' for it\n", "        cursor.close()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 2}
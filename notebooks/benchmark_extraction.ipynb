{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Development/fastmoatless/.venv/lib/python3.12/site-packages/urllib3/connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'huggingface.co'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/Development/fastmoatless/.venv/lib/python3.12/site-packages/urllib3/connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'huggingface.co'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}], "source": ["import spacy\n", "from spacy_layout import spaCyLayout\n", "\n", "nlp = spacy.blank(\"en\")\n", "layout = spaCyLayout(nlp)\n", "\n", "doc = layout(\"data/test/pdf/TxGemma.pdf\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["TxGemma:\n", "\n", "Efficient and Agentic LLMs for Therapeutics\n", "\n", "<PERSON> ∗ † , ,1 , <PERSON> ∗ ,1 , 1 2 2 <PERSON><PERSON> , <PERSON><PERSON> 2 1 , <PERSON> 1 and <PERSON><PERSON><PERSON><PERSON> † ,1\n", "\n", "<PERSON> , <PERSON> , <PERSON> , 1 Google DeepMind, 2 Google Research\n", "\n", "Therapeutic development is a costly and high-risk endeavor that is often plagued by high failure rates. To address this, we introduce TxGemma, a suite of efficient, generalist large language models (LLMs) capable of therapeutic property prediction as well as interactive reasoning and explainability. Unlike task-specific models, TxGemma synthesizes information from diverse sources, enabling broad application across the therapeutic development pipeline. The suite includes 2B, 9B, and 27B parameter models, fine-tuned from Gemma-2 on a comprehensive dataset of small molecules, proteins, nucleic acids, diseases, and cell lines. Across 66 therapeutic development tasks, TxGemma achieved superior or comparable performance to the state-of-the-art generalist model on 64 (superior on 45), and against state-of-the-art specialist models on 50 (superior on 26). Fine-tuning TxGemma models on therapeutic downstream tasks, such as clinical trial adverse event prediction, requires less training data than fine-tuning base LLMs, making TxGemma suitable for data-limited applications. Beyond these predictive capabilities, TxGemma features conversational models that bridge the gap between general LLMs and specialized property predictors. These allow scientists to interact in natural language, provide mechanistic reasoning for predictions based on molecular structure, and engage in scientific discussions. Building on this, we further introduce Agentic-Tx, a generalist therapeutic agentic system powered by Gemini 2.5 that reasons, acts, manages diverse workflows, and acquires external domain knowledge. Agentic-Tx surpasses prior leading models on the Humanity's Last Exam benchmark (Chemistry & Biology) with 52.3% relative improvement over o3-mini (high) and 26.7% over o3-mini (high) on GPQA (Chemistry). On ChemBench, TxGemma excels with improvements of 6.3% (ChemBench-Preference) and 2.4% (ChemBench-Mini) over o3-mini (high), as well as 17.7% and 5.6% over o1, respectively. TxGemma's collection is released as open models, enabling researchers to adapt and validate it on their own diverse datasets, thus facilitating more challenging real-world applications.\n", "\n", "1 Introduction\n", "\n", "The pharmaceutical industry faces significant challenges in bringing new therapeutics to market. High attrition rates and lengthy, costly development timelines [3, 4] necessitate innovative approaches to therapeutic development. Success requires a drug candidate to not only demonstrate efficacy but also possess favorable safety, metabolic stability, pharmacokinetic/pharmacodynamic properties and developability, among other characteristics. Determining these diverse characteristics often relies on a large array of complex and expensive experimental procedures, highlighting the need for more efficient methods.\n", "\n", "Computational approaches, such as machine learning, are emerging as powerful tools to address these challenges. Leveraging predictive models trained on curated datasets allows researchers to prioritize promising candidates early in the development process, reducing reliance on costly experimental assays [5]. Publicly available databases of molecular properties and biological activity are crucial for training and validating these models. In this area, a major development was the curation of the Therapeutics Data Commons (TDC) [6, 7, 8], which contains datasets and benchmarks for many different tasks throughout the therapeutic development pipeline, ranging from early-stage target identification to late-stage clinical trial approval.\n", "\n", "Recent advancements in large language models (LLMs) offer a compelling opportunity to leverage available datasets and address limitations in the therapeutic development process. LLMs have demonstrated the capacity to integrate and learn from diverse data sources across various domains, including scientific applications [9, 10,\n", "\n", "∗ Equal contributions.\n", "\n", "† Corresponding authors: {<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>}@google.com\n", "\n", "11]. Their potential to connect disparate aspects of drug development, such as chemical structure, biological activity, and clinical trial outcomes, is particularly exciting. In this context, we have previously introduced Tx-LLM, a LLM fine-tuned from a collection of question-answer instruction-tuning datasets based on TDC [12]. While promising, the model's lack of conversational capabilities prevented reasoning or user interaction, limiting its value for scientists who require a model that can understand complex queries and engage in nuanced discussions.\n", "\n", "In this work, we introduce TxGemma, a suite of efficient, generalist LLMs trained for therapeutics. Building on, but significantly extending, our previous work [12], TxGemma leverages LLMs to synthesize information from diverse sources. The suite includes 2B, 9B, and 27B parameter models, fine-tuned from Gemma-2 [13, 14] using a collection of therapeutic instruction-tuning datasets encompassing small molecules, proteins, nucleic acids, diseases, and cell lines. For the first time in therapeutic AI, TxGemma features conversational counterparts capable of reasoning and explanation, moving beyond black-box predictions to facilitate mechanistic understanding and scientific discussions. Our key contributions are as follows:\n", "\n", "· Efficient Generalist Therapeutic LLMs: TxGemma represents a potential shift from task-specific AI to efficient generalist models in therapeutic development. These efficient LLMs (2B-27B parameters) offer a competitive alternative to specialized models, achieving strong performance across a broad range of predictive and generative tasks. Out of 66 therapeutic development tasks curated by TDC, TxGemmaPredict outperforms or nearly matches the state-of-the-art generalist model on 64 (outperforms on 45) and state-of-the-art specialist models on 50 (outperforms on 26). Additionally, fine-tuning TxGemma models on clinical trial adverse event prediction requires less data to achieve strong performance compared to base Gemma-2 models, an important advantage for data-limited fields.\n", "\n", "· Explainable and Interactive Therapeutic Models: TxGemma-Chat introduces reasoning and explanation capabilities, bridging the gap between general LLMs and specialized property predictors. Scientists can interact with TxGemma-Chat using natural language, exploring complex questions, receive explanations for predictions (e.g., based on molecular structure), and engage in scientific discussions.\n", "\n", "· Agentic Orchestration of Therapeutic Development Workflows: We further introduce Agentic-Tx, a therapeutics-focused agentic system powered by Gemini 2.5, demonstrating how TxGemma models can be integrated as tools. Equipped with 18 tools, Agentic-Tx solves complex, multi-step problems, achieving state-of-the-art results on reasoning-intensive chemistry and biology benchmarks, including Humanity's Last Exam [15] and ChemBench [1].\n", "\n", "· Enabling Innovative Research with Open Models: Understanding the prevalence of proprietary data in therapeutic research, we release TxGemma models trained only on datasets with commercial licenses as open models to empower researchers to adapt and refine them on their own data. This facilitates validation and potential performance improvements tailored to their specific research needs, paving the way for therapy safety and efficacy in more challenging real-world therapeutic applications.\n", "\n", "2 Methods\n", "\n", "2.1 Data\n", "\n", "Therapeutic Data Commons (TDC) We leverage the Therapeutic Data Commons (TDC) [7, 6], a comprehensive collection of 66 AI-ready datasets spanning the drug discovery and development pipeline. TDC includes over 15 million datapoints across various biomedical entities and encompasses single-instance prediction, multi-instance prediction, and generation tasks [7]. We focus on TDC tasks relevant to drug discovery, incorporating diverse therapeutic representations: SMILES strings (small molecules), amino acid sequences (proteins and peptides, including specialized representations for MHC molecules and T-cell receptors), nucleotide sequences (nucleic acids), and natural language text (disease/cell line names) (see Table S.6 for examples). Many tasks combine multiple representations. (See Table S.1 for task inclusion criteria and Tables S.7 and S.8 for biological contexts of certain tasks.)\n", "\n", "Therapeutic Instruction-Tuning Following <PERSON><PERSON> et al. [12], we transform the raw TDC data into an instruction-tuning format suitable for LLMs. Each data point is formatted as a prompt:\n", "\n", "· Instruction: <PERSON>riefly describes the task.\n", "\n", "· Question: Queries a specific therapeutic property, incorporating textual representations of therapeutics and/or targets (e.g., 'Does the following molecule cross the blood-brain barrier? <molecule>' ).\n", "\n", "· Context: Provides 2-3 sentences of relevant biochemical background, derived from TDC descriptions and literature.\n", "\n", "· Answer: Formatted as (A)/(B) for binary classification, a binned continuous value for regression, or a SMILES string for generation.\n", "\n", "This process yields 7,080,338 training, 956,575 validation, and 1,917,297 test data points (Figure S.1, Tables S.2 and S.3). Data splits closely follow TDC's recommended methodologies (random, scaffold, cold-start, combination, temporal) (Table S.2, Table S.3). Detailed task descriptions are in Tables S.4 and S.5.\n", "\n", "We employ a few-shot prompting strategy to promote in-context learning [16], using a blend of 70% zero-shot and 30% few-shot prompts [17, 12]. For few-shot prompts, we randomly sample examples from the training set (Table S.9), as intra-training set similarity is higher than training-test set similarity (Figure S.2). The number of examples is uniformly selected between 1 and 10 so that few-shot prompting is robust to the number of examples during evaluation.\n", "\n", "2.2 Modeling\n", "\n", "Base LLM. TxGemma is built upon the Gemma-2 [14] family of lightweight, state-of-the-art open LLMs. Gemma-2 models utilize a decoder-only transformer architecture, incorporating architectural modifications such as interleaved local-global attention and group-query attention, and are trained using Gemini technology [18]. We utilize Gemma-2 base models at 2B, 9B, and 27B parameters. 2B and 9B Gemma-2 models were initially trained via knowledge distillation [14].\n", "\n", "Predictive Model Fine-Tuning. We fine-tune the 2B, 9B, and 27B Gemma-2 base models on the therapeutic instruction-tuning data derived from TDC, creating TxGemma-2B-Predict, TxGemma-9B-Predict, and TxGemma-27B-Predict, respectively. Training was performed across all TDC tasks, with mixture ratios proportional to the number of training data points (see Tables S.2 and S.3 for data distribution). This encompassed all approximately 7 million training examples, comprising 3.3 million from regression/generation and 3.7 million from binary classification tasks. Fine-tuning proceeded for 67B tokens (12 epochs) using 256 TPUv4 chips with 8-way data replication, 4-way sequence sharding, and 4-way model sharding. In this work, 'TxGemma' generally refers to the generalist, predictive TxGemma-27B-Predict.\n", "\n", "Conversational Model Fine-Tuning. We also trained conversational counterparts, TxGemma-9B-Chat and TxGemma-27B-Chat, by supplementing the therapeutic instruction-tuning data with general instructiontuning data, as detailed in the Gemma-2 report [14]. The training data mixture comprised 30% therapeutic data and 70% general instruction-tuning data. Conversational models were trained using the same number of tokens and TPU configuration as the predictive models.\n", "\n", "2.3 Evaluating Predictive Performance\n", "\n", "Prompting strategy For test set evaluations, we use 10-shot prompting, selecting exemplars from the nearest neighbors within the combined training and validation set (not the test set), as detailed in Table S.9. Nearest neighbors were determined using different methods based on molecule type. For small molecules, we used RDKit [19] to generate Morgan fingerprints (radius 2 and size 2048), representing molecular substructures as binary vectors. Subsequently, we used Chemfp [20] to compute Tanimoto similarities, which quantify fingerprint overlap. For amino acid and nucleotide sequences, nearest neighbors were defined by percent sequence identity, determined through multiple sequence alignments performed with <PERSON><PERSON><PERSON> Omega [21].\n", "\n", "Performance Metrics and Statistical Tests We assess performance using the preferred metrics for each task, as defined by TDC [7] and used by previous models. Binary classification tasks are assessed with area under the receiver operating characteristic curve (AUROC), area under the precision-recall curve (AUPRC), and accuracy. Regression tasks use <PERSON><PERSON><PERSON>'s and <PERSON> correlation coefficients, mean absolute error (MAE), and mean squared error (MSE). The USPTO generation task uses \"set accuracy,\" scoring 1 for perfect overlap between predicted and true reactant sets, and 0 otherwise. Bootstrapped metrics are calculated\n", "\n", "using 1000 samples. To compare overall performance between two models across all TDC tasks, we use the non-parametric Wilcoxon signed-rank test and report the corresponding p-value (details in Appendix C.1).\n", "\n", "2.4 Agentic System\n", "\n", "One limitation of LLMs for discovery is that, while their prediction capabilities are powerful, they do not have access to up-to-date external knowledge, such as research articles or domain-specific prediction models. These knowledge cut-offs prevent the model from answering questions outside of its training scope. Additionally, some questions involve multiple reasoning steps to solve, for example, the question 'What structural modifications could improve the potency of the given drug?' requires iteratively searching the drug's structural space and then prompting TxGemma to predict potency.\n", "\n", "Agentic-Tx, our therapeutics-focused agentic system powered by Gemini 2.5 [18], extends TxGemma's capabilities by orchestrating such complex workflows. Agentic-Tx employs a modular, tool-usage paradigm, in contrast to TxGemma's direct generation of solutions.\n", "\n", "Reasoning and Action Framework Agentic-Tx utilizes the ReAct framework [22], allowing it to interleave reasoning steps ('thoughts') with actions (tool use). The agentic system receives a task or question and iteratively takes actions based on its current context. Each action typically involves using a tool, which\n", "\n", "returns an observation. Key to ReAct is this iterative process of observing, reasoning, and acting, allowing Agentic-Tx to dynamically adjust its approach based on the information it gathers. Because tools may return large outputs, we summarize these observations in order to maintain a concise and relevant context. This iterative process of observing, reasoning, acting, and updating its context allows Agentic-Tx to dynamically adjust its approach and gather the necessary information to answer the initial query. Finally, Agentic-Tx integrates the gathered information and formulates a user-friendly response.\n", "\n", "Agentic Tools Agentic-Tx is equipped with 18 tools across four categories (detailed tool descriptions are in Table S.12). They can be broadly categorized as:\n", "\n", "1. TxGemma-based Tools: These provide access to TxGemma's capabilities. The Chat tool enables interaction with TxGemma-27B-Chat. The ClinicalTox and ToxCast tools utilize TxGemma-27B-Predict for toxicity predictions. IC 50 returns the predicted normalized IC 50 between a drug and protein, the Mutagenicity tool predicts drug mutagenicity, and the Phase1 Trial tool predicts whether a drug would pass a Phase 1 clinical trial.\n", "\n", "3. Molecule Tools: These leverage domain-specific libraries for tasks such as retrieving molecular descriptors (e.g., from PubChem) and performing chemical structure conversions.\n", "\n", "2. General Tools: These query external knowledge resources, including PubMed, Wikipedia, and the web.\n", "\n", "4. Gene & Protein Tools: These leverage domain-specific libraries for tasks involving genes or proteins, such as retrieving gene descriptions and protein descriptions (e.g., from the NCBI Gene database).\n", "\n", "3 Results\n", "\n", "3.1 TxGemma Predictive Performance\n", "\n", "3.1.1 Comparison with best-in-class therapeutic models\n", "\n", "To provide a comprehensive evaluation of our models' predictive capabilities, we benchmark against both specialist and generalist baselines. For specialist comparisons, we define best-in-class performance metrics for each task using previous models. Specifically, we utilize TDC leaderboard scores for tasks where available (ADMET, DrugCombo, DTI DG). For remaining tasks, values are reported from a literature review and are detailed in Tables S.13 and S.14. These specialist performance values align with those reported in <PERSON><PERSON> et al. [12]. Additionally, we compare our models against three prominent therapeutic generalist and multi-task models: Tx-LLM [12], LlaSMol [23], and MolE [24]. Tx-LLM, with its two size-variants S and M, shares similar training data to our approach enabling a direct comparison across all tasks. LlaSMol a suite of generalist models built upon fine-tuned open-source LLMs trained for small-molecule applications [23]. Similarly, MolE was developed as a graph-based multi-task foundation model for small molecules. LlaSMol and MolE, specialized for small molecules, offer strong baselines for small molecule tasks.\n", "\n", "TxGemma shows improved performance compared to therapeutic generalist models In Figure 3, we compare the performance of TxGemma-27B-Predict to the two existing models in the Tx-LLM [12] family, Tx-LLM M and Tx-LLM S, built over PaLM-2 on TDC tasks. TxGemma-27B-Predict surpasses Tx-LLM M on 45 out of 66 tasks, while underperforming on 21. In addition, it outperforms Tx-LLM S on 62 and underperforms Tx-LLM S on only 4. Aggregating performance over task, we observe a statistically significant improvement of TxGemma-27B-Predict over Tx-LLM models (p=0.003, <PERSON>on signed-rank test). These results demonstrate that TxGemma provides a highly competitive alternative to its predecessor with improved functionality at a substantially reduced model size.\n", "\n", "TxGemma is competitive with specialist therapeutic models Figure 4 and Figure S.4 compare TxGemma's performance with best-in-class specialist model across tasks containing various combinations of SMILES, amino acid, nucleotide, and text inputs. In a comparison with specialist best-in-class models, TxGemma-27B-Predict outperforms the state-of-the-art (SOTA) on 26 and performs near SOTA on 50. This is a substantial improvement over its predecessor Tx-LLM M, which outperformed SOTA on 22 tasks and near SOTA on 43. These results demonstrate the improved capabilities of TxGemma-27B-Predict and its competitiveness with current specialist models designed for specific tasks and therapeutic feature types.\n", "\n", "TxGemma performs similarly to multi-task models specialized for small molecules Table 1 and Figure S.6 compare the predictive performance of TxGemma-27B-Predict with MolE, a graph-based multi-task foundation model for small molecules. MolE performs within the 95% CIs of TxGemma-27B-Predict for 15 out of 22 tasks. Furthermore, both TxGemma-27B-Predict and TxGemma-9B-Predict outperform LlaSMol Mistral (7B), the top performing model from the LlaSMol suite, on 2 of 5 shared tasks and within 95% CIs on 2 additional tasks (Table 2 and Figure S.5). All metrics for MolE and LlaSMol are reported from <PERSON>dez<PERSON> et al. [24] and <PERSON> et al. [23]. Given their specialization in small-molecule tasks, LlaSMol and MolE provide strong baselines for evaluating generalist models. Notably, TxGemma, a generalist model encompassing diverse drug types and many different tasks, achieves competitive performance with these dedicated models designed for a narrower range of small-molecule tasks.\n", "\n", "3.2 TxGemma Conversational Capabilities\n", "\n", "While TxGemma-27B-Predict performs well on prediction tasks, training solely on instruction tuning data for therapeutic properties limits its conversational capacity. TxGemma-27B-Predict can engage in general\n", "\n", "<PERSON><PERSON><PERSON>\n", "\n", "TxGemma\n", "\n", "<PERSON>\n", "\n", "SMILES\n", "\n", "Amino acid\n", "\n", "SMILES + Text\n", "\n", "Nucleotide + Amino acid\n", "\n", "Amino acid + Text\n", "\n", "Amino acid + SMILES\n", "\n", "Nucleotide\n", "\n", "Figure 4 | Comparison of TxGemma's performance with best-in-class specialist models. TxGemma27B-Predict is evaluated on each task in TDC and compared to the corresponding best-in-class competitor. The panels depict different metrics used to evaluate the tasks. Tasks are colored by their feature types including one or a combination of SMILE, Amino acid, Nucleotide and text as indicated in the legend. Marker sizes illustrate the number of data points in the task on a log scale. The larger shaded area in blue indicates where TxGemma outperforms best-in-class models, while the narrower light blue shaded area indicates where TxGemma is performing near best-in-class model (defined as within 10%). MAE and MSE values are log-transformed since the magnitudes of these values depend on the units of outputs. Generation accuracy is the fraction of correct SMILES strings in the USPTO generation task. Values for each task can also be found in Tables S.13 and S.14.\n", "\n", "conversation, but its performance deteriorates when prompts deviate from the expected format. Figure S.9 shows an example of such decline in TxGemma-27B-Predict's conversational capabilities. To expand the TxGemma family's capabilities and provide a more versatile tool with the ability to explain its reasoning, we trained TxGemma-Chat with a mix of therapeutic and general instruction-tuning data as detailed in Section 2.2. We evaluate these new conversational capabilities through a combination of standard LLM benchmarks and qualitative examples. We also run our models through assurance evaluations, as done for Gemma-3 [25], to verify that TxGemma models adhere to safety policies.\n", "\n", "TABLE\n", "\n", "Table 1 | Comparative performance of TxGemma and MolE on small molecule tasks. Details of the predictive performance of TxGemma-27B-Predict and MolE, a graph-based molecular multi-task foundation model, across various pharmacokinetics and toxicity tasks. Bold values indicate the best performance for each task. Metrics for MolE are reported from <PERSON>dez<PERSON> et al. [24]. TxGemma-27B-Predict values are bootstrapped averages and 95% CIs. These pharmacokinetics and toxicity tasks are publicly available in TDC [7].\n", "\n", "TABLE\n", "\n", "Table 2 | Comparative performance of TxGemma and LlaSMol on small molecule tasks. Comparison of TxGemma-27B-Predict with LlaSMol Mistral (best LlaSMol model at 7B) across shared small-molecule tasks. Bold values indicate the best performance for each task. Metrics for LlaSMol Mistral are reported from <PERSON> et al. [23]. TxGemmaPredict values are bootstrapped averages and 95% CIs. These pharmacokinetics, toxicity, and high-throughput screening data and tasks are publicly available in TDC [7]\n", "\n", "∗ To predict whether compounds have anti-HIV properties.\n", "\n", "† Task name is modified to match the nomenclature from <PERSON> et al. [23].\n", "\n", "TxGemma-Chat bridges the gap between property predictors and general language models To assess the performance of TxGemma-Chat as a general conversational LLM, we evaluated it on the Massive Multitask Language Understanding (MMLU) [26] benchmark, a comprehensive suite of 57 diverse tasks spanning mathematics, history, computer science, law, etc . This benchmark evaluates knowledge, reasoning,\n", "\n", "and problem-solving abilities across a wide range of academic subjects, providing a measure of overall language understanding. It comprises 14,079 multiple-choice questions, each with four possible answers. For this multiple-choice format, we took the model's prediction as the option with the highest log-likelihood in a zero-shot setting and report overall accuracy as well as per-subject accuracy.\n", "\n", "Figure S.7 compares the performance of TxGemma-27B-Chat, TxGemma-27B-Predict, and Gemma-2-27B on MMLU, a standard benchmark for evaluating general LLMs. TxGemma-27B-Chat achieves an accuracy of 73.87%, slightly lower than Gemma-2-27B's 75.38%, but TxGemma-27B-Chat shows slight improvements in areas such as medical genetics, high school statistics, and college chemistry. Furthermore, TxGemma-27B-Chat significantly outperforms TxGemma-27B-Predict, which has an accuracy of 53.60%. This suggests that while fine-tuning solely on therapeutic data can diminish general knowledge acquired during pre-training, incorporating general instruction-tuning data can mitigate this effect.\n", "\n", "Furthermore, we assess TxGemma-27B-Chat on all therapeutic tasks within TDC. Figure 5 compares the relative performance changes of TxGemma-27B-Chat to TxGemma-27B-Predict and Gemma-2-27B for both 9B and 27B variants across these tasks. As anticipated, TxGemma-27B-Predict outperforms TxGemma-27B-Chat on these predictive tasks, with a median relative performance reduction of 11% observed for TxGemma-27BChat. Nevertheless, TxGemma-27B-Chat surpasses the baseline Gemma-2-27B, demonstrating a median relative improvement of 30%. Similarly, TxGemma-9B-Chat shows a 10% median relative performance reduction compared to TxGemma-9B-Predict. Regression tasks experience the greatest performance decline from the general-purpose training. These results demonstrate how TxGemma-Chat bridges the gap between therapeutic property predictors and general LLMs, functioning as a unified model for both capabilities.\n", "\n", "TxGemma-Chat can provide reasoning for complex tasks. A particularly compelling application of conversational models lies in prompting them to explain their predictions to users. While general LLMs may possess some foundational knowledge concerning therapeutic challenges, they are not accurate for property prediction (Figure 5). In Figure 6, we prompt TxGemma-27<PERSON><PERSON><PERSON><PERSON> to answer a question regarding blood-brain barrier permeability using the BBB Martins prompt format. TxGemma-27B-Chat provides only the answer in the initial turn, but when given a subsequent prompt to articulate its rationale, the model provides mechanistic reasoning for its answer based on molecular solubility and the structure of the input molecule derived from the SMILES string. All of this reasoning occurred directly within the model weights, without requiring any preprocessing of the SMILES string.\n", "\n", "Interestingly, prompting structures enable TxGemma-Chat to provide additional reasoning on complex tasks. For instance, while the relationship between blood-brain barrier permeability and lipophilicity is intuitive, some\n", "\n", "TABLE\n", "\n", "Table 3 | Performance of Agentic-Tx. Accuracy of Agentic-Tx compared with SOTA models on ChemBench, GPQA, and HLE benchmarks.\n", "\n", "( † ) Using ReAct framework, ( ∗ ) Extracted from [1], ( ∗∗ ) Extracted from [2]\n", "\n", "tasks such as predicting clinical trial approval are more challenging to reason over. If TxGemma-27B-<PERSON><PERSON> is prompted to provide reasoning in the same manner as in Figure 6 for predicting clinical trial approval, TxGemma-27B-Cha<PERSON> refuses and directs the user to alternative sources. However, when modifying the original prompt, instructing the model to output reasoning steps before the final answer, it bypasses the refusal and restores reasoning capabilities (Figure S.10).\n", "\n", "3.3 Agentic Planning and Execution based on TxGemma\n", "\n", "Agentic-Tx demonstrates competitive performance on therapeutic benchmarks . We evaluate the capability of Agentic-Tx to assist with therapeutics tasks by means of questions from three benchmarks: GPQA (Diamond) [27], ChemBench [1], and Humanity's Last Exam (HLE) [15]. Within each benchmark, we use existing selections of therapeutic-relevant questions; for GPQA we evaluate GPQA-Chemistry (47 questions), for ChemBench we evaluate ChemBench-Chemical Preference which aims to select an ideal candidate molecule for therapeutic development (1,001 question) and ChemBench-mini, which evaluates across 8 categories of chemistry from toxicity/safety to organic chemistry (236 questions). Finally, for HLE, we evaluate HLE-Chemistry and HLE-Biology (235 questions). For open-ended questions in HLE, we observed a high variation of metric scores depending on the selection of the LLM-rater model [15]. To ensure an objective accuracy measure, we restrict the evaluation to multiple choice questions (MCQs).\n", "\n", "As shown in Table 3, Agentic-Tx (Gemini 2.5-Pro), Agentic-Tx (Gemini 2.0-Pro), and Agentic-Tx (Gemini 1.5-Pro) achieve competitive or greater accuracy compared to existing SOTA models across several benchmarks. Specifically, Agentic-Tx (Gemini 2.5-Pro) and Agentic-Tx (Gemini 2.0-Pro) surpasses prior SOTA models on the exceptionally difficult Humanity's Last Exam benchmark (Chemistry & Biology tasks), with Agentic-Tx (Gemini 2.5-Pro) achieving 52.3% relative improvement over o3-mini (high) and 13.4% over Gemini 2.5-pro, as well as on ChemBench, with relative improvements of 6.3% (ChemBench-Preference) and 2.4% (ChemBenchMini) over o3-mini (high) and 1.1% (ChemBench-Preference) and 2.0% (ChemBench-Mini) over Gemini 2.5-pro. On GPQA (Diamond), Agentic-Tx also achieves SOTA accuracy with 26.7% relative improvements over o3-mini and 2.7% over Gemini 2.5-pro. All variants of Agentic-Tx outperform their corresponding base Gemini models across all benchmarks, indicating the effectiveness of the Agentic-Tx framework in enhancing LLMs efficacy for advanced reasoning within this domain. This suggest that agentic workflows such as ours represent useful tools for therapeutic development, particularly in areas requiring domain knowledge and the selection of candidate molecules. The agent's ability to leverage external tools and perform multi-step\n", "\n", "Instructions: Answer the following question about drug properties.\n", "\n", "Context: As  a  membrane  separating  circulating  blood  and  brain  extracellular  fluid,  the  blood-brain  barrier (BBB) is the protection layer that blocks most foreign drugs. Thus the ability of a drug to penetrate the barrier to deliver to the site of action forms a crucial challenge in development of drugs for central nervous system.\n", "\n", "Question: Given a drug SMILES string, predict whether it\n", "\n", "(A) does not cross the BBB (B) crosses the BBB\n", "\n", "Drug SMILES: C1CN(CCC1(C(=O)NC(CCO)C2=CC=C(C=C2)Cl)N)C3=NC=NC4=C3C=CN44\n", "\n", "Answer: (B)\n", "\n", "Explain your reasoning based on the molecule structure.\n", "\n", "TxGemma\n", "\n", "The molecule has several properties that suggest it can cross the blood-brain barrier (BBB):\n", "\n", "Lipophilicity: The  molecule  contains  multiple  aromatic  rings  and  a  long  alkyl  chain,  contributing  to  its lipophilicity. Lipophilicity is a key factor in BBB penetration, as the barrier is primarily composed of lipids.\n", "\n", "Molecular  weight: The  molecular  weight  is  within  a  range  (around  400  Da)  that  is  generally  considered favorable for BBB permeability.\n", "\n", "Hydrogen bond donors and acceptors: While there are hydrogen bond donors and acceptors present, they are not excessive, which can sometimes hinder BBB passage.\n", "\n", "Lack of charged groups: The molecule lacks any charged groups at physiological pH, making it less likely to be repelled by the BBB's tight junctions.\n", "\n", "Overall, the combination of lipophilicity, moderate molecular weight, balanced hydrogen bonding capacity, and lack of charged groups suggests that this molecule has a good chance of crossing the blood-brain barrier.\n", "\n", "What makes passing through the blood-brain barrier different from passive diffusion through a membrane?\n", "\n", "TxGemma\n", "\n", "While passive diffusion is a major mechanism for molecules to cross the blood-brain barrier (BBB), there are key differences that make BBB passage more challenging and selective:\n", "\n", "1. Tight Junctions : The cells forming the BBB are packed very tightly together with specialized structures called  tight  junctions.  These  junctions  seal  the  spaces  between  cells,  significantly  reducing  the  gaps through which molecules can passively diffuse.\n", "\n", "2. Specialized Transport Systems: The BBB has a variety of specialized transport systems that actively move  specific  molecules  across  the  barrier.  These  systems  are  highly  selective  and  often  require molecules to have specific structural features or be recognized by specific receptors.\n", "\n", "3. Efflux Pumps: The BBB also expresses efflux pumps, which are proteins that actively pump molecules back out of the brain. This mechanism helps to prevent potentially harmful substances from entering the brain and maintain a stable brain environment.\n", "\n", "4. Metabolic Enzymes: The BBB contains enzymes that can metabolize and inactivate certain molecules, further limiting their entry into the brain.\n", "\n", "5. Astrocyte Endfeet: Astrocytes, a type of glial cell, have projections called endfeet that surround blood vessels in the brain. These endfeet contribute to the BBB's barrier function and regulate the passage of molecules.\n", "\n", "In  summary,  while  passive  diffusion  plays  a  role  in  BBB  permeability,  the  presence  of  tight  junctions, specialized transport systems, efflux pumps, metabolic enzymes, and astrocyte endfeet make the BBB a highly selective barrier that restricts the entry of many molecules. This selectivity is crucial for protecting the brain from harmful substances and maintaining a stable internal environment, but it also poses challenges for drug delivery to the central nervous system.\n", "\n", "Is my molecule likely to run into these issues?\n", "\n", "Let's analyze how your molecule might be affected by the specific challenges of the BBB . . .\n", "\n", "Figure 6 | TxGemma-Cha<PERSON> can be prompted to explain its reasoning or provide additional context.\n", "\n", "Following an initial question, TxGemma-27B-<PERSON><PERSON> is asked to explain its reasoning based on molecule structure in the second turn. The model uses its understanding of chemistry and biology to justify its answer and can continually engage with the user on follow-up questions.\n", "\n", "reasoning enables it to address more complex queries beyond the scope of traditional LLMs.\n", "\n", "Agentic-Tx effectively leverages various tools based on the therapeutic task requirement. In Figure S.14, we investigate tool usage frequency within the Agentic-Tx system across the ChemBenchPreference and Biology and Chemistry (B&C) HLE datasets. Our analysis reveals that Agentic-Tx tool usage distribution varies significantly depending on the task and dataset. For the ChemBench-Preference task, which focuses on selecting ideal candidate molecules for therapeutic development, the Agentic-Tx system exhibits a high frequency of usage for tools such as SMILES description and toxicity prediction. This suggests a strong emphasis on molecular characterization and safety assessment in this task correctly invoked by Agentic-Tx. In contrast, on the B&C HLE dataset, tool usage is predominantly concentrated on general knowledge retrieval tools like PubMed or Wikipedia search. This indicates that the Agentic-Tx system relies heavily on accessing and synthesizing broad biological or chemical knowledge to address questions in these domains. In Figure S.15, we investigate the breakdown of tool interactions per question and explore how these interactions contribute to performance variations. Our analysis shows that each question can involve up to 8 tool calls, and the high usage of tools such as SMILES description and toxicity prediction tools correlates with overall performance improvement. These results highlight the Agentic-Tx system's adaptive nature, demonstrating its ability to leverage different tools based on the specific requirements of the task.\n", "\n", "Agentic-Tx inference time is suitable for real time human interaction Analysis of Agentic-Tx's inference time indicates efficient performance characteristics. The median time observed for tool execution is 0.55 seconds. The fastest tool (Gene Sequence) completes execution in 0.15 seconds, while the slowest (ToxCast) requires 28.2 seconds. This suggests that Agentic-Tx operates within a timeframe conducive to real-time user interaction. The observed latencies demonstrate suitability for integration into workflows where immediate feedback and responsiveness are desired. The system's ability to maintain a median inference time below one second contributes to an efficient user experience.\n", "\n", "3.4 Additional Analysis and Ablations\n", "\n", "Data contamination analysis and data leakage considerations To assess potential data contamination from the Gemma-2 pretraining data, we calculated the overlap between features in the therapeutic instructiontuning data and the pretraining corpus. For multi-instance tasks, contamination was defined as the presence of any constituent feature (e.g., drug SMILES or target protein sequence in drug-target binding) in the pretraining data. The majority of tasks showed no direct contamination (Figure S.12). For tasks with some contamination, filtering contaminated datapoints and recalculating TxGemma-27B-Predict performance revealed no significant changes (Figure S.13).\n", "\n", "While direct contamination was minimal, we further investigated potential indirect contamination. Although SMILES strings are less common in general web text, pretraining on molecular names could have created learned associations between names and SMILES, potentially influencing test set performance. To test this, we compared the similarity of TxGemma-27B-Predict embeddings for PubChem molecules represented as SMILES strings and their corresponding IUPAC names, against the similarity of embeddings for SMILES strings paired with decoy (randomly selected, incorrect) names. The similarities were statistically equivalent (Figure S.12), confirmed by a two one-sided t-test ( p = 3 × 10 -12 , δ = 0 02 . ). This suggests that TxGemma-27B-Predict did not learn spurious name-SMILES associations during pretraining, likely because names and SMILES were encountered in separate training phases and for different molecules. Therefore, both direct and indirect contamination from pretraining are unlikely to significantly affect our results.\n", "\n", "Fine-tuning TxGemma models improves data efficiency Given the scarcity of therapeutic data and the potential of TxGemma to serve as a pretrained model for further adaptation, we investigated TxGemma's data efficiency and generalization to new tasks in out-of-distribution settings. Specifically, we fine-tuned the baseline model Gemma-2-27B as well as our TxGemma-27B-Predict on adverse event prediction data from TrialBench [29]. Serious adverse events are critical in assessing the safety profile of a new treatment and accurate prediction of these events allows for better risk management and resource allocation [29]. To ensure a fair evaluation of generalization, we filtered the TrialBench test set to exclude samples overlapping with phase 1, 2, or 3 of clinical trial outcome prediction data in TDC. In addition, datapoints without available SMILES strings are excluded. This lead to 14,368 train and 3,184 test samples.\n", "\n", "We consider two settings. Initially, we focus exclusively on drug SMILES strings as the only feature contributing to clinical trial outcome, thereby isolating the influence of therapeutic information by excluding this additional context. To simulate data limitations, we fine-tuned TxGemma-27B-Predict and the baseline Gemma-2-27B on varying fractions of the training data, and then evaluated the newly fine-tuned models performance on the test set after 30 epochs of training (Figure 7). Overall, TxGemma-27B-Predict achieved higher AUROCs with lower amounts of training data, matching the performance of Gemma-2-27B with less than 10% of retraining data. In the second setting, we explored the performance ceiling by incorporating textual information about the clinical trials, increasing the number of tokens provided to the models by a factor of 4 (Table S.10). This is the setting used by the best-in-class model for adverse event prediction [29]. The addition of textual information allowed our models to consistently outperform existing SOTA methods [29]. However, the performance difference between TxGemma-27B-Predict and Gemma-2-27B decreased in this scenario because the additional textual information diluted the relative importance of the drug SMILES strings.\n", "\n", "TxGemma inference time is suitable for virtual screening In Figure S.11, we plot the inference speed of TxGemma models of all sizes normalized by the number of TPUv5e chips used for serving. All model sizes are suitably fast for virtual screening, as even the largest 27B model is able to inference around 9,000 samples per day per TPU chip. Using 64 chips for serving, this would yield around 600,000 samples per day for the 27B model, and the smallest 2B model would reach 3,000,000 samples per day.\n", "\n", "Correlation between clinical trial approval and toxicity predictions We investigated the correlation between TxGemma's clinical trial approval predictions (based on SMILES and target disease) and its toxicity predictions (using TDC's AMES, DILI, and hERG tasks). Figure S.18 shows a consistent, but weak (0.15-0.35), positive <PERSON><PERSON><PERSON> correlation across all phases. This suggests TxGemma associates lower predicted toxicity with approval, but may also consider other factors such as efficacy or drug-likeness.\n", "\n", "Impact of feature types Figure S.16 presents a performance breakdown of TxGemma-27B-Predict by feature type, compared to Tx-LLM M. In both models, tasks incorporating both SMILES strings and textual features (e.g., disease names, cell line names/descriptions) show the most significant improvement over SOTA. This suggests that the contextual knowledge acquired during LLM pretraining could aid in synthesizing textual information with molecular representations.\n", "\n", "Model size and domain fine-tuning ablations Figure S.17 compares the performance of TxGemma-Predict models across different sizes (2B, 9B, and 27B) on TDC tasks. Pairwise comparisons using the Wilcoxon\n", "\n", "signed-rank test indicate that model size is a significant factor: TxGemma-27B-Predict outperforms TxGemma9B-Predict ( p = 0 013 . ) and TxGemma-2B-Predict ( p = 6 2 . × 10 -6 ), and TxGemma-9B-Predict outperforms TxGemma-2B-Predict ( p = 0 048 . ). Furthermore, comparing TxGemma models to their corresponding base Gemma-2 models reveals the significant impact of domain fine-tuning. All TxGemma models significantly outperform their Gemma-2 counterparts ( p < 10 -10 , <PERSON><PERSON> signed-rank test), underscoring the importance of specialized training for therapeutic tasks.\n", "\n", "4 Related work\n", "\n", "Task-specific models for chemistry and therapeutics. In recent years, there has been a surge in the development of deep learning models designed for various chemistry applications. Amongst those, graph neural networks (GNNs) have been applied for a wide variety of molecular prediction or generation tasks because small molecules are naturally represented as graphs [30, 31, 32, 33, 34, 35, 36, 37, 24]. Another common representation for small molecules is molecular fingerprints [38], which are binary vectors that capture the local environment of each atom [30, 39, 40].\n", "\n", "TxGNN trained a GNN on medical knowledge graphs in order to perform zero-shot drug repurposing for diseases with limited treatment options [41]. AlphaFold and its successors have also significantly advanced the field of protein structure prediction and protein design [42, 43, 44, 45, 46]. These models have been influential for both mechanistic research and the development of structure-based drugs [47].\n", "\n", "Large language models for biology and chemistry. Transformer-based models [48] have fueled the development of LLMs, which are trained on massive textual datasets with subsequent instruction-tuning [49] or alignment [50]. LLMs have demonstrated exceptional proficiency in various tasks, including text summarization, translation, and question answering [16, 51, 52]. Their ability to encode vast amounts of information and generalize to new tasks has sparked considerable interest in their potential applications across diverse domains.\n", "\n", "There has been increasing interest in applying the development for LLMs to scientific research. BrainGPT fine-tuned LLMs on neuroscience literature and found greater performance than domain experts [53]. LlaSMol fine-tuned LLMs on small molecule datasets and achieved near-SOTA performance on multiple tasks [23]. CLAMP used separate modules for natural language and molecular inputs, combining them together in a contrastive pre-training objective [54]. Protein language models [55, 56, 57, 58] and genomic language models [59, 60, 61] have used self-supervised pretraining to generate embeddings useful for downstream tasks. ProtLLM [62], BioT5 [63], and GraphToken [64] combine molecule or proteins with LLMs using textual or multi-modal strategies. Cellular foundation models such as scGPT [65], GenePT [66], Geneformer [67], Nicheformer [68], and Cell2Sentence [69] represent cells based on their gene expression to differentiate cell types and understand gene perturbations. NatureLM [70] trained a foundation model that represents small molecules, proteins, RNA, and materials as sequences over a wide variety of scientific tasks.\n", "\n", "Agentic Systems. Unlike traditional passive models, agentic systems proactively choose actions to achieve goals [71, 72, 73, 74, 75], involving planning [76, 77, 78, 79, 80] and interaction with external tools [81, 82, 83, 84]. LLMs have enabled such systems by processing complex information and generating actiondriving responses. The ReAct framework [22] combines reasoning, action, and observation, with variations incorporating self-reflection [85] or model architectures for internal tool usage [82]. Agentic frameworks enable automating tasks like software development [73, 86, 87, 88] and scientific research [89, 90, 91] including biomedical applications such as nanobody design [90], drug discovery [92], or reaction optimization [93]. Chem<PERSON>row [92] is an agent designed to perform chemistry experiments in drug discovery and materials design. The coscientist by <PERSON><PERSON> et al. [93] designs and performs chemical experiments by integrating web knowledge, code execution, and experiment automation, demonstrating successful reaction optimization of palladium-catalysed cross-couplings. The multi-agent system AI co-scientist [88] is designed for hypothesis generation over a variety of scientific fields. TxAgent was developed as an agentic framework that provides multi-step reasoning and tool use aimed towards therapeutic applications, processing clinical information to support tasks like treatment recommendation [94]. In contrast to recommending existing therapeutics, Agentic-Tx generally focuses on developing new therapeutics.\n", "\n", "5 Discussion\n", "\n", "TxGemma's performance suggests a paradigm shift in therapeutic AI development, demonstrating the viability of generalist LLMs. Despite the established dominance of specialist models in niche areas, <PERSON><PERSON>G<PERSON><PERSON>, a relatively lightweight and efficient generalist, achieves competitive results across a wide array of therapeutic tasks. This highlights the potential for broadly trained LLMs, such as those leveraging the comprehensive dataset Therapeutics Data Commons (TDC), to serve as powerful preliminary tools for hypothesis generation, information synthesis, and candidate prioritization. While specialist models would likely retain their value for complex, domain-specific challenges, future research should explore synergistic approaches that combine the strengths of both generalist and specialist therapeutic AI.\n", "\n", "A significant advancement with TxGemma-Chat is its ability to provide reasoning for its predictions, a first in therapeutic AI and a feature lost in TxGemma-Predict, likely due to 'catastrophic forgetting' [95]. While explainability may introduce a small trade-off in raw predictive power, it provides a crucial window into the model's decision-making, a factor of paramount importance in therapeutic development. For instance, explaining blood-brain barrier permeability based on molecular structure provides valuable insights for medicinal chemists. Beyond its research applications, TxGemma-Chat holds a significant educational potential, enabling students and researchers to explore complex therapeutic concepts. At the same time, it is important to acknowledge that provided explanations are correlations, not necessarily causal, and must be interpreted with caution. The model's occasional inability to explain certain predictions reveals its knowledge boundaries. Future research should prioritize improving reliability and comprehensive explanations. Even with current limitations, TxGemma-Chat represents an important improvement over the 'black-box' paradigm.\n", "\n", "Expanding beyond single-step predictions, Agentic-Tx demonstrates the potential for LLMs to orchestrate complex workflows. By integrating TxGemma with a suite of external tools (PubMed, Wikipedia, chemical databases, etc ), Agentic-Tx can tackle multi-step reasoning tasks that would be difficult for a standalone LLM. Its strong performance on benchmarks like ChemBench Chemical Preference and Humanity's Last Exam (HLE) highlights the synergistic value of integrating domain-specific knowledge from TxGemma with general reasoning and information retrieval. This modular, tool-based design further ensures flexibility and extensibility, allowing for future integration of new tools and data. Importantly, it solves the issue of knowledge cut-off in LLMs by providing access to up-to-date information. Agentic-Tx with its autonomous and collaborative operation is a powerful asset for augmenting researchers and advancing therapeutic development.\n", "\n", "The data efficiency of TxGemma is clearly demonstrated in fine-tuning experiments on TrialBench. It achieves robust performance on novel tasks with substantially less training data compared to baseline models, showcasing the benefits of pre-training on a broad and diverse dataset like TDC. This efficiency is particularly critical in therapeutic domains, where data is often proprietary and limited. Moreover, our finding that adding textual context, while improving overall results, can dilute the influence of molecular representations emphasizes the importance of balancing the benefits of additional information with strategic feature selection.\n", "\n", "Although our in-silico results across a diverse range of therapeutic tasks are highly encouraging, we acknowledge that TxGemma's performance has not yet been validated in real-world, wet-lab experiments. Prospective validation in these settings represents a crucial next step. However, a cornerstone of this work is our commitment to open model release. By making TxGemma readily accessible to the research community, we aim to facilitate its rigorous validation and adaptation. Researchers can tailor TxGemma to their specific datasets, encompassing tasks and distribution shifts beyond the scope of TDC. Given the predominantly proprietary nature of therapeutic data, we believe this collaborative, community-driven approach is essential for translating TxGemma into tangible therapeutic applications\n", "\n", "6 Conclusion\n", "\n", "In conclusion, this work introduced TxGemma, a suite of efficient, generalist LLMs designed to improve therapeutic development. By leveraging extensive therapeutic instruction-tuning datasets and building upon the foundation of Gemma-2, TxGemma achieves exceptional performance across a wide range of predictive and generative therapeutic tasks, surpassing or matching both generalist and specialist state-of-the-art models. Notably, TxGemma's conversational counterparts, a first in therapeutic AI, provide reasoning and explanations,\n", "\n", "moving beyond traditional black-box predictions to facilitate mechanistic understanding and scientific discourse. Furthermore, the integration of TxGemma into an agentic system, Agentic-Tx, demonstrates its capacity to solve complex, multi-step problems, achieving state-of-the-art results on challenging reasoning-intensive tasks. Finally, and critically, the open release of TxGemma empowers the research community and scientist to adapt and refine the models on their own private data, potentially leading to significant advancements in drug discovery and development. Through these contributions, TxGemma represents a meaningful step towards more efficient, transparent, and collaborative AI-driven therapeutic research.\n", "\n", "Acknowledgments\n", "\n", "This project was a collaboration between teams at Google DeepMind and Google Research. We thank <PERSON>, <PERSON>, <PERSON>, and <PERSON> for the feedback and insight which significantly contributed to the enhancement of this report. We thank <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> for their collaborative efforts in enabling the open model launch of TxGemma. We also thank <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> for their valuable insights and technical support. We are also grateful to <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> for their support during the course of this project.\n", "\n", "Inclusion and ethics\n", "\n", "While AI offers transformative potential in drug discovery, ethical considerations and transparency remain crucial. Biases in training data can lead to inequities, highlighting the need for diverse datasets and explainable AI systems. Our model, while still in the research stage, highlights the continuous need for development and refinement in this field. We acknowledge the difficulty in explaining the inner workings of complex models, but remain dedicated to advancing research in this area.\n", "\n", "Data availability\n", "\n", "The Therapeutics Data Commons (TDC) datasets used for developing, benchmarking, and evaluating TxGemma are publicly available on their website. The benchmarking datasets used in this study-Humanity's Last Exam (HLE), GPQA (Diamond), ChemBench, and TrialBench (Serious Adverse Event Prediction)-are all publicly available via their respective websites.\n", "\n", "Code availability\n", "\n", "All of the components used in this work are available publicly. For reproducibility, we have documented technical methods and data curation detail in depth, while keeping the paper accessible to clinical and general scientific audiences. Specifically, all the data needs to reproduce this work is publicly accessible to the community. TxGemma, a collection of lightweight state-of-the-art, open language models, are provided for researchers in three model size of 2B, 9B, and 27B and is accessible through Vertex AI Model Garden and Hugging Face. TxGemma's Github repository including supporting code and colab notebooks for quick start are also available at: https://github.com/google-gemini/gemma-cookbook/tree/main/TxGemma. We have specifically provided starter colabs for inference, fine-tuning, and exploring agentic capabilities. TxGemma remains a research model and requires refinement. We look forward to working with research partners, regulators, and providers to validate and explore safe onward uses of TxGemma.\n", "\n", "Author Contributions\n", "\n", "<PERSON><PERSON><PERSON><PERSON>, S.S., and S.A. made substantial contributions to the conception, design, and evaluation of this work. They played a key role in data analysis, interpretation of results, and the drafting and revision of the manuscript. P.F.J. contributed to drafting and revision of the manuscript. F.Z. contributed to the data processing and model training in the manuscript. R.P. contributed to obtaining necessary legal approvals,\n", "\n", "and organizational support. All authors participated in critically reviewing and revising the manuscript and interpreting the data and findings.\n", "\n", "Competing interests\n", "\n", "This study was funded by Alphabet Inc and/or a subsidiary thereof ('Alphabet'). E.W., S.S., P.F.J., F.Z., R.P., Y.M., J.B., D.F., and S.A. are employees of Alphabet and may own stock as part of the standard compensation package.\n", "\n", "References\n", "\n", "1. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Are large language models superhuman chemists? arXiv preprint arXiv:2404.01475 (2024).\n", "\n", "2. OpenAI. Learning to Reason with LLMs https://openai.com/index/learning-to-reason-with-llms/. Accessed: Wednesday 9 th April, 2025. 2024.\n", "\n", "3. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, S. Why 90% of clinical drug development fails and how to improve it? Acta Pharmaceutica Sinica B 12, 3049-3062 (2022).\n", "\n", "4. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, E. A. Accelerating therapeutics for opportunities in medicine: a paradigm shift in drug discovery. Frontiers in pharmacology 11, 770 (2020).\n", "\n", "5. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, K. <PERSON>. <PERSON>ag<PERSON> based drug design: from experimental to computational approaches. Current medicinal chemistry 19, 5128-5147 (2012).\n", "\n", "6. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, B. L. & Zitnik, M. TDC-2: Multimodal foundation for therapeutic science. bioRxiv, 2024-06 (2024).\n", "\n", "7. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, J. & <PERSON>, M. Therapeutics data commons: Machine learning datasets and tasks for drug discovery and development. arXiv preprint arXiv:2102.09548 (2021).\n", "\n", "8. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, J<PERSON> & <PERSON>, M. Artificial intelligence foundation for therapeutic science. Nature chemical biology 18, 1033-1036 (2022).\n", "\n", "9. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Sparks of artificial general intelligence: Early experiments with GPT-4. arXiv preprint arXiv:2303.12712 (2023).\n", "\n", "10.\n", "\n", "<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, T., Hart<PERSON>, A., Saravia, E., Poulton, A., <PERSON>, V<PERSON> & <PERSON>, R.\n", "\n", "Galactica: A large language model for science.\n", "\n", "arXiv preprint arXiv:2211.09085\n", "\n", "(2022).\n", "\n", "11. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, S. & <PERSON>, J. P. Large language models for science and medicine. European journal of clinical investigation 54, e14183 (2024).\n", "\n", "12. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, S. S., <PERSON><PERSON><PERSON><PERSON>, C., Fleet, D., <PERSON>, <PERSON> & <PERSON>, S. Tx-LLM: A Large Language Model for Therapeutics. arXiv preprint arXiv:2406.06316 (2024).\n", "\n", "13. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Gemma: Open models based on gemini research and technology. arXiv preprint arXiv:2403.08295 (2024).\n", "\n", "14. <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Gemma 2: Improving open language models at a practical size. arXiv preprint arXiv:2408.00118 (2024).\n", "\n", "15. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, et al. Humanity's Last Exam. arXiv preprint arXiv:2501.14249 (2025).\n", "\n", "16. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Language models are few-shot learners. Advances in neural information processing systems 33, 1877-1901 (2020).\n", "\n", "17. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. The FLAN collection: Designing data and methods for effective instruction tuning in International Conference on Machine Learning (2023), 22631-22648.\n", "\n", "18. <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Gemini: a family of highly capable multimodal models. arXiv preprint arXiv:2312.11805 (2023).\n", "\n", "19. <PERSON><PERSON>, G. <PERSON>: Open-Source Cheminformatics Software. https://github.com/rdkit/rdkit/releases/tag/Release_2016_ 09_4 (2016).\n", "\n", "20. <PERSON><PERSON>, <PERSON><PERSON> The chemfp project. Journal of cheminformatics 11, 1-21 (2019).\n", "\n", "21. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Fast, scalable generation of high-quality protein multiple sequence alignments using Clustal Omega. Molecular systems biology 7, 539 (2011).\n", "\n", "22. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>: Synergizing reasoning and acting in language models. arXiv preprint arXiv:2210.03629 (2022).\n", "\n", "23. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>. & <PERSON>, <PERSON><PERSON>: Advancing large language models for chemistry with a large-scale, comprehensive, high-quality instruction tuning dataset. arXiv preprint arXiv:2402.09391 (2024).\n", "\n", "24. <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> & <PERSON>, <PERSON>. <PERSON>: a foundation model for molecular graphs using disentangled attention. Nature Communications 15, 9431 (2024).\n", "\n", "25. <PERSON>, <PERSON><PERSON> 3 technical report. Google DeepMind (2025).\n", "\n", "26. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> & <PERSON>, <PERSON>. Measuring massive multitask language understanding. arXiv preprint arXiv:2009.03300 (2020).\n", "\n", "27. <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, S. R. G<PERSON>: A graduate-level google-proof q&a benchmark in First Conference on Language Modeling (2024).\n", "\n", "28. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, S. G. & White, A. D. Language agents achieve superhuman synthesis of scientific knowledge. arXiv preprint arXiv:2409.13740 (2024).\n", "\n", "29. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. TrialBench: Multi-modal artificial intelligence-ready clinical trial datasets. arXiv preprint arXiv:2407.00631 (2024).\n", "\n", "30. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>h convolutional neural networks for predicting drug-target interactions. Journal of chemical information and modeling 59, 4131-4149 (2019).\n", "\n", "31. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> & <PERSON>, <PERSON><PERSON>: Geometric deep learning for drug binding structure prediction in International conference on machine learning (2022), 20503-20521.\n", "\n", "32. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Pushing the boundaries of molecular representation for drug discovery with the graph attention mechanism. Journal of medicinal chemistry 63, 8749-8760 (2019).\n", "\n", "33. <PERSON><PERSON>, E<PERSON> & <PERSON>, <PERSON><PERSON> <PERSON>. Machine learning of reaction properties via learned representations of the condensed graph of reaction. Journal of Chemical Information and Modeling 62, 2101-2110 (2021).\n", "\n", "34. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Analyzing learned molecular representations for property prediction. Journal of chemical information and modeling 59, 3370-3388 (2019).\n", "\n", "35. <PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, H. & Cornell, W. D. Combining docking pose rank and structure with deep learning improves protein-ligand binding mode prediction over a baseline docking approach. Journal of chemical information and modeling 60, 4170-4179 (2020).\n", "\n", "36. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, A. L. & <PERSON>, T. Data-driven discovery of cardiolipin-selective small molecules by computational active learning. Chemical Science 13, 4498-4511 (2022).\n", "\n", "37. <PERSON>, <PERSON><PERSON> <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, N. <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. A deep learning approach to antibiotic discovery. Cell 180, 688-702 (2020).\n", "\n", "38. <PERSON>, <PERSON>, <PERSON>. Extended-connectivity fingerprints. Journal of chemical information and modeling 50, 742-754 (2010).\n", "\n", "39. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, M. J. & Power, J. Prediction of organic compound aqueous solubility using machine learning: a comparison study of descriptor-based and fingerprints-based models. Journal of Cheminformatics 15, 99 (2023).\n", "\n", "40. <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, S<PERSON> & Lee, J. Development of machine learning models based on molecular fingerprints for selection of small molecule inhibitors against JAK2 protein. Journal of Computational Chemistry 44, 1493-1504 (2023).\n", "\n", "41. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, G. <PERSON>, <PERSON><PERSON>, B. S., Gehlenborg, N. & Zitnik, M. A foundation model for clinician-centered drug repurposing. Nature Medicine, 1-13 (2024).\n", "\n", "42. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, et al. Highly accurate protein structure prediction with AlphaFold. nature 596, 583-589 (2021).\n", "\n", "43. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Highly accurate protein structure prediction for the human proteome. Nature 596, 590-596 (2021).\n", "\n", "44. <PERSON>, <PERSON><PERSON> <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, et al. Improved protein structure prediction using potentials from deep learning. Nature 577, 706-710 (2020).\n", "\n", "45. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, et al. Accurate structure prediction of biomolecular interactions with AlphaFold 3. Nature, 1-3 (2024).\n", "\n", "46. <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, et al. De novo design of high-affinity protein binders with AlphaProteo. arXiv preprint arXiv:2409.08022 (2024).\n", "\n", "47. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. AlphaFold accelerates artificial intelligence powered drug discovery: efficient discovery of a novel CDK20 small molecule inhibitor. Chemical science 14, 1443-1452 (2023).\n", "\n", "48. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> is all you need. Advances in Neural Information Processing Systems (2017).\n", "\n", "49. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Instruction tuning for large language models: A survey. arXiv preprint arXiv:2308.10792 (2023).\n", "\n", "50. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, E. A survey of reinforcement learning from human feedback. arXiv preprint arXiv:2312.14925 (2023).\n", "\n", "51. <PERSON>, <PERSON>, M. Text summarization with pretrained encoders. arXiv preprint arXiv:1908.08345 (2019).\n", "\n", "52. <PERSON><PERSON>, J. D. <PERSON>.-W. C. & <PERSON>, L. K. BERT: Pre-training of deep bidirectional transformers for language understanding in Proceedings of naacL-HLT 1 (2019).\n", "\n", "53. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Large language models surpass human experts in predicting neuroscience results. Nature human behaviour, 1-11 (2024).\n", "\n", "54. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, S<PERSON> & <PERSON>, G. Enhancing activity prediction models in drug discovery with the ability to understand human language in International Conference on Machine Learning (2023), 30458-30490.\n", "\n", "55. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Biological structure and function emerge from scaling unsupervised learning to 250 million protein sequences. Proceedings of the National Academy of Sciences 118, e2016239118 (2021).\n", "\n", "56.\n", "\n", "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al.\n", "\n", "Evolutionary- scale prediction of atomic-level protein structure with a language model.\n", "\n", "Science\n", "\n", "379,\n", "\n", "1123-1130 (2023).\n", "\n", "57. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, M. & Church, G. M. Unified rational protein engineering with sequence-based deep representation learning. Nature methods 16, 1315-1322 (2019).\n", "\n", "58. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, B. ProtGPT2 is a deep unsupervised language model for protein design. Nature communications 13, 4348 (2022).\n", "\n", "59. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. Sequence modeling and design from molecular to genome scale with Evo. Science 386, eado9336 (2024).\n", "\n", "60. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, F<PERSON>, <PERSON>, C., <PERSON>, <PERSON>, <PERSON>, B. <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, et al. Nucleotide Transformer: building and evaluating robust foundation models for human genomics. Nature Methods, 1-11 (2024).\n", "\n", "61. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> & <PERSON>, Y. The OMG dataset: An Open MetaGenomic corpus for mixed-modality genomic language modeling. bioRxiv, 2024-08 (2024).\n", "\n", "62. <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> & <PERSON>, <PERSON><PERSON>: An interleaved protein-language llm with protein-as-word pre-training. arXiv preprint arXiv:2403.07920 (2024).\n", "\n", "63. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> & <PERSON>, R. <PERSON>5: Enriching cross-modal integration in biology with chemical knowledge and natural language associations. arXiv preprint arXiv:2310.07276 (2023).\n", "\n", "64. Anonymous. Parameter Efficient Graph Encoding for Large Language Models 2025. https://openreview.net/forum?id= RbcXV63ZJk.\n", "\n", "65. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> & <PERSON>, B. scGPT: toward building a foundation model for single-cell multi-omics using generative AI. Nature Methods, 1-11 (2024).\n", "\n", "66. <PERSON>, <PERSON>, <PERSON><PERSON>: a simple but effective foundation model for genes and cells built from ChatGPT. bioRxiv, 2023-10 (2024).\n", "\n", "67. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, M<PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Transfer learning enables predictions in network biology. Nature 618, 616-624 (2023).\n", "\n", "68. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, R., Halle, L., <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, et al. Nicheformer: a foundation model for single-cell and spatial omics. bioRxiv, 2024-04 (2024).\n", "\n", "69. <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Cell2Sentence: teaching large language models the language of biology. BioRxiv, 2023-09 (2023).\n", "\n", "70. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>.-<PERSON><PERSON>, et al. NatureLM: Deciphering the Language of Nature for Scientific Discovery. arXiv preprint arXiv:2502.07527 (2025).\n", "\n", "71. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. A survey on large language model based autonomous agents. Frontiers of Computer Science 18, 186345 (2024).\n", "\n", "72. <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> & <PERSON>, L. Role play with large language models. Nature 623, 493-498 (2023).\n", "\n", "73. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> & <PERSON>, M. Communicative agents for software development. arXiv preprint arXiv:2307.07924 6 (2023).\n", "\n", "74. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Metagpt: Meta programming for multi-agent collaborative framework. arXiv preprint arXiv:2308.00352 (2023).\n", "\n", "75. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, A. Multi-agent collaboration: Harnessing the power of intelligent llm agents. arXiv preprint arXiv:2306.03314 (2023).\n", "\n", "76. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>. Reasoning with language model is planning with world model. arXiv preprint arXiv:2305.14992 (2023).\n", "\n", "77. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> & <PERSON>, I. Language models as zero-shot planners: Extracting actionable knowledge for embodied agents in International conference on machine learning (2022), 9118-9147.\n", "\n", "78. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, W.<PERSON><PERSON> & <PERSON>, Y<PERSON>-planner: Few-shot grounded planning for embodied agents with large language models in Proceedings of the IEEE/CVF International Conference on Computer Vision (2023), 2998-3009.\n", "\n", "79. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>. Describe, explain, plan and select: Interactive planning with large language models enables open-world multi-task agents. arXiv preprint arXiv:2302.01560 (2023).\n", "\n", "80. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> & <PERSON>, <PERSON><PERSON> of thoughts: Deliberate problem solving with large language models. Advances in Neural Information Processing Systems 36 (2024).\n", "\n", "81. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>: Tool augmented language models. arXiv preprint arXiv:2205.12255 (2022).\n", "\n", "82. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, L<PERSON>, Cancedda, N. & <PERSON>, <PERSON><PERSON>: Language models can teach themselves to use tools. Advances in Neural Information Processing Systems 36, 68539-68551 (2023).\n", "\n", "83. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Tool learning with foundation models. ACM Computing Surveys 57, 1-40 (2024).\n", "\n", "84. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, D. Large language models as tool makers. arXiv preprint arXiv:2305.17126 (2023).\n", "\n", "85. <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, S. <PERSON>flexi<PERSON>: Language agents with verbal reinforcement learning. Advances in Neural Information Processing Systems 36 (2024).\n", "\n", "86. <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, K. & Press, O. Swe-agent: Agent-computer interfaces enable automated software engineering. arXiv preprint arXiv:2405.15793 (2024).\n", "\n", "87. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> & <PERSON>, M. Experiential co-learning of software-developing agents. arXiv preprint arXiv:2312.17025 (2023).\n", "\n", "88. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Towards an AI co-scientist. arXiv preprint arXiv:2502.18864 (2025).\n", "\n", "89. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, E. Agent Laboratory: Using LLM Agents as Research Assistants. arXiv preprint arXiv:2501.04227 (2025).\n", "\n", "90. <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, N. <PERSON>, <PERSON>, J. <PERSON> & <PERSON>, J. The virtual lab: Ai agents design new sars-cov-2 nanobodies with experimental validation. bioRxiv, 2024-11 (2024).\n", "\n", "91. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, D. The ai scientist: Towards fully automated open-ended scientific discovery. arXiv preprint arXiv:2408.06292 (2024).\n", "\n", "92. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>. <PERSON> & <PERSON>, P. Augmenting large language models with chemistry tools. Nature Machine Intelligence, 1-11 (2024).\n", "\n", "93. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, B. & <PERSON>, G. Autonomous chemical research with large language models. Nature 624, 570-578 (2023).\n", "\n", "94. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, T. & <PERSON>, M. TxA<PERSON>: An AI Agent for Therapeutic Reasoning Across a Universe of Tools. arXiv preprint arXiv:2503.10970 (2025).\n", "\n", "95. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> & <PERSON>, E. Catastrophic forgetting in deep learning: A comprehensive taxonomy. arXiv preprint arXiv:2312.10549 (2023).\n", "\n", "Supplementary Material\n", "\n", "Version control\n", "\n", "V0 (25 March 2025) → V1\n", "\n", "· Upgraded the Agentic-Tx system's orchestrator from Gemini 2.0 to Gemini 2.5. This enhancement results in significant performance improvements in complex workflow orchestration, as detailed in Table 3.\n", "\n", "· Added performance results of TxGemma-Predict and TxGemma-Chat (trained only on commercially licensed datasets) for binary classification (Table S.17), regression, and generation tasks (Table S.18).\n", "\n", "A Summary\n", "\n", "· Data details as listed in Section B:\n", "\n", "-Table S.1: Excluded TDC tasks and reasons for exclusion.\n", "\n", "-Table S.3: Number of samples in training, validation, and test sets for all regression and generation tasks.\n", "\n", "-Table S.2: Number of samples in training, validation, and test sets for all binary classification tasks.\n", "\n", "-Table S.4: Descriptions of the binary classification tasks.\n", "\n", "-Table S.6 Types of features in the processed TDC data along with illustrative examples.\n", "\n", "-Table S.5: Descriptions of the regression and generation tasks.\n", "\n", "-Figure S.1: Distribution of TDC task sizes, aggregated over train, validation, and test sets.\n", "\n", "· Method and modeling details as listed in Section C:\n", "\n", "-Table S.7 Examples of prompts for binary classification tasks.\n", "\n", "-Table S.9 Example of a 10-shot prompt for a binary classification task.\n", "\n", "-Table S.8 Examples of prompts for regression and generation tasks.\n", "\n", "-Table S.10 Example of prompts for predicting adverse events in clinical trials.\n", "\n", "-Table S.12 List of tools available to Agentic-Tx.\n", "\n", "-Table S.11 Example of Agentic-Tx response to a chemical preference question.\n", "\n", "-Figure S.2 Distribution of Tanimoto similarities for 10 nearest neighbors by dataset splits in the AMES task.\n", "\n", "-Section C.1 Details about <PERSON>on signed-rank test used to assess model performance.\n", "\n", "• Additional results as listed in Section D:\n", "\n", "-Additional prediction results for TxGemma (Section D.1)\n", "\n", "∗ Table S.13 Performance on binary classification tasks for specialist SOTA, base Gemma-2, and TxGemma-Predict models.\n", "\n", "∗ Table S.15 Performance on binary classification tasks for TxGemma-Predict, TxGemma-Chat, and Tx-LLM models.\n", "\n", "∗ Table S.14 Performance on regression and generation tasks for specialist SOTA, base Gemma-2, and TxGemma-Predict models.\n", "\n", "∗ Table S.16 Performance on regression and generation tasks for TxGemma-Predict, TxGemmaChat, and Tx-LLM models.\n", "\n", "∗ Table S.18 Performance on regression and generation tasks for TxGemma-Predict and TxGemmaChat models trained only on datasets with commercial licenses.\n", "\n", "∗ Table S.17 Performance on binary classification tasks for TxGemma-Predict and TxGemmaChat models trained only on datasets with commercial licenses.\n", "\n", "∗ Figure S.4 Performance of TxGemma-27B-Predict compared to generalist and specialist SOTA models.\n", "\n", "∗ Figure S.6 Comparison of TxGemma-27B-Predict with MolE on select small molecule tasks.\n", "\n", "∗ Figure S.5 Comparison of TxGemma-27B-Predict with LlaSMol on select small molecule tasks.\n", "\n", "∗ Figure S.11 Inference speed of TxGemma models at various sizes.\n", "\n", "∗ Figure S.12 Percent contamination for datasets and cosine similarity analysis.\n", "\n", "∗ Figure S.16 Performance by feature type of all TxGemma-Predict sizes.\n", "\n", "∗ Figure S.13 Performance on contaminated datasets before and after filtering out contaminated datapoints.\n", "\n", "∗ Figure S.17 Comparison of TxGemma-Predict performances over different sizes and with Gemma-2 models.\n", "\n", "∗ Figure S.18 Correlations of TxGemma-27B-Predict predictions for toxicity and clinical trial approval tasks.\n", "\n", "-Conversing with TxGemma-27B-Predict and TxGemma-27B-Chat (Section D.2)\n", "\n", "∗ Figure S.7 Comparison of TxGemma-27B-Predict, TxGemma-27B-Cha<PERSON>, and Gemma-2-27B on MMLU.\n", "\n", "∗ Figure S.9 Example of a multi-turn dialogue with TxGemma-27B-Predict about its predictions.\n", "\n", "∗ Figure S.8 Example of a dialogue with TxGemma-27B-Predict about general topics.\n", "\n", "∗ Figure S.10 Example of a prompt format the enables TxGemma-Chat to provide reasoning for challenging tasks.\n", "\n", "-Additional Agentic-Tx Results (Section D.3)\n", "\n", "∗ Figure S.14 Agentic-Tx tool use frequencies for chemical preference and HLE benchmarks.\n", "\n", "∗ Figure S.15 Agentic-Tx tool use frequency per question for chemical preference questions.\n", "\n", "-Proof-of-concept example using TxGemma (Section D.4)\n", "\n", "∗ Figure S.3 Illustration of a possible application of TxGemma to end-to-end therapeutic development.\n", "\n", "B Data details\n", "\n", "This section provides a breakdown of the tasks used in our study, including information on excluded tasks and the size of training, validation, and test sets for binary classification, regression, and generation tasks.\n", "\n", "As previously mentioned, we excluded a small number of tasks from TDC for various reasons. Table S.1 provides an overview of the excluded tasks and the rationale behind their exclusion. The primary reasons for exclusion were the tasks' relevance to the study, limitations of LLMs, and specific data characteristics, such as the absence of clear metrics or redundancy. For instance, tasks like QM7b, QM8, and QM9, which focus on predicting quantum properties, were not directly relevant to the study's focus on therapeutic development. Similarly, IEDB <PERSON>sen and <PERSON><PERSON> were excluded due to their small size and the complexity of implementing token prediction, as opposed to binary classification, within an LLM framework. Tasks such as DrugBank DDI, TWOSIDES, and USPTO Catalyst posed challenges due to the large number of potential labels, making them difficult for LLMs to process effectively. MOSES, ZINC, and ChEMBL were excluded because they lacked well-defined evaluation metrics. Finally, USPTO 50K and USPTO Reaction were excluded as they either overlapped with or were subsets of the USPTO task.\n", "\n", "Tables S.2 and S.3 specify the number of samples in the training, validation, and test sets for the included binary classification, regression, and generation tasks, respectively. Substantial variability in task sizes across different tasks is shown in these tables. The binary classification tasks range from 196 to 1,406,988 samples, while the regression and generation tasks range from 345 to 775,767 samples. This variability highlights the diverse data availability landscape across various tasks. Figure S.1 provides a visual representation of the distribution of TDC task sizes, aggregated across train, validation, and test sets. For tasks encompassing multiple subtasks, like ToxCast, the task size is computed by summing the sizes of each individual dataset.\n", "\n", "Tables S.4 and S.5 provide a brief description of the tasks, as well as the types of inputs (e.g. protein, small molecules, etc.). These tasks are diverse and encompass many different aspects of development. Some tasks corresponding to gene-disease association or protein-protein interaction prediction are useful for early-stage development, in order to identify mechanisms of disease and relevant targets. Predictions of antibody affinity, drug-target interaction, high-throughput screening, drug synergy are useful for intermediate development steps that involve proposing candidate therapeutics based on their interaction with a target. Predictions of toxicity, pharmacokinetics, and developability are useful for filtering candidates down based on favorable druglike properties. Predictions of clinical trial outcome, reaction yields, retrosynthesis are useful for late-stage development where understanding the likelihood of clinical trial approval and manufacturing potential are critical. There are also tasks that are highly specific for particular therapeutics types, which include predictions of CRISPR repair, peptide-MHC binding, miRNA-Target interaction, and TCR-epitope binding.\n", "\n", "Binary classification tasks always output '(A)' or '(B)', where '(A)' is a negative answer to the question which is specified in the prompt and '(B)' is a positive answer. Regression tasks output an integer between\n", "\n", "0 and 1000, which can be transformed back into the original task-specific label space. The output of the USPTO generation task is the SMILES string of the predicted molecules. Table S.6 lists the different types of inputs in the processed TDC data along with illustrative examples.\n", "\n", "TABLE\n", "\n", "Table S.1 | Excluded TDC tasks and reasons for exclusion. The tasks were excluded primarily due to their relevance to the study, limitations inherent to large language models (LLMs), and specific data characteristics, such as a lack of clear evaluation metrics or redundancy.\n", "\n", "TABLE\n", "\n", "Table S.2 | Number of samples in training, validation, and test sets for all binary classification tasks. The binary classification tasks range in size from a minimum of 196 samples (Carcinogens <PERSON>) to a maximum of 1,406,988 samples (<PERSON><PERSON><PERSON>), highlighting the considerable variability in data availability across different tasks. The task type and split type are also indicated following the TDC classification and recommendation.\n", "\n", "∗ To predict whether compounds have Anti-HIV properties.\n", "\n", "TABLE\n", "\n", "Table S.3 | Number of samples in training, validation, and test sets for all regression and generation tasks. The regression and generation tasks vary significantly in size, ranging from a minimum of 345 samples (Protein SAbDab) to a maximum of 775,767 samples (USPTO). The task type and split type are also indicated following the TDC classification and recommendation.\n", "\n", "TABLE\n", "\n", "Table S.4 | Inputs and task descriptions for binary classification tasks. All output responses are either (A) for negative or (B) for positive.\n", "\n", "∗ To predict whether compounds have Anti-HIV properties.\n", "\n", "TABLE\n", "\n", "Table S.5 | Inputs and task descriptions for regression and generation tasks. Regression task outputs are integers between 0 and 1000, which represents a binned transformation of the original numeric label. On evaluation, the integer output is transformed back into the original numeric label space. For the USPTO generation task, the output is the SMILES string of the predicted set of small molecules.\n", "\n", "TABLE\n", "\n", "Table S.6 | Types of drugs and targets found in our data. Features found in our data as well as their textual representation and an illustrative example. Protein sequences are divided into several subtypes: some proteins and peptides are represented using their full amino acid sequence whereas MHC molecules are represented using the amino acid pseudo-sequences that only use residues in contact with a peptide, and TCRs only use CDR3 hypervariable loops.\n", "\n", "† Only for residues in contact with a peptide.\n", "\n", "C Method details\n", "\n", "This section elaborates on the modeling choices employed in the development of TxGemma. Tables S.7 and S.8 illustrate prompts used for binary classification, regression, and generation tasks, showcasing the input structure for the model including the instructions and context provided to the model. Table S.9 provide a concrete example of few-shot prompting applied to a binary classification task using 10 examples with nearest-neighbor shots. Each dataset in our data is structured as a text prompt, consisting of instructions, context, a question, and the corresponding answer. To provide relevant background, we created 2-3 sentence contexts based on TDC dataset descriptions and literature searches. Prompts used for predicting adverse events in clinical trials based on the TrialBench dataset [1] are shown in Table S.10. To illustrate the reasoning process of Agentic-Tx, Table S.11 provides an example of the steps taken to answer a chemical preference question from ChemBench. Table S.12 also provides a comprehensive list of the tools available of Agentic-Tx. Section C.1 provides details of the Wilcoxon signed-rank test used to assess the performance of our models across all tasks.\n", "\n", "We utilize random data points from the training set for few-shot learning during training. Although we use nearest neighbor shots for evaluation, we opt for random shots during training due to the higher intra-set similarity observed within the training data compared to between training and test sets, as illustrated in Figure S.2.\n", "\n", "C.1 Aggregated method comparison\n", "\n", "For a pair of performances ( x i , y i ) of a task i , the test statistic of the <PERSON>on signed-rank test is calculated as the minimum of the positive-rank sum ( W + ) and the negative-rank sum ( W -),\n", "\n", "where X i = x i -y i and R i is the rank of | x i -y i | . In order to account for the differences in magnitudes for MAE and MSE metrics, we normalized all performances by the mean of the performances from both models. We also reversed the sign of MAEs and MSEs because lower MAEs and MSEs correspond to better performances.\n", "\n", "Instructions : Answer the following question about drug properties.\n", "\n", "Context : As a membrane separating circulating blood and brain extracellular fluid, the blood-brain barrier (BBB) is the protection layer that blocks most foreign drugs. Thus the ability of a drug to penetrate the barrier to deliver to the site of action forms a crucial challenge in development of drugs for central nervous system.\n", "\n", "Question : Given a drug SMILES string, predict whether it\n", "\n", "(A) does not cross the BBB (B) crosses the BBB\n", "\n", "Drug SMILES: CN1C(=O)CN=C(C2=CCCCC2)c2cc(Cl)ccc21\n", "\n", "Answer: (B)\n", "\n", "Instructions : Answer the following question about peptide-MHC binding.\n", "\n", "Context : In the human body, T cells monitor the existing peptides and trigger an immune response if the peptide is foreign. To decide whether or not if the peptide is not foreign, the peptide must bind to a major histocompatibility complex (MHC) molecule. Therefore, predicting peptide-MHC binding affinity is pivotal for determining immunogenicity. In some experiments, the peptide binding is measured against cells that express multiple MHCs, so the peptide could be binding any one of the possible MHCs. Class 1 MHC molecules bind to peptides that are usually 8-14 amino acids long and activate CD8 T cells.\n", "\n", "Question : Given the amino acid sequence of the peptide and possible pseudo amino acid sequences of MHC 1, predict whether the peptide\n", "\n", "(A) does not bind to any of the MHCs (B) binds to any of the MHCs\n", "\n", "Peptide amino acid sequence: QLADETLLKV\n", "\n", "Possible MHC pseudosequences: <PERSON><PERSON><PERSON><PERSON>GEKVAHTH<PERSON>TLYVRYHYYTWAEWAYTWY\n", "\n", "Answer: (B)\n", "\n", "Instructions : Answer the following question about miRNA protein interactions.\n", "\n", "Context : MicroRNAs (miRNAs) are, small non-coding RNAs with 18-25 nucleotides, which are central regulators at the post-transcriptional level in both animals and plants. Perfect or near-perfect complementary binding of miRNAs and their target mRNA negatively regulates gene expression by accelerating mRNA degradation or suppressing mRNA translation.\n", "\n", "Question : Given the miRNA mature sequence and target amino acid sequence, predict whether\n", "\n", "(A) the miRNA and target do not interact (B) the miRNA and target interact miRNA sequence: UUCCUGUCAGCCGUGGGUGCC\n", "\n", "Target amino acid sequence: MSVNMDELRHQVMINQFVLAAGCAADQAKQLLQAAHWQFETALSTFFQETNIPNSHHHHQMMCTPSNTPATPPNFPDALAMFSKLRASEGLQSSNSPMTAAACSPPANFSPFWASSPPSHQAPWIPPSSPTTFHHLHRPQPTWPPGAQQGGAQQKAMAAMDGQR\n", "\n", "Answer: (A)\n", "\n", "Instructions : Answer the following question about clinical trials.\n", "\n", "Context : Clinical trial is the most time and cost-consuming step in the drug discovery process. Phase 1 clinical trials test the safety and basic properties of a new drug or treatment in a small group of people for the first time. Optimizing and designing trials with machine learning could drastically lead to the speedup of delivery of life-saving therapeutics to patients. Clinical trial outcome prediction is a machine learning task that aims to forecast the outcome of clinical trials, such as the approval rate of a drug or treatment. It utilizes various clinical trial features, including the drug's molecular structure and patient disease.\n", "\n", "Question : Given a drug SMILES string and disease, predict if the phase 1 trial\n", "\n", "(A) would not be approved (B) would be approved\n", "\n", "Drug SMILES: COC1=NC(N)=NC2=C1N=CN2[C@@H]1O[C@H](CO)[C@@H](O)[C@@H]1O\n", "\n", "Disease: Chronic myeloproliferative disease\n", "\n", "Answer: (A)\n", "\n", "Instructions : Answer the following question about drug properties.\n", "\n", "Context : The human colon epithelial cancer cell line, Caco-2, is used as an in vitro model to simulate the human intestinal tissue. The experimental result on the rate of drug passing through the Caco-2 cells can approximate the rate at which the drug permeates through the human intestinal tissue.\n", "\n", "Question : Given a drug SMILES string, predict its normalized Caco-2 cell effective permeability from 000 to 1000, where 000 is minimum permeability and 1000 is maximum permeability.\n", "\n", "Drug SMILES: O=C(O)COC(=O)Cc1ccccc1Nc1c(Cl)cccc1Cl\n", "\n", "Answer: 788\n", "\n", "Instructions : Answer the following question about drug responses.\n", "\n", "Context : The same drug compound could have various levels of responses in different patients. To design drug for individual or a group with certain characteristics is the central goal of precision medicine. In experiments, IC50s of drugs were measured against cancer cell lines.\n", "\n", "Question : Given a drug SMILES string and a cell line description, predict the normalized drug sensitivity from 000 to 1000, where 000 is minimum drug sensitivity and 1000 is maximum drug sensitivity.\n", "\n", "Drug SMILES: CN1C=C(C2=CC=CC=C21)/C=C\\3/C4=C(C=CC=N4)NC3=O\n", "\n", "Cell line description: SNU-1, stomach cell sourced from cancer\n", "\n", "Answer: 615\n", "\n", "Instructions : Answer the following question about drug target interactions.\n", "\n", "Context : Drug-target binding is the physical interaction between a drug and a specific biological molecule, such as a protein or enzyme. This interaction is essential for the drug to exert its pharmacological effect. The strength of the drug-target binding is determined by the binding affinity, which is a measure of how tightly the drug binds to the target. Kd is the dissociation constant of a drug-target complex. It is the concentration of drug at which half of the drug-target complexes have dissociated. A lower Kd value indicates a stronger binding affinity.\n", "\n", "Question : Given the target amino acid sequence and compound SMILES string, predict their normalized binding affinity Kd from 000 to 1000, where 000 is minimum Kd and 1000 is maximum Kd.\n", "\n", "Drug SMILES: O=S(=O)(O)c1cccc2cccc(Nc3ccccc3)c12\n", "\n", "Target amino acid sequence: MATVQQLEGRWRLVDSKGFDEYMKELGVGIALRKMGAMAKPDCIITCDGKNLTIKTESTLKTTQFSCTLGEKFEETTADGRKTQTVCNFTDGALVQHQEWDGKESTITRKLKDGKLVVECVMNNVTCTRIYEKVE\n", "\n", "Answer: 397\n", "\n", "Instructions : Answer the following question about reactions.\n", "\n", "Context : Retrosynthesis is the process of finding a set of reactants that can synthesize a target molecule, i.e., product, which is a fundamental task in drug manufacturing. The target is recursively transformed into simpler precursor molecules until commercially available \"starting\" molecules are identified. In a data sample, there is only one product molecule, reactants can be one or multiple molecules.\n", "\n", "Question : Given a product SMILES string, predict the reactant SMILES string.\n", "\n", "Product SMILES: [CH2:12]1[C:7]2([CH2:6][CH2:5][O:15][CH2:1][CH2:8]2)[CH2:13][CH2:14][O:10][C:11]1=[O:17]\n", "\n", "Answer: [CH:1]12B[CH:5]([CH2:6][CH2:7][CH2:8]1)CCC2.[O:10]1[CH2:14][CH2:13][CH2:12] [CH2:11]1.[OH- :15].[Na+].[OH:17]O.Cl\n", "\n", "Table S.9 | Example of a 10-shot prompt for a binary classification task. Shots are selected from nearest neighbors in the combined training and validation set (not the test set).\n", "\n", "Instructions : Answer the following question about drug properties.\n", "\n", "Context : As a membrane separating circulating blood and brain extracellular fluid, the blood-brain barrier (BBB) is the protection layer that blocks most foreign drugs. Thus the ability of a drug to penetrate the barrier to deliver to the site of action forms a crucial challenge in development of drugs for central nervous system.\n", "\n", "Question : Given a drug SMILES string, predict whether it (A) does not cross the BBB (B) crosses the BBB\n", "\n", "Drug SMILES: CN1C(=O)CN=C(c2ccccc2)c2cc(Cl)ccc21\n", "\n", "Answer: (B)\n", "\n", "Drug SMILES: CN1C(=O)CN=C(c2ccccc2F)c2cc(Cl)ccc21\n", "\n", "Answer: (B)\n", "\n", "Drug SMILES: CN1C(=S)CN=C(c2ccccc2)c2cc(Cl)ccc21\n", "\n", "Answer: (B)\n", "\n", "Drug SMILES: CP(C)(=O)CN1C(=O)CN=C(c2ccccc2)c2cc(Cl)ccc21 Answer: (B)\n", "\n", "Drug SMILES: CN1C(=O)CN=C(c2ccccc2)c2cc([N+](=O)[O-])ccc21 Answer: (B)\n", "\n", "Drug SMILES: CCN(CC)CCN1C(=O)CN=C(c2ccccc2F)c2cc(Cl)ccc21\n", "\n", "Answer: (B)\n", "\n", "Drug SMILES: O=C1CN=C(c2ccccc2)c2cc(Cl)ccc2N1CC1CC1\n", "\n", "Answer: (B)\n", "\n", "Drug SMILES: C#CCN1C(=O)CN=C(c2ccccc2)c2cc(Cl)ccc21\n", "\n", "Answer: (B)\n", "\n", "Drug SMILES: O=C1CN=C(c2ccccc2)c2cc(Cl)ccc2N1CC(F)(F)F\n", "\n", "Answer: (B)\n", "\n", "Drug SMILES: CCS(=O)(=O)CCN1C(=O)CN=C(c2ccccc2F)c2cc(Cl)ccc21\n", "\n", "Answer: (B)\n", "\n", "Drug SMILES: CN1C(=O)CN=C(C2=CCCCC2)c2cc(Cl)ccc21\n", "\n", "Answer: (B)\n", "\n", "Table S.10 | Example of prompts for predicting adverse events in clinical trials. The top prompt only provides drug SMILES strings while the bottom prompt also includes textual information about the clinical trial.\n", "\n", "From the following information about a clinical trial, predict whether it would have an adverse event.\n", "\n", "Drug: CC[C@H]1[C@@H](COC2=C3C=C(OC)C(=CC3=CC=N2)C(N)=O)NC(=O)[C@H]1F .[H][C@@]12CC[C@H](O)[C@@]1(C)CC[C@]1([H])C3=C(CC[C@@]21[H])C=C(O)C=C3\n", "\n", "Answer : No\n", "\n", "From the following information about a clinical trial, predict whether it would have an adverse event.\n", "\n", "Title: A Study To Estimate The Effect of PF-06650833 On The Pharmacokinetics (PK) of Oral Contraceptive (OC) Summary: This is a Phase 1, open label, fixed sequence study of the effect of multiple dose PF-06650833 on single dose OC PK in healthy female subjects.\n", "\n", "Phase: 1\n", "\n", "Disease:\n", "\n", "Healthy\n", "\n", "Minimum age: 18 Years\n", "\n", "Maximum age: 60 Years\n", "\n", "Healthy volunteers: Accepts Healthy Volunteers\n", "\n", "Interventions: 400 mg by mouth (PO) Once daily (QD) for 11 days; Single dose of Oral tablet containing 30 ug EE and 150 ug of LN\n", "\n", "Drug: CC[C@H]1[C@@H](COC2=C3C=C(OC)C(=CC3=CC=N2)C(N)=O)NC(=O)[C@H]1F .[H][C@@]12CC[C@H](O)[C@@]1(C)CC[C@]1([H])C3=C(CC[C@@]21[H])C=C(O)C=C3\n", "\n", "Answer : No\n", "\n", "TABLE\n", "\n", "Table S.11 | Example of Agentic-Tx ChemBench chemical preference question answering.\n", "\n", "TABLE\n", "\n", "Table S.12 | Descriptions of tools used by Agentic-Tx.\n", "\n", "D Additional results\n", "\n", "D.1 TxGemma-Predict performance\n", "\n", "Figure S.4 compares TxGemma-27B-Predict with previous SOTA models, taking into account that Tx-LLM M achieved SOTA performance on many tasks. We provide detailed results tables for binary classification tasks in Table S.13 (comparing against specialist SOTA and base models) and Table S.15 (comparing against TxGemmaChat and Tx-LLM), and for regression and generation tasks in Table S.14 (comparing against specialist SOTA and base models) and Table S.16 (comparing against TxGemma-Chat and Tx-LLM). Tables S.17 and S.18 list the performances of released TxGemma models trained only on datasets with commercial licenses. Figures S.5 and S.6 compares TxGemma-27B-Predict with LlaSMol and MolE, models specialized for small molecules, on small molecule tasks. Figure S.12 plots the percentage of tasks that contain contaminated datapoints overlapping with the Gemma-2 pretraining data, the percent of contaminated datapoints for these tasks, and Figure S.13 shows the results of TxGemma-27B-Predict after filtering contaminated datapoints out. We observe that most tasks have no contamination, and filtering these datapoints out does not negatively impact TxGemma-27B-Predict performance. Figure S.16 plots performances for particular feature types across multiple model sizes, showing that the integration of SMILES strings and textual information is consistent. Figure S.17 plots performances over all tasks for comparisons of model size and domain fine-tuning, showing that these variables are significant. Figure S.18 shows that TxGemma-27B-Predict toxicity and clinical trial approval predictions are correlated, likely because toxicity in an important component of trial approval. Figure S.11 plots the inference speed, normalized by the number of chips used for serving, for all model sizes.\n", "\n", "D.2 Conversing with TxGemma-27B-Predict and TxGemma-27B-Chat\n", "\n", "Figure S.8 illustrates an example of providing a prompt to TxGemma-27B-Predict that is not in the processed data format. TxGemma-27B-Predict is able to provide a coherent response in a manner similar to the general LLMs. Figure S.9 illustrates an example of first providing a prompt to TxGemma-27B-Predict in the processed format and asking follow-up questions in subsequent turns. In the second turn, instructing the model to not in the processed data format is able to elicit a reasonable but succinct response. However, the third turn leads to the model answering in the processed data format, highlighting the difficulty of multi-turn dialogue after training only on the processed TDC data. Figure S.7 plots the performance of TxGemma-27B-Chat on the MMLU benchmark in comparison with both Gemma-2-27B and TxGemma-27B-Predict. TxGemma-27B-Chat performs similarly to Gemma-2-27B on MMLU while TxGemma-27B-Predict scores much lower. Figure S.10 shows an example of using a specific prompting structure with TxGemma-27B-Chat to elicit reasoning on a more challenging task of clinical trial approval. If this prompting structure is not used, the model refuses to provide reasoning.\n", "\n", "D.3 Agentic-Tx Tool Use Analysis\n", "\n", "Figure S.14 shows the tool usage frequency for different benchmarks, illustrating that Agentic-Tx dynamically adjusts it tool usage to suit the problem. Figure S.15 shows the most frequent tools used per question for chemical preference questions, showing consistent usage of molecule-based tools.\n", "\n", "D.4 Proof-of-concept use of TxGemma for end-to-end therapeutic development\n", "\n", "In Figure S.3, we illustrate a simplified example of how TxGemma might be helpful in identifying a drug for ovarian cancer. In this example, we chose to directly prompt TxGemma, rather than using Agentic-Tx, to strictly isolate potential information leakage introduced by web search, which is outside of our training data. This approach allows us to examine the model's inherent capabilities, though we acknowledge that a full agent-based workflow is a plausible extension.\n", "\n", "We initially use the DisGeNET prompt to identify an ovarian cancer-associated target gene from a short list of genes including PIK3CA, JAK2, RET. TxGemma-27B-Predict predicts that PIK3CA, a gene not found in the training set which is known to be mutated in ovarian cancer [2], has an association score of 0.7 with ovarian cancer. This association score is nearly 2.5 standard deviations above the mean score ( µ = 0 37 . , σ = 0 13 . ), indicating a strong association. JAK2 and RET share an association score of 0.3 which is below\n", "\n", "the mean score. We then used TxGemma-27B-Predict to select a potential therapeutic from a molecule shortlist, prioritizing predicted IC 50 against the E545K mutant (an oncogenic mutation [3]), toxicity, and clinical trial success. Our manually curated shortlist of drugs, unseen to the model during training, include two existing cancer therapies including alpelisib and afatinib and a novel molecule which we randomly generated. Both afatinib (1.02 µ M IC 50 ) and the novel molecule (10.2 µ M IC 50 ) exhibit high predicted IC 50 values, suggesting weak inhibition. However, alpelisib has a predicted IC 50 of 30 nM, suggestive of potent inhibition and relatively close to the experimental value of 5 nM suggested by <PERSON> et al. [4] and <PERSON><PERSON><PERSON> et al. [5]. TxGemma-27B-Predict also predicts that alpelisib is not mutagenic and would pass a phase 1 clinical trial for ovarian cancer. This iterative evaluation also corroborated by existing evidence: alpelisib is approved for breast cancer [6] and has shown activity in ovarian cancer [7, 8, 9].\n", "\n", "This workflow demonstrates a proof-of-concept for TxGemma's application in automating and optimizing therapeutic selection. We anticipate an agentic system capable of generating comprehensive lists of potential therapies and gene-disease associations paired with TxGemma would enable rapid prioritization and filtering, helping in reducing the candidate pool and accelerating the transition to preclinical studies. However, it's crucial to acknowledge the limitations of this demonstration. Clinical trial predictions are limited to Phase 1 success, and mutagenicity predictions do not encompass all aspects of small molecule toxicity. Future work should include experimental validation of TxGemma predictions and consideration of additional toxicity factors, such as hematologic toxicity, which were not included in our data.\n", "\n", "TABLE\n", "\n", "Table S.13 | Model performance on binary classification tasks. TxGemma-Predict and Gemma-2 performances compared with specialist SOTA for each binary classification task, along with the metric type.\n", "\n", "TABLE\n", "\n", "Table S.14 | Model performance on regression and generation tasks. TxGemma-Predict and Gemma-2 performances compared with specialist SOTA for each regression and generation task, along with the metric type. Tasks for which we did not find a specialist SOTA value are indicated with N/A.\n", "\n", "TABLE\n", "\n", "Table S.15 | Model performance on binary classification tasks. TxGemma-Predict, TxGemma-Chat, and Tx-LLM performances for each binary classification task, along with the metric type.\n", "\n", "∗ To predict whether compounds have Anti-HIV properties.\n", "\n", "TABLE\n", "\n", "Table S.16 | Model performance on regression and generation tasks. TxGemma-Predict, TxGemma-Chat, and Tx-LLM performances for each regression and generation task, along with the metric type.\n", "\n", "TABLE\n", "\n", "Table S.17 | Model performance on binary classification tasks for models trained only on datasets with commercial licenses. TxGemma-Predict and TxGemma-Chat performances for each binary classification task, along with the metric type.\n", "\n", "∗ To predict whether compounds have Anti-HIV properties.\n", "\n", "TABLE\n", "\n", "Table S.18 | Model performance on regression and generation tasks for models trained only on datasets with commercial licenses. TxGemma-Predict and TxGemma-Chat performances for each regression or generation task, along with the metric type.\n", "\n", "Tell me about the blood-brain barrier\n", "\n", "Figure S.9 | Predictive model struggles to hold a conversation about processed TDC data prompts after fine-tuning only on this data. Example of a multi-turn dialogue with TxGemma-27B-Predict. The initial turn is a question in the processed TDC data format. After the initial turn, the model is prompted to describe some properties associated with the problem of crossing the blood-brain barrier. An additional instruction to avoid answering with (A) or (B) is necessary. The model is able to provide some answers in natural language, but another turn leads to an inappropriate multiple choice answer.\n", "\n", "Provide five steps of reasoning to think through the problem and then provide an answer. Let's think step by step. Consider whether the drug would pass phase 1 trials, not whether the drug would be fully approved. You must also discuss the specific drug and disease as part of your reasoning that leads to your answer. This should be emphasized.\n", "\n", "Instructions: Answer the following question about clinical trials.\n", "\n", "Context: Clinical  trial  is  the  most  time  and  cost-consuming  step  in  the  drug  discovery  process.  Phase  1 clinical trials test the safety and basic properties of a new drug or treatment in a small group of people for the first  time.  Optimizing  and  designing  trials  with  machine  learning  could  drastically  lead  to  the  speedup  of delivery of life-saving therapeutics to patients. Clinical trial outcome prediction is a machine learning task that aims to forecast the outcome of clinical trials, such as the approval rate of a drug or treatment. It utilizes various clinical trial features, including the drug's molecular structure and patient disease.\n", "\n", "Question: Given a drug SMILES string and disease, predict if the phase 1 trial (A) would not be approved (B) would be approved\n", "\n", "Drug SMILES: CC1=C(SC(=N1)NC(=O)N2CCCC2C(=O)N)C3=CC(=NC=C3)C(C)(C)C(F)(F)F\n", "\n", "Disease: Malignant neoplasm of ovary: Ovarian cancer is a disease that affects women. In this form of cancer, certain cells in the ovary become abnormal and multiply uncontrollably to form a tumor. The ovaries are the female reproductive organs in which egg cells are produced. In about 90 percent of cases,  . . .\n", "\n", "Remember: (A) would not be approved (B) would be approved\n", "\n", "Reasoning:\n", "\n", "TxGemma\n", "\n", "Here's a step-by-step reasoning process to predict the outcome of the Phase 1 trial:\n", "\n", "1. Analyze the Drug's Chemical Structure:\n", "\n", "Observation: The SMILES string indicates a complex molecule with multiple aromatic rings, fluorine atoms, and amide groups.\n", "\n", "Reasoning: Such structures often point to potential biological activity, but also raise concerns about potential toxicity and side effects.\n", "\n", "2. Consider the Disease:\n", "\n", "Observation: Ovarian cancer is a serious and often aggressive disease with limited treatment options.\n", "\n", "Reasoning: The unmet medical need is high, meaning there's a greater chance for a new drug to be approved if it shows any promise of efficacy.\n", "\n", "3. Evaluate Potential Toxicity:\n", "\n", "Observation: We don't have specific information about the drug's toxicity profile from the given data.\n", "\n", "Reasoning: Phase  1  trials  primarily  focus  on  safety.    The  drug's  chemical  structure  suggests  potential  for toxicity, which would need to be carefully assessed in the trial.\n", "\n", "4. Assess the Likelihood of Demonstrating Safety:\n", "\n", "Observation: While the drug's structure raises concerns, there's no information suggesting inherent, severe toxicity that would preclude Phase 1 testing.\n", "\n", "Reasoning: Most drugs with novel structures undergo preclinical testing before reaching Phase 1. If the drug passed those tests, it likely has a reasonable chance of demonstrating acceptable safety in a small group of patients.\n", "\n", "5.  Weighing the Factors:\n", "\n", "Observation: We have a potentially effective drug for a serious disease, but with potential toxicity concerns.\n", "\n", "Reasoning: Phase 1 trials are designed to assess safety and dosage. Even if the drug has some side effects, it's likely to be approved to proceed to further testing if it shows any signs of efficacy and the benefits outweigh the risks in the context of a serious disease.\n", "\n", "Answer: (B) would be approved\n", "\n", "Figure S.10 | Enabling reasoning with TxGemma-Chat on challenging tasks. Example of prompting TxGemma-27B-<PERSON><PERSON> to explain its prediction for predicting clinical trial approval, a relatively unintuitive task. A prefix is added before the prompt to provide instructions for reasoning, and a reminder is added at the end so the model correctly specifies the option corresponding to its desired answer. Lastly, the 'Answer' text is changed to 'Reasoning:' to enable reasoning steps. The reasoning provided by the model is not comprehensive but can provide useful insights into the drug-disease interaction.\n", "\n", "Figure S.15 | Breakdown of tool-usage per question in chemical preference dataset . Marker size represents usage count and corresponds to the number of uses per each tool; blue indicates accuracy increase, light red indicates decrease associated with each tool per question. We observe questions involve up to 8 tool calls. High usage of SMILES description and toxicity prediction correlates with improved performance. This demonstrates Agentic-Tx's adaptive tool selection to meet task requirements and improved performance.\n", "\n", "References\n", "\n", "1. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. TrialBench: Multi-modal artificial intelligence-ready clinical trial datasets. arXiv preprint arXiv:2407.00631 (2024).\n", "\n", "2. <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, et al. Frequent activating mutations of PIK3CA in ovarian clear cell carcinoma. The American journal of pathology 174, 1597-1601 (2009).\n", "\n", "3. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, C. & <PERSON>ia, Z. Insights into the mechanism of the PIK3CA E545K activating mutation using MD simulations. Scientific reports 8, 15544 (2018).\n", "\n", "4. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> & <PERSON>, O. P110 α inhibitor alpelisib exhibits a synergistic effect with pyrotinib and reverses pyrotinib resistant in HER2+ breast cancer. Neoplasia 43, 100913 (2023).\n", "\n", "5. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Characterization of the novel and specific PI3K α inhibitor NVP-BYL719 and development of the patient stratification strategy for clinical trials. Molecular cancer therapeutics 13, 1117-1129 (2014).\n", "\n", "6. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. FDA approval summary: alpelisib plus fulvestrant for patients with HR-positive, HER2-negative, PIK3CA-mutated, advanced or metastatic breast cancer. Clinical Cancer Research 27, 1842-1849 (2021).\n", "\n", "7. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, D., Scambia, G., Canova, S., Di Palma, T., Ta<PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Alpelisib for PIK3CA-mutated advanced gynecological cancers: first clues of clinical activity. Gynecologic Oncology 183, 61-67 (2024).\n", "\n", "8. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, C. & G<PERSON>, J. PI3K α -specific inhibitor BYL-719 synergizes with cisplatin in vitro in PIK3CA-mutated ovarian cancer cells. Scientific Reports 15, 6265 (2025).\n", "\n", "9. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> & <PERSON>, L. Dual PI3K/mTOR inhibitor PKI-402 suppresses the growth of ovarian cancer cells by degradation of Mcl-1 through autophagy. Biomedicine & Pharmacotherapy 129, 110397 (2020).\n", "\n", "10. <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, K<PERSON> & <PERSON>-<PERSON>, M. First fully-automated AI/ML virtual screening cascade implemented at a drug discovery centre in Africa. Nature Communications 14, 5736 (2023).\n", "\n", "11. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> & <PERSON>, P. Predicting a Compounds Blood-BrainBarrier Permeability with Lantern Pharma's AI and ML Platform, RADR 2023.\n", "\n", "12. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, A. & <PERSON>, B. <PERSON> for TDC Benchmarks (2022).\n", "\n", "13. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON> & <PERSON>, <PERSON><PERSON>: Machine learning models for the prediction of inhibitors of cytochrome P450 enzymes. Bioorganic & medicinal chemistry 46, 116388 (2021).\n", "\n", "14. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> & <PERSON>, J. <PERSON> for pre-training graph neural networks. arXiv preprint arXiv:1905.12265 (2019).\n", "\n", "15. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>Purpose: a deep learning library for drug-target interaction prediction. Bioinformatics 36, 5545-5547 (2020).\n", "\n", "16. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> & <PERSON>, V. Computer-aided prediction of rodent carcinogenicity by PASS and CISOC-PSCT. QSAR & Combinatorial Science 28, 806-810 (2009).\n", "\n", "17. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON> & <PERSON>, X. TrimNet: learning molecular representation from triplet messages for biomedicine. Briefings in Bioinformatics 22, bbaa266 (2021).\n", "\n", "18. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> & <PERSON>, A. A Unified System for Molecular Property Predictions: Oloren ChemEngine and its Applications (2022).\n", "\n", "19. <PERSON>, <PERSON>, <PERSON>, <PERSON>, X. Learning graph-level representation for drug discovery. arXiv preprint arXiv:1709.03741 (2017).\n", "\n", "20. <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Y. A novel method for data fusion over entity-relation graphs and its application to protein-protein interaction prediction. Bioinformatics 37, 2275-2281 (2021).\n", "\n", "21. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> & <PERSON>, A. Improved predictions of antigen presentation and TCR recognition with MixMHCpred2. 2 and PRIME2. 0 reveal potent SARS-CoV-2 CD8+ T-cell epitopes. Cell Systems 14, 72-83 (2023).\n", "\n", "22. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, D. & <PERSON>, P. Peptide-binding specificity prediction using fine-tuned protein structure prediction networks. Proceedings of the National Academy of Sciences 120, e2216697120 (2023).\n", "\n", "23. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>., <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, P. Validating ADME QSAR models using marketed drugs. SLAS DISCOVERY: Advancing the Science of Drug Discovery 26, 1326-1336 (2021).\n", "\n", "24. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, M. Machine learning enabled identification of potential SARS-CoV-2 3CLpro inhibitors based on fixed molecular fingerprints and Graph-CNN neural representations. Journal of Biomedical Informatics 119, 103821 (2021).\n", "\n", "25. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, L. COVID-19 multi-targeted drug repurposing using few-shot learning. Frontiers in Bioinformatics 1, 693177 (2021).\n", "\n", "26. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Y.-C. & <PERSON>, I. Predicting antibody developability from sequence using machine learning. biorxiv, 2020-06 (2020).\n", "\n", "27. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, D<PERSON>, <PERSON>, J<PERSON>, <PERSON>, N., Andrade, C. H. & Tropsha, A. Predicting chemically-induced skin reactions. Part I: QSAR models of skin sensitization and their application to identify potentially hazardous compounds. Toxicology and applied pharmacology 284, 262-272 (2015).\n", "\n", "28. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, D<PERSON>, M. Structure to Property: Chemical Element Embeddings and a Deep Learning Approach for Accurate Prediction of Chemical Properties. arXiv preprint arXiv:2309.09355 (2023).\n", "\n", "29. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, D<PERSON> & <PERSON>, J. <PERSON>:: Mol2D-a robust atom environment descriptor for QSAR modeling and lead optimization. Journal of computer-aided molecular design 33, 477-486 (2019).\n", "\n", "30. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON> & <PERSON>, A. <PERSON> net: a robust predictor for hERG channel blockade based on deep learning meta-feature ensembles. Journal of Cheminformatics 13, 1-13 (2021).\n", "\n", "31. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, D<PERSON>, S. <PERSON>on of deep learning with multiple machine learning methods and metrics using diverse drug discovery data sets. Molecular pharmaceutics 14, 4462-4475 (2017).\n", "\n", "32. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> & <PERSON>, M.-Y. MIPDH: a novel computational model for predicting microRNA-mRNA interactions by DeepWalk on a heterogeneous network. ACS omega 5, 17022-17032 (2020).\n", "\n", "33. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, L. M. & Sun, <PERSON><PERSON>: Hierarchical interaction network for clinical-trial-outcome predictions. Patterns 3 (2022).\n", "\n", "34. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>: T-cell receptor specificity prediction with bimodal attention networks. Bioinformatics 37, i237-i244 (2021).\n", "\n", "35. <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, C. B. & <PERSON>, <PERSON><PERSON>-Knowledge: benchmarks of multimodal knowledge graph representation learning from different sources for drug discovery. arXiv preprint arXiv:2306.12802 (2023).\n", "\n", "36. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, L. & Bourne, P. E. A machine learning-based method to improve docking scoring functions and its application to drug repurposing. Journal of chemical information and modeling 51, 408-419 (2011).\n", "\n", "37. <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>DTA: Drug-target binding affinity prediction through complementary biological-related and compression-based featurization approach. PLOS Computational Biology 19, e1011036 (2023).\n", "\n", "38. <PERSON>, <PERSON>, X. DeepPLA: a novel deep learning-based model for protein-ligand binding affinity prediction (2021).\n", "\n", "39. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> & <PERSON>, J.-L. <PERSON>action classification and yield prediction using the differential reaction fingerprint DRFP. Digital discovery 1, 91-97 (2022).\n", "\n", "40. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>Y<PERSON> & <PERSON>, P.-W. In silico Evaluation of the Feasibility of Magnolia officinalis Electronshuttling Compounds as Parkinson's Disease Remedy. Letters in Drug Design & Discovery 21, 3039-3048 (2024).\n", "\n", "41. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON> & <PERSON>, R. Breaking the barriers of data scarcity in drug-target affinity prediction. Briefings in Bioinformatics 24, bbad386 (2023).\n", "\n", "42. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, S. L., <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Y. A<PERSON>, et al. Predicting tumor cell line response to drug pairs with deep learning. BMC bioinformatics 19, 71-79 (2018).\n", "\n", "43. <PERSON><PERSON>, <PERSON><PERSON>, P. C. Predicting drug activity against cancer cells by random forest models based on minimal genomic information and chemical properties. PloS one 14, e0219774 (2019).\n", "\n", "44. <PERSON><PERSON><PERSON>. https://github.com/euclia/public-models. 2023.\n", "\n", "45. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Large dataset enables prediction of repair after CRISPR-Cas9 editing in primary T cells. Nature biotechnology 37, 1034-1037 (2019).\n", "\n", "46. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, et al. Analyzing learned molecular representations for property prediction. Journal of chemical information and modeling 59, 3370-3388 (2019).\n", "\n", "47. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, K. C. & <PERSON>, <PERSON>. DeepSynergy: predicting anti-cancer drug synergy with Deep Learning. Bioinformatics 34, 1538-1546 (2018).\n", "\n", "48. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>. Predicting retrosynthetic reactions using self-corrected transformer neural networks. Journal of chemical information and modeling 60, 47-55 (2019).\n", "\n", "49. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, M. Accountable prediction of drug ADMET Properties with molecular descriptors. bioRxiv, 2022-06 (2022).\n", "\n", "50. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, S<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> & <PERSON>, <PERSON>. Measuring massive multitask language understanding. arXiv preprint arXiv:2009.03300 (2020).\n"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["doc"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DocLayout(pages=[PageLayout(page_no=1, width=612.0, height=792.0), PageLayout(page_no=2, width=612.0, height=792.0), PageLayout(page_no=3, width=612.0, height=792.0), PageLayout(page_no=4, width=612.0, height=792.0), PageLayout(page_no=5, width=612.0, height=792.0), PageLayout(page_no=6, width=612.0, height=792.0), PageLayout(page_no=7, width=612.0, height=792.0), PageLayout(page_no=8, width=612.0, height=792.0), PageLayout(page_no=9, width=612.0, height=792.0), PageLayout(page_no=10, width=612.0, height=792.0), PageLayout(page_no=11, width=612.0, height=792.0), PageLayout(page_no=12, width=612.0, height=792.0), PageLayout(page_no=13, width=612.0, height=792.0), PageLayout(page_no=14, width=612.0, height=792.0), PageLayout(page_no=15, width=612.0, height=792.0), PageLayout(page_no=16, width=612.0, height=792.0), PageLayout(page_no=17, width=612.0, height=792.0), PageLayout(page_no=18, width=612.0, height=792.0), PageLayout(page_no=19, width=612.0, height=792.0), PageLayout(page_no=20, width=612.0, height=792.0), PageLayout(page_no=21, width=612.0, height=792.0), PageLayout(page_no=22, width=612.0, height=792.0), PageLayout(page_no=23, width=612.0, height=792.0), PageLayout(page_no=24, width=612.0, height=792.0), PageLayout(page_no=25, width=612.0, height=792.0), PageLayout(page_no=26, width=612.0, height=792.0), PageLayout(page_no=27, width=612.0, height=792.0), PageLayout(page_no=28, width=612.0, height=792.0), PageLayout(page_no=29, width=612.0, height=792.0), PageLayout(page_no=30, width=612.0, height=792.0), PageLayout(page_no=31, width=612.0, height=792.0), PageLayout(page_no=32, width=612.0, height=792.0), PageLayout(page_no=33, width=612.0, height=792.0), PageLayout(page_no=34, width=612.0, height=792.0), PageLayout(page_no=35, width=612.0, height=792.0), PageLayout(page_no=36, width=612.0, height=792.0), PageLayout(page_no=37, width=612.0, height=792.0), PageLayout(page_no=38, width=612.0, height=792.0), PageLayout(page_no=39, width=612.0, height=792.0), PageLayout(page_no=40, width=612.0, height=792.0), PageLayout(page_no=41, width=612.0, height=792.0), PageLayout(page_no=42, width=612.0, height=792.0), PageLayout(page_no=43, width=612.0, height=792.0), PageLayout(page_no=44, width=612.0, height=792.0), PageLayout(page_no=45, width=612.0, height=792.0), PageLayout(page_no=46, width=612.0, height=792.0), PageLayout(page_no=47, width=612.0, height=792.0), PageLayout(page_no=48, width=612.0, height=792.0), PageLayout(page_no=49, width=612.0, height=792.0), PageLayout(page_no=50, width=612.0, height=792.0), PageLayout(page_no=51, width=612.0, height=792.0), PageLayout(page_no=52, width=612.0, height=792.0), PageLayout(page_no=53, width=612.0, height=792.0), PageLayout(page_no=54, width=612.0, height=792.0), PageLayout(page_no=55, width=612.0, height=792.0), PageLayout(page_no=56, width=612.0, height=792.0), PageLayout(page_no=57, width=612.0, height=792.0), PageLayout(page_no=58, width=612.0, height=792.0)])\n"]}], "source": ["# Document layout including pages and page sizes\n", "print(doc._.layout)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[TABLE, TABLE, TABLE, TABLE, TABLE, TABLE, TABLE, TABLE, TABLE, TABLE, TABLE, TABLE, TABLE, TABLE, TABLE, TABLE, TABLE]\n"]}], "source": ["# Tables in the document and their extracted data\n", "print(doc._.tables)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4046 4047 SpanLayout(x=74.988037109375, y=142.78932189941406, width=465.0032958984375, height=307.70469665527344, page_no=9)\n", "           Task Type                                               Task  \\\n", "0   Pharmacokinetics  Caco2 Wang Lipophilicity AstraZeneca Solubilit...   \n", "1   Pharmacokinetics                                      CarbonMangels   \n", "2   Pharmacokinetics                                      CarbonMangels   \n", "3   Pharmacokinetics                                      CarbonMangels   \n", "4   Pharmacokinetics                                                      \n", "5   Pharmacokinetics                                                      \n", "6   Pharmacokinetics                                                      \n", "7   Pharmacokinetics                            Clearance Hepatocyte AZ   \n", "8   Pharmacokinetics                                           LD50 Zhu   \n", "9           Toxicity                                               hERG   \n", "10  Pharmacokinetics                                               AMES   \n", "11  Pharmacokinetics                                               DILI   \n", "\n", "                                               Metric  \\\n", "0   MAE ( ↓ ) MAE ( ↓ ) MAE ( ↓ ) MAE ( ↓ ) AUROC ...   \n", "1                                                       \n", "2                                                       \n", "3                                                       \n", "4                                                   )   \n", "5                                                   )   \n", "6                                                   )   \n", "7                                                   )   \n", "8                                           MAE ( ↓ )   \n", "9                                         AUROC ( ↑ )   \n", "10                                        AUROC ( ↑ )   \n", "11                                        AUROC ( ↑ )   \n", "\n", "                                            <PERSON><PERSON><PERSON> [24]  \\\n", "0   0.329 0.406 0.776 7.229 0.984 0.930 0.640 0.90...   \n", "1                                                       \n", "2                                                       \n", "3                                                       \n", "4                                                       \n", "5                                                       \n", "6                                                       \n", "7                                               0.456   \n", "8                                               0.602   \n", "9                                               0.835   \n", "10                                              0.834   \n", "11                                              0.852   \n", "\n", "                                  TxGemma-27B-Predict  \n", "0   0.401 (0.358-0.449) 0.538 (0.507-0.570) 0.907 ...  \n", "1                                                      \n", "2                                                      \n", "3                                                      \n", "4                                                      \n", "5                                                      \n", "6                                                      \n", "7                                 0.260 (0.129-0.384)  \n", "8                                 0.627 (0.597-0.660)  \n", "9                                 0.885 (0.813-0.946)  \n", "10                                0.816 (0.795-0.838)  \n", "11                                0.886 (0.810-0.947)  \n", "4145 4146 SpanLayout(x=70.54798889160156, y=535.5758666992188, width=473.2656707763672, height=96.13726806640625, page_no=9)\n", "                   Task Type     Task          Metric LlaSMol Mistral [23]  \\\n", "0           Pharmacokinetics   BBBP †  Accuracy ( ↑ )                0.746   \n", "1                              ESOL †      RMSE ( ↓ )                1.150   \n", "2                              Lipo †      RMSE ( ↓ )                1.010   \n", "3                   Toxicity  Clintox  Accuracy ( ↑ )                0.931   \n", "4  High-throughput screening    HIV ∗  Accuracy ( ↑ )                0.967   \n", "\n", "   TxGemma-27B-Predict   TxGemma-9B-Predict  \n", "0  0.869 (0.835-0.901)  0.847 (0.813-0.881)  \n", "1  1.250 (1.185-1.321)  1.360 (1.246-1.480)  \n", "2  0.710 (0.668-0.752)  0.742 (0.700-0.787)  \n", "3  0.926 (0.896-0.956)  0.925 (0.892-0.953)  \n", "4  0.968 (0.964-0.972)  0.965 (0.961-0.969)  \n", "4876 4877 SpanLayout(x=90.54725646972656, y=108.024169921875, width=432.98277282714844, height=206.22952270507812, page_no=11)\n", "                                Model. ChemBench.Mini ChemBench.Preference  \\\n", "0          Agentic-Tx (Gemini 2.5-Pro)           84.5                 66.2   \n", "1          Agentic-Tx (Gemini 2.0-Pro)           83.4                 65.5   \n", "2          Agentic-Tx (Gemini 1.5-Pro)           80.6                 65.0   \n", "3                  Claude-3.5 (<PERSON><PERSON>)         73.0 ∗              60.0 ∗†   \n", "4                               GPT-4o         72.0 ∗               59.0 ∗   \n", "5                       Gemini 2.5-pro           82.8                 65.5   \n", "6                       Gemini 2.0-pro           79.6                 58.4   \n", "7                       Gemini 1.5-pro           74.9                 55.6   \n", "8                        PaperQA2 [28]         67.0 ∗               56.0 ∗   \n", "9                                   o1         80.0 ∗               56.0 ∗   \n", "10                    o3-mini (medium)           82.4                 61.3   \n", "11                      o3-mini (high)           82.5                 62.0   \n", "12  Human Expert (Average Performance)           27.0                    -   \n", "\n", "   GPQA (Diamond).Chemistry Humanity's Last Exam.Chemistry & Biology  \n", "0                      81.7                                     20.1  \n", "1                      62.4                                     14.5  \n", "2                      51.8                                     11.9  \n", "3                      40.4                                        -  \n", "4                   43.8 ∗∗                                      3.8  \n", "5                      79.5                                     17.9  \n", "6                      53.3                                     11.1  \n", "7                      48.2                                     10.6  \n", "8                         -                                        -  \n", "9                   64.7 ∗∗                                     12.3  \n", "10                     62.5                                     13.0  \n", "11                     64.5                                     13.2  \n", "12                        -                                        -  \n", "16807 16808 SpanLayout(x=72.69422912597656, y=167.03480529785156, width=464.6383514404297, height=221.**************, page_no=26)\n", "         Task Name                               Reason for Exclusion\n", "0             QM7b  Prediction of quantum properties is not closel...\n", "1              QM8                                         Prediction\n", "2              QM9                                         Prediction\n", "3   IEDB Je<PERSON>sen             Amount LLM than binary classification.\n", "4    PDB Jespersen  Amount of data is small, and token prediction ...\n", "5     DrugBank DDI  Large number of possible labels is difficult t...\n", "6         TWOSIDES  Large number of possible labels is difficult t...\n", "7   USPTO Catalyst  Large number of possible labels is difficult t...\n", "8            MOSES                                   No clear metric.\n", "9             ZINC                                   No clear metric.\n", "10          ChEMBL                                   No clear metric.\n", "11       USPTO 50K                                   Subset of USPTO.\n", "12  USPTO Reaction                                Same data as USPTO.\n", "16860 16861 SpanLayout(x=48.**************, y=192.*************, width=515.*************, height=457.*************, page_no=27)\n", "                         Task Name                    Task Type  Split Type  \\\n", "0                             AMES                     Toxicity    Scaffold   \n", "1                      BBB Martins             Pharmacokinetics    Scaffold   \n", "2               Bioavailability Ma             Pharmacokinetics    Scaffold   \n", "3                     CYP1A2 Veith             Pharmacokinetics    Scaffold   \n", "4                    CYP2C19 Veith             Pharmacokinetics    Scaffold   \n", "5   CYP2C9 Substrate CarbonMangels             Pharmacokinetics    Scaffold   \n", "6                     CYP2C9 Veith             Pharmacokinetics    Scaffold   \n", "7   CYP2D6 Substrate CarbonMangels             Pharmacokinetics    Scaffold   \n", "8                     CYP2D6 Veith             Pharmacokinetics    Scaffold   \n", "9   CYP3A4 Substrate CarbonMangels             Pharmacokinetics    Scaffold   \n", "10                    CYP3A4 Veith             Pharmacokinetics    Scaffold   \n", "11             Carcinogens Lagunin                     Toxicity    Scaffold   \n", "12                         ClinTox                     Toxicity    Scaffold   \n", "13                            DILI                     Toxicity    Scaffold   \n", "14                         HIA Hou             Pharmacokinetics    Scaffold   \n", "15                           HIV ∗    High-throughput screening    Scaffold   \n", "16                            HuRI  Protein-protein interaction  Cold-start   \n", "17          MHC1 IEDB IMGT Nielsen          Peptide-MHC binding      Random   \n", "18                MHC2 IEDB Jensen          Peptide-MHC binding      Random   \n", "19                     PAMPA NCATS             Pharmacokinetics    Scaffold   \n", "20                 Pgp Broccatelli             Pharmacokinetics    Scaffold   \n", "21         SARSCOV2 3CLPro Diamond    High-throughput screening    Scaffold   \n", "22           SARSCoV2 Vitro Touret    High-throughput screening    Scaffold   \n", "23                     SAbDab Chen               Developability      Random   \n", "24                   Skin Reaction                     Toxicity    Scaffold   \n", "25                           Tox21                     Toxicity    Scaffold   \n", "26                         ToxCast                     Toxicity    Scaffold   \n", "27                      <PERSON><PERSON><PERSON>    High-throughput screening      Random   \n", "28                            hERG                     Toxicity    Scaffold   \n", "29                      hERG Karim                     Toxicity    Scaffold   \n", "30                    herg central                     Toxicity    Scaffold   \n", "31                      miRTarBase     miRNA-target interaction      Random   \n", "32                          phase1       Clinical trial outcome  Cold-start   \n", "33                          phase2       Clinical trial outcome  Cold-start   \n", "34                          phase3       Clinical trial outcome  Cold-start   \n", "35                           weber          TCR-epitope binding  Cold-start   \n", "\n", "   Training Size Validation Size Test Size  \n", "0          5,093             728     1,457  \n", "1          1,421             203       406  \n", "2          1,344             192       384  \n", "3          8,805           1,257     2,517  \n", "4          8,865           1,266     2,534  \n", "5            467              67       135  \n", "6          8,463           1,210     2,419  \n", "7            465              67       135  \n", "8          9,191           1,313     2,626  \n", "9            468              67       135  \n", "10         8,628           1,233     2,467  \n", "11           196              28        56  \n", "12         1,034             147       297  \n", "13           325              54        96  \n", "14           403              58       117  \n", "15        28,788           4,112     8,227  \n", "16        45,855             987     3,694  \n", "17       130,190          18,598    37,197  \n", "18        93,997          13,428    26,856  \n", "19         1,423             203       408  \n", "20           851             122       245  \n", "21           616              88       176  \n", "22         1,038             148       298  \n", "23         1,686             241       482  \n", "24           282              40        82  \n", "25        54,556           7,790    15,600  \n", "26     1,073,279         153,099   307,282  \n", "27     1,406,988         200,998   40,1997  \n", "28           457              66       132  \n", "29         9,411           1,344     2,690  \n", "30       214,825          30,689    61,379  \n", "31       559,591          79,948   159,889  \n", "32         1,546             258       598  \n", "33         5,792             716     1,282  \n", "34         41,25             532     1,084  \n", "35        33,013           4,748     9,421  \n", "16949 16950 SpanLayout(x=68.51274871826172, y=215.5430908203125, width=475.2001419067383, height=421.96429443359375, page_no=28)\n", "                    Task Name                 Task Type   Split Type  \\\n", "0            BindingDB Patent   Drug-target interaction     Temporal   \n", "1              BindingDB ic50   Drug-target interaction   Cold-start   \n", "2                BindingDB kd   Drug-target interaction   Cold-start   \n", "3                BindingDB ki   Drug-target interaction   Cold-start   \n", "4            Buchwald <PERSON>wig           Reaction yields       Random   \n", "5                  Caco2 Wang          Pharmacokinetics     Scaffold   \n", "6     Clearance Hepatocyte AZ          Pharmacokinetics     Scaffold   \n", "7      Clearance Microsome AZ          Pharmacokinetics     Scaffold   \n", "8                       DAVIS   Drug-target interaction   Cold-start   \n", "9                    DisGeNET  Gene-disease association       Random   \n", "10             DrugComb Bliss              Drug synergy  Combination   \n", "11               DrugComb CSS              Drug synergy  Combination   \n", "12               DrugComb HSA              Drug synergy  Combination   \n", "13             DrugComb Loewe              Drug synergy  Combination   \n", "14               DrugComb ZIP              Drug synergy  Combination   \n", "15                      GDSC1             Drug response       Random   \n", "16                      GDSC2             Drug response       Random   \n", "17            Half Life Obach          Pharmacokinetics     Scaffold   \n", "18                       KIBA   Drug-target interaction   Cold-start   \n", "19                   LD50 Zhu                  Toxicity     Scaffold   \n", "20                     Leenay             CRISPR repair       Random   \n", "21  Lipophilicity AstraZeneca          Pharmacokinetics     Scaffold   \n", "22       OncoPolyPharmacology              Drug synergy  Combination   \n", "23                    PPBR AZ          Pharmacokinetics     Scaffold   \n", "24             Protein SAbDab         Antibody affinity       Random   \n", "25         Solubility AqSolDB          Pharmacokinetics     Scaffold   \n", "26                        TAP            Developability       Random   \n", "27                      USPTO            Retrosynthesis       Random   \n", "28               USPTO Yields           Reaction yields       Random   \n", "29              VDss Lombardo          Pharmacokinetics     Scaffold   \n", "\n", "   Training Size Validation Size Test Size  \n", "0        146,800          36,630    49,028  \n", "1        375,127           7,531    31,495  \n", "2         19,034             376     2,321  \n", "3         57,656           1,189     4,709  \n", "4          2,768             396       791  \n", "5            637              91       182  \n", "6            848             122       243  \n", "7            770             111       221  \n", "8         12,455             266     1,064  \n", "9         39,425           5,621    11,200  \n", "10       207,772          29,618    59,708  \n", "11       207,772          29,618    59,708  \n", "12       207,772          29,618    59,708  \n", "13       207,772          29,618    59,708  \n", "14       207,772          29,618    59,708  \n", "15       124,117          17,731    35,462  \n", "16        64,892           9,270    18,541  \n", "17           465              67       135  \n", "18        59,326           1,042     4,524  \n", "19         5,168             739     1,478  \n", "20         5,325             760     1,520  \n", "21         2,940             420       840  \n", "22        16,014           2,331     4,707  \n", "23         1,952             279       559  \n", "24           345              49        99  \n", "25         6,988             998     1,996  \n", "26           845             120       240  \n", "27       775,767         110,824   221,648  \n", "28       597,546          85,364   170,728  \n", "29           791             113       226  \n", "17020 17021 SpanLayout(x=57.064151763916016, y=146.0068359375, width=500.71874618530273, height=526.685546875, page_no=29)\n", "                         Task Name                     Input  \\\n", "0                             AMES            Small molecule   \n", "1                      BBB Martins            Small molecule   \n", "2               Bioavailability Ma            Small molecule   \n", "3                     CYP1A2 Veith            Small molecule   \n", "4                    CYP2C19 Veith            Small molecule   \n", "5   CYP2C9 Substrate CarbonMangels            Small molecule   \n", "6                     CYP2C9 Veith            Small molecule   \n", "7   CYP2D6 Substrate CarbonMangels            Small molecule   \n", "8                     CYP2D6 Veith            Small molecule   \n", "9   CYP3A4 Substrate CarbonMangels            Small molecule   \n", "10                    CYP3A4 Veith            Small molecule   \n", "11             Carcinogens Lagunin            Small molecule   \n", "12                         ClinTox            Small molecule   \n", "13                            DILI            Small molecule   \n", "14                         HIA Hou            Small molecule   \n", "15                           HIV ∗            Small molecule   \n", "16                            Hu<PERSON>                   Protein   \n", "17          MHC1 IEDB IMGT Nielsen                   Protein   \n", "18                MHC2 IEDB Jensen                   Protein   \n", "19                     PAMPA NCATS            Small molecule   \n", "20                 Pgp Broccatelli            Small molecule   \n", "21         SARSCOV2 3CLPro Diamond            Small molecule   \n", "22           SARSCoV2 Vitro Touret            Small molecule   \n", "23                     <PERSON><PERSON><PERSON><PERSON>   \n", "24                   Skin Reaction            Small molecule   \n", "25                           Tox21            Small molecule   \n", "26                         ToxCast            Small molecule   \n", "27                      <PERSON><PERSON><PERSON>            Small molecule   \n", "28                            hERG            Small molecule   \n", "29                      hERG Karim            Small molecule   \n", "30                    herg central            Small molecule   \n", "31                      miRTarBase    Nucleic acid & protein   \n", "32                          phase1  Small molecule & disease   \n", "33                          phase2  Small molecule & disease   \n", "34                          phase3  Small molecule & disease   \n", "35                           weber                   <PERSON><PERSON>   \n", "\n", "                                          Description  \n", "0   Given a drug SMILES, predict whether it is mut...  \n", "1   Given a drug SMILES, predict whether it can cr...  \n", "2   Given a drug SMILES, predict whether it is ora...  \n", "3   Given a drug SMILES, predict whether it inhibi...  \n", "4   Given a drug SMILES, predict whether it inhibi...  \n", "5   Given a drug SMILES, predict whether it is a s...  \n", "6   Given a drug SMILES, predict whether it inhibi...  \n", "7   Given a drug SMILES, predict whether it is a s...  \n", "8   Given a drug SMILES, predict whether it inhibi...  \n", "9   Given a drug SMILES, predict whether it is a s...  \n", "10  Given a drug SMILES, predict whether it inhibi...  \n", "11  Given a drug SMILES, predict whether it is a c...  \n", "12  Given a drug SMILES, predict whether it is toxic.  \n", "13  Given a drug SMILES, predict whether it can ca...  \n", "14  Given a drug SMILES, predict whether it is abs...  \n", "15  Given a drug SMILES, predict whether it has an...  \n", "16  Given the amino acid sequences of two proteins...  \n", "17  Given the amino acid of the peptide and pseudo...  \n", "18  Given the amino acid of the peptide and pseudo...  \n", "19  Given a drug SMILES, predict whether it is per...  \n", "20  Given a drug SMILES, predict whether it inhibi...  \n", "21  Given a drug SMILES, predict whether it binds ...  \n", "22  Given a drug SMILES, predict whether it inhibi...  \n", "23  Given an antibody heavy chain and light chain ...  \n", "24  Given a drug SMILES, predict whether it can ca...  \n", "25  Given a drug SMILES, predict whether it is tox...  \n", "26  Given a drug SMILES, predict whether it is tox...  \n", "27  Given a drug SMILES, predict whether it is act...  \n", "28  Given a drug SMILES, predict whether it blocks...  \n", "29  Given a drug SMILES, predict whether it inhibi...  \n", "30  Given a drug SMILES, predict whether it inhibi...  \n", "31  Given the miRNA mature and target amino acid, ...  \n", "32  Given a drug SMILES and disease, predict wheth...  \n", "33  Given a drug SMILES and disease, predict wheth...  \n", "34  Given a drug SMILES and disease, predict wheth...  \n", "35  Given the amino acid of the epitope and a T-ce...  \n", "17064 17065 SpanLayout(x=49.42787551879883, y=188.8984832763672, width=512.3169364929199, height=473.07386779785156, page_no=30)\n", "                                            Task Name  \\\n", "0   BindingDB Patent BindingDB ic50 BindingDB kd B...   \n", "1                                        Clearance AZ   \n", "2                                           Clearance   \n", "3                                                       \n", "4                                                       \n", "5                                                       \n", "6                                                       \n", "7                                                       \n", "8                                                       \n", "9                                                       \n", "10                                                      \n", "11                                                      \n", "12                                                      \n", "13                                                      \n", "14                                                      \n", "15                                                      \n", "16                                                      \n", "17                                                      \n", "18                                                      \n", "19                                                      \n", "20                                                      \n", "21                                               VDss   \n", "22                                           Lombard<PERSON>   \n", "\n", "                                                       \\\n", "0   Input Protein & small molecule Protein Protein...   \n", "1                                                       \n", "2                                                       \n", "3                                                       \n", "4                                                       \n", "5                                                       \n", "6                                                       \n", "7                                                       \n", "8                                                       \n", "9                                                       \n", "10                                                      \n", "11                                                      \n", "12                                                      \n", "13                                                      \n", "14                                                      \n", "15                                                      \n", "16                                                      \n", "17                                                      \n", "18                                                      \n", "19                                                      \n", "20                                     Small molecule   \n", "21                                     Small molecule   \n", "22                                                      \n", "\n", "                                                       \n", "0   Description Given the target amino acid and dr...  \n", "1                                                      \n", "2                                                      \n", "3   Given the disease description and the amino ac...  \n", "4   Given two drug SMILESs and a cell line descrip...  \n", "5   Given two drug SMILESs and a cell line descrip...  \n", "6   Given two drug SMILESs and a cell line descrip...  \n", "7   Given two drug SMILESs and a cell line descrip...  \n", "8   Given two drug SMILESs and a cell line descrip...  \n", "9   Given a drug SMILES and a cell line descriptio...  \n", "10  Given a drug SMILES and a cell line descriptio...  \n", "11  Given a drug SMILES, predict the half life dur...  \n", "12    Given a drug SMILES, predict its LD50 toxicity.  \n", "13  Given a GuideSeq sequence, predict various pro...  \n", "14     Given a drug SMILES, predict the lipohilicity.  \n", "15  Given two drug SMILESs and a cell line descrip...  \n", "16  Given the amino acid of the antibody and antig...  \n", "17  Given a drug SMILES, predict the activity of s...  \n", "18  Given an antibody heavy chain and light chain ...  \n", "19  Given the product SMILES, generate the reactan...  \n", "20  Given a catalyst SMILES, reactant SMILES, and ...  \n", "21  Given a drug SMILES, predict the volume of dis...  \n", "22                                                     \n", "17137 17138 SpanLayout(x=73.67182159423828, y=372.21673583984375, width=463.5001754760742, height=93.70718383789062, page_no=31)\n", "                                 Representation Type  \\\n", "0  Small Molecules Amino Acid: Proteins and pepti...   \n", "\n", "                                      Representation  \\\n", "0  SMILES string Amino acid sequences Pseudo-sequ...   \n", "\n", "                                             Example  \n", "0  CN1C(=O)CN=C(C2=CCCCC2)c2cc(Cl)ccc21 QLADETLLK...  \n", "19308 19309 SpanLayout(x=71.0680923461914, y=108.73417663574219, width=476.9194564819336, height=599.5170440673828, page_no=37)\n", "                                           0  \\\n", "0   Example of Chembench Chemical Preference   \n", "1                                   Question   \n", "2                         Thought 1 Action 1   \n", "3                              Observation 1   \n", "4                              Observation 1   \n", "5                                  Thought 2   \n", "6                                   Action 2   \n", "7                              Observation 2   \n", "8                              Observation 2   \n", "9                                  Thought 3   \n", "10                                  Action 3   \n", "11                             Observation 3   \n", "12                             Observation 3   \n", "13                                     Final   \n", "14                                  Response   \n", "\n", "                                                    1  \n", "0            Example of Chembench Chemical Preference  \n", "1   Consider the following two drug candidates: A....  \n", "2   First, I need to obtain more information about...  \n", "3   Input SMILES: N#Cc1c(NC(=O)c2cc([N+](=O)[O-])c...  \n", "4   (Summarized) The provided information describe...  \n", "5   Now, I will use the SMILES to Description tool...  \n", "6                               SMILES to Description  \n", "7   Input SMILES: O=C(C=Cc1ccccn1)c1ccccc1 PubChem...  \n", "8   1-Phenyl-3-(2-pyridinyl)-2-propen-1-one (Summa...  \n", "9   Based on the descriptions, candidate B (XLogP ...  \n", "10                                        ClinicalTox  \n", "11  Context: Humans are exposed to a variety of ch...  \n", "12  (Summary) Based on the provided information, d...  \n", "13  Candidate B is more preferable for further dev...  \n", "14                                                     \n", "19325 19326 SpanLayout(x=75.6094970703125, y=162.72621154785156, width=461.32122802734375, height=489.2640838623047, page_no=38)\n", "                Tool Name                                        Description\n", "0                 ToxCast  Uses TxGemma to predict the toxicity of a give...\n", "1             ClinicalTox  Uses TxGemma to predict the clinical toxicity ...\n", "2                    Chat  Allows conversational interaction with TxGemma...\n", "3            Mutagenicity  Uses TxGemma to predict whether a given drug (...\n", "4                   IC 50  Uses TxGemma to predict the normalized IC 50 b...\n", "5           Phase 1 Trial  Uses TxGemma to predict the approval outcome o...\n", "6        Wikipedia Search  Searches Wikipedia for a given text query. Ret...\n", "7           PubMed Search  Queries PubMed for scientific articles based o...\n", "8              Web Search  Performs a general web search. Returns titles,...\n", "9              HTML Fetch  Fetches the raw HTML content of a given URL. U...\n", "10  SMILES to Description  Retrieves molecular information from PubChem f...\n", "11         SMILES Therapy  Retrieves therapeutic information (ChEMBL ID, ...\n", "12          Molecule Tool  Provides molecule-related functions: searching...\n", "13       Molecule Convert  Converts a molecules representation from one t...\n", "14          Gene Sequence  Retrieves amino acid sequences for a given gen...\n", "15       Gene Description  Retrieves descriptive information about a gene...\n", "16                 BlastP  Runs a BLASTP search against NCBI databases fo...\n", "17    Protein Description  Provides descriptive information (organism, de...\n", "20490 20491 SpanLayout(x=40.22138214111328, y=191.25624084472656, width=531.600944519043, height=445.60423278808594, page_no=43)\n", "                         Task Name    Metric        Specialist SOTA  \\\n", "0                             AMES     AUROC             0.871 [10]   \n", "1                      BBB Martins     AUROC             0.915 [11]   \n", "2               Bioavailability Ma     AUROC             0.748 [12]   \n", "3                     CYP1A2 Veith     AUPRC             0.900 [13]   \n", "4                    CYP2C19 Veith     AUROC             0.890 [13]   \n", "5   CYP2C9 Substrate CarbonMangels     AUPRC             0.441 [10]   \n", "6                     CYP2C9 Veith     AUPRC             0.839 [14]   \n", "7   CYP2D6 Substrate CarbonMangels     AUPRC             0.736 [14]   \n", "8                     CYP2D6 Veith     AUPRC             0.739 [14]   \n", "9   CYP3A4 Substrate CarbonMangels     AUROC             0.662 [15]   \n", "10                    CYP3A4 Veith     AUPRC             0.904 [14]   \n", "11             Carcinogens Lagunin  Accuracy             0.770 [16]   \n", "12                         ClinTox     AUROC             0.948 [17]   \n", "13                            DILI     AUROC             0.925 [10]   \n", "14                         HIA Hou     AUROC             0.988 [18]   \n", "15                             HIV     AUROC             0.851 [19]   \n", "16                            HuRI     AUPRC             0.724 [20]   \n", "17          MHC1 IEDB IMGT Nielsen     AUROC             0.986 [21]   \n", "18                MHC2 IEDB Jensen     AUROC             0.940 [22]   \n", "19                     PAMPA NCATS     AUROC             0.900 [23]   \n", "20                 Pgp B<PERSON>ccatelli     AUROC             0.935 [10]   \n", "21         SARSCOV2 3CLPro Diamond     AUROC             0.800 [24]   \n", "22           SARSCoV2 Vitro Touret     AUROC             0.640 [25]   \n", "23                     SAbDab Chen     AUPRC             0.510 [26]   \n", "24                   Skin Reaction     AUROC             0.840 [27]   \n", "25                           Tox21     AUROC             0.961 [28]   \n", "26                         ToxCast     AUROC  0.777 [17] 0.840 [29]   \n", "27                      <PERSON><PERSON><PERSON>     AUROC                          \n", "28                            hERG     AUROC             0.874 [12]   \n", "29                      hERG Karim  Accuracy             0.770 [30]   \n", "30                    herg central     AUROC             0.860 [31]   \n", "31                      miRTarBase  Accuracy             0.804 [32]   \n", "32                          phase1     AUROC             0.576 [33]   \n", "33                          phase2     AUROC             0.645 [33]   \n", "34                          phase3     AUROC             0.723 [33]   \n", "35                           weber     AUROC             0.870 [34]   \n", "36                                                                    \n", "\n", "    Gemma- 2-2B  <PERSON>- 2-9B <PERSON>- 2-27B TxG<PERSON>ma- 2B-Predict  \\\n", "0         0.487        0.605        0.508               0.796   \n", "1         0.250        0.645        0.546               0.864   \n", "2         0.479        0.584        0.579               0.715   \n", "3         0.388        0.533        0.562               0.910   \n", "4         0.456        0.595        0.619               0.905   \n", "5         0.293        0.336        0.367               0.457   \n", "6         0.283        0.374        0.417               0.801   \n", "7         0.233        0.329        0.386               0.605   \n", "8         0.145        0.166        0.185               0.637   \n", "9         0.514        0.585        0.596               0.669   \n", "10        0.427        0.531        0.535               0.844   \n", "11        0.250        0.286        0.339               0.821   \n", "12        0.437        0.482        0.424               0.810   \n", "13        0.320        0.651        0.627               0.875   \n", "14        0.257        0.932        0.783               0.937   \n", "15        0.491        0.495        0.537               0.737   \n", "16        0.496        0.484        0.526               0.751   \n", "17        0.498        0.504        0.517               0.910   \n", "18        0.498        0.526        0.544               0.812   \n", "19        0.465        0.583        0.544               0.642   \n", "20        0.416        0.670        0.497               0.900   \n", "21        0.301        0.388        0.477               0.733   \n", "22        0.568        0.611        0.479               0.650   \n", "23        0.532        0.696        0.701               0.676   \n", "24        0.429        0.546        0.493               0.671   \n", "25        0.358        0.436        0.497               0.881   \n", "26  0.485 0.457  0.512 0.491  0.558 0.491         0.784 0.791   \n", "27                                                              \n", "28        0.538        0.639        0.500               0.876   \n", "29        0.529        0.532        0.522               0.778   \n", "30        0.481        0.511        0.517               0.880   \n", "31        0.498        0.501        0.498               0.805   \n", "32        0.562        0.562        0.553               0.642   \n", "33        0.543        0.571        0.531               0.665   \n", "34        0.559        0.567        0.559               0.731   \n", "35        0.466        0.586                                    \n", "36                                  0.469               0.730   \n", "\n", "   TxGemma- 9B-Predict TxGemma- 27B-Predict  \n", "0                0.798                0.816  \n", "1                0.874                0.907  \n", "2                0.655                0.696  \n", "3                0.916                0.922  \n", "4                0.906                0.899  \n", "5                0.468                0.427  \n", "6                0.799                0.798  \n", "7                0.603                0.706  \n", "8                0.664                0.681  \n", "9                0.622                0.690  \n", "10               0.839                0.854  \n", "11               0.839                0.857  \n", "12               0.831                0.888  \n", "13               0.848                0.887  \n", "14               0.967                0.988  \n", "15               0.734                0.764  \n", "16               0.779                0.799  \n", "17               0.927                0.929  \n", "18               0.850                0.851  \n", "19               0.671                0.705  \n", "20               0.911                0.936  \n", "21               0.708                0.769  \n", "22               0.668                0.598  \n", "23               0.807                0.767  \n", "24               0.648                0.708  \n", "25               0.896                0.893  \n", "26               0.767                0.800  \n", "27               0.772                0.831  \n", "28               0.881                0.884  \n", "29               0.794                0.774  \n", "30               0.861                0.896  \n", "31               0.829                0.801  \n", "32               0.635                0.622  \n", "33               0.668                0.676  \n", "34               0.729                0.739  \n", "35                                           \n", "36               0.727                0.749  \n", "20525 20526 SpanLayout(x=58.350643157958984, y=231.337646484375, width=496.015079498291, height=376.88262939453125, page_no=44)\n", "                    Task Name    Metric Specialist SOTA Gemma- 2-2B  \\\n", "0            BindingDB Patent       PCC      0.588 [35]      -0.066   \n", "1              BindingDB ic50  Spearman      0.637 [36]       0.001   \n", "2                BindingDB kd       PCC      0.712 [37]       0.197   \n", "3                BindingDB ki       PCC      0.840 [38]      -0.018   \n", "4            <PERSON><PERSON><PERSON>       PCC      0.786 [39]       0.528   \n", "5                  Caco2 Wang       <PERSON>      0.285 [18]       1.057   \n", "6     Clearance Hepatocyte AZ  Spearman      0.440 [40]       0.141   \n", "7      Clearance Microsome AZ  Spearman      0.625 [18]       0.239   \n", "8                       DAVIS       MSE      0.219 [41]       2.705   \n", "9                    DisGeNET       MAE             N/A       0.294   \n", "10             DrugComb Bliss       MAE      4.560 [42]       8.213   \n", "11               DrugComb CSS       MAE     16.858 [42]      36.847   \n", "12               DrugComb HSA       MAE      4.453 [42]       7.458   \n", "13             DrugComb Loewe       MAE      9.184 [42]      13.873   \n", "14               DrugComb ZIP       MAE      4.027 [42]       8.588   \n", "15                      GDSC1       PCC      0.860 [43]      -0.041   \n", "16                      GDSC2       PCC      0.860 [43]      -0.043   \n", "17            Half <PERSON> O<PERSON>  Spear<PERSON>      0.547 [44]       0.288   \n", "18                       KIBA       MSE      0.154 [41]       2.887   \n", "19                   LD50 Zhu       MAE      0.552 [18]       1.971   \n", "20                     <PERSON><PERSON>      0.740 [45]       0.085   \n", "21  Lipophilicity AstraZeneca       MAE      0.467 [46]       1.506   \n", "22       OncoPolyPharmacology       PCC      0.730 [47]      -0.040   \n", "23                    PPBR AZ       MAE      7.788 [46]      10.836   \n", "24             Protein SAbDab       MAE             N/A       1.280   \n", "25         Solubility AqSolDB       MAE      0.761 [46]       4.214   \n", "26                        TAP       MAE             N/A       5.008   \n", "27                      USPTO  Accuracy      0.415 [48]       0.000   \n", "28               USPTO Yields       PCC      0.361 [39]      -0.015   \n", "29              V<PERSON><PERSON> <PERSON><PERSON>      0.627 [49]       0.100   \n", "\n", "   Gemma- 2-9B <PERSON>- 2-27B TxGemma- 2B-Predict TxGemma- 9B-Predict  \\\n", "0       -0.039        0.030               0.422               0.524   \n", "1        0.002        0.044               0.399               0.398   \n", "2       -0.009        0.119               0.352               0.370   \n", "3       -0.053       -0.027               0.661               0.737   \n", "4        0.636        0.684               0.861               0.915   \n", "5        0.533        0.618               0.476               0.373   \n", "6        0.163        0.214               0.353               0.338   \n", "7        0.325        0.294               0.468               0.623   \n", "8        9.054        4.473               0.601               0.587   \n", "9        0.295        0.277               0.057               0.054   \n", "10       7.413        6.456               4.230               4.337   \n", "11      33.837       22.614              15.752              16.480   \n", "12       7.365        6.670               4.231               4.335   \n", "13      13.369       14.731              17.342              18.665   \n", "14       6.226        5.404               3.950               3.904   \n", "15       0.073        0.093               0.876               0.545   \n", "16      -0.037        0.086               0.824               0.539   \n", "17       0.284        0.485               0.386               0.494   \n", "18       1.925        2.016               0.588               0.548   \n", "19       0.896        0.874               0.710               0.630   \n", "20       0.091        0.146               0.097               0.067   \n", "21       1.207        1.032               0.610               0.565   \n", "22       0.064        0.072               0.473               0.518   \n", "23       9.768        9.879               9.266               8.889   \n", "24       1.170        1.163               1.066               1.106   \n", "25       2.549        3.096               0.961               0.868   \n", "26       4.241        3.958               5.301               4.473   \n", "27       0.001        0.000               0.287               0.097   \n", "28       0.026        0.064               0.011               0.031   \n", "29       0.413        0.354               0.564               0.607   \n", "\n", "   TxGemma- 27B-Predict  \n", "0                 0.538  \n", "1                 0.445  \n", "2                 0.456  \n", "3                 0.676  \n", "4                 0.910  \n", "5                 0.401  \n", "6                 0.259  \n", "7                 0.462  \n", "8                 0.555  \n", "9                 0.054  \n", "10                4.156  \n", "11               15.000  \n", "12                4.209  \n", "13               17.336  \n", "14                3.807  \n", "15                0.892  \n", "16                0.912  \n", "17                0.458  \n", "18                0.633  \n", "19                0.628  \n", "20                0.276  \n", "21                0.539  \n", "22                0.540  \n", "23                9.029  \n", "24                1.210  \n", "25                0.821  \n", "26                4.280  \n", "27                0.084  \n", "28                0.395  \n", "29                0.560  \n", "20579 20580 SpanLayout(x=45.25455093383789, y=186.0126190185547, width=521.7303123474121, height=445.67286682128906, page_no=45)\n", "                         Task Name    Metric TxGemma- 9B-Predict  \\\n", "0                             AMES     AUROC               0.798   \n", "1                      BBB Martins     AUROC               0.874   \n", "2               Bioavailability Ma     AUROC               0.655   \n", "3                     CYP1A2 Veith     AUPRC               0.916   \n", "4                    CYP2C19 Veith     AUROC               0.906   \n", "5   CYP2C9 Substrate CarbonMangels     AUPRC               0.468   \n", "6                     CYP2C9 Veith     AUPRC               0.799   \n", "7   CYP2D6 Substrate CarbonMangels     AUPRC               0.603   \n", "8                     CYP2D6 Veith     AUPRC               0.664   \n", "9   CYP3A4 Substrate CarbonMangels     AUROC               0.622   \n", "10                    CYP3A4 Veith     AUPRC               0.839   \n", "11             Carcinogens Lagunin  Accuracy               0.839   \n", "12                         ClinTox     AUROC               0.831   \n", "13                            DILI     AUROC               0.848   \n", "14                         HIA Hou     AUROC               0.967   \n", "15                           HIV ∗     AUROC               0.734   \n", "16                            HuRI     AUPRC               0.779   \n", "17          MHC1 IEDB IMGT Nielsen     AUROC               0.927   \n", "18                MHC2 IEDB Jensen     AUROC               0.850   \n", "19                     PAMPA NCATS     AUROC               0.671   \n", "20                 Pgp B<PERSON>ccatelli     AUROC               0.911   \n", "21         SARSCOV2 3CLPro Diamond     AUROC               0.708   \n", "22           SARSCoV2 Vitro Touret     AUROC               0.668   \n", "23                     SAbDab Chen     AUPRC               0.807   \n", "24                   Skin Reaction     AUROC               0.648   \n", "25                           Tox21     AUROC               0.896   \n", "26                         ToxCast     AUROC               0.767   \n", "27                      <PERSON><PERSON><PERSON>     AUROC               0.772   \n", "28                            hERG     AUROC               0.881   \n", "29                      hERG Karim  Accuracy               0.794   \n", "30                    herg central     AUROC               0.861   \n", "31                      miRTarBase  Accuracy               0.829   \n", "32                          phase1     AUROC               0.635   \n", "33                          phase2     AUROC               0.668   \n", "34                          phase3     AUROC               0.729   \n", "35                           weber     AUROC               0.727   \n", "\n", "   TxGemma- 27B-Predict TxGemma- 9B-Chat TxGemma- 27B-Chat Tx-LLM S Tx-LLM M  \n", "0                 0.816            0.721             0.733    0.785    0.786  \n", "1                 0.907            0.811             0.861    0.805    0.882  \n", "2                 0.696            0.620             0.659    0.605    0.702  \n", "3                 0.922            0.839             0.823    0.906    0.914  \n", "4                 0.899            0.837             0.828    0.877    0.895  \n", "5                 0.427            0.382             0.427    0.403    0.436  \n", "6                 0.798            0.667             0.682    0.750    0.788  \n", "7                 0.706            0.549             0.700    0.643    0.600  \n", "8                 0.681            0.504             0.435    0.605    0.659  \n", "9                 0.690            0.642             0.666    0.637    0.647  \n", "10                0.854            0.749             0.750    0.800    0.840  \n", "11                0.857            0.893             0.911    0.857    0.786  \n", "12                0.888            0.711             0.637    0.818    0.863  \n", "13                0.887            0.688             0.766    0.727    0.882  \n", "14                0.988            0.872             0.897    0.942    0.990  \n", "15                0.764            0.612             0.582    0.686    0.732  \n", "16                0.799            0.628             0.621    0.705    0.753  \n", "17                0.929            0.875             0.825    0.913    0.907  \n", "18                0.851            0.724             0.683    0.781    0.863  \n", "19                0.705            0.735             0.664    0.646    0.668  \n", "20                0.936            0.899             0.912    0.909    0.939  \n", "21                0.769            0.699             0.722    0.755    0.712  \n", "22                0.598            0.503             0.506    0.512    0.601  \n", "23                0.767            0.702             0.719    0.390    0.473  \n", "24                0.708            0.638             0.543    0.564    0.615  \n", "25                0.893            0.807             0.797    0.858    0.882  \n", "26                0.800            0.754             0.734    0.779    0.792  \n", "27                0.831            0.629             0.619    0.574    0.566  \n", "28                0.884            0.830             0.832    0.879    0.909  \n", "29                0.774            0.657             0.668    0.724    0.745  \n", "30                0.896            0.830             0.807    0.880    0.888  \n", "31                0.801            0.679             0.644    0.765    0.799  \n", "32                0.622            0.576             0.557    0.624    0.667  \n", "33                0.676            0.638             0.626    0.639    0.676  \n", "34                0.739            0.683             0.668    0.701    0.728  \n", "35                0.749            0.672             0.643    0.738    0.743  \n", "20629 20630 SpanLayout(x=65.78443145751953, y=225.69627380371094, width=481.2307662963867, height=376.51283264160156, page_no=46)\n", "                    Task Name    Metric TxGemma- 9B-Predict  \\\n", "0            BindingDB Patent       PCC               0.524   \n", "1              BindingDB ic50  Spearman               0.398   \n", "2                BindingDB kd       PCC               0.370   \n", "3                BindingDB ki       PCC               0.737   \n", "4            Buch<PERSON>wig       PCC               0.915   \n", "5                  Caco2 Wang       <PERSON>               0.373   \n", "6     Clearance Hepatocyte AZ  Spearman               0.338   \n", "7      Clearance Microsome AZ  Spearman               0.623   \n", "8                       DAVIS       MSE               0.587   \n", "9                    DisGeNET       MAE               0.054   \n", "10             DrugComb Bliss       MAE               4.337   \n", "11               DrugComb CSS       MAE              16.480   \n", "12               DrugComb HSA       MAE               4.335   \n", "13             DrugComb Loewe       MAE              18.665   \n", "14               DrugComb ZIP       MAE               3.904   \n", "15                      GDSC1       PCC               0.545   \n", "16                      GDSC2       PCC               0.539   \n", "17            Half <PERSON> O<PERSON>  Spearman               0.494   \n", "18                       KIBA       MSE               0.548   \n", "19                   LD50 Zhu       MAE               0.630   \n", "20                     <PERSON><PERSON>               0.067   \n", "21  Lipophilicity AstraZeneca       MAE               0.565   \n", "22       OncoPolyPharmacology       PCC               0.518   \n", "23                    PPBR AZ       MAE               8.889   \n", "24             Protein SAbDab       MAE               1.106   \n", "25         Solubility AqSolDB       MAE               0.868   \n", "26                        TAP       MAE               4.473   \n", "27                      USPTO  Accuracy               0.097   \n", "28               USPTO Yields       PCC               0.031   \n", "29              V<PERSON><PERSON> <PERSON><PERSON>               0.607   \n", "\n", "   TxGemma- 27B-Predict TxGemma- 9B-Chat TxGemma- 27B-Chat Tx-LLM S Tx-LLM M  \n", "0                 0.538            0.452             0.220    0.474    0.531  \n", "1                 0.445            0.412             0.362    0.326    0.311  \n", "2                 0.456            0.162             0.159    0.317    0.391  \n", "3                 0.676            0.448             0.211    0.565    0.726  \n", "4                 0.910            0.255             0.757    0.682    0.905  \n", "5                 0.401            0.643             0.398    0.621    0.432  \n", "6                 0.259            0.197             0.150    0.256    0.385  \n", "7                 0.462            0.345             0.420    0.385    0.413  \n", "8                 0.555            0.608             0.561    0.564    0.704  \n", "9                 0.054            0.066             0.064    0.059    0.057  \n", "10                4.156            4.502             4.511    4.425    4.104  \n", "11               15.000           16.384            16.900   14.740   14.057  \n", "12                4.209            4.497             4.520    4.311    4.118  \n", "13               17.336           16.994            16.914   17.428   17.381  \n", "14                3.807            4.139             4.141    4.047    3.777  \n", "15                0.892            0.861             0.802    0.876    0.887  \n", "16                0.912            0.864             0.823    0.896    0.900  \n", "17                0.458            0.330             0.414    0.525    0.448  \n", "18                0.633            0.705             0.852    0.709    0.548  \n", "19                0.628            0.740             0.705    0.808    0.618  \n", "20                0.276            0.128             0.095    0.048    0.083  \n", "21                0.539            0.985             0.842    0.779    0.587  \n", "22                0.540            0.359             0.193    0.418    0.552  \n", "23                9.029           11.367            10.895   11.138    9.108  \n", "24                1.210            1.268             1.116    1.432    1.268  \n", "25                0.821            1.159             1.133    0.931    0.987  \n", "26                4.280            4.859             4.083    5.075    4.983  \n", "27                0.084            0.086             0.091    0.220    0.239  \n", "28                0.395            0.003             0.026    0.042    0.070  \n", "29                0.560            0.396             0.407    0.497    0.609  \n", "20669 20670 SpanLayout(x=70.47370147705078, y=209.32337951660156, width=470.98937225341797, height=410.04783630371094, page_no=47)\n", "                         Task Name    Metric TxGemma- 2B-Predict  \\\n", "0                             AMES     AUROC               0.812   \n", "1                      BBB Martins     AUROC               0.883   \n", "2               Bioavailability Ma     AUROC               0.688   \n", "3                     CYP1A2 Veith     AUPRC               0.911   \n", "4                    CYP2C19 Veith     AUROC               0.905   \n", "5   CYP2C9 Substrate CarbonMangels     AUPRC               0.417   \n", "6                     CYP2C9 Veith     AUPRC               0.787   \n", "7   CYP2D6 Substrate CarbonMangels     AUPRC               0.626   \n", "8                     CYP2D6 Veith     AUPRC               0.666   \n", "9   CYP3A4 Substrate CarbonMangels     AUROC               0.638   \n", "10                    CYP3A4 Veith     AUPRC               0.842   \n", "11             Carcinogens Lagunin  Accuracy               0.911   \n", "12                         ClinTox     AUROC               0.917   \n", "13                            DILI     AUROC               0.829   \n", "14                         HIA Hou     AUROC               0.984   \n", "15                             HIV     AUROC               0.781   \n", "16                            HuRI     AUPRC               0.735   \n", "17          MHC1 IEDB IMGT Nielsen     AUROC               0.930   \n", "18                MHC2 IEDB Jensen     AUROC               0.855   \n", "19                     PAMPA NCATS     AUROC               0.694   \n", "20                 Pgp Broccatelli     AUROC               0.922   \n", "21         SARSCOV2 3CLPro Diamond     AUROC               0.748   \n", "22           SARSCoV2 Vitro Touret     AUROC               0.659   \n", "23                     SAbDab Chen     AUPRC               0.726   \n", "24                   Skin Reaction     AUROC               0.691   \n", "25                           Tox21     AUROC               0.897   \n", "26                         ToxCast     AUROC               0.787   \n", "27                      <PERSON><PERSON><PERSON>     AUROC               0.811   \n", "28                            hERG     AUROC               0.902   \n", "29                      hERG Karim  Accuracy               0.778   \n", "30                    herg central     AUROC               0.890   \n", "31                      miRTarBase  Accuracy               0.818   \n", "32                           weber     AUROC               0.750   \n", "\n", "   TxGemma- 9B-Predict TxGemma- 27B-Predict TxGemma- 9B-Chat TxGemma- 27B-Chat  \n", "0                0.803                0.826            0.723             0.729  \n", "1                0.849                0.899            0.832             0.848  \n", "2                0.688                0.724            0.666             0.625  \n", "3                0.914                0.916            0.862             0.817  \n", "4                0.897                0.897            0.844             0.823  \n", "5                0.390                0.460            0.414             0.375  \n", "6                0.800                0.793            0.700             0.685  \n", "7                0.697                0.706            0.653             0.704  \n", "8                0.662                0.677            0.517             0.422  \n", "9                0.680                0.692            0.644             0.653  \n", "10               0.839                0.852            0.760             0.747  \n", "11               0.857                0.875            0.893             0.929  \n", "12               0.815                0.884            0.716             0.595  \n", "13               0.823                0.927            0.675             0.797  \n", "14               0.954                0.990            0.906             0.927  \n", "15               0.730                0.768            0.641             0.589  \n", "16               0.767                0.797            0.685             0.620  \n", "17               0.929                0.933            0.887             0.826  \n", "18               0.852                0.855            0.733             0.682  \n", "19               0.630                0.724            0.684             0.659  \n", "20               0.932                0.941            0.873             0.920  \n", "21               0.799                0.676            0.716             0.712  \n", "22               0.622                0.597            0.527             0.516  \n", "23               0.745                0.793            0.523             0.731  \n", "24               0.624                0.733            0.621             0.571  \n", "25               0.893                0.890            0.818             0.797  \n", "26               0.766                0.797            0.754             0.735  \n", "27               0.775                0.826            0.681             0.606  \n", "28               0.890                0.894            0.855             0.829  \n", "29               0.796                0.772            0.649             0.673  \n", "30               0.860                0.892            0.842             0.805  \n", "31               0.834                0.802            0.672             0.649  \n", "32               0.697                0.749            0.692             0.645  \n", "20723 20724 SpanLayout(x=87.98839569091797, y=249.15658569335938, width=435.6459426879883, height=341.5226745605469, page_no=48)\n", "                    Task Name    Metric TxGemma- 2B-Predict  \\\n", "0            BindingDB Patent       PCC               0.556   \n", "1              BindingDB ic50  Spearman               0.425   \n", "2                BindingDB kd       PCC               0.490   \n", "3                BindingDB ki       PCC               0.728   \n", "4            Buchwald Hartwig       PCC               0.920   \n", "5                  Caco2 Wang       <PERSON>               0.619   \n", "6     Clearance Hepatocyte AZ  Spearman               0.292   \n", "7      Clearance Microsome AZ  Spearman               0.521   \n", "8                       DAVIS       MSE               0.576   \n", "9              DrugComb Bliss       MAE               4.088   \n", "10               DrugComb CSS       MAE              14.568   \n", "11               DrugComb HSA       MAE               4.063   \n", "12             DrugComb Loewe       MAE              17.313   \n", "13               DrugComb ZIP       MAE               3.737   \n", "14            Half <PERSON> Obach  Spearman               0.423   \n", "15                       KIBA       MSE               0.562   \n", "16                   LD50 Zhu       MAE               0.698   \n", "17                     <PERSON><PERSON>               0.114   \n", "18  Lipophilicity AstraZeneca       MAE               0.571   \n", "19       OncoPolyPharmacology       PCC               0.556   \n", "20                    PPBR AZ       MAE               8.813   \n", "21             Protein SAbDab       MAE               1.117   \n", "22         Solubility AqSolDB       MAE               0.911   \n", "23                        TAP       MAE               5.498   \n", "24                      USPTO  Accuracy               0.316   \n", "25               USPTO Yields       PCC               0.471   \n", "26              VDss <PERSON><PERSON>               0.594   \n", "\n", "   TxGemma- 9B-Predict TxGemma- 27B-Predict TxGemma- 9B-Chat TxGemma- 27B-Chat  \n", "0                0.376                0.537            0.438             0.118  \n", "1                0.313                0.465            0.443             0.361  \n", "2                0.393                0.289            0.207             0.156  \n", "3                0.712                0.670            0.387             0.218  \n", "4                0.918                0.903            0.574             0.818  \n", "5                0.491                0.479            0.588             0.383  \n", "6                0.378                0.350            0.166             0.190  \n", "7                0.524                0.510            0.394             0.395  \n", "8                0.564                0.575            0.561             0.561  \n", "9                4.286                4.157            4.454             4.519  \n", "10              15.370               14.925           15.960            16.649  \n", "11               4.282                4.178            4.486             4.529  \n", "12              17.862               17.327           17.190            16.873  \n", "13               3.848                3.823            4.093             4.132  \n", "14               0.348                0.491            0.269             0.393  \n", "15               0.525                0.554            0.830             0.858  \n", "16               0.718                0.677            0.724             0.721  \n", "17               0.089                0.259            0.078             0.183  \n", "18               0.667                0.613            0.834             0.837  \n", "19               0.437                0.531            0.388             0.148  \n", "20               9.177                8.792           11.004            11.025  \n", "21               1.022                1.072            1.348             1.173  \n", "22               1.185                0.802            1.160             1.135  \n", "23               4.839                4.088            4.611             4.444  \n", "24               0.041                0.281            0.145             0.090  \n", "25               0.002                0.350            0.114             0.002  \n", "26               0.538                0.591            0.410             0.487  \n"]}], "source": ["for table in doc._.tables:\n", "    # Token position and bounding box\n", "    print(table.start, table.end, table._.layout)\n", "    # pandas.DataFrame of contents\n", "    print(table._.data)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["17"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["len(doc._.tables)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Type</th>\n", "      <th>Task</th>\n", "      <th>Metric</th>\n", "      <th><PERSON><PERSON><PERSON> [24]</th>\n", "      <th>TxGemma-27B-Predict</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Pharmacokinetics</td>\n", "      <td>Caco2 Wang Lipophilicity AstraZeneca Solubilit...</td>\n", "      <td>MAE ( ↓ ) MAE ( ↓ ) MAE ( ↓ ) MAE ( ↓ ) AUROC ...</td>\n", "      <td>0.329 0.406 0.776 7.229 0.984 0.930 0.640 0.90...</td>\n", "      <td>0.401 (0.358-0.449) 0.538 (0.507-0.570) 0.907 ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Pharmacokinetics</td>\n", "      <td>CarbonMangels</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Pharmacokinetics</td>\n", "      <td>CarbonMangels</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Pharmacokinetics</td>\n", "      <td>CarbonMangels</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Pharmacokinetics</td>\n", "      <td></td>\n", "      <td>)</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Pharmacokinetics</td>\n", "      <td></td>\n", "      <td>)</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Pharmacokinetics</td>\n", "      <td></td>\n", "      <td>)</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Pharmacokinetics</td>\n", "      <td>Clearance Hepatocyte AZ</td>\n", "      <td>)</td>\n", "      <td>0.456</td>\n", "      <td>0.260 (0.129-0.384)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Pharmacokinetics</td>\n", "      <td>LD50 Zhu</td>\n", "      <td>MAE ( ↓ )</td>\n", "      <td>0.602</td>\n", "      <td>0.627 (0.597-0.660)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Toxicity</td>\n", "      <td>hERG</td>\n", "      <td>AUROC ( ↑ )</td>\n", "      <td>0.835</td>\n", "      <td>0.885 (0.813-0.946)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Pharmacokinetics</td>\n", "      <td>AMES</td>\n", "      <td>AUROC ( ↑ )</td>\n", "      <td>0.834</td>\n", "      <td>0.816 (0.795-0.838)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Pharmacokinetics</td>\n", "      <td>DILI</td>\n", "      <td>AUROC ( ↑ )</td>\n", "      <td>0.852</td>\n", "      <td>0.886 (0.810-0.947)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           Task Type                                               Task  \\\n", "0   Pharmacokinetics  Caco2 Wang Lipophilicity AstraZeneca Solubilit...   \n", "1   Pharmacokinetics                                      CarbonMangels   \n", "2   Pharmacokinetics                                      CarbonMangels   \n", "3   Pharmacokinetics                                      CarbonMangels   \n", "4   Pharmacokinetics                                                      \n", "5   Pharmacokinetics                                                      \n", "6   Pharmacokinetics                                                      \n", "7   Pharmacokinetics                            Clearance Hepatocyte AZ   \n", "8   Pharmacokinetics                                           LD50 Zhu   \n", "9           Toxicity                                               hERG   \n", "10  Pharmacokinetics                                               AMES   \n", "11  Pharmacokinetics                                               DILI   \n", "\n", "                                               Metric  \\\n", "0   MAE ( ↓ ) MAE ( ↓ ) MAE ( ↓ ) MAE ( ↓ ) AUROC ...   \n", "1                                                       \n", "2                                                       \n", "3                                                       \n", "4                                                   )   \n", "5                                                   )   \n", "6                                                   )   \n", "7                                                   )   \n", "8                                           MAE ( ↓ )   \n", "9                                         AUROC ( ↑ )   \n", "10                                        AUROC ( ↑ )   \n", "11                                        AUROC ( ↑ )   \n", "\n", "                                            <PERSON><PERSON><PERSON> [24]  \\\n", "0   0.329 0.406 0.776 7.229 0.984 0.930 0.640 0.90...   \n", "1                                                       \n", "2                                                       \n", "3                                                       \n", "4                                                       \n", "5                                                       \n", "6                                                       \n", "7                                               0.456   \n", "8                                               0.602   \n", "9                                               0.835   \n", "10                                              0.834   \n", "11                                              0.852   \n", "\n", "                                  TxGemma-27B-Predict  \n", "0   0.401 (0.358-0.449) 0.538 (0.507-0.570) 0.907 ...  \n", "1                                                      \n", "2                                                      \n", "3                                                      \n", "4                                                      \n", "5                                                      \n", "6                                                      \n", "7                                 0.260 (0.129-0.384)  \n", "8                                 0.627 (0.597-0.660)  \n", "9                                 0.885 (0.813-0.946)  \n", "10                                0.816 (0.795-0.838)  \n", "11                                0.886 (0.810-0.947)  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["doc._.tables[0]._.data"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Task Type</th>\n", "      <th>Task</th>\n", "      <th>Metric</th>\n", "      <th>LlaSMol Mistral [23]</th>\n", "      <th>TxGemma-27B-Predict</th>\n", "      <th>TxGemma-9B-Predict</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Pharmacokinetics</td>\n", "      <td>BBBP †</td>\n", "      <td>Accuracy ( ↑ )</td>\n", "      <td>0.746</td>\n", "      <td>0.869 (0.835-0.901)</td>\n", "      <td>0.847 (0.813-0.881)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td></td>\n", "      <td>ESOL †</td>\n", "      <td>RMSE ( ↓ )</td>\n", "      <td>1.150</td>\n", "      <td>1.250 (1.185-1.321)</td>\n", "      <td>1.360 (1.246-1.480)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td></td>\n", "      <td><PERSON><PERSON> †</td>\n", "      <td>RMSE ( ↓ )</td>\n", "      <td>1.010</td>\n", "      <td>0.710 (0.668-0.752)</td>\n", "      <td>0.742 (0.700-0.787)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Toxicity</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Accuracy ( ↑ )</td>\n", "      <td>0.931</td>\n", "      <td>0.926 (0.896-0.956)</td>\n", "      <td>0.925 (0.892-0.953)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>High-throughput screening</td>\n", "      <td>HIV ∗</td>\n", "      <td>Accuracy ( ↑ )</td>\n", "      <td>0.967</td>\n", "      <td>0.968 (0.964-0.972)</td>\n", "      <td>0.965 (0.961-0.969)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   Task Type     Task          Metric LlaSMol Mistral [23]  \\\n", "0           Pharmacokinetics   BBBP †  Accuracy ( ↑ )                0.746   \n", "1                              ESOL †      RMSE ( ↓ )                1.150   \n", "2                              Lipo †      RMSE ( ↓ )                1.010   \n", "3                   Toxicity  Clintox  Accuracy ( ↑ )                0.931   \n", "4  High-throughput screening    HIV ∗  Accuracy ( ↑ )                0.967   \n", "\n", "   TxGemma-27B-Predict   TxGemma-9B-Predict  \n", "0  0.869 (0.835-0.901)  0.847 (0.813-0.881)  \n", "1  1.250 (1.185-1.321)  1.360 (1.246-1.480)  \n", "2  0.710 (0.668-0.752)  0.742 (0.700-0.787)  \n", "3  0.926 (0.896-0.956)  0.925 (0.892-0.953)  \n", "4  0.968 (0.964-0.972)  0.965 (0.961-0.969)  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["doc._.tables[1]._.data"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Model.</th>\n", "      <th>ChemBench.Mini</th>\n", "      <th>ChemBench.Preference</th>\n", "      <th>GPQA (Diamond).Chemistry</th>\n", "      <th>Humanity's Last Exam.Chemistry &amp; Biology</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Agentic-Tx (Gemini 2.5-Pro)</td>\n", "      <td>84.5</td>\n", "      <td>66.2</td>\n", "      <td>81.7</td>\n", "      <td>20.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Agentic-Tx (Gemini 2.0-Pro)</td>\n", "      <td>83.4</td>\n", "      <td>65.5</td>\n", "      <td>62.4</td>\n", "      <td>14.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Agentic-Tx (Gemini 1.5-Pro)</td>\n", "      <td>80.6</td>\n", "      <td>65.0</td>\n", "      <td>51.8</td>\n", "      <td>11.9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Claude-3.5 (Sonnet)</td>\n", "      <td>73.0 ∗</td>\n", "      <td>60.0 ∗†</td>\n", "      <td>40.4</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>GPT-4o</td>\n", "      <td>72.0 ∗</td>\n", "      <td>59.0 ∗</td>\n", "      <td>43.8 ∗∗</td>\n", "      <td>3.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Gemini 2.5-pro</td>\n", "      <td>82.8</td>\n", "      <td>65.5</td>\n", "      <td>79.5</td>\n", "      <td>17.9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Gemini 2.0-pro</td>\n", "      <td>79.6</td>\n", "      <td>58.4</td>\n", "      <td>53.3</td>\n", "      <td>11.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Gemini 1.5-pro</td>\n", "      <td>74.9</td>\n", "      <td>55.6</td>\n", "      <td>48.2</td>\n", "      <td>10.6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>PaperQA2 [28]</td>\n", "      <td>67.0 ∗</td>\n", "      <td>56.0 ∗</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>o1</td>\n", "      <td>80.0 ∗</td>\n", "      <td>56.0 ∗</td>\n", "      <td>64.7 ∗∗</td>\n", "      <td>12.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>o3-mini (medium)</td>\n", "      <td>82.4</td>\n", "      <td>61.3</td>\n", "      <td>62.5</td>\n", "      <td>13.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>o3-mini (high)</td>\n", "      <td>82.5</td>\n", "      <td>62.0</td>\n", "      <td>64.5</td>\n", "      <td>13.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Human Expert (Average Performance)</td>\n", "      <td>27.0</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "      <td>-</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                Model. ChemBench.Mini ChemBench.Preference  \\\n", "0          Agentic-Tx (Gemini 2.5-Pro)           84.5                 66.2   \n", "1          Agentic-Tx (Gemini 2.0-Pro)           83.4                 65.5   \n", "2          Agentic-Tx (Gemini 1.5-Pro)           80.6                 65.0   \n", "3                  Claude-3.5 (<PERSON><PERSON>)         73.0 ∗              60.0 ∗†   \n", "4                               GPT-4o         72.0 ∗               59.0 ∗   \n", "5                       Gemini 2.5-pro           82.8                 65.5   \n", "6                       Gemini 2.0-pro           79.6                 58.4   \n", "7                       Gemini 1.5-pro           74.9                 55.6   \n", "8                        PaperQA2 [28]         67.0 ∗               56.0 ∗   \n", "9                                   o1         80.0 ∗               56.0 ∗   \n", "10                    o3-mini (medium)           82.4                 61.3   \n", "11                      o3-mini (high)           82.5                 62.0   \n", "12  Human Expert (Average Performance)           27.0                    -   \n", "\n", "   GPQA (Diamond).Chemistry Humanity's Last Exam.Chemistry & Biology  \n", "0                      81.7                                     20.1  \n", "1                      62.4                                     14.5  \n", "2                      51.8                                     11.9  \n", "3                      40.4                                        -  \n", "4                   43.8 ∗∗                                      3.8  \n", "5                      79.5                                     17.9  \n", "6                      53.3                                     11.1  \n", "7                      48.2                                     10.6  \n", "8                         -                                        -  \n", "9                   64.7 ∗∗                                     12.3  \n", "10                     62.5                                     13.0  \n", "11                     64.5                                     13.2  \n", "12                        -                                        -  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["doc._.tables[2]._.data"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:docling.document_converter:Going to convert document batch...\n", "INFO:docling.document_converter:Initializing pipeline for StandardPdfPipeline with options hash 9a673474fc72ddd4bd76bc411d7f7850\n", "INFO:docling.utils.accelerator_utils:Accelerator device: 'mps'\n", "/Users/<USER>/Development/fastmoatless/.venv/lib/python3.12/site-packages/urllib3/connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'huggingface.co'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"ename": "JSONDecodeError", "evalue": "Expecting value: line 1 column 1 (char 0)", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mJSONDecodeError\u001b[39m                           <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/Development/fastmoatless/.venv/lib/python3.12/site-packages/requests/models.py:974\u001b[39m, in \u001b[36mResponse.json\u001b[39m\u001b[34m(self, **kwargs)\u001b[39m\n\u001b[32m    973\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m974\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mcomplex<PERSON><PERSON>\u001b[49m\u001b[43m.\u001b[49m\u001b[43mloads\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mtext\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    975\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m JSONDecodeError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m    976\u001b[39m     \u001b[38;5;66;03m# Catch JSON-related errors and raise as requests.JSONDecodeError\u001b[39;00m\n\u001b[32m    977\u001b[39m     \u001b[38;5;66;03m# This aliases json.JSONDecodeError and simplej<PERSON>.JSONDecodeError\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/homebrew/Cellar/python@3.12/3.12.8/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/__init__.py:346\u001b[39m, in \u001b[36mloads\u001b[39m\u001b[34m(s, cls, object_hook, parse_float, parse_int, parse_constant, object_pairs_hook, **kw)\u001b[39m\n\u001b[32m    343\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m (\u001b[38;5;28mcls\u001b[39m \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m object_hook \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m\n\u001b[32m    344\u001b[39m         parse_int \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m parse_float \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mN<PERSON>\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m\n\u001b[32m    345\u001b[39m         parse_constant \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m object_pairs_hook \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m kw):\n\u001b[32m--> \u001b[39m\u001b[32m346\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_default_decoder\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdecode\u001b[49m\u001b[43m(\u001b[49m\u001b[43ms\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    347\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mcls\u001b[39m \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/homebrew/Cellar/python@3.12/3.12.8/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/decoder.py:338\u001b[39m, in \u001b[36mJSONDecoder.decode\u001b[39m\u001b[34m(self, s, _w)\u001b[39m\n\u001b[32m    334\u001b[39m \u001b[38;5;250m\u001b[39m\u001b[33;03m\"\"\"Return the Python representation of ``s`` (a ``str`` instance\u001b[39;00m\n\u001b[32m    335\u001b[39m \u001b[33;03mcontaining a JSON document).\u001b[39;00m\n\u001b[32m    336\u001b[39m \n\u001b[32m    337\u001b[39m \u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m338\u001b[39m obj, end = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mraw_decode\u001b[49m\u001b[43m(\u001b[49m\u001b[43ms\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43midx\u001b[49m\u001b[43m=\u001b[49m\u001b[43m_w\u001b[49m\u001b[43m(\u001b[49m\u001b[43ms\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[32;43m0\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mend\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    339\u001b[39m end = _w(s, end).end()\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/homebrew/Cellar/python@3.12/3.12.8/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/decoder.py:356\u001b[39m, in \u001b[36mJSONDecoder.raw_decode\u001b[39m\u001b[34m(self, s, idx)\u001b[39m\n\u001b[32m    355\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mStopIteration\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[32m--> \u001b[39m\u001b[32m356\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m JSONDecodeError(\u001b[33m\"\u001b[39m\u001b[33mExpecting value\u001b[39m\u001b[33m\"\u001b[39m, s, err.value) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[32m    357\u001b[39m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m obj, end\n", "\u001b[31mJSONDecodeError\u001b[39m: Expecting value: line 1 column 1 (char 0)", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[31mJSONDecodeError\u001b[39m                           <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[9]\u001b[39m\u001b[32m, line 15\u001b[39m\n\u001b[32m      8\u001b[39m pipeline_options.do_table_structure = \u001b[38;5;28;01mTrue\u001b[39;00m \u001b[38;5;66;03m# pick what you need\u001b[39;00m\n\u001b[32m     10\u001b[39m doc_converter = DocumentConverter(\n\u001b[32m     11\u001b[39m     format_options={\n\u001b[32m     12\u001b[39m         InputFormat.PDF: PdfFormatOption(pipeline_options=pipeline_options, backend=DoclingParseV2DocumentBackend)  \u001b[38;5;66;03m# switch to beta PDF backend\u001b[39;00m\n\u001b[32m     13\u001b[39m         }\n\u001b[32m     14\u001b[39m )\n\u001b[32m---> \u001b[39m\u001b[32m15\u001b[39m conv_result = \u001b[43mdoc_converter\u001b[49m\u001b[43m.\u001b[49m\u001b[43mconvert\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mdata/test/pdf/TxGemma.pdf\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m     17\u001b[39m \u001b[38;5;28mprint\u001b[39m(conv_result.document.export_to_markdown())\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Development/fastmoatless/.venv/lib/python3.12/site-packages/pydantic/_internal/_validate_call.py:38\u001b[39m, in \u001b[36mupdate_wrapper_attributes.<locals>.wrapper_function\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m     36\u001b[39m \u001b[38;5;129m@functools\u001b[39m.wraps(wrapped)\n\u001b[32m     37\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mwrapper_function\u001b[39m(*args, **kwargs):\n\u001b[32m---> \u001b[39m\u001b[32m38\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mwrapper\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Development/fastmoatless/.venv/lib/python3.12/site-packages/pydantic/_internal/_validate_call.py:111\u001b[39m, in \u001b[36mValidateCallWrapper.__call__\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m    110\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m__call__\u001b[39m(\u001b[38;5;28mself\u001b[39m, *args: Any, **kwargs: Any) -> Any:\n\u001b[32m--> \u001b[39m\u001b[32m111\u001b[39m     res = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m__pydantic_validator__\u001b[49m\u001b[43m.\u001b[49m\u001b[43mvalidate_python\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpydantic_core\u001b[49m\u001b[43m.\u001b[49m\u001b[43mArgsKwargs\u001b[49m\u001b[43m(\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    112\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.__return_pydantic_validator__:\n\u001b[32m    113\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m.__return_pydantic_validator__(res)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Development/fastmoatless/.venv/lib/python3.12/site-packages/docling/document_converter.py:220\u001b[39m, in \u001b[36mDocumentConverter.convert\u001b[39m\u001b[34m(self, source, headers, raises_on_error, max_num_pages, max_file_size, page_range)\u001b[39m\n\u001b[32m    202\u001b[39m \u001b[38;5;129m@validate_call\u001b[39m(config=ConfigDict(strict=\u001b[38;5;28;01mTrue\u001b[39;00m))\n\u001b[32m    203\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mconvert\u001b[39m(\n\u001b[32m    204\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m    210\u001b[39m     page_range: PageRange = DEFAULT_PAGE_RANGE,\n\u001b[32m    211\u001b[39m ) -> ConversionResult:\n\u001b[32m    212\u001b[39m     all_res = \u001b[38;5;28mself\u001b[39m.convert_all(\n\u001b[32m    213\u001b[39m         source=[source],\n\u001b[32m    214\u001b[39m         raises_on_error=raises_on_error,\n\u001b[32m   (...)\u001b[39m\u001b[32m    218\u001b[39m         page_range=page_range,\n\u001b[32m    219\u001b[39m     )\n\u001b[32m--> \u001b[39m\u001b[32m220\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mnext\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mall_res\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Development/fastmoatless/.venv/lib/python3.12/site-packages/docling/document_converter.py:243\u001b[39m, in \u001b[36mDocumentConverter.convert_all\u001b[39m\u001b[34m(self, source, headers, raises_on_error, max_num_pages, max_file_size, page_range)\u001b[39m\n\u001b[32m    240\u001b[39m conv_res_iter = \u001b[38;5;28mself\u001b[39m._convert(conv_input, raises_on_error=raises_on_error)\n\u001b[32m    242\u001b[39m had_result = \u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m243\u001b[39m \u001b[43m\u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mconv_res\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mconv_res_iter\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    244\u001b[39m \u001b[43m    \u001b[49m\u001b[43mhad_result\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\n\u001b[32m    245\u001b[39m \u001b[43m    \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mraises_on_error\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mand\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mconv_res\u001b[49m\u001b[43m.\u001b[49m\u001b[43mstatus\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01mnot\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43m{\u001b[49m\n\u001b[32m    246\u001b[39m \u001b[43m        \u001b[49m\u001b[43mConversionStatus\u001b[49m\u001b[43m.\u001b[49m\u001b[43mSUCCESS\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    247\u001b[39m \u001b[43m        \u001b[49m\u001b[43mConversionStatus\u001b[49m\u001b[43m.\u001b[49m\u001b[43mPARTIAL_SUCCESS\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    248\u001b[39m \u001b[43m    \u001b[49m\u001b[43m}\u001b[49m\u001b[43m:\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Development/fastmoatless/.venv/lib/python3.12/site-packages/docling/document_converter.py:278\u001b[39m, in \u001b[36mDocumentConverter._convert\u001b[39m\u001b[34m(self, conv_input, raises_on_error)\u001b[39m\n\u001b[32m    269\u001b[39m _log.info(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mGoing to convert document batch...\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    271\u001b[39m \u001b[38;5;66;03m# parallel processing only within input_batch\u001b[39;00m\n\u001b[32m    272\u001b[39m \u001b[38;5;66;03m# with ThreadPoolExecutor(\u001b[39;00m\n\u001b[32m    273\u001b[39m \u001b[38;5;66;03m#    max_workers=settings.perf.doc_batch_concurrency\u001b[39;00m\n\u001b[32m    274\u001b[39m \u001b[38;5;66;03m# ) as pool:\u001b[39;00m\n\u001b[32m    275\u001b[39m \u001b[38;5;66;03m#   yield from pool.map(self.process_document, input_batch)\u001b[39;00m\n\u001b[32m    276\u001b[39m \u001b[38;5;66;03m# Note: PDF backends are not thread-safe, thread pool usage was disabled.\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m278\u001b[39m \u001b[43m\u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mitem\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mmap\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[32m    279\u001b[39m \u001b[43m    \u001b[49m\u001b[43mpartial\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_process_document\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mraises_on_error\u001b[49m\u001b[43m=\u001b[49m\u001b[43mraises_on_error\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    280\u001b[39m \u001b[43m    \u001b[49m\u001b[43minput_batch\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    281\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    282\u001b[39m \u001b[43m    \u001b[49m\u001b[43melapsed\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mtime\u001b[49m\u001b[43m.\u001b[49m\u001b[43mmonotonic\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[43m-\u001b[49m\u001b[43m \u001b[49m\u001b[43mstart_time\u001b[49m\n\u001b[32m    283\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstart_time\u001b[49m\u001b[43m \u001b[49m\u001b[43m=\u001b[49m\u001b[43m \u001b[49m\u001b[43mtime\u001b[49m\u001b[43m.\u001b[49m\u001b[43mmonotonic\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Development/fastmoatless/.venv/lib/python3.12/site-packages/docling/document_converter.py:324\u001b[39m, in \u001b[36mDocumentConverter._process_document\u001b[39m\u001b[34m(self, in_doc, raises_on_error)\u001b[39m\n\u001b[32m    320\u001b[39m valid = (\n\u001b[32m    321\u001b[39m     \u001b[38;5;28mself\u001b[39m.allowed_formats \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m in_doc.format \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m.allowed_formats\n\u001b[32m    322\u001b[39m )\n\u001b[32m    323\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m valid:\n\u001b[32m--> \u001b[39m\u001b[32m324\u001b[39m     conv_res = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_execute_pipeline\u001b[49m\u001b[43m(\u001b[49m\u001b[43min_doc\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mraises_on_error\u001b[49m\u001b[43m=\u001b[49m\u001b[43mraises_on_error\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    325\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    326\u001b[39m     error_message = \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mFile format not allowed: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00min_doc.file\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Development/fastmoatless/.venv/lib/python3.12/site-packages/docling/document_converter.py:345\u001b[39m, in \u001b[36mDocumentConverter._execute_pipeline\u001b[39m\u001b[34m(self, in_doc, raises_on_error)\u001b[39m\n\u001b[32m    341\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_execute_pipeline\u001b[39m(\n\u001b[32m    342\u001b[39m     \u001b[38;5;28mself\u001b[39m, in_doc: InputDocument, raises_on_error: \u001b[38;5;28mbool\u001b[39m\n\u001b[32m    343\u001b[39m ) -> ConversionResult:\n\u001b[32m    344\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m in_doc.valid:\n\u001b[32m--> \u001b[39m\u001b[32m345\u001b[39m         pipeline = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_get_pipeline\u001b[49m\u001b[43m(\u001b[49m\u001b[43min_doc\u001b[49m\u001b[43m.\u001b[49m\u001b[43mformat\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    346\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m pipeline \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    347\u001b[39m             conv_res = pipeline.execute(in_doc, raises_on_error=raises_on_error)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Development/fastmoatless/.venv/lib/python3.12/site-packages/docling/document_converter.py:307\u001b[39m, in \u001b[36mDocumentConverter._get_pipeline\u001b[39m\u001b[34m(self, doc_format)\u001b[39m\n\u001b[32m    303\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m cache_key \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m.initialized_pipelines:\n\u001b[32m    304\u001b[39m     _log.info(\n\u001b[32m    305\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mInitializing pipeline for \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpipeline_class.\u001b[34m__name__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m with options hash \u001b[39m\u001b[38;5;132;01m{\u001b[39;00moptions_hash\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m\n\u001b[32m    306\u001b[39m     )\n\u001b[32m--> \u001b[39m\u001b[32m307\u001b[39m     \u001b[38;5;28mself\u001b[39m.initialized_pipelines[cache_key] = \u001b[43mpipeline_class\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    308\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpipeline_options\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpipeline_options\u001b[49m\n\u001b[32m    309\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    310\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    311\u001b[39m     _log.debug(\n\u001b[32m    312\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mReusing cached pipeline for \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpipeline_class.\u001b[34m__name__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m with options hash \u001b[39m\u001b[38;5;132;01m{\u001b[39;00moptions_hash\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m\n\u001b[32m    313\u001b[39m     )\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Development/fastmoatless/.venv/lib/python3.12/site-packages/docling/pipeline/standard_pdf_pipeline.py:79\u001b[39m, in \u001b[36mStandardPdfPipeline.__init__\u001b[39m\u001b[34m(self, pipeline_options)\u001b[39m\n\u001b[32m     64\u001b[39m \u001b[38;5;28mself\u001b[39m.glm_model = ReadingOrderModel(options=ReadingOrderOptions())\n\u001b[32m     66\u001b[39m ocr_model = \u001b[38;5;28mself\u001b[39m.get_ocr_model(artifacts_path=artifacts_path)\n\u001b[32m     68\u001b[39m \u001b[38;5;28mself\u001b[39m.build_pipe = [\n\u001b[32m     69\u001b[39m     \u001b[38;5;66;03m# Pre-processing\u001b[39;00m\n\u001b[32m     70\u001b[39m     PagePreprocessingModel(\n\u001b[32m     71\u001b[39m         options=PagePreprocessingOptions(\n\u001b[32m     72\u001b[39m             images_scale=pipeline_options.images_scale,\n\u001b[32m     73\u001b[39m             create_parsed_page=pipeline_options.generate_parsed_pages,\n\u001b[32m     74\u001b[39m         )\n\u001b[32m     75\u001b[39m     ),\n\u001b[32m     76\u001b[39m     \u001b[38;5;66;03m# OCR\u001b[39;00m\n\u001b[32m     77\u001b[39m     ocr_model,\n\u001b[32m     78\u001b[39m     \u001b[38;5;66;03m# Layout model\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m79\u001b[39m     \u001b[43mLayoutModel\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m     80\u001b[39m \u001b[43m        \u001b[49m\u001b[43martifacts_path\u001b[49m\u001b[43m=\u001b[49m\u001b[43martifacts_path\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     81\u001b[39m \u001b[43m        \u001b[49m\u001b[43maccelerator_options\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpipeline_options\u001b[49m\u001b[43m.\u001b[49m\u001b[43maccelerator_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     82\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m,\n\u001b[32m     83\u001b[39m     \u001b[38;5;66;03m# Table structure model\u001b[39;00m\n\u001b[32m     84\u001b[39m     TableStructureModel(\n\u001b[32m     85\u001b[39m         enabled=pipeline_options.do_table_structure,\n\u001b[32m     86\u001b[39m         artifacts_path=artifacts_path,\n\u001b[32m     87\u001b[39m         options=pipeline_options.table_structure_options,\n\u001b[32m     88\u001b[39m         accelerator_options=pipeline_options.accelerator_options,\n\u001b[32m     89\u001b[39m     ),\n\u001b[32m     90\u001b[39m     \u001b[38;5;66;03m# Page assemble\u001b[39;00m\n\u001b[32m     91\u001b[39m     PageAssembleModel(options=PageAssembleOptions()),\n\u001b[32m     92\u001b[39m ]\n\u001b[32m     94\u001b[39m \u001b[38;5;66;03m# Picture description model\u001b[39;00m\n\u001b[32m     95\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m (\n\u001b[32m     96\u001b[39m     picture_description_model := \u001b[38;5;28mself\u001b[39m.get_picture_description_model(\n\u001b[32m     97\u001b[39m         artifacts_path=artifacts_path\n\u001b[32m     98\u001b[39m     )\n\u001b[32m     99\u001b[39m ) \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Development/fastmoatless/.venv/lib/python3.12/site-packages/docling/models/layout_model.py:54\u001b[39m, in \u001b[36mLayoutModel.__init__\u001b[39m\u001b[34m(self, artifacts_path, accelerator_options)\u001b[39m\n\u001b[32m     51\u001b[39m device = decide_device(accelerator_options.device)\n\u001b[32m     53\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m artifacts_path \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m54\u001b[39m     artifacts_path = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mdownload_models\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m / \u001b[38;5;28mself\u001b[39m._model_path\n\u001b[32m     55\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m     56\u001b[39m     \u001b[38;5;66;03m# will become the default in the future\u001b[39;00m\n\u001b[32m     57\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m (artifacts_path / \u001b[38;5;28mself\u001b[39m._model_repo_folder).exists():\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Development/fastmoatless/.venv/lib/python3.12/site-packages/docling/models/layout_model.py:89\u001b[39m, in \u001b[36mLayoutModel.download_models\u001b[39m\u001b[34m(local_dir, force, progress)\u001b[39m\n\u001b[32m     87\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m progress:\n\u001b[32m     88\u001b[39m     disable_progress_bars()\n\u001b[32m---> \u001b[39m\u001b[32m89\u001b[39m download_path = \u001b[43msnapshot_download\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m     90\u001b[39m \u001b[43m    \u001b[49m\u001b[43mrepo_id\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mds4sd/docling-models\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     91\u001b[39m \u001b[43m    \u001b[49m\u001b[43mforce_download\u001b[49m\u001b[43m=\u001b[49m\u001b[43mforce\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     92\u001b[39m \u001b[43m    \u001b[49m\u001b[43mlocal_dir\u001b[49m\u001b[43m=\u001b[49m\u001b[43mlocal_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     93\u001b[39m \u001b[43m    \u001b[49m\u001b[43mrevision\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mv2.1.0\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m     94\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     96\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m Path(download_path)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Development/fastmoatless/.venv/lib/python3.12/site-packages/huggingface_hub/utils/_validators.py:114\u001b[39m, in \u001b[36mvalidate_hf_hub_args.<locals>._inner_fn\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m    111\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m check_use_auth_token:\n\u001b[32m    112\u001b[39m     kwargs = smoothly_deprecate_use_auth_token(fn_name=fn.\u001b[34m__name__\u001b[39m, has_token=has_token, kwargs=kwargs)\n\u001b[32m--> \u001b[39m\u001b[32m114\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Development/fastmoatless/.venv/lib/python3.12/site-packages/huggingface_hub/_snapshot_download.py:155\u001b[39m, in \u001b[36msnapshot_download\u001b[39m\u001b[34m(repo_id, repo_type, revision, cache_dir, local_dir, library_name, library_version, user_agent, proxies, etag_timeout, force_download, token, local_files_only, allow_patterns, ignore_patterns, max_workers, tqdm_class, headers, endpoint, local_dir_use_symlinks, resume_download)\u001b[39m\n\u001b[32m    146\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    147\u001b[39m     \u001b[38;5;66;03m# if we have internet connection we want to list files to download\u001b[39;00m\n\u001b[32m    148\u001b[39m     api = HfApi(\n\u001b[32m    149\u001b[39m         library_name=library_name,\n\u001b[32m    150\u001b[39m         library_version=library_version,\n\u001b[32m   (...)\u001b[39m\u001b[32m    153\u001b[39m         headers=headers,\n\u001b[32m    154\u001b[39m     )\n\u001b[32m--> \u001b[39m\u001b[32m155\u001b[39m     repo_info = \u001b[43mapi\u001b[49m\u001b[43m.\u001b[49m\u001b[43mrepo_info\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrepo_id\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrepo_id\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrepo_type\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrepo_type\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mrevision\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrevision\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtoken\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtoken\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    156\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m (requests.exceptions.SSLError, requests.exceptions.ProxyError):\n\u001b[32m    157\u001b[39m     \u001b[38;5;66;03m# Actually raise for those subclasses of ConnectionError\u001b[39;00m\n\u001b[32m    158\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Development/fastmoatless/.venv/lib/python3.12/site-packages/huggingface_hub/utils/_validators.py:114\u001b[39m, in \u001b[36mvalidate_hf_hub_args.<locals>._inner_fn\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m    111\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m check_use_auth_token:\n\u001b[32m    112\u001b[39m     kwargs = smoothly_deprecate_use_auth_token(fn_name=fn.\u001b[34m__name__\u001b[39m, has_token=has_token, kwargs=kwargs)\n\u001b[32m--> \u001b[39m\u001b[32m114\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Development/fastmoatless/.venv/lib/python3.12/site-packages/huggingface_hub/hf_api.py:2734\u001b[39m, in \u001b[36mHfApi.repo_info\u001b[39m\u001b[34m(self, repo_id, revision, repo_type, timeout, files_metadata, expand, token)\u001b[39m\n\u001b[32m   2732\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m   2733\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mUnsupported repo type.\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m-> \u001b[39m\u001b[32m2734\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mmethod\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   2735\u001b[39m \u001b[43m    \u001b[49m\u001b[43mrepo_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2736\u001b[39m \u001b[43m    \u001b[49m\u001b[43mrevision\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrevision\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2737\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtoken\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtoken\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2738\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2739\u001b[39m \u001b[43m    \u001b[49m\u001b[43mexpand\u001b[49m\u001b[43m=\u001b[49m\u001b[43mexpand\u001b[49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# type: ignore[arg-type]\u001b[39;49;00m\n\u001b[32m   2740\u001b[39m \u001b[43m    \u001b[49m\u001b[43mfiles_metadata\u001b[49m\u001b[43m=\u001b[49m\u001b[43mfiles_metadata\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2741\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Development/fastmoatless/.venv/lib/python3.12/site-packages/huggingface_hub/utils/_validators.py:114\u001b[39m, in \u001b[36mvalidate_hf_hub_args.<locals>._inner_fn\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m    111\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m check_use_auth_token:\n\u001b[32m    112\u001b[39m     kwargs = smoothly_deprecate_use_auth_token(fn_name=fn.\u001b[34m__name__\u001b[39m, has_token=has_token, kwargs=kwargs)\n\u001b[32m--> \u001b[39m\u001b[32m114\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Development/fastmoatless/.venv/lib/python3.12/site-packages/huggingface_hub/hf_api.py:2520\u001b[39m, in \u001b[36mHfApi.model_info\u001b[39m\u001b[34m(self, repo_id, revision, timeout, securityStatus, files_metadata, expand, token)\u001b[39m\n\u001b[32m   2518\u001b[39m r = get_session().get(path, headers=headers, timeout=timeout, params=params)\n\u001b[32m   2519\u001b[39m hf_raise_for_status(r)\n\u001b[32m-> \u001b[39m\u001b[32m2520\u001b[39m data = \u001b[43mr\u001b[49m\u001b[43m.\u001b[49m\u001b[43mjson\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   2521\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m ModelInfo(**data)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Development/fastmoatless/.venv/lib/python3.12/site-packages/requests/models.py:978\u001b[39m, in \u001b[36mResponse.json\u001b[39m\u001b[34m(self, **kwargs)\u001b[39m\n\u001b[32m    974\u001b[39m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m complexjson.loads(\u001b[38;5;28mself\u001b[39m.text, **kwargs)\n\u001b[32m    975\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m JSONDecodeError \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[32m    976\u001b[39m     \u001b[38;5;66;03m# Catch JSON-related errors and raise as requests.JSONDecodeError\u001b[39;00m\n\u001b[32m    977\u001b[39m     \u001b[38;5;66;03m# This aliases json.JSONDecodeError and simplejson.JSONDecodeError\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m978\u001b[39m     \u001b[38;5;28;01m<PERSON>se\u001b[39;00m RequestsJSONDecodeError(e.msg, e.doc, e.pos)\n", "\u001b[31mJSONDecodeError\u001b[39m: Expecting value: line 1 column 1 (char 0)"]}], "source": ["from docling.document_converter import DocumentConverter\n", "from docling.datamodel.pipeline_options import PdfPipelineOptions\n", "from docling.document_converter import DocumentConverter, PdfFormatOption, InputFormat\n", "from docling.backend.docling_parse_v2_backend import DoclingParseV2DocumentBackend\n", "\n", "pipeline_options = PdfPipelineOptions()\n", "pipeline_options.do_ocr = False # pick what you need\n", "pipeline_options.do_table_structure = True # pick what you need\n", "\n", "doc_converter = DocumentConverter(\n", "    format_options={\n", "        InputFormat.PDF: PdfFormatOption(pipeline_options=pipeline_options, backend=DoclingParseV2DocumentBackend)  # switch to beta PDF backend\n", "        }\n", ")\n", "conv_result = doc_converter.convert(\"data/test/pdf/TxGemma.pdf\")\n", "\n", "print(conv_result.document.export_to_markdown())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the htmls from the folder"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["from openai import OpenAI\n", "from gemini_ocr import process_pdf_content\n", "\n", "import os\n", "from dotenv import load_dotenv, find_dotenv\n", "load_dotenv(find_dotenv())\n", "\n", "client = OpenAI(\n", "    api_key=os.getenv(\"GEMINI_API_KEY\"), \n", "    base_url=os.getenv(\"GEMINI_BASE_URL\")\n", "    )"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["chunks = await process_pdf_content(client=client, abs_url=\"https://arxiv.org/pdf/2504.06196\", full_ocr=False)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["('--- Page 1 ---\\n<chunk>\\nTherapeutic development is a costly and high-risk endeavor that is often plagued by high failure rates. To\\naddress this, we introduce TxGemma, a suite of efficient, generalist large language models (LLMs) capable\\nof therapeutic property prediction as well as interactive reasoning and explainability. Unlike task-specific\\nmodels, TxGemma synthesizes information from diverse sources, enabling broad application across the\\ntherapeutic development pipeline. The suite includes 2B, 9B, and 27B parameter models, fine-tuned from\\nGemma-2 on a comprehensive dataset of small molecules, proteins, nucleic acids, diseases, and cell lines.\\nAcross 66 therapeutic development tasks, TxGemma achieved superior or comparable performance to the\\nstate-of-the-art generalist model on 64 (superior on 45), and against state-of-the-art specialist models on\\n50 (superior on 26). Fine-tuning TxGemma models on therapeutic downstream tasks, such as clinical trial\\nadverse event prediction, requires less training data than fine-tuning base LLMs, making TxGemma suitable\\nfor data-limited applications. Beyond these predictive capabilities, TxGemma features conversational\\nmodels that bridge the gap between general LLMs and specialized property predictors. These allow\\nscientists to interact in natural language, provide mechanistic reasoning for predictions based on molecular\\nstructure, and engage in scientific discussions. Building on this, we further introduce Agentic-Tx, a\\ngeneralist therapeutic agentic system powered by Gemini 2.5 that reasons, acts, manages diverse workflows,\\nand acquires external domain knowledge. Agentic-Tx surpasses prior leading models on the Humanity\\'s\\nLast Exam benchmark (Chemistry & Biology) with 52.3% relative improvement over 03-mini (high) and\\n26.7% over 03-mini (high) on GPQA (Chemistry). On ChemBench, TxGemma excels with improvements\\nof 6.3% (ChemBench-Preference) and 2.4% (ChemBench-Mini) over 03-mini (high), as well as 17.7% and\\n5.6% over ol, respectively. TxGemma\\'s collection is released as open models, enabling researchers to adapt\\nand validate it on their own diverse datasets, thus facilitating more challenging real-world applications.\\n</chunk>\\n\\n<chunk>\\n1 Introduction\\nThe pharmaceutical industry faces significant challenges in bringing new therapeutics to market. High\\nattrition rates and lengthy, costly development timelines [3, 4] necessitate innovative approaches to therapeutic\\ndevelopment. Success requires a drug candidate to not only demonstrate efficacy but also possess favorable\\nsafety, metabolic stability, pharmacokinetic/pharmacodynamic properties and developability, among other\\ncharacteristics. Determining these diverse characteristics often relies on a large array of complex and expensive\\nexperimental procedures, highlighting the need for more efficient methods.\\n</chunk>\\n\\n<chunk>\\nComputational approaches, such as machine learning, are emerging as powerful tools to address these challenges.\\nLeveraging predictive models trained on curated datasets allows researchers to prioritize promising candidates\\nearly in the development process, reducing reliance on costly experimental assays [5]. Publicly available\\ndatabases of molecular properties and biological activity are crucial for training and validating these models.\\nIn this area, a major development was the curation of the Therapeutics Data Commons (TDC) [6, 7, 8], which\\ncontains datasets and benchmarks for many different tasks throughout the therapeutic development pipeline,\\nranging from early-stage target identification to late-stage clinical trial approval.\\nRecent advancements in large language models (LLMs) offer a compelling opportunity to leverage available\\ndatasets and address limitations in the therapeutic development process. LLMs have demonstrated the capacity\\nto integrate and learn from diverse data sources across various domains, including scientific applications [9, 10,\\n</chunk>\\n\\n--- Page 2 ---\\n<chunk>\\n<html>\\n<body>\\n<h1>TxGemma Overview</h1>\\n\\n<p>The image provides an overview of TxGemma, a system designed for therapeutic development tasks. It highlights the different components and their interactions within the system.</p>\\n\\n<h2>Components of TxGemma:</h2>\\n<ul>\\n    <li><b>Gemma:</b> Serves as the foundational model.</li>\\n    <li><b>Predict:</b> A module focused on therapeutic tasks, available in three sizes: 2B, 9B, and 27B.</li>\\n    <li><b>Chat:</b> A module for advanced reasoning, available in 9B and 27B sizes.</li>\\n    <li><b>Agentic-Tx:</b> A therapeutics-focused agentic system that utilizes TxGemma-Predict and TxGemma-Chat, equipped with 18 tools for knowledge collection and task management.</li>\\n</ul>\\n\\n<h2>Agentic-Tx Workflow:</h2>\\n<p>Agentic-Tx follows a structured workflow:</p>\\n<ol>\\n    <li>Receives a <b>Question</b>.</li>\\n    <li>Takes <b>Action</b>, using tools like Molecule Search, Gene Sequence analysis, and PubMed Search.</li>\\n    <li>Provides a <b>Reason</b> for the action taken.</li>\\n    <li>Generates a <b>Response</b> based on the information gathered.</li>\\n    <li>Incorporates input from a <b>Chemist/Scientist</b>, who can Review, Add ideas, and Add guidance.</li>\\n</ol>\\n</body>\\n</html>\\n</chunk>\\n\\n<chunk>\\n<html>\\n<body>\\n<h2>Data Sources and Training</h2>\\n<p>TxGemma variants are trained on diverse data from the Therapeutic Data Commons (TDC). TxGemma-Chat is trained on TDC data combined with general Gemma-2 instruction tuning data to enable conversational and reasoning abilities.</p>\\n\\n<h2>Performance Evaluation</h2>\\n<p>The image includes visualizations of TxGemma\\'s performance on various tasks:</p>\\n<ul>\\n    <li><b>Relative Improvement:</b> Bar charts compare the relative performance of TxGemma-Predict against the SOTA generalist model across several tasks like Drug-target interaction, Gene-disease association, and CRISPR Repair.</li>\\n    <li><b>Absolute Performance:</b> Agentic-Tx\\'s performance is benchmarked on ChemBench-Mini, ChemBench (Chemical Preference), GPQA Diamond (Chemistry), and Humanity\\'s Last Exam (Chem & Bio MCQA). These charts compare Agentic-Tx (Gemini 2.5-pro) with other models like 03-mini, Claude-3.5 (Sonnet), and GPT-4o, along with human performance benchmarks.</li>\\n    <li><b>Overall Results:</b> A bar chart summarizes the number of therapeutic TDC tasks where TxGemma-Predict outperforms or nearly matches SOTA, and where it significantly outperforms SOTA.</li>\\n</ul>\\n</body>\\n</html>\\n</chunk>\\n\\n<chunk>\\n<html>\\n<body>\\n<h2>Figure 1 Description</h2>\\n<p><b>Figure 1 | Overview of TxGemma.</b> (top) All TxGemma variants are trained on diverse data sources of the Therapeutic Data Commons (TDC). TxGemma-Predict comes in three size variants (2B, 9B, and 27B) and is trained for high-performance predictions on a broad set of therapeutic development tasks. TxGemma-Chat features two variants (9B and 27B) and is trained on a combination of TDC data with general Gemma-2 instruction tuning data to retain conversational and reasoning capabilities. Agentic-Tx, a therapeutics-focused agentic system powered by Gemini 2.5, has access to 18 tools including TxGemma-Predict and TxGemma-Chat to collect external knowledge and manages complex tasks in either autonomous or interactive settings. (bottom-right) Absolute performance of Agentic-Tx compared to best-in-class models on three complex therapeutic-related reasoning benchmarks. The state-of-the-art (SOTA) values are obtained from [1, 2] and details are listed in Table 3. Dashed lines: L=lowest, M=mean, H=highest human scores. (bottom-left) Relative performance changes of TxGemma-Predict compared to the SOTA generalist model for each task type. The assignment of the 66 evaluated TDC tasks to task types is shown in Tables S.2 and S.3. The bottom bar chart shows a summary of results where TxGemma-Predict outperforms or nearly matches SOTA (light blue), and outperforms SOTA (darker blue).</p>\\n</body>\\n</html>\\n</chunk>\\n\\n--- Page 3 ---\\n<chunk>\\n In this work, we introduce TxGemma, a suite of efficient, generalist LLMs trained for therapeutics. Building\\non, but significantly extending, our previous work [12], TxGemma leverages LLMs to synthesize information\\nfrom diverse sources. The suite includes 2B, 9B, and 27B parameter models, fine-tuned from Gemma-2 [13, 14]\\nusing a collection of therapeutic instruction-tuning datasets encompassing small molecules, proteins, nucleic\\nacids, diseases, and cell lines. For the first time in therapeutic AI, TxGemma features conversational counter-\\nparts capable of reasoning and explanation, moving beyond black-box predictions to facilitate mechanistic\\nunderstanding and scientific discussions. Our key contributions are as follows:\\n </chunk>\\n<chunk>\\n• Efficient Generalist Therapeutic LLMs: TxGemma represents a potential shift from task-specific AI\\nto efficient generalist models in therapeutic development. These efficient LLMs (2B-27B parameters)\\noffer a competitive alternative to specialized models, achieving strong performance across a broad range\\nof predictive and generative tasks. Out of 66 therapeutic development tasks curated by TDC, TxGemma-\\nPredict outperforms or nearly matches the state-of-the-art generalist model on 64 (outperforms on 45)\\nand state-of-the-art specialist models on 50 (outperforms on 26). Additionally, fine-tuning TxGemma\\nmodels on clinical trial adverse event prediction requires less data to achieve strong performance compared\\nto base Gemma-2 models, an important advantage for data-limited fields.\\n </chunk>\\n<chunk>\\n• Explainable and Interactive Therapeutic Models: TxGemma-Chat introduces reasoning and\\nexplanation capabilities, bridging the gap between general LLMs and specialized property predictors.\\nScientists can interact with TxGemma-Chat using natural language, exploring complex questions, receive\\nexplanations for predictions (e.g., based on molecular structure), and engage in scientific discussions.\\n\\n• Agentic Orchestration of Therapeutic Development Workflows: We further introduce Agentic-Tx,\\na therapeutics-focused agentic system powered by Gemini 2.5, demonstrating how TxGemma models\\ncan be integrated as tools. Equipped with 18 tools, Agentic-Tx solves complex, multi-step problems,\\nachieving state-of-the-art results on reasoning-intensive chemistry and biology benchmarks, including\\nHumanity\\'s Last Exam [15] and ChemBench [1].\\n\\n• Enabling Innovative Research with Open Models: Understanding the prevalence of proprietary\\ndata in therapeutic research, we release TxGemma models trained only on datasets with commercial\\nlicenses as open models to empower researchers to adapt and refine them on their own data. This\\nfacilitates validation and potential performance improvements tailored to their specific research needs,\\npaving the way for therapy safety and efficacy in more challenging real-world therapeutic applications.\\n</chunk>\\n<chunk>\\n2 Methods\\n2.1 Data\\n\\nTherapeutic Data Commons (TDC) We leverage the Therapeutic Data Commons (TDC) [7, 6], a\\ncomprehensive collection of 66 AI-ready datasets spanning the drug discovery and development pipeline.\\nTDC includes over 15 million datapoints across various biomedical entities and encompasses single-instance\\nprediction, multi-instance prediction, and generation tasks [7]. We focus on TDC tasks relevant to drug\\ndiscovery, incorporating diverse therapeutic representations: SMILES strings (small molecules), amino acid\\nsequences (proteins and peptides, including specialized representations for MHC molecules and T-cell receptors),\\nnucleotide sequences (nucleic acids), and natural language text (disease/cell line names) (see Table S.6 for\\nexamples). Many tasks combine multiple representations. (See Table S.1 for task inclusion criteria and\\nTables S.7 and S.8 for biological contexts of certain tasks.)\\n\\nTherapeutic Instruction-Tuning Following Chaves et al. [12], we transform the raw TDC data into an\\ninstruction-tuning format suitable for LLMs. Each data point is formatted as a prompt:\\n</chunk>\\n\\n--- Page 4 ---\\n<chunk>\\n• **Instruction:** Briefly describes the task.\\n• **Context:** Provides 2-3 sentences of relevant biochemical background, derived from TDC descriptions\\nand literature.\\n• **Question:** Queries a specific therapeutic property, incorporating textual representations of therapeutics\\nand/or targets (e.g., \"Does the following molecule cross the blood-brain barrier? <molecule>\").\\n• **Answer:** Formatted as (A)/(B) for binary classification, a binned continuous value for regression, or a\\nSMILES string for generation.\\n\\nThis process yields 7,080,338 training, 956,575 validation, and 1,917,297 test data points (Figure S.1, Tables S.2\\nand S.3). Data splits closely follow TDC\\'s recommended methodologies (random, scaffold, cold-start,\\ncombination, temporal) (Table S.2, Table S.3). Detailed task descriptions are in Tables S.4 and S.5.\\n\\nWe employ a few-shot prompting strategy to promote in-context learning [16], using a blend of 70% zero-shot\\nand 30% few-shot prompts [17, 12]. For few-shot prompts, we randomly sample examples from the training set\\n(Table S.9), as intra-training set similarity is higher than training-test set similarity (Figure S.2). The number\\nof examples is uniformly selected between 1 and 10 so that few-shot prompting is robust to the number of\\nexamples during evaluation.\\n</chunk>\\n\\n<chunk>\\n## 2.2 Modeling\\n\\n**Base LLM.** TxGemma is built upon the Gemma-2 [14] family of lightweight, state-of-the-art open LLMs.\\nGemma-2 models utilize a decoder-only transformer architecture, incorporating architectural modifications such\\nas interleaved local-global attention and group-query attention, and are trained using Gemini technology [18].\\nWe utilize Gemma-2 base models at 2B, 9B, and 27B parameters. 2B and 9B Gemma-2 models were initially\\ntrained via knowledge distillation [14].\\n\\n**Predictive Model Fine-Tuning.** We fine-tune the 2B, 9B, and 27B Gemma-2 base models on the\\ntherapeutic instruction-tuning data derived from TDC, creating TxGemma-2B-Predict, TxGemma-9B-Predict,\\nand TxGemma-27B-Predict, respectively. Training was performed across all TDC tasks, with mixture ratios\\nproportional to the number of training data points (see Tables S.2 and S.3 for data distribution). This\\nencompassed all approximately 7 million training examples, comprising 3.3 million from regression/generation\\nand 3.7 million from binary classification tasks. Fine-tuning proceeded for 67B tokens (12 epochs) using 256\\nTPUv4 chips with 8-way data replication, 4-way sequence sharding, and 4-way model sharding. In this work,\\n\"TxGemma\" generally refers to the generalist, predictive TxGemma-27B-Predict.\\n\\n**Conversational Model Fine-Tuning.** We also trained conversational counterparts, TxGemma-9B-Chat\\nand TxGemma-27B-Chat, by supplementing the therapeutic instruction-tuning data with general instruction-\\ntuning data, as detailed in the Gemma-2 report [14]. The training data mixture comprised 30% therapeutic\\ndata and 70% general instruction-tuning data. Conversational models were trained using the same number of\\ntokens and TPU configuration as the predictive models.\\n</chunk>\\n\\n<chunk>\\n## 2.3 Evaluating Predictive Performance\\n\\nPrompting strategy For test set evaluations, we use 10-shot prompting, selecting exemplars from the nearest\\nneighbors within the combined training and validation set (not the test set), as detailed in Table S.9. Nearest\\nneighbors were determined using different methods based on molecule type. For small molecules, we used\\nRDKit [19] to generate Morgan fingerprints (radius 2 and size 2048), representing molecular substructures\\nas binary vectors. Subsequently, we used Chemfp [20] to compute Tanimoto similarities, which quantify\\nfingerprint overlap. For amino acid and nucleotide sequences, nearest neighbors were defined by percent\\nsequence identity, determined through multiple sequence alignments performed with Clustal Omega [21].\\n\\nPerformance Metrics and Statistical Tests We assess performance using the preferred metrics for each\\ntask, as defined by TDC [7] and used by previous models. Binary classification tasks are assessed with area\\nunder the receiver operating characteristic curve (AUROC), area under the precision-recall curve (AUPRC),\\nand accuracy. Regression tasks use Spearman\\'s and Pearson correlation coefficients, mean absolute error\\n(MAE), and mean squared error (MSE). The USPTO generation task uses \"set accuracy,\" scoring 1 for perfect\\noverlap between predicted and true reactant sets, and 0 otherwise. Bootstrapped metrics are calculated\\n</chunk>\\n\\n--- Page 5 ---\\n<chunk>\\nFigure 2 illustrates an example workflow of agentic planning and execution using Agentic-Tx. Agentic-Tx leverages the ReAct framework [22] to seamlessly integrate reasoning with tool utilization. When a user initiates a query, Agentic-Tx assesses whether the query\\'s structure aligns with any predefined tool triggers. If a match is found, the query is directed to the appropriate tool, which (i) parses the request, (ii) executes specialized logic, and (iii) provides a structured response back to the agent. The agent then formulates a user-facing reply. This adaptive approach to tool usage is particularly valuable for tasks demanding external references, transformations of chemical data, or precise chemical information, areas where self-contained LLMs often struggle and may hallucinate. The example shown demonstrates Agentic-Tx employing two distinct tools to tackle a complex therapeutic challenge: TxGemma-Chat and the clinical toxicity prediction tool, TxGemma-Predict.\\n</chunk>\\n\\n<chunk>\\nTo comprehensively assess the performance of the models, 1000 samples were used. To compare overall performance between two models across all TDC tasks, we use the non-parametric Wilcoxon signed-rank test and report the corresponding p-value (details in Appendix C.1).\\n</chunk>\\n\\n<chunk>\\n## 2.4 Agentic System\\n\\nA key limitation of LLMs in the domain of drug discovery lies in their reliance on static knowledge. While their predictive capabilities are substantial, they often lack access to real-time, up-to-date external knowledge sources, such as the latest research articles and domain-specific prediction models. These inherent knowledge limitations can hinder the model\\'s ability to address queries that extend beyond its predefined training scope. Moreover, certain intricate questions necessitate multiple iterative reasoning steps to arrive at a solution. For instance, answering a question like \"What structural modifications could improve the potency of the given drug?\" requires iteratively exploring the drug\\'s structural space and then prompting TxGemma to predict potency.\\n</chunk>\\n\\n<chunk>\\nAgentic-Tx, a therapeutics-focused agentic system powered by Gemini 2.5 [18], enhances TxGemma\\'s capabilities by orchestrating complex workflows. Agentic-Tx employs a modular, tool-usage paradigm, in contrast to TxGemma\\'s direct generation of solutions. Agentic-Tx utilizes the ReAct framework [22], allowing it to interleave reasoning steps (\"thoughts\") with actions (tool use). The agentic system receives a task or question and iteratively takes actions based on its current context. Each action typically involves using a tool, which\\n</chunk>\\n\\n--- Page 6 ---\\n<chunk>\\nAgentic-Tx returns an observation. Key to ReAct is this iterative process of observing, reasoning, and acting, allowing Agentic-Tx to dynamically adjust its approach based on the information it gathers. Because tools may return large outputs, we summarize these observations in order to maintain a concise and relevant context. This iterative process of observing, reasoning, acting, and updating its context allows Agentic-Tx to dynamically adjust its approach and gather the necessary information to answer the initial query. Finally, Agentic-Tx integrates the gathered information and formulates a user-friendly response.\\n\\nAgentic Tools Agentic-Tx is equipped with 18 tools across four categories (detailed tool descriptions are in Table S.12). They can be broadly categorized as:\\n\\n1.  **TxGemma-based Tools:** These provide access to TxGemma\\'s capabilities. The *Chat* tool enables interaction with TxGemma-27B-Chat. The *ClinicalTox* and *ToxCast* tools utilize TxGemma-27B-Predict for toxicity predictions. *IC50* returns the predicted normalized IC50 between a drug and protein, the *Mutagenicity* tool predicts drug mutagenicity, and the *Phase1 Trial* tool predicts whether a drug would pass a Phase 1 clinical trial.\\n2.  **General Tools:** These query external knowledge resources, including PubMed, Wikipedia, and the web.\\n3.  **Molecule Tools:** These leverage domain-specific libraries for tasks such as retrieving molecular descriptors (e.g., from PubChem) and performing chemical structure conversions.\\n4.  **Gene & Protein Tools:** These leverage domain-specific libraries for tasks involving genes or proteins, such as retrieving gene descriptions and protein descriptions (e.g., from the NCBI Gene database).\\n</chunk>\\n\\n<chunk>\\n3 Results\\n\\n3.1 TxGemma Predictive Performance\\n\\n3.1.1 Comparison with best-in-class therapeutic models\\n\\nTo provide a comprehensive evaluation of our models\\' predictive capabilities, we benchmark against both specialist and generalist baselines. For specialist comparisons, we define best-in-class performance metrics for each task using previous models. Specifically, we utilize TDC leaderboard scores for tasks where available (ADMET, DrugCombo, DTI DG). For remaining tasks, values are reported from a literature review and are detailed in Tables S.13 and S.14. These specialist performance values align with those reported in Chaves et al. [12]. Additionally, we compare our models against three prominent therapeutic generalist and multi-task models: Tx-LLM [12], LlaSMol [23], and MolE [24]. Tx-LLM, with its two size-variants S and M, shares similar training data to our approach enabling a direct comparison across all tasks. LlaSMol a suite of generalist models built upon fine-tuned open-source LLMs trained for small-molecule applications [23]. Similarly, MolE was developed as a graph-based multi-task foundation model for small molecules. LlaSMol and MolE, specialized for small molecules, offer strong baselines for small molecule tasks.\\n</chunk>\\n\\n<chunk>\\nTxGemma shows improved performance compared to therapeutic generalist models In Figure 3, we compare the performance of TxGemma-27B-Predict to the two existing models in the Tx-LLM [12] family, Tx-LLM M and Tx-LLM S, built over PaLM-2 on TDC tasks. TxGemma-27B-Predict surpasses Tx-LLM M on 45 out of 66 tasks, while underperforming on 21. In addition, it outperforms Tx-LLM S on 62 and underperforms Tx-LLM S on only 4. Aggregating performance over task, we observe a statistically significant improvement of TxGemma-27B-Predict over Tx-LLM models (p=0.003, Wilcoxon signed-rank test). These results demonstrate that TxGemma provides a highly competitive alternative to its predecessor with improved functionality at a substantially reduced model size.\\n</chunk>\\n\\n<chunk>\\nTxGemma is competitive with specialist therapeutic models Figure 4 and Figure S.4 compare TxGemma\\'s performance with best-in-class specialist model across tasks containing various combinations of SMILES, amino acid, nucleotide, and text inputs. In a comparison with specialist best-in-class models, TxGemma-27B-Predict outperforms the state-of-the-art (SOTA) on 26 and performs near SOTA on 50. This is a substantial improvement over its predecessor Tx-LLM M, which outperformed SOTA on 22 tasks and near SOTA on 43. These results demonstrate the improved capabilities of TxGemma-27B-Predict and its competitiveness with current specialist models designed for specific tasks and therapeutic feature types.\\n</chunk>\\n\\n--- Page 7 ---\\n<chunk>\\nFigure 3 | Comparison of TxGemma-Predict\\'s performance with therapeutic generalist models. (top)\\nrelative performance improvement of TxGemma-27B-Predict in comparison to Tx-LLM S. TxGemma-27B-Predict\\noutperforms Tx-LLM S on 62 and underperforms on only 4. (bottom) relative performance improvement of TxGemma-\\n27B-Predict in comparison to Tx-LLM M. TxGemma-27B-Predict outperforms TX-LLM M on 45 out of 66 tasks, while\\nunderperforming on 21. When aggregating performance over task, we observe a net improvement of TxGemma-27B-\\nPredict over Tx-LLM models, with a statistically significant difference (p=0.003, Wilcoxon signed-rank test). These\\nresults establish TxGemma-27B-Predict as a competitive and functionally enhanced alternative at practical model\\nsizes. Values for each task can be found in Tables S.15 and S.16.\\n\\n</chunk>\\n<chunk>\\nTxGemma performs similarly to multi-task models specialized for small molecules Table 1\\nand Figure S.6 compare the predictive performance of TxGemma-27B-Predict with MolE, a graph-based\\nmulti-task foundation model for small molecules. MolE performs within the 95% CIs of TxGemma-27B-Predict\\nfor 15 out of 22 tasks. Furthermore, both TxGemma-27B-Predict and TxGemma-9B-Predict outperform\\nLlaSMolMistral (7B), the top performing model from the LlaSMol suite, on 2 of 5 shared tasks and within\\n95% CIs on 2 additional tasks (Table 2 and Figure S.5). All metrics for MolE and LlaSMol are reported\\nfrom Mendez-Lucio et al. [24] and Yu et al. [23]. Given their specialization in small-molecule tasks, LlaSMol\\nand MolE provide strong baselines for evaluating generalist models. Notably, TxGemma, a generalist model\\nencompassing diverse drug types and many different tasks, achieves competitive performance with these\\ndedicated models designed for a narrower range of small-molecule tasks.\\n</chunk>\\n<chunk>\\n3.2 TxGemma Conversational Capabilities\\n\\nWhile TxGemma-27B-Predict performs well on prediction tasks, training solely on instruction tuning data\\nfor therapeutic properties limits its conversational capacity. TxGemma-27B-Predict can engage in general\\n</chunk>\\n\\n--- Page 8 ---\\n<chunk>\\nFigure 4 presents a comparison of TxGemma\\'s performance against best-in-class specialist models. Specifically, TxGemma-27B-Predict is evaluated across various tasks within the TDC (Therapeutics Data Commons) framework. The performance is contrasted against the corresponding best-in-class competitor for each task. The different panels of the figure display different metrics used to assess the tasks, providing a comprehensive evaluation.\\n\\nThe data points in the figure are color-coded to represent the feature types involved in each task. These feature types include, but are not limited to, SMILES strings, amino acids, nucleotides, and text, or combinations thereof. A legend is provided to map colors to feature types. Furthermore, the size of each marker is indicative of the number of data points present in the task, scaled logarithmically. This visual representation allows for a quick assessment of the scale of the tasks.\\n\\n</chunk>\\n\\n<chunk>\\nThe plots include shaded regions. A larger blue shaded area denotes regions where TxGemma outperforms the best-in-class models. Conversely, a narrower, light-blue shaded region highlights where TxGemma\\'s performance is near that of the best-in-class model, specifically defined as being within 10% of the specialist model\\'s performance. This provides insight into the competitiveness of TxGemma across different tasks. The MAE (Mean Absolute Error) and MSE (Mean Squared Error) values presented are log-transformed. This transformation is applied to account for the magnitudes of the values, which depend on the units of the respective outputs. The generation accuracy metric represents the fraction of correctly generated SMILES strings in the USPTO (United States Patent and Trademark Office) generation task. Additional details regarding values for each task can be found in Tables S.13 and S.14.\\n</chunk>\\n\\n<chunk>\\nBeyond the specific figure, the document touches upon the model\\'s conversational abilities and safety measures. The text mentions that while TxGemma can engage in conversation, its performance diminishes when the prompts deviate significantly from the expected format. Figure S.9 illustrates this decline in TxGemma-27B-Predict\\'s conversational capabilities. To broaden the TxGemma family\\'s capabilities and create a more versatile tool that explains its reasoning, TxGemma-Chat was trained using a combination of therapeutic and general instruction-tuning data, as detailed in Section 2.2. These new conversational capabilities are assessed through standard LLM benchmarks and qualitative examples. The models undergo assurance evaluations based on Gemma-3 [25] to make sure that they comply with safety regulations.\\n</chunk>\\n\\n--- Page 9 ---\\n<chunk>\\n```html\\nTable 1 | **Comparative performance of TxGemma and MolE on small molecule tasks.** Details of the\\npredictive performance of TxGemma-27B-Predict and MolE, a graph-based molecular multi-task foundation model,\\nacross various pharmacokinetics and toxicity tasks. Bold values indicate the best performance for each task. Metrics\\nfor MolE are reported from Mendez-Lucio et al. [24]. TxGemma-27B-Predict values are bootstrapped averages and\\n95% CIs. These pharmacokinetics and toxicity tasks are publicly available in TDC [7].\\n```\\n</chunk>\\n\\n<chunk>\\n```html\\n<table>\\n<thead>\\n<tr>\\n<th>Task Type</th>\\n<th>Task</th>\\n<th>Metric</th>\\n<th>MolE [24]</th>\\n<th>TxGemma-27B-Predict</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n<td rowspan=\"11\">Pharmacokinetics</td>\\n<td>Caco2 Wang</td>\\n<td>MAE (↓)</td>\\n<td><b>0.329</b></td>\\n<td>0.401 (0.358-0.449)</td>\\n</tr>\\n<tr>\\n<td>Lipophilicity AstraZeneca</td>\\n<td>MAE (↓)</td>\\n<td><b>0.406</b></td>\\n<td>0.538 (0.507-0.570)</td>\\n</tr>\\n<tr>\\n<td>Solubility AqSolDB</td>\\n<td>MAE (↓)</td>\\n<td><b>0.776</b></td>\\n<td>0.907 (0.870-0.948)</td>\\n</tr>\\n<tr>\\n<td>PPBR AZ</td>\\n<td>MAE (↓)</td>\\n<td><b>7.229</b></td>\\n<td>9.048 (8.141-10.111)</td>\\n</tr>\\n<tr>\\n<td>HIA Hou</td>\\n<td>AUROC (↑)</td>\\n<td>0.984</td>\\n<td><b>0.988</b> (0.972-0.999)</td>\\n</tr>\\n<tr>\\n<td>Pgp Broccatelli</td>\\n<td>AUROC (↑)</td>\\n<td>0.930</td>\\n<td><b>0.937</b> (0.904-0.964)</td>\\n</tr>\\n<tr>\\n<td>Bioavailability Ma</td>\\n<td>AUROC (↑)</td>\\n<td>0.640</td>\\n<td><b>0.694</b> (0.575-0.801)</td>\\n</tr>\\n<tr>\\n<td>BBB Martins</td>\\n<td>AUROC (↑)</td>\\n<td>0.903</td>\\n<td><b>0.908</b> (0.872-0.938)</td>\\n</tr>\\n<tr>\\n<td>CYP3A4 Substrate CarbonMangels</td>\\n<td>AUROC (↑)</td>\\n<td><b>0.692</b></td>\\n<td>0.691 (0.601-0.784)</td>\\n</tr>\\n<tr>\\n<td>CYP2D6 Veith</td>\\n<td>AUPRC (↑)</td>\\n<td>0.679</td>\\n<td><b>0.683</b> (0.639-0.726)</td>\\n</tr>\\n<tr>\\n<td>CYP3A4 Veith</td>\\n<td>AUPRC (↑)</td>\\n<td><b>0.876</b></td>\\n<td>0.854 (0.836-0.872)</td>\\n</tr>\\n<tr>\\n<td></td>\\n<td>CYP2C9 Veith</td>\\n<td>AUPRC (↑)</td>\\n<td>0.782</td>\\n<td><b>0.798</b> (0.767-0.826)</td>\\n</tr>\\n<tr>\\n<td></td>\\n<td>CYP2D6 Substrate CarbonMangels</td>\\n<td>AUPRC (↑)</td>\\n<td>0.692</td>\\n<td><b>0.711</b> (0.570-0.830)</td>\\n</tr>\\n<tr>\\n<td></td>\\n<td>CYP2C9 Substrate CarbonMangels</td>\\n<td>AUPRC (↑)</td>\\n<td>0.409</td>\\n<td><b>0.438</b> (0.302-0.576)</td>\\n</tr>\\n<tr>\\n<td></td>\\n<td>VDss Lombardo</td>\\n<td>Spearman (↑)</td>\\n<td><b>0.644</b></td>\\n<td>0.559 (0.457-0.655)</td>\\n</tr>\\n<tr>\\n<td></td>\\n<td>Half Life Obach</td>\\n<td>Spearman (↑)</td>\\n<td><b>0.578</b></td>\\n<td>0.458 (0.306-0.594)</td>\\n</tr>\\n<tr>\\n<td></td>\\n<td>Clearance Microsome AZ</td>\\n<td>Spearman (↑)</td>\\n<td><b>0.632</b></td>\\n<td>0.462 (0.353-0.565)</td>\\n</tr>\\n<tr>\\n<td></td>\\n<td>Clearance Hepatocyte AZ</td>\\n<td>Spearman (↑)</td>\\n<td><b>0.456</b></td>\\n<td>0.260 (0.129-0.384)</td>\\n</tr>\\n<tr>\\n<td rowspan=\"4\">Toxicity</td>\\n<td>LD50 Zhu</td>\\n<td>MAE (↓)</td>\\n<td><b>0.602</b></td>\\n<td>0.627 (0.597-0.660)</td>\\n</tr>\\n<tr>\\n<td>hERG</td>\\n<td>AUROC (↑)</td>\\n<td>0.835</td>\\n<td><b>0.885</b> (0.813-0.946)</td>\\n</tr>\\n<tr>\\n<td>AMES</td>\\n<td>AUROC (↑)</td>\\n<td>0.834</td>\\n<td>0.816 (0.795-0.838)</td>\\n</tr>\\n<tr>\\n<td>DILI</td>\\n<td>AUROC (↑)</td>\\n<td>0.852</td>\\n<td><b>0.886</b> (0.810-0.947)</td>\\n</tr>\\n</tbody>\\n</table>\\n```\\n</chunk>\\n\\n<chunk>\\n```html\\nTable 2 | **Comparative performance of TxGemma and LlaSMol on small molecule tasks.** Comparison of\\nTxGemma-27B-Predict with LlaSMolMistral (best LlaSMol model at 7B) across shared small-molecule tasks. Bold values\\nindicate the best performance for each task. Metrics for LlaSMolMistral are reported from Yu et al. [23]. TxGemma-\\nPredict values are bootstrapped averages and 95% CIs. These pharmacokinetics, toxicity, and high-throughput\\nscreening data and tasks are publicly available in TDC [7]\\n```\\n</chunk>\\n\\n<chunk>\\n```html\\n<table>\\n<thead>\\n<tr>\\n<th>Task Type</th>\\n<th>Task</th>\\n<th>Metric</th>\\n<th>LlaSMolMistral [23]</th>\\n<th>TxGemma-27B-Predict</th>\\n<th>TxGemma-9B-Predict</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n<td rowspan=\"3\">Pharmacokinetics</td>\\n<td>BBBP<sup>†</sup></td>\\n<td>Accuracy (↑)</td>\\n<td>0.746</td>\\n<td><b>0.869</b> (0.835-0.901)</td>\\n<td>0.847 (0.813-0.881)</td>\\n</tr>\\n<tr>\\n<td>ESOL<sup>†</sup></td>\\n<td>RMSE (↓)</td>\\n<td><b>1.150</b></td>\\n<td>1.250 (1.185-1.321)</td>\\n<td>1.360 (1.246-1.480)</td>\\n</tr>\\n<tr>\\n<td>Lipo<sup>†</sup></td>\\n<td>RMSE (↓)</td>\\n<td>1.010</td>\\n<td><b>0.710</b> (0.668-0.752)</td>\\n<td>0.742 (0.700-0.787)</td>\\n</tr>\\n<tr><td>Toxicity</td>\\n<td>Clintox</td>\\n<td>Accuracy (↑)</td>\\n<td><b>0.931</b></td>\\n<td>0.926 (0.896-0.956)</td>\\n<td>0.925 (0.892-0.953)</td>\\n</tr>\\n<tr><td>High-throughput screening</td>\\n<td>HIV*</td>\\n<td>Accuracy (↑)</td>\\n<td>0.967</td>\\n<td><b>0.968</b> (0.964-0.972)</td>\\n<td>0.965 (0.961-0.969)</td>\\n</tr>\\n</tbody>\\n</table>\\n```\\n</chunk>\\n\\n<chunk>\\n```html\\n<p>* To predict whether compounds have anti-HIV properties.</p>\\n<p>† Task name is modified to match the nomenclature from Yu et al. [23].</p>\\n```\\n</chunk>\\n\\n<chunk>\\nTxGemma-Chat bridges the gap between property predictors and general language models To\\nassess the performance of TxGemma-Chat as a general conversational LLM, we evaluated it on the Massive\\nMultitask Language Understanding (MMLU) [26] benchmark, a comprehensive suite of 57 diverse tasks\\nspanning mathematics, history, computer science, law, etc. This benchmark evaluates knowledge, reasoning,\\n</chunk>\\n\\n--- Page 10 ---\\n<chunk>\\nFigure 5 **TxGemma-Chat bridges the gap between property predictors and general LLMs.** Each point represents a therapeutic task in the TDC. The figure depicts relative predictive performance changes of TxGemma-Chat in comparison to TxGemma-Predict (top) and Gemma-2 (bottom) for 9B variants **left** and 27B variants in **right**. As expected, TxGemma-27B-Predict outperforms TxGemma-27B-Chat on therapeutic tasks, with TxGemma-27B-Chat showing a 10.69% median relative performance reduction. However, TxGemma-27B-Chat exceeds the Gemma-2-27B baseline by 29.67% on TDC therapeutic tasks. Similarly, TxGemma-9B-Chat\\'s performance is 10.32% lower than TxGemma-9B-Predict\\'s. Values for each task can be found in Tables S.15 and S.16.\\n\\n</chunk>\\n\\n<chunk>\\nand problem-solving abilities across a wide range of academic subjects, providing a measure of overall language understanding. It comprises 14,079 multiple-choice questions, each with four possible answers. For this multiple-choice format, we took the model\\'s prediction as the option with the highest log-likelihood in a zero-shot setting and report overall accuracy as well as per-subject accuracy.\\n\\nFigure S.7 compares the performance of TxGemma-27B-Chat, TxGemma-27B-Predict, and Gemma-2-27B on MMLU, a standard benchmark for evaluating general LLMs. TxGemma-27B-Chat achieves an accuracy of 73.87%, slightly lower than Gemma-2-27B\\'s 75.38%, but TxGemma-27B-Chat shows slight improvements in areas such as medical genetics, high school statistics, and college chemistry. Furthermore, TxGemma-27B-Chat significantly outperforms TxGemma-27B-Predict, which has an accuracy of 53.60%. This suggests that while fine-tuning solely on therapeutic data can diminish general knowledge acquired during pre-training, incorporating general instruction-tuning data can mitigate this effect.\\n\\n</chunk>\\n\\n<chunk>\\nFurthermore, we assess TxGemma-27B-Chat on all therapeutic tasks within TDC. Figure 5 compares the relative performance changes of TxGemma-27B-Chat to TxGemma-27B-Predict and Gemma-2-27B for both 9B and 27B variants across these tasks. As anticipated, TxGemma-27B-Predict outperforms TxGemma-27B-Chat on these predictive tasks, with a median relative performance reduction of 11% observed for TxGemma-27B-Chat. Nevertheless, TxGemma-27B-Chat surpasses the baseline Gemma-2-27B, demonstrating a median relative improvement of 30%. Similarly, TxGemma-9B-Chat shows a 10% median relative performance reduction compared to TxGemma-9B-Predict. Regression tasks experience the greatest performance decline from the general-purpose training. These results demonstrate how TxGemma-Chat bridges the gap between therapeutic property predictors and general LLMs, functioning as a unified model for both capabilities.\\n\\n</chunk>\\n\\n<chunk>\\n**TxGemma-Chat can provide reasoning for complex tasks.** A particularly compelling application of conversational models lies in prompting them to explain their predictions to users. While general LLMs may possess some foundational knowledge concerning therapeutic challenges, they are not accurate for property prediction (Figure 5). In Figure 6, we prompt TxGemma-27B-Chat to answer a question regarding blood-brain barrier permeability using the BBB Martins prompt format. TxGemma-27B-Chat provides only the answer in the initial turn, but when given a subsequent prompt to articulate its rationale, the model provides mechanistic reasoning for its answer based on molecular solubility and the structure of the input molecule derived from the SMILES string. All of this reasoning occurred directly within the model weights, without requiring any preprocessing of the SMILES string.\\nInterestingly, prompting structures enable TxGemma-Chat to provide additional reasoning on complex tasks. For instance, while the relationship between blood-brain barrier permeability and lipophilicity is intuitive, some\\n</chunk>\\n\\n--- Page 11 ---\\n<chunk>\\n```html\\n<table>\\n<caption>Table 3 | Performance of Agentic-Tx. Accuracy of Agentic-Tx compared with SOTA models on ChemBench,\\nGPQA, and HLE benchmarks.</caption>\\n<thead>\\n<tr>\\n<th>Model</th>\\n<th colspan=\"2\">ChemBench</th>\\n<th>GPQA (Diamond)</th>\\n<th>Humanity\\'s Last Exam</th>\\n</tr>\\n<tr>\\n<th></th>\\n<th>Mini</th>\\n<th>Preference</th>\\n<th>Chemistry</th>\\n<th>Chemistry & Biology</th>\\n</tr>\\n</thead>\\n<tbody>\\n<tr>\\n<td>Agentic-Tx (Gemini 2.5-Pro)</td>\\n<td><b>84.5</b></td>\\n<td><b>66.2</b></td>\\n<td><b>81.7</b></td>\\n<td><b>20.1</b></td>\\n</tr>\\n<tr>\\n<td>Agentic-Tx (Gemini 2.0-Pro)</td>\\n<td>83.4</td>\\n<td>65.5</td>\\n<td>62.4</td>\\n<td>14.5</td>\\n</tr>\\n<tr>\\n<td>Agentic-Tx (Gemini 1.5-Pro)</td>\\n<td>80.6</td>\\n<td>65.0</td>\\n<td>51.8</td>\\n<td>11.9</td>\\n</tr>\\n<tr>\\n<td>Claude-3.5 (Sonnet)</td>\\n<td>73.0*</td>\\n<td>60.0*†</td>\\n<td>40.4</td>\\n<td>-</td>\\n</tr>\\n<tr>\\n<td>GPT-4o</td>\\n<td>72.0*</td>\\n<td>59.0*</td>\\n<td>43.8**</td>\\n<td>3.8</td>\\n</tr>\\n<tr>\\n<td>Gemini 2.5-pro</td>\\n<td>82.8</td>\\n<td>65.5</td>\\n<td>79.5</td>\\n<td>17.9</td>\\n</tr>\\n<tr>\\n<td>Gemini 2.0-pro</td>\\n<td>79.6</td>\\n<td>58.4</td>\\n<td>53.3</td>\\n<td>11.1</td>\\n</tr>\\n<tr>\\n<td>Gemini 1.5-pro</td>\\n<td>74.9</td>\\n<td>55.6</td>\\n<td>48.2</td>\\n<td>10.6</td>\\n</tr>\\n<tr>\\n<td>PaperQA2 [28]</td>\\n<td>67.0*</td>\\n<td>56.0*</td>\\n<td>-</td>\\n<td>-</td>\\n</tr>\\n<tr>\\n<td>o1</td>\\n<td>80.0*</td>\\n<td>56.0*</td>\\n<td>64.7**</td>\\n<td>12.3</td>\\n</tr>\\n<tr>\\n<td>o3-mini (medium)</td>\\n<td>82.4</td>\\n<td>61.3</td>\\n<td>62.5</td>\\n<td>13.0</td>\\n</tr>\\n<tr>\\n<td>o3-mini (high)</td>\\n<td>82.5</td>\\n<td>62.0</td>\\n<td>64.5</td>\\n<td>13.2</td>\\n</tr>\\n<tr>\\n<td>Human Expert (Average Performance)</td>\\n<td>27.0</td>\\n<td>-</td>\\n<td>-</td>\\n<td>-</td>\\n</tr>\\n</tbody>\\n<tfoot>\\n<tr>\\n<td colspan=\"5\">(†) Using ReAct framework, (*) Extracted from [1], (**) Extracted from [2]</td>\\n</tr>\\n</tfoot>\\n</table>\\n```\\n</chunk>\\n\\n<chunk>\\ntasks such as predicting clinical trial approval are more challenging to reason over. If TxGemma-27B-Chat\\nis prompted to provide reasoning in the same manner as in Figure 6 for predicting clinical trial approval,\\nTxGemma-27B-Chat refuses and directs the user to alternative sources. However, when modifying the original\\nprompt, instructing the model to output reasoning steps before the final answer, it bypasses the refusal and\\nrestores reasoning capabilities (Figure S.10).\\n</chunk>\\n\\n<chunk>\\n## 3.3 Agentic Planning and Execution based on TxGemma\\n\\nAgentic-Tx demonstrates competitive performance on therapeutic benchmarks. We evaluate the\\ncapability of Agentic-Tx to assist with therapeutics tasks by means of questions from three benchmarks:\\nGPQA (Diamond) [27], ChemBench [1], and Humanity\\'s Last Exam (HLE) [15]. Within each benchmark,\\nwe use existing selections of therapeutic-relevant questions; for GPQA we evaluate GPQA-Chemistry (47\\nquestions), for ChemBench we evaluate ChemBench-Chemical Preference which aims to select an ideal\\ncandidate molecule for therapeutic development (1,001 question) and ChemBench-mini, which evaluates across\\n8 categories of chemistry from toxicity/safety to organic chemistry (236 questions). Finally, for HLE, we\\nevaluate HLE-Chemistry and HLE-Biology (235 questions). For open-ended questions in HLE, we observed a\\nhigh variation of metric scores depending on the selection of the LLM-rater model [15]. To ensure an objective\\naccuracy measure, we restrict the evaluation to multiple choice questions (MCQs).\\n</chunk>\\n\\n<chunk>\\nAs shown in Table 3, Agentic-Tx (Gemini 2.5-Pro), Agentic-Tx (Gemini 2.0-Pro), and Agentic-Tx (Gemini\\n1.5-Pro) achieve competitive or greater accuracy compared to existing SOTA models across several benchmarks.\\nSpecifically, Agentic-Tx (Gemini 2.5-Pro) and Agentic-Tx (Gemini 2.0-Pro) surpasses prior SOTA models on\\nthe exceptionally difficult Humanity\\'s Last Exam benchmark (Chemistry & Biology tasks), with Agentic-Tx\\n(Gemini 2.5-Pro) achieving 52.3% relative improvement over 03-mini (high) and 13.4% over Gemini 2.5-pro, as\\nwell as on ChemBench, with relative improvements of 6.3% (ChemBench-Preference) and 2.4% (ChemBench-\\nMini) over 03-mini (high) and 1.1% (ChemBench-Preference) and 2.0% (ChemBench-Mini) over Gemini\\n2.5-pro. On GPQA (Diamond), Agentic-Tx also achieves SOTA accuracy with 26.7% relative improvements\\nover 03-mini and 2.7% over Gemini 2.5-pro. All variants of Agentic-Tx outperform their corresponding base\\nGemini models across all benchmarks, indicating the effectiveness of the Agentic-Tx framework in enhancing\\nLLMs efficacy for advanced reasoning within this domain. This suggest that agentic workflows such as ours\\nrepresent useful tools for therapeutic development, particularly in areas requiring domain knowledge and\\nthe selection of candidate molecules. The agent\\'s ability to leverage external tools and perform multi-step\\n</chunk>\\n\\n--- Page 12 ---\\n<chunk>\\nInstructions: Answer the following question about drug properties.\\nContext: As a membrane separating circulating blood and brain extracellular fluid, the blood-brain barrier\\n(BBB) is the protection layer that blocks most foreign drugs. Thus the ability of a drug to penetrate the barrier\\nto deliver to the site of action forms a crucial challenge in development of drugs for central nervous system.\\nQuestion: Given a drug SMILES string, predict whether it\\n(A) does not cross the BBB (B) crosses the BBB\\nDrug SMILES:\\nCICN(CCC1(C(=O)NC(CCO)C2=CC=C(C=C2)CI)N)C3=NC=NC4=C3C=CN44\\n\\nTxGemma\\nAnswer: (B)\\n</chunk>\\n\\n<chunk>\\nExplain your reasoning based on the molecule structure.\\n\\nTxGemma\\nThe molecule has several properties that suggest it can cross the blood-brain barrier (BBB):\\nLipophilicity: The molecule contains multiple aromatic rings and a long alkyl chain, contributing to its\\nlipophilicity. Lipophilicity is a key factor in BBB penetration, as the barrier is primarily composed of lipids.\\nMolecular weight: The molecular weight is within a range (around 400 Da) that is generally considered\\nfavorable for BBB permeability.\\nHydrogen bond donors and acceptors: While there are hydrogen bond donors and acceptors present, they\\nare not excessive, which can sometimes hinder BBB passage.\\nLack of charged groups: The molecule lacks any charged groups at physiological pH, making it less likely to be\\nrepelled by the BBB\\'s tight junctions.\\nOverall, the combination of lipophilicity, moderate molecular weight, balanced hydrogen bonding capacity, and\\nlack of charged groups suggests that this molecule has a good chance of crossing the blood-brain barrier.\\n</chunk>\\n\\n<chunk>\\nWhat makes passing through the blood-brain barrier different from passive diffusion through a membrane?\\n\\nTxGemma\\nWhile passive diffusion is a major mechanism for molecules to cross the blood-brain barrier (BBB), there are\\nkey differences that make BBB passage more challenging and selective:\\n\\n<ol>\\n    <li><b>Tight Junctions:</b> The cells forming the BBB are packed very tightly together with specialized structures\\n        called tight junctions. These junctions seal the spaces between cells, significantly reducing the gaps\\n        through which molecules can passively diffuse.</li>\\n    <li><b>Specialized Transport Systems:</b> The BBB has a variety of specialized transport systems that actively\\n        move specific molecules across the barrier. These systems are highly selective and often require\\n        molecules to have specific structural features or be recognized by specific receptors.</li>\\n    <li><b>Efflux Pumps:</b> The BBB also expresses efflux pumps, which are proteins that actively pump molecules\\n        back out of the brain. This mechanism helps to prevent potentially harmful substances from entering the\\n        brain and maintain a stable brain environment.</li>\\n    <li><b>Metabolic Enzymes:</b> The BBB contains enzymes that can metabolize and inactivate certain molecules,\\n        further limiting their entry into the brain.</li>\\n    <li><b>Astrocyte Endfeet:</b> Astrocytes, a type of glial cell, have projections called endfeet that surround blood\\n        vessels in the brain. These endfeet contribute to the BBB\\'s barrier function and regulate the passage of\\n        molecules.</li>\\n</ol>\\n\\nIn summary, while passive diffusion plays a role in BBB permeability, the presence of tight junctions,\\nspecialized transport systems, efflux pumps, metabolic enzymes, and astrocyte endfeet make the BBB a highly\\nselective barrier that restricts the entry of many molecules. This selectivity is crucial for protecting the brain\\nfrom harmful substances and maintaining a stable internal environment, but it also poses challenges for drug\\ndelivery to the central nervous system.\\n</chunk>\\n\\n<chunk>\\nIs my molecule likely to run into these issues?\\n\\nTxGemma\\nLet\\'s analyze how your molecule might be affected by the specific challenges of the BBB ...\\n</chunk>\\n\\n<chunk>\\nFigure 6 | TxGemma-Chat can be prompted to explain its reasoning or provide additional context.\\nFollowing an initial question, TxGemma-27B-Chat is asked to explain its reasoning based on molecule structure in the\\nsecond turn. The model uses its understanding of chemistry and biology to justify its answer and can continually\\nengage with the user on follow-up questions.\\n</chunk>\\n\\n--- Page 13 ---\\n<chunk>\\nreasoning enables it to address more complex queries beyond the scope of traditional LLMs.\\nAgentic-Tx effectively leverages various tools based on the therapeutic task requirement. In\\nFigure S.14, we investigate tool usage frequency within the Agentic-Tx system across the ChemBench-\\nPreference and Biology and Chemistry (B&C) HLE datasets. Our analysis reveals that Agentic-Tx tool usage\\ndistribution varies significantly depending on the task and dataset. For the ChemBench-Preference task, which\\nfocuses on selecting ideal candidate molecules for therapeutic development, the Agentic-Tx system exhibits a\\nhigh frequency of usage for tools such as SMILES description and toxicity prediction. This suggests a strong\\nemphasis on molecular characterization and safety assessment in this task correctly invoked by Agentic-Tx. In\\ncontrast, on the B&C HLE dataset, tool usage is predominantly concentrated on general knowledge retrieval\\ntools like PubMed or Wikipedia search. This indicates that the Agentic-Tx system relies heavily on accessing\\nand synthesizing broad biological or chemical knowledge to address questions in these domains. In Figure S.15,\\nwe investigate the breakdown of tool interactions per question and explore how these interactions contribute\\nto performance variations. Our analysis shows that each question can involve up to 8 tool calls, and the high\\nusage of tools such as SMILES description and toxicity prediction tools correlates with overall performance\\nimprovement. These results highlight the Agentic-Tx system\\'s adaptive nature, demonstrating its ability to\\nleverage different tools based on the specific requirements of the task.\\n</chunk>\\n<chunk>\\nAgentic-Tx inference time is suitable for real time human interaction Analysis of Agentic-Tx\\'s\\ninference time indicates efficient performance characteristics. The median time observed for tool execution\\nis 0.55 seconds. The fastest tool (Gene Sequence) completes execution in 0.15 seconds, while the slowest\\n(ToxCast) requires 28.2 seconds. This suggests that Agentic-Tx operates within a timeframe conducive to\\nreal-time user interaction. The observed latencies demonstrate suitability for integration into workflows where\\nimmediate feedback and responsiveness are desired. The system\\'s ability to maintain a median inference time\\nbelow one second contributes to an efficient user experience.\\n</chunk>\\n<chunk>\\n3.4 Additional Analysis and Ablations\\nData contamination analysis and data leakage considerations To assess potential data contamination\\nfrom the Gemma-2 pretraining data, we calculated the overlap between features in the therapeutic instruction-\\ntuning data and the pretraining corpus. For multi-instance tasks, contamination was defined as the presence\\nof any constituent feature (e.g., drug SMILES or target protein sequence in drug-target binding) in the\\npretraining data. The majority of tasks showed no direct contamination (Figure S.12). For tasks with\\nsome contamination, filtering contaminated datapoints and recalculating TxGemma-27B-Predict performance\\nrevealed no significant changes (Figure S.13).\\n</chunk>\\n<chunk>\\nWhile direct contamination was minimal, we further investigated potential indirect contamination. Although\\nSMILES strings are less common in general web text, pretraining on molecular names could have created\\nlearned associations between names and SMILES, potentially influencing test set performance. To test this, we\\ncompared the similarity of TxGemma-27B-Predict embeddings for PubChem molecules represented as SMILES\\nstrings and their corresponding IUPAC names, against the similarity of embeddings for SMILES strings paired\\nwith decoy (randomly selected, incorrect) names. The similarities were statistically equivalent (Figure S.12),\\nconfirmed by a two one-sided t-test (p = 3 × 10-12, δ = 0.02). This suggests that TxGemma-27B-Predict\\ndid not learn spurious name-SMILES associations during pretraining, likely because names and SMILES\\nwere encountered in separate training phases and for different molecules. Therefore, both direct and indirect\\ncontamination from pretraining are unlikely to significantly affect our results.\\n</chunk>\\n<chunk>\\nFine-tuning TxGemma models improves data efficiency Given the scarcity of therapeutic data and\\nthe potential of TxGemma to serve as a pretrained model for further adaptation, we investigated TxGemma\\'s\\ndata efficiency and generalization to new tasks in out-of-distribution settings. Specifically, we fine-tuned the\\nbaseline model Gemma-2-27B as well as our TxGemma-27B-Predict on adverse event prediction data from\\nTrialBench [29]. Serious adverse events are critical in assessing the safety profile of a new treatment and\\naccurate prediction of these events allows for better risk management and resource allocation [29]. To ensure\\na fair evaluation of generalization, we filtered the TrialBench test set to exclude samples overlapping with\\nphase 1, 2, or 3 of clinical trial outcome prediction data in TDC. In addition, datapoints without available\\nSMILES strings are excluded. This lead to 14,368 train and 3,184 test samples.\\n</chunk>\\n\\n--- Page 14 ---\\n<chunk>\\n```html\\n<figure>\\n    <img src=\"data:image/png;base64,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\\n\\n--- Page 15 ---\\n<chunk>\\nsigned-rank test indicate that model size is a significant factor: TxGemma-27B-Predict outperforms TxGemma-\\n9B-Predict (p = 0.013) and TxGemma-2B-Predict (p = 6.2 × 10-6), and TxGemma-9B-Predict outperforms\\nTxGemma-2B-Predict (p = 0.048). Furthermore, comparing TxGemma models to their corresponding base\\nGemma-2 models reveals the significant impact of domain fine-tuning. All TxGemma models significantly\\noutperform their Gemma-2 counterparts (p < 10-10, Wilcoxon signed-rank test), underscoring the importance\\nof specialized training for therapeutic tasks.\\n</chunk>\\n\\n<chunk>\\n4 Related work\\nTask-specific models for chemistry and therapeutics. In recent years, there has been a surge in the\\ndevelopment of deep learning models designed for various chemistry applications. Amongst those, graph neural\\nnetworks (GNNs) have been applied for a wide variety of molecular prediction or generation tasks because\\nsmall molecules are naturally represented as graphs [30, 31, 32, 33, 34, 35, 36, 37, 24]. Another common\\nrepresentation for small molecules is molecular fingerprints [38], which are binary vectors that capture the\\nlocal environment of each atom [30, 39, 40].\\nTxGNN trained a GNN on medical knowledge graphs in order to perform zero-shot drug repurposing for\\ndiseases with limited treatment options [41]. AlphaFold and its successors have also significantly advanced the\\nfield of protein structure prediction and protein design [42, 43, 44, 45, 46]. These models have been influential\\nfor both mechanistic research and the development of structure-based drugs [47].\\n</chunk>\\n\\n<chunk>\\nLarge language models for biology and chemistry. Transformer-based models [48] have fueled the\\ndevelopment of LLMs, which are trained on massive textual datasets with subsequent instruction-tuning [49] or\\nalignment [50]. LLMs have demonstrated exceptional proficiency in various tasks, including text summarization,\\ntranslation, and question answering [16, 51, 52]. Their ability to encode vast amounts of information and\\ngeneralize to new tasks has sparked considerable interest in their potential applications across diverse domains.\\nThere has been increasing interest in applying the development for LLMs to scientific research. BrainGPT\\nfine-tuned LLMs on neuroscience literature and found greater performance than domain experts [53]. LlaSMol\\nfine-tuned LLMs on small molecule datasets and achieved near-SOTA performance on multiple tasks [23].\\nCLAMP used separate modules for natural language and molecular inputs, combining them together in\\na contrastive pre-training objective [54]. Protein language models [55, 56, 57, 58] and genomic language\\nmodels [59, 60, 61] have used self-supervised pretraining to generate embeddings useful for downstream tasks.\\nProtLLM [62], BioT5 [63], and GraphToken [64] combine molecule or proteins with LLMs using textual\\nor multi-modal strategies. Cellular foundation models such as scGPT [65], GenePT [66], Geneformer [67],\\nNicheformer [68], and Cell2Sentence [69] represent cells based on their gene expression to differentiate cell\\ntypes and understand gene perturbations. NatureLM [70] trained a foundation model that represents small\\nmolecules, proteins, RNA, and materials as sequences over a wide variety of scientific tasks.\\n</chunk>\\n\\n<chunk>\\nAgentic Systems. Unlike traditional passive models, agentic systems proactively choose actions to achieve\\ngoals [71, 72, 73, 74, 75], involving planning [76, 77, 78, 79, 80] and interaction with external tools [81,\\n82, 83, 84]. LLMs have enabled such systems by processing complex information and generating action-\\ndriving responses. The ReAct framework [22] combines reasoning, action, and observation, with variations\\nincorporating self-reflection [85] or model architectures for internal tool usage [82]. Agentic frameworks enable\\nautomating tasks like software development [73, 86, 87, 88] and scientific research [89, 90, 91] including\\nbiomedical applications such as nanobody design [90], drug discovery [92], or reaction optimization [93].\\nChemCrow [92] is an agent designed to perform chemistry experiments in drug discovery and materials\\ndesign. The coscientist by Boiko et al. [93] designs and performs chemical experiments by integrating web\\nknowledge, code execution, and experiment automation, demonstrating successful reaction optimization of\\npalladium-catalysed cross-couplings. The multi-agent system AI co-scientist [88] is designed for hypothesis\\ngeneration over a variety of scientific fields. TxAgent was developed as an agentic framework that provides\\nmulti-step reasoning and tool use aimed towards therapeutic applications, processing clinical information\\nto support tasks like treatment recommendation [94]. In contrast to recommending existing therapeutics,\\nAgentic-Tx generally focuses on developing new therapeutics.\\n</chunk>',\n", " True)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["chunks"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import aiohttp\n", "import asyncio\n", "from typing import Tuple, Optional\n", "from pdf2image import convert_from_path\n", "from PIL import Image\n", "import io\n", "import base64\n", "import os \n", "\n", "from prompts import table_extraction\n", "\n", "async def extract_tables_from_pdf(client, abs_url: str, force_reprocess: bool = False) -> Tuple[str, bool]:\n", "    \"\"\"\n", "    Fetches PDF from arXiv, converts to images, processes with LLM and saves text output.\n", "    \n", "    Args:\n", "        client: The API client to use for OCR\n", "        abs_url (str): The arXiv abstract URL\n", "        force_reprocess (bool): Whether to force reprocessing even if file exists\n", "        \n", "    Returns:\n", "        Tuple[str, bool]: (text_content or path to text file, success status)\n", "    \"\"\"\n", "    try:\n", "        # Convert abs URL to PDF URL\n", "        paper_id = abs_url.split('/')[-1]\n", "        pdf_url = f\"https://arxiv.org/pdf/{paper_id}.pdf\"\n", "        output_path = f\"tables/{paper_id}.txt\"\n", "\n", "        # Check if we already have the processed text\n", "        if os.path.exists(output_path) and not force_reprocess:\n", "            with open(output_path, 'r', encoding='utf-8') as f:\n", "                content = f.read()\n", "                if content and len(content) > 100:  # Ensure it's not empty or too short\n", "                    return content, True\n", "        \n", "        # Create output directory if it doesn't exist\n", "        os.makedirs(\"paper_texts\", exist_ok=True)\n", "        \n", "        # Download PDF\n", "        timeout = aiohttp.ClientTimeout(total=120)  # Increased from 30 to 120 seconds\n", "        try:\n", "            async with aiohttp.ClientSession(timeout=timeout) as session:\n", "                async with session.get(pdf_url) as response:\n", "                    if response.status != 200:\n", "                        return f\"Error fetching PDF: {response.status}\", False\n", "                    \n", "                    # Save PDF temporarily\n", "                    pdf_content = await response.read()\n", "                    temp_pdf = f\"temp_{paper_id}.pdf\"\n", "                    with open(temp_pdf, 'wb') as f:\n", "                        f.write(pdf_content)\n", "        except asyncio.TimeoutError:\n", "            return f\"Timeout downloading PDF from {pdf_url}\", False\n", "\n", "        try:\n", "            # Prepare OCR prompt text - we need the text content, not the function reference\n", "            table_extraction_prompt = table_extraction()  # Call the function to get the prompt text\n", "            \n", "            try:\n", "                # Convert PDF pages to images\n", "                # Limit to first N pages to avoid excessive API usage\n", "                max_pages = 15  # Adjust based on your needs and budget\n", "                \n", "                try:\n", "                    # This will fail if poppler is not installed\n", "                    images = convert_from_path(temp_pdf)\n", "                    images = images[:max_pages]  # Limit to first N pages\n", "                except Exception as pdf_error:\n", "                    # Check if it's a poppler error\n", "                    if \"poppler\" in str(pdf_error).lower():\n", "                        error_msg = (\n", "                            f\"Poppler is not installed or not found in PATH. \"\n", "                            f\"Please install poppler-utils package. \"\n", "                            f\"On macOS: brew install poppler, \"\n", "                            f\"On Ubuntu/Debian: apt-get install poppler-utils\"\n", "                        )\n", "                        print(error_msg)\n", "                        return error_msg, False\n", "                    else:\n", "                        # Re-raise if it's a different error\n", "                        raise\n", "                \n", "                tables = []\n", "                \n", "                # Create a progress indicator\n", "                print(f\"Processing PDF for {paper_id}: {len(images)} pages\")\n", "\n", "                # Process each page\n", "                for i, image in enumerate(images):\n", "                    print(f\"Processing page {i+1}/{len(images)} for {paper_id}\")\n", "                    \n", "                    # Convert PIL image to bytes and base64\n", "                    img_byte_arr = io.BytesIO()\n", "                    image.save(img_byte_arr, format='PNG')\n", "                    img_base64 = base64.b64encode(img_byte_arr.getvalue()).decode('utf-8')\n", "\n", "                    # Send to LLM API\n", "                    response = client.chat.completions.create(\n", "                        messages=[{\n", "                            \"role\": \"user\",\n", "                            \"content\": [\n", "                                {\n", "                                    \"type\": \"text\",\n", "                                    \"text\": table_extraction_prompt\n", "                                      # Use the text content of the prompt\n", "                                },\n", "                                {\n", "                                    \"type\": \"image_url\",\n", "                                    \"image_url\": {\n", "                                        \"url\": f\"data:image/png;base64,{img_base64}\"\n", "                                    }\n", "                                }\n", "                            ]\n", "                        }],\n", "                        model=os.getenv(\"GEMINI_MODEL\")\n", "                    )\n", "\n", "                    # Add processed text to collection\n", "                    processed_tables = response.choices[0].message.content.strip()\n", "                    tables.append(processed_tables)\n", "                    \n", "                    # Small delay to avoid rate limits\n", "                    await asyncio.sleep(0.5)\n", "\n", "                # Save combined text to file\n", "                all_tables = '\\n\\n'.join(tables)\n", "                with open(output_path, 'w', encoding='utf-8') as f:\n", "                    f.write(all_tables)\n", "\n", "                return all_tables, True\n", "                \n", "            except Exception as processing_error:\n", "                error_msg = f\"Error processing PDF images: {str(processing_error)}\"\n", "                print(error_msg)\n", "                return error_msg, False\n", "\n", "        finally:\n", "            # Clean up temporary PDF file\n", "            if os.path.exists(temp_pdf):\n", "                os.remove(temp_pdf)\n", "                \n", "    except aiohttp.ClientError as e:\n", "        error_msg = f\"Network error processing {paper_id}: {str(e)}\"\n", "        print(error_msg)\n", "        return error_msg, False\n", "    except Exception as e:\n", "        error_msg = f\"Error processing PDF {paper_id}: {str(e)}\"\n", "        print(error_msg)\n", "        return error_msg, False\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processing PDF for 2502.14727: 15 pages\n", "Processing page 1/15 for 2502.14727\n", "Processing page 2/15 for 2502.14727\n", "Processing page 3/15 for 2502.14727\n", "Processing page 4/15 for 2502.14727\n", "Processing page 5/15 for 2502.14727\n", "Processing page 6/15 for 2502.14727\n", "Processing page 7/15 for 2502.14727\n", "Processing page 8/15 for 2502.14727\n", "Processing page 9/15 for 2502.14727\n", "Processing page 10/15 for 2502.14727\n", "Processing page 11/15 for 2502.14727\n", "Processing page 12/15 for 2502.14727\n", "Processing page 13/15 for 2502.14727\n", "Processing page 14/15 for 2502.14727\n", "Processing page 15/15 for 2502.14727\n"]}], "source": ["tables, _ = await extract_tables_from_pdf(client, \"https://arxiv.org/abs/2502.14727\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["[\"I didn't find any tables in the document.\\n\\nOkay, here are the tables extracted from the image and converted into Markdown format.\\n\\n\",\n", " '\\n| Component         | Description                                                                                                                                                                                               |\\n|-----------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\\n| **Question**     | When will Captain America 4 be released?                                                                                                                                                                |\\n| **Internal Reasoning** | The release date of \"Captain America 4\" has not been disclosed.                                                                                                                                                                    |\\n| **Knowledge Base**    | The director\\'s statement for Captain America 4. ... I\\'m thrilled to introduce Captain America 4, hitting theaters on February 14, 2025! ...                                                                                         |\\n| **Retrieval Augmentation**| \"Captain America 4: New World Order\" will release on February 14, 2025, in North America and Mainland China. It\\'s the first film with <PERSON> (<PERSON>) as Captain America.                  |\\n\\n',\n", " \"\\n| Component         | Description                                                                                                                                                                        |\\n|-----------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\\n| **Question**     | Talk about the recent zodiac sign without snakes and cats?                                                                                                                                     |\\n| **Internal Reasoning** | Excluding snakes and cats from zodiac signs likely relates to alternative or less common zodiac systems or cultural variations.                                                        |\\n| **Knowledge Base**    | A news text about the zodiac having cats but not snakes. ... display at Yuelu Academy, shows a zodiac with a cat instead of a snake. It may date back ...                       |\\n| **Retrieval Augmentation**| Ancient murals from Weishan tombs in Hunan, displayed at Yuelu Academy show a Chinese zodiac with a cat replacing the snake. Experts believe this is a local variation, likely due to regional traditions or artisans' choices. |\\n\\n\",\n", " '\\n| Component         | Description                                                                                                                                                                           |\\n|-----------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\\n| **Question**     | What is the source of the noise in this audio file?                                                                                                                                        |\\n| **Internal Reasoning** | This noise appears to be consistent static, likely from an electronic device like a stereo or speaker, caused by faulty components or interference.                                   |\\n| **Knowledge Base**    | An audio sample from a vinyl record player and related knowledge. White noise static can come from ... or physical issues like wear and tear on equipment like vinyl record players or radios.  |\\n| **Retrieval Augmentation**| The static noise resembles sounds from analog devices like vinyl record players or older stereos. It is usually caused by signal interference from electronic malfunctions, radio wave distortions, or equipment wear. |\\n\\n',\n", " '\\n| Component         | Description                                                                                                                                                                |\\n|-----------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|\\n| **Question**     | Which Chinese ethnic group does this song belong to?                                                                                                                                    |\\n| **Internal Reasoning** | This song is likely a folk song from the Dong people, typically featuring simple melodies that reflect the daily life, festivals, and emotions of people.                                                       |\\n| **Knowledge Base**    | Miao folk song and related knowledge. The name of the song is \"Song After the Rain\", a traditional Miao folk song. Its melodies ...                                          |\\n| **Retrieval Augmentation**| \"Song After the Rain\" is a traditional Miao folk song with soothing melodies and heartfelt lyrics. It celebrates the beauty of nature after rain, symbolizing renewal and hope. |\\n\\nOkay, here are the tables extracted from the text and converted to Markdown format, each preceded by a ',\n", " \" tag.\\nI only extracted the formulas, since that's what can be expressed as markdown tables.\\n\\n\",\n", " '\\n```markdown\\n|  |                                                                  |\\n| :---: | :--------------------------------------------------------------- |\\n| p(d \\\\| qi) |  exp(sim(Rø(qi), Rø(d))) / Edied exp(sim(Rø(qi), Rø(di))) |\\n\\n```\\n\\n',\n", " '\\n```markdown\\n|        |                  |  |\\n| :-----: | :--------------- | :-: |\\n| P(Yi \\\\| qi, Dk) | ∏ P(Yi \\\\| qi, Dk, Y<m) | N |\\n|        | m=1 |  |\\n```\\n\\nHere are the tables extracted from the image:\\n\\n',\n", " '\\n|  |  |\\n|---|---|\\n| This song likely belongs to the Miao ethnic group. | This song might belong to the Dong ethnic group. | This is most likely a Miao folk song. |\\n| Confirm strong similarity in style and theme with Miao music. | Identify it as a Dong \"grand song\" due to stylistic similarities. | Verify thematic alignment with Miao cultural traditions. |\\n| Compare with retrieved Miao folk songs, which share multi-layered harmonies and natural themes. | Compare with retrieved Dong folk songs, noting similar multi-part harmonies and flowing melodies. | Compare these traits with retrieved Miao songs, known for joyful rhythms and celebratory tones. |\\n| Analyze the timbre and harmony of the user-provided audio. | Analyze the melody and harmony of the user-provided audio. | Focus on the rhythm and tonal structure of the user-provided audio. |\\n\\nOkay, here are the tables extracted and OCRed into Markdown format, marked with ',\n", " ' tags.\\n\\n',\n", " '\\n| Index | Knowledge  $K_{uni} = \\\\{k_1, \\\\dots, k_i\\\\}$ |\\n|---|---|\\n|  | ≡ █III |\\n|  | █III ≡ |\\n|  |  ≡ █III |\\n\\nOkay, here are the tables extracted from the image with OCR, formatted in Markdown.\\n\\n',\n", " \"\\n| Task | Dataset | Model | Whisper size | WER | Avg. Time | Metric R@1 | R@5 | R@10 | NDCG@10 |\\n| :------------- | :------------- | :------------- | :------------- | :------------- | :------------- | :------------- | :------------- | :------------- | :------------- |\\n| Speech2Text | HotpotQA | BGE | Tiny | 37.55% | 1.26 | 0.3741 | 0.7024 | 0.7509 | 0.4628 |\\n|  |  | BGE | Medium | 21.67% | 1.48 | 0.4440 | 0.8319 | 0.8736 | 0.5190 |\\n|  |  | BGE | Large | 19.2% | 1.92 | 0.4533 | 0.8519 | 0.8895 | 0.5252 |\\n|  |  | Ours | - | - | 0.23 | 0.4532 | 0.8492 | 0.8898 | 0.5117 |\\n|  | Comparison vs. BGE (Tiny / Medium / Large): |  | *Speed-up ≈ 5.49 ×/6.43 × /8.35×,* |  |  | *ΔR@10 ≈ +0.139/ +0.016/ +0.0003.* |  |  |  |\\n| Text2Speech | Spoken-SQuAD | CLSR | - | - | - | 0.4982 | 0.7963 | 0.8583 | - |\\n|  |  | BGE | - | 44.22% | - | 0.5464 | 0.7767 | 0.8497 | 0.6947 |\\n|  |  | Ours | - | - | 0.11 | 0.6844 | 0.8374 | 0.9023 | 0.8483 |\\n|  | CLSR | - | - | 16.69% | - | 0.3065 | 0.6219 | 0.7443 | - |\\n| Speech2Speech | SLUE-SQA-5 | BGE | Tiny | 45.34%/53.66% | 0.62/1.27 | 0.1696 | 0.3871 | 0.4828 | 0.2194 |\\n|  |  | BGE | Medium | 26.14%/44.46% | 0.87/3.44 | 0.3228 | 0.5940 | 0.6982 | 0.2989 |\\n|  |  | BGE | Large | 23.59%/42.19% | 0.98/4.63 | 0.3312 | 0.6121 | 0.7196 | 0.3269 |\\n|  |  | Ours | - | - | 0.17/0.22 | 0.3392 | 0.6308 | 0.7221 | 0.3623 |\\n|  | Comparison vs. BGE (Tiny / Medium / Large): |  | *Speed-up ≈ 4.84 × /11.05 × /14.38×,* |  |  | *ΔR@10 ≈ +0.2393/ +0.0282/ +0.0025.* |  |  |  |\\n| Audio+Text2Audio+Text | Ours | CIAP (AT) | - | - | 0.05 | 0.1260 | 0.2940 | 0.3989 | 0.2474 |\\n|  |  | CLAP(TA) | - | - | 0.05 | 0.0998 | 0.2577 | 0.3588 | 0.2135 |\\n|  |  | CLAP(AT2AT) | - | - | 0.09 | 0.1345 | 0.2145 | 0.2379 | 0.1849 |\\n|  |  | CIAP (ALL) | - | - | 0.06 | 0.0001 | 0.0012 | 0.0018 | 0.0002 |\\n|  |  | BGE (Caption) | - | - | 1.99 | 0.0251 | 0.0585 | 0.0775 | 0.0483 |\\n|  |  | Ours | - | - | 0.19 | 0.2728 | 0.5184 | 0.6313 | 0.4381 |\\n</table>\\n\\nHere are the tables from the image, OCR'd into Markdown, with each table preceded by a \",\n", " ' tag:\\n\\n',\n", " '\\n```markdown\\n| Method     | Model      | Input    |   HotpotQA |   SLUE-SQA-5 |   Avg EM |   FS (Ours) |\\n|:-----------|:-----------|:---------|-----------:|-------------:|---------:|------------:|\\n|            |            |          |  **(a) TextRAG**     |               |          |             |\\n|            |            | top-1    |     0.3124 |       0.3237 |   0.3181 |         -   |\\n| GPT-40     |            | top-2    |     0.3457 |       0.3359 |   0.3408 |         -   |\\n|            |            | top-3    |     0.3623 |       0.3531 |   0.3577 |         -   |\\n|            |            | Oracle   |     0.5853 |       0.5931 |   0.5892 |         -   |\\n|            | QwenAudio | top-1    |     0.1783 |       0.2439 |   0.2111 |         -   |\\n|            |            | top-2    |     0.2336 |       0.2502 |   0.2419 |         -   |\\n|            |            | top-3    |     0.2417 |       0.2561 |   0.2489 |         -   |\\n|            |            | Oracle   |     0.4867 |       0.4784 |   0.4824 |         -   |\\n|            |            |          |  **(b) WavRAG**     |               |          |             |\\n|            |            | top-1    |     0.4019 |       0.3904 | **0.3962** | **0.5732** |\\n| GPT-40     |            | top-2    |     0.4186 |       0.4315 | **0.4249** | **0.6408** |\\n|            |            | top-3    |     0.4271 |       0.4007 | **0.4139** | **0.5129** |\\n|            |            | Oracle   |     0.5941 |       0.6164 | **0.6053** | **0.7096** |\\n|            | QwenAudio | top-1    |     0.2033 |       0.2647 | **0.2340** | **0.5387** |\\n|            |            | top-2    |     0.2439 |       0.2956 | **0.2698** | **0.5521** |\\n|            |            | top-3    |     0.2658 |       0.3063 | **0.2860** | **0.5387** |\\n|            |            | Oracle   |     0.5032 |       0.5294 | **0.5163** | **0.6079** |\\n|            |            |          | **(c) WavRAG-COT**    |               |          |             |\\n|            |            | top-1    |     0.4261 |       0.4520 | **0.4390** | **0.6412** |\\n| GPT-40     |            | top-2    |     0.4286 |       0.5239 | **0.4983** | **0.6487** |\\n|            |            | top-3    |     0.4403 |       0.4918 | **0.4662** | **0.5981** |\\n|            |            | Oracle   |     0.5976 |       0.6849 | **0.6413** | **0.7389** |\\n|            | QwenAudio | top-1    |     0.2688 |       0.3132 | **0.2910** | **0.6386** |\\n|            |            | top-2    |     0.3026 |       0.3352 | **0.3189** | **0.6017** |\\n|            |            | top-3    |     0.3152 |       0.3397 | **0.3275** | **0.5612** |\\n|            |            | Oracle   |     0.5863 |       0.6103 | **0.5983** | **0.7122** |\\n```\\n</table>\\n\\n',\n", " '\\n```markdown\\n| Dataset       | Metric    |   Qwen2audio (Original) |   WavRAG |   Improvement |\\n|:--------------|:----------|------------------------:|---------:|----------------:|\\n|               | R@1       |                  0.0675 |   0.2728 |        +0.2053 |\\n| Ours          | R@5       |                  0.1457 |   0.5184 |        +0.3727 |\\n|               | R@10      |                  0.1868 |   0.6313 |        +0.4445 |\\n|               | nDCG@10   |                  0.1212 |   0.5381 |        +0.4169 |\\n|               | R@1       |                  0.3407 |   0.6844 |        +0.3437 |\\n| Spoken-SQuAD  | R@5       |                  0.4995 |   0.8374 |        +0.3379 |\\n|               | R@10      |                  0.6003 |   0.9023 |        +0.302  |\\n|               | nDCG@10   |                  0.3554 |   0.8483 |        +0.4929 |\\n|               | R@1       |                  0.1457 |   0.4532 |        +0.3075 |\\n| HotpotQA      | R@5       |                  0.3172 |   0.8492 |        +0.532  |\\n|               | R@10      |                  0.3858 |   0.8898 |        +0.504  |\\n|               | nDCG@10   |                  0.2868 |   0.5117 |        +0.2249 |\\n```\\n</table>\\n\\n',\n", " '\\nGrammatical\\n600\\n400\\n200\\nRelevant\\n200\\n100\\n0\\n0\\n5\\n0\\n1\\n2\\n3\\n4\\n5\\n</table>\\n\\n',\n", " '\\nFactual\\n600\\n400\\n200\\nHelpful\\n300\\n0\\n3\\n5\\n</table>\\n\\n',\n", " '\\nharmful\\nHelpful\\n200\\n100\\nneutral\\n0 1\\n2 4 5 1 2 3\\n4\\n</table>\\n\\nOkay, here are the tables extracted from the provided text in Markdown format, marked with ',\n", " ' tags.\\n\\n',\n", " \"\\n| Reference |\\n|:---|\\n| <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. 2023. *Musiclm: Generating music from text*. Preprint, arXiv:2301.11325. |\\n| <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, et al. 2024. *Funaudiollm: Voice understanding and generation foundation models for natural interaction between humans and llms*. arXiv preprint arXiv:2407.04051. |\\n| Anonymous. 2025. *CLSR: End-to-end contrastive language-speech retriever for better speech retrieval augmented generation*. In *Submitted to ACL Rolling Review - December 2024*. Under review. |\\n| <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. 2020. *Common voice: A massively-multilingual speech corpus*. In *Proceedings of the Twelfth Language Resources and Evaluation Conference*, pages 4218–4222, Marseille, France. European Language Resources Association. |\\n| <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>mit<PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>va <PERSON>. 2024. *Llm2vec: Large language models are secretly powerful text encoders*. Preprint, arXiv:2404.05961. |\\n| Ciprian Chelba, Timothy J. Hazen, and Murat Saraclar. 2008. *<PERSON>trieval and browsing of spoken content*. *IEEE Signal Processing Magazine*, 25(3):39–49. |\\n| Wenhu Chen, Hexiang Hu, Xi Chen, Pat Verga, and William W. Cohen. 2022. *Murag: Multi-modal retrieval-augmented generator for open question answering over images and text*. Preprint, arXiv:2210.02928. |\\n| Xinyun Chen, Renat Aksitov, Uri Alon, Jie Ren, Kefan Xiao, Pengcheng Yin, Sushant Prakash, Charles Sutton, Xuezhi Wang, and Denny Zhou. 2023. *Universal self-consistency for large language model generation*. arXiv preprint arXiv:2311.17311. |\\n| Konstantinos Drossos, Samuel Lipping, and Tuomas Virtanen. 2019. *Clotho: An audio captioning dataset*. Preprint, arXiv:1910.09387. |\\n| Zhihao Du, Yuxuan Wang, Qian Chen, Xian Shi, Xiang Lv, Tianyu Zhao, Zhifu Gao, Yexin Yang, Changfeng Gao, Hui Wang, Fan Yu, Huadai Liu, Zhengyan Sheng, Yue Gu, Chong Deng, Wen Wang, Shiliang Zhang, Zhijie Yan, and Jingren Zhou. 2024. *Cosyvoice 2: Scalable streaming speech synthesis with large language models*. Preprint, arXiv:2412.10117. |\\n| Benjamin Elizalde, Soham Deshmukh, Mahmoud Al Ismail, and Huaming Wang. 2022. *Clap: Learning audio concepts from natural language supervision*. Preprint, arXiv:2206.04769. |\\n| Angela Fan, Yacine Jernite, Ethan Perez, David Grangier, Jason Weston, and Michael Auli. 2019. *ELI5: Long form question answering*. In *Proceedings of the 57th Annual Meeting of the Association for Computational Linguistics*, pages 3558–3567, Florence, Italy. Association for Computational Linguistics. |\\n| Qingkai Fang, Shoutao Guo, Yan Zhou, Zhengrui Ma, Shaolei Zhang, and Yang Feng. 2024. *Llama-omni: Seamless speech interaction with large language models*. Preprint, arXiv:2409.06666. |\\n</table>\\n\\nOkay, here are the tables extracted and OCRed into Markdown. Since there are no actual tables in the document, I'm simply going to represent all the information as a single table for demonstration purposes.\\n\\n\",\n", " '\\n\\n| Author(s) and Year | Title | Publication |\\n|---|---|---|\\n| <PERSON><PERSON> et al., 2024 | Retrieval-augmented generation for large language models: A survey | Preprint, arXiv:2312.10997 |\\n| <PERSON><PERSON> and <PERSON>, 2024 | A retrieval augmented approach for text-to-music generation | Proceedings of the 3rd Workshop on NLP for Music and Audio (NLP4MusA), pages 31-36, Oakland, USA. Association for Computational Lingustics |\\n| <PERSON><PERSON> et al., 2020 | Realm: Retrieval-augmented language model pre-training | Preprint, arXiv:2002.08909 |\\n| <PERSON> et al., 2021 | Lora: Low-rank adaptation of large language models | Preprint, arXiv:2106.09685 |\\n| <PERSON><PERSON><PERSON><PERSON> et al., 2023 | Make-an-audio: Text-to-audio generation with prompt-enhanced diffusion models | Preprint, arXiv:2301.12661 |\\n| Shengpeng Ji et al., 2024a | Wavchat: A survey of spoken dialogue models | Preprint, arXiv:2411.13577 |\\n| Sheng<PERSON><PERSON> et al., 2024b | Wavtokenizer: an efficient acoustic discrete codec tokenizer for audio language modeling | arXiv preprint arXiv:2408.16532 |\\n| Shengpeng Ji et al., 2024c | Textrolspeech: A text style control speech corpus with codec language text-to-speech models | ICASSP 2024-2024 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP), pages 10301-10305. IEEE |\\n| Shengpeng Ji et al., 2024d | Control-speech: Towards simultaneous zero-shot speaker cloning and zero-shot language style control with decoupled codec | arXiv preprint arXiv:2406.01205 |\\n| Ting Jiang et al., 2023 | Scaling sentence embeddings with large language models | Preprint, arXiv:2307.16645 |\\n| Ting Jiang et al., 2024 | E5-v: Universal embeddings with multimodal large language models | Preprint, arXiv:2407.12580 |\\n| Ziyan Jiang et al., 2025 | Vlm2vec: Training vision-language models for massive multimodal embedding tasks | Preprint, arXiv:2410.05160 |\\n| Mandar Joshi et al., 2017 | TriviaQA: A large scale distantly supervised challenge dataset for reading comprehension | Proceedings of the 55th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers), pages 1601-1611, Vancouver, Canada. Association for Computational Linguistics |\\n| Chris Dongjoo Kim et al., 2019 | AudioCaps: Generating captions for audios in the wild | Proceedings of the 2019 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies, Volume 1 (Long and Short Papers), pages 119-132, Minneapolis, Minnesota. Association for Computational Linguistics |\\n| Yuma Koizumi et al., 2020 | Audio captioning using pre-trained large-scale language model guided by audio-based similar caption retrieval | Preprint, arXiv:2012.07331 |\\n| Takeshi Kojima et al., 2022 | Large language models are zero-shot reasoners | Advances in neural information processing systems, 35:22199-22213 |\\n| Tom Kwiatkowski et al., 2019 | Natural questions: A benchmark for question answering research | Transactions of the Association for Computational Linguistics, 7:452-466 |\\n| Chankyu Lee et al., 2025 | Nv-embed: Improved techniques for training llms as generalist embedding models | Preprint, arXiv:2405.17428 |\\n| Lin-shan Lee et al., 2015 | Spoken content retrieval—beyond cascading speech recognition with text retrieval | IEEE/ACM Transactions on Audio, Speech, and Language Processing, 23(9):1389-1420 |\\n| Chaofan Li et al., 2024 | Making text embedders few-shot learners | Preprint, arXiv:2409.15700 |\\n\\n</table>\\n\\nOkay, here are the tables OCRed into Markdown format, each preceded by a ',\n", " ' tag. There are no apparent tables in the image provided.\\n\\nOkay, here are the tables extracted and OCRed into Markdown format:\\n\\n',\n", " '\\n```markdown\\n| Task                     | Dataset      | Train   Q-D pairs | Retrieval Test | Generation Test |\\n|--------------------------|--------------|--------------------|----------------|-------------------|\\n| Speech-to-Text           | Quora        | 60202              | -              | -                 |\\n|                          | HotpotQA     | 84516              | 7405           | 7405              |\\n| Text-to-Text             | ELI5         | 325475             | -              | -                 |\\n|                          | TrivialQA    | 60315              | -              | -                 |\\n|                          | SQuAD        | 87599              | -              | -                 |\\n|                          | MS MARCO     | 485823             | -              | -                 |\\n| Speech-to-Speech         | SLUE-SQA-5   | 46186              | 2382           | 2382              |\\n| Text-to-Speech           | SpokenSQuAD  | 37111              | 5351           | -                 |\\n|                          | Ours         | 78746              | 8834           | 1200              |\\n|                          | AudioCaps    | 35327              | 4043           | 572               |\\n|                          | MusicCap     | 4080               | 442            | 76                |\\n| Audio+Text-to-Audio+Text | Clotho       | 2852               | 314            | 68                |\\n| (Ours)                   | VoxCeleb     | 1091               | 120            | -                 |\\n|                          | Xeno-canto   | 8771               | 956            | -                 |\\n|                          | Collected    | -                  | -              | 43                |\\n|                          | **Total**    | **130867**         | **14709**      | **1959**          |\\n```\\n\\n</table>\\n\\nOkay, I will extract and OCR the tables from the provided image and mark each with a ',\n", " \" tag.  Since the image only describes the existence of tables 6, 7, 8, 9, and 10, but doesn't actually contain them, I can't produce them for you.\\n\\nHere are the requested tables in markdown format:\\n\\n\",\n", " '\\n```\\n| PCA Component 2 |\\n| --- |\\n| 0.4 |\\n| 0.2 |\\n| 0.0 |\\n| -0.2 |\\n| -0.4 |\\n\\n```\\n</table>\\n\\n',\n", " '\\n```\\n| PCA Component 2 |\\n| --- |\\n| 75 |\\n| 50 |\\n| 25 |\\n| 0 |\\n| -25 |\\n| -50 |\\n| -75 |\\n\\n```\\n</table>\\n\\n',\n", " '\\n```\\n| PCA Component 2 |\\n| --- |\\n| 200 |\\n| 150 |\\n| 100 |\\n| 50 |\\n| 0 |\\n| -50 |\\n| -100 |\\n\\n```\\n</table>\\n\\n```html\\n',\n", " '\\n```\\n| Task                         | Dataset      | Instuction                                                                                                                                      |\\n| :--------------------------- | :----------- | :---------------------------------------------------------------------------------------------------------------------------------------------- |\\n| Speech-to-Text               | Quora        | Given a question, retrieve questions that are semantically equivalent to the given question                                                  |\\n|                              | HotpotQA     | Given a multi-hop question, retrieve documents that can help answer the question.                                                              |\\n|                              | ELI5         | Given a question, retrieve relevant documents that best answer the question.                                                                     |\\n| Text-to-Text                 | TrivialQA    | Given a question, retrieve relevant documents that best answer the question.                                                                     |\\n|                              | SQuAD        | Given a question, retrieve relevant documents that best answer the question.                                                                     |\\n|                              | MS MARCO     | Given a web search query, retrieve relevant passages that answer the query.                                                                     |\\n| Speech-to-Specch             | SLUE-SQA-5   | Please retrieve the most relevant speech in the document based on the following questions                                                      |\\n| Text-to-Speech               | SpokenSQuAD  | Based on the following text query, retrieve the most relevant speech.                                                                         |\\n|                              |              |                                                                                                                                                 |\\n|                              | AudioSetSL   | Audio2Text:Based on the following audio, extract the most relevant text description                                                             |\\n|                              |              | Text2Audio:Based on the following text description, extract the most relevant audio                                                             |\\n| Audio+Text-to-Audio+Text Ours | AudioCaps    | AT2AT:Extract the most relevant knowledge based on the following questions                                                                     |\\n|                              |              | Audio2Text:Based on the following audio, extract the most relevant text description                                                             |\\n|                              |              | Text2Audio:Based on the following text description, extract the most relevant audio                                                             |\\n|                              | MusicCap     | AT2AT:Extract the most relevant knowledge based on the following questions                                                                     |\\n|                              |              | Audio2Text:Based on the following audio, extract the most relevant text description                                                             |\\n|                              |              | Text2Audio:Based on the following text description, extract the most relevant audio                                                             |\\n|                              | Clotho       | AT2AT:Extract the most relevant knowledge based on the following questions                                                                     |\\n|                              |              | Extract the most relevant knowledge based on the following questions                                                                           |\\n|                              | VoxCeleb     | Extract the most relevant knowledge based on the following questions                                                                           |\\n|                              | Xeno-canto   | Extract the most relevant knowledge based on the following questions                                                                           |\\n|                              | Collected    | Extract the most relevant knowledge based on the following questions                                                                           |\\n\\n```html']"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["tables.split(\"<table>\")"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [], "source": ["from typing import List, <PERSON><PERSON>\n", "import asyncio\n", "import os\n", "import aiohttp\n", "import io\n", "import base64\n", "from pdf2image import convert_from_path\n", "from prompts import table_extraction\n", "\n", "async def extract_table_from_page(client, abs_url: str, page_number: int, image, table_extraction_prompt: str) -> Tuple[str, bool]:\n", "    \"\"\"\n", "    Extract tables from a specific page of a PDF file asynchronously.\n", "    \"\"\"\n", "    try:\n", "        # Convert PIL image to bytes and base64\n", "        img_byte_arr = io.BytesIO()\n", "        image.save(img_byte_arr, format='PNG')\n", "        img_base64 = base64.b64encode(img_byte_arr.getvalue()).decode('utf-8')\n", "\n", "        # Send to LLM API\n", "        response = client.chat.completions.create(\n", "            messages=[{\n", "                \"role\": \"user\",\n", "                \"content\": [\n", "                    {\n", "                        \"type\": \"text\",\n", "                        \"text\": table_extraction_prompt\n", "                    },\n", "                    {\n", "                        \"type\": \"image_url\",\n", "                        \"image_url\": {\n", "                            \"url\": f\"data:image/png;base64,{img_base64}\"\n", "                        }\n", "                    }\n", "                ]\n", "            }],\n", "            model=os.getenv(\"GEMINI_MODEL\")\n", "        )\n", "\n", "        # Extract processed text\n", "        processed_tables = response.choices[0].message.content.strip()\n", "        return processed_tables, True\n", "\n", "    except Exception as e:\n", "        error_msg = f\"Error processing page {page_number}: {str(e)}\"\n", "        print(error_msg)\n", "        return error_msg, False\n", "\n", "\n", "async def extract_tables_from_pdf(client, abs_url: str, force_reprocess: bool = False, max_concurrent: int = 5) -> Tuple[str, bool]:\n", "    \"\"\"\n", "    Fetches PDF from arXiv, converts to images, processes with LLM, and saves text output.\n", "\n", "    Args:\n", "        client: The API client to use for OCR\n", "        abs_url (str): The arXiv abstract URL\n", "        force_reprocess (bool): Whether to force reprocessing even if file exists\n", "        max_concurrent (int): Maximum number of pages to process concurrently\n", "\n", "    Returns:\n", "        Tuple[str, bool]: (text_content or path to text file, success status)\n", "    \"\"\"\n", "    try:\n", "        # Convert abs URL to PDF URL\n", "        paper_id = abs_url.split('/')[-1]\n", "        pdf_url = f\"https://arxiv.org/pdf/{paper_id}.pdf\"\n", "\n", "        # Check if the PDF URL is valid\n", "        async with aiohttp.ClientSession() as session:\n", "            async with session.head(pdf_url) as response:\n", "                if response.status == 404:\n", "                    return f\"Error: PDF not found at {pdf_url}. Please check the paper ID.\", False\n", "\n", "        output_path = f\"tables/{paper_id}.txt\"\n", "\n", "        # Check if we already have the processed text\n", "        if os.path.exists(output_path) and not force_reprocess:\n", "            with open(output_path, 'r', encoding='utf-8') as f:\n", "                content = f.read()\n", "                if content and len(content) > 100:  # Ensure it's not empty or too short\n", "                    return content, True\n", "\n", "        # Create output directory if it doesn't exist\n", "        os.makedirs(\"tables\", exist_ok=True)\n", "\n", "        # Download PDF\n", "        timeout = aiohttp.ClientTimeout(total=120)\n", "        try:\n", "            async with aiohttp.ClientSession(timeout=timeout) as session:\n", "                async with session.get(pdf_url) as response:\n", "                    if response.status != 200:\n", "                        return f\"Error fetching PDF: {response.status}\", False\n", "\n", "                    # Save PDF temporarily\n", "                    pdf_content = await response.read()\n", "                    temp_pdf = f\"temp_{paper_id}.pdf\"\n", "                    with open(temp_pdf, 'wb') as f:\n", "                        f.write(pdf_content)\n", "        except asyncio.TimeoutError:\n", "            return f\"Timeout downloading PDF from {pdf_url}\", False\n", "\n", "        try:\n", "            # Prepare OCR prompt text\n", "            table_extraction_prompt = table_extraction()\n", "\n", "            # Convert PDF pages to images\n", "            max_pages = 15  # Adjust based on your needs and budget\n", "            images = convert_from_path(temp_pdf)\n", "            images = images[:max_pages]  # Limit to first N pages\n", "\n", "            # Process pages in parallel\n", "            semaphore = asyncio.Semaphore(max_concurrent)\n", "\n", "            async def sem_task(page_number, image):\n", "                async with semaphore:\n", "                    return await extract_table_from_page(client, abs_url, page_number, image, table_extraction_prompt)\n", "\n", "            tasks = [sem_task(i + 1, image) for i, image in enumerate(images)]\n", "            results = await asyncio.gather(*tasks)\n", "\n", "            # Combine results\n", "            tables = [result[0] for result in results if result[1]]\n", "            all_tables = '\\n\\n'.join(tables)\n", "\n", "            # Save combined text to file\n", "            with open(output_path, 'w', encoding='utf-8') as f:\n", "                f.write(all_tables)\n", "\n", "            return all_tables, True\n", "\n", "        finally:\n", "            # Clean up temporary PDF file\n", "            if os.path.exists(temp_pdf):\n", "                os.remove(temp_pdf)\n", "\n", "    except aiohttp.ClientError as e:\n", "        error_msg = f\"Network error processing {paper_id}: {str(e)}\"\n", "        print(error_msg)\n", "        return error_msg, False\n", "    except Exception as e:\n", "        error_msg = f\"Error processing PDF {paper_id}: {str(e)}\"\n", "        print(error_msg)\n", "        return error_msg, False\n", "\n", "async def main():\n", "    from openai import OpenAI\n", "\n", "    client = OpenAI(\n", "        api_key=os.getenv(\"GEMINI_API_KEY\"), \n", "        base_url=os.getenv(\"GEMINI_BASE_URL\")\n", "        )   \n", "    \n", "    abs_url = \"https://arxiv.org/abs/2502.14727\"\n", "    result, success = await extract_tables_from_pdf(client, abs_url, force_reprocess=True, max_concurrent=3)\n", "    if success:\n", "        print(\"Tables extracted successfully!\")\n", "        #print(result)\n", "    else:\n", "        print(\"Failed to extract tables:\", result)\n", "\n", "    return result"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/openai/chat/completions \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Tables extracted successfully!\n"]}], "source": ["# Run the example\n", "#asyncio.run(main())\n", "tables = await main()"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [], "source": ["tables"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Matplotlib is building the font cache; this may take a moment.\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Found 2 tables (using La<PERSON><PERSON>).\n", "  0 1\n", "0    \n"]}], "source": ["import camelot\n", "import pandas as pd\n", "\n", "pdf_path = \"data/test/pdf/TxGemma.pdf\"\n", "\n", "# Try <PERSON><PERSON>ce first (good for tables with lines)\n", "tables_lattice = camelot.read_pdf(pdf_path, flavor='lattice', pages='1-end') # Or specify pages='1,3,5' etc.\n", "\n", "# If <PERSON><PERSON><PERSON> doesn't work well, try <PERSON> (good for tables without lines)\n", "# tables_stream = camelot.read_pdf(pdf_path, flavor='stream', pages='1-end')\n", "\n", "print(f\"Found {tables_lattice.n} tables (using <PERSON><PERSON><PERSON>).\")\n", "\n", "# Export tables to DataFrames or other formats\n", "if tables_lattice.n > 0:\n", "    df = tables_lattice[0].df # Get the first table as a pandas DataFrame\n", "    print(df.head())\n", "    # tables_lattice.export('extracted_tables.csv', f='csv', compress=True) # Export all tables"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  0 1\n", "0    "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["The operation couldn’t be completed. Unable to locate a Java Runtime.\n", "Please visit http://www.java.com for information on installing Java.\n", "\n"]}, {"ename": "CalledProcessError", "evalue": "Command '['/usr/libexec/java_home']' returned non-zero exit status 1.", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mCalledProcessError\u001b[39m                        Traceback (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 8\u001b[39m\n\u001b[32m      4\u001b[39m pdf_path = \u001b[33m\"\u001b[39m\u001b[33mdata/test/pdf/TxGemma.pdf\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m      6\u001b[39m \u001b[38;5;66;03m# Reads all tables from all pages by default\u001b[39;00m\n\u001b[32m      7\u001b[39m \u001b[38;5;66;03m# Returns a list of pandas DataFrames\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m8\u001b[39m tables = \u001b[43mtabula\u001b[49m\u001b[43m.\u001b[49m\u001b[43mread_pdf\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpdf_path\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpages\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mall\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmultiple_tables\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[32m     10\u001b[39m \u001b[38;5;66;03m# For specific pages: pages='1,3-5'\u001b[39;00m\n\u001b[32m     11\u001b[39m \u001b[38;5;66;03m# To guess table areas (might be slower but sometimes needed): guess=True\u001b[39;00m\n\u001b[32m     12\u001b[39m \u001b[38;5;66;03m# To specify an area: area=[top, left, bottom, right]\u001b[39;00m\n\u001b[32m     14\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mFound \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(tables)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m tables.\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Development/fastmoatless/.venv/lib/python3.12/site-packages/tabula/io.py:400\u001b[39m, in \u001b[36mread_pdf\u001b[39m\u001b[34m(input_path, output_format, encoding, java_options, pandas_options, multiple_tables, user_agent, use_raw_url, pages, guess, area, relative_area, lattice, stream, password, silent, columns, relative_columns, format, batch, output_path, force_subprocess, options)\u001b[39m\n\u001b[32m    397\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpath\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m is empty. Check the file, or download it manually.\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    399\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m400\u001b[39m     output = \u001b[43m_run\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    401\u001b[39m \u001b[43m        \u001b[49m\u001b[43mtabula_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    402\u001b[39m \u001b[43m        \u001b[49m\u001b[43mjava_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    403\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpath\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    404\u001b[39m \u001b[43m        \u001b[49m\u001b[43mencoding\u001b[49m\u001b[43m=\u001b[49m\u001b[43mencoding\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    405\u001b[39m \u001b[43m        \u001b[49m\u001b[43mforce_subprocess\u001b[49m\u001b[43m=\u001b[49m\u001b[43mforce_subprocess\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    406\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    407\u001b[39m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[32m    408\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m temporary:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Development/fastmoatless/.venv/lib/python3.12/site-packages/tabula/io.py:74\u001b[39m, in \u001b[36m_run\u001b[39m\u001b[34m(options, java_options, path, encoding, force_subprocess)\u001b[39m\n\u001b[32m     69\u001b[39m     _tabula_vm = SubprocessTabula(\n\u001b[32m     70\u001b[39m         java_options=java_options, silent=options.silent, encoding=encoding\n\u001b[32m     71\u001b[39m     )\n\u001b[32m     73\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m _tabula_vm:\n\u001b[32m---> \u001b[39m\u001b[32m74\u001b[39m     _tabula_vm = \u001b[43mTabulaVm\u001b[49m\u001b[43m(\u001b[49m\u001b[43mjava_options\u001b[49m\u001b[43m=\u001b[49m\u001b[43mjava_options\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msilent\u001b[49m\u001b[43m=\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m.\u001b[49m\u001b[43msilent\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     75\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m _tabula_vm \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m _tabula_vm.tabula:\n\u001b[32m     76\u001b[39m         _tabula_vm = SubprocessTabula(\n\u001b[32m     77\u001b[39m             java_options=java_options, silent=options.silent, encoding=encoding\n\u001b[32m     78\u001b[39m         )\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Development/fastmoatless/.venv/lib/python3.12/site-packages/tabula/backend.py:45\u001b[39m, in \u001b[36mTabulaVm.__init__\u001b[39m\u001b[34m(self, java_options, silent)\u001b[39m\n\u001b[32m     36\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m silent:\n\u001b[32m     37\u001b[39m         java_options.extend(\n\u001b[32m     38\u001b[39m             (\n\u001b[32m     39\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33m-Dorg.slf4j.simpleLogger.defaultLogLevel=off\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m     42\u001b[39m             )\n\u001b[32m     43\u001b[39m         )\n\u001b[32m---> \u001b[39m\u001b[32m45\u001b[39m     \u001b[43mjpype\u001b[49m\u001b[43m.\u001b[49m\u001b[43mstartJVM\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43mjava_options\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconvertStrings\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mF<PERSON>e\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[32m     47\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mjava\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mlang\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mlang\u001b[39;00m\n\u001b[32m     48\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtechnology\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtabula\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mtabula\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Development/fastmoatless/.venv/lib/python3.12/site-packages/jpype/_core.py:267\u001b[39m, in \u001b[36mstartJVM\u001b[39m\u001b[34m(jvmpath, classpath, ignoreUnrecognized, convertStrings, interrupt, *jvmargs)\u001b[39m\n\u001b[32m    264\u001b[39m         jvm_args = jvm_args[\u001b[32m1\u001b[39m:]\n\u001b[32m    266\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m jvmpath:\n\u001b[32m--> \u001b[39m\u001b[32m267\u001b[39m     jvmpath = \u001b[43mgetDefaultJVMPath\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    268\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    269\u001b[39m     \u001b[38;5;66;03m# Allow the path to be a PathLike.\u001b[39;00m\n\u001b[32m    270\u001b[39m     jvmpath = os.fspath(jvmpath)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Development/fastmoatless/.venv/lib/python3.12/site-packages/jpype/_jvmfinder.py:70\u001b[39m, in \u001b[36mgetDefaultJVMPath\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m     68\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m     69\u001b[39m     finder = LinuxJVMFinder()\n\u001b[32m---> \u001b[39m\u001b[32m70\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfinder\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget_jvm_path\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Development/fastmoatless/.venv/lib/python3.12/site-packages/jpype/_jvmfinder.py:184\u001b[39m, in \u001b[36mJVMFinder.get_jvm_path\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    182\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m method \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m._methods:\n\u001b[32m    183\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m184\u001b[39m         jvm = \u001b[43mmethod\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    186\u001b[39m         \u001b[38;5;66;03m# If found check the architecture\u001b[39;00m\n\u001b[32m    187\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m jvm:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Development/fastmoatless/.venv/lib/python3.12/site-packages/jpype/_jvmfinder.py:311\u001b[39m, in \u001b[36mDarwinJVMFinder._javahome_binary\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    309\u001b[39m \u001b[38;5;66;03m# TODO: check if the java_home tool is still available and fix the version boundaries.\u001b[39;00m\n\u001b[32m    310\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m Version(\u001b[33m'\u001b[39m\u001b[33m10.6\u001b[39m\u001b[33m'\u001b[39m) <= current:  \u001b[38;5;66;03m# < Version('10.9'):\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m311\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43msubprocess\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcheck_output\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    312\u001b[39m \u001b[43m        \u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43m/usr/libexec/java_home\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m)\u001b[49m.strip()\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/homebrew/Cellar/python@3.12/3.12.8/Frameworks/Python.framework/Versions/3.12/lib/python3.12/subprocess.py:466\u001b[39m, in \u001b[36mcheck_output\u001b[39m\u001b[34m(timeout, *popenargs, **kwargs)\u001b[39m\n\u001b[32m    463\u001b[39m         empty = \u001b[33mb\u001b[39m\u001b[33m'\u001b[39m\u001b[33m'\u001b[39m\n\u001b[32m    464\u001b[39m     kwargs[\u001b[33m'\u001b[39m\u001b[33minput\u001b[39m\u001b[33m'\u001b[39m] = empty\n\u001b[32m--> \u001b[39m\u001b[32m466\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mrun\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43mpopenargs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstdout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mPIPE\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcheck\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[32m    467\u001b[39m \u001b[43m           \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m.stdout\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/homebrew/Cellar/python@3.12/3.12.8/Frameworks/Python.framework/Versions/3.12/lib/python3.12/subprocess.py:571\u001b[39m, in \u001b[36mrun\u001b[39m\u001b[34m(input, capture_output, timeout, check, *popenargs, **kwargs)\u001b[39m\n\u001b[32m    569\u001b[39m     retcode = process.poll()\n\u001b[32m    570\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m check \u001b[38;5;129;01mand\u001b[39;00m retcode:\n\u001b[32m--> \u001b[39m\u001b[32m571\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m CalledProcessError(retcode, process.args,\n\u001b[32m    572\u001b[39m                                  output=stdout, stderr=stderr)\n\u001b[32m    573\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m CompletedProcess(process.args, retcode, stdout, stderr)\n", "\u001b[31mCalledProcessError\u001b[39m: Command '['/usr/libexec/java_home']' returned non-zero exit status 1."]}], "source": ["import tabula\n", "import pandas as pd\n", "\n", "pdf_path = \"data/test/pdf/TxGemma.pdf\"\n", "\n", "# Reads all tables from all pages by default\n", "# Returns a list of pandas DataFrames\n", "tables = tabula.read_pdf(pdf_path, pages='all', multiple_tables=True)\n", "\n", "# For specific pages: pages='1,3-5'\n", "# To guess table areas (might be slower but sometimes needed): guess=True\n", "# To specify an area: area=[top, left, bottom, right]\n", "\n", "print(f\"Found {len(tables)} tables.\")\n", "\n", "if tables:\n", "    df = tables[0] # Get the first table DataFrame\n", "    print(df.head())\n", "    # You can save directly to CSV/TSV/JSON\n", "    # tabula.convert_into(pdf_path, \"output.csv\", output_format=\"csv\", pages='all')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 10 potential <table> tags.\n", "Filtered down to 0 likely data tables.\n", "\n", "Successfully parsed 0 tables.\n"]}], "source": ["from bs4 import BeautifulSoup\n", "import pandas as pd\n", "\n", "def is_likely_data_table(table_soup, min_rows=2, min_cols=2):\n", "    \"\"\"\n", "    Applies heuristics to determine if a BeautifulSoup <table> object\n", "    is likely representing tabular data, not just layout.\n", "\n", "    Args:\n", "        table_soup: A BeautifulSoup object representing a <table> tag.\n", "        min_rows: Minimum number of <tr> rows required.\n", "        min_cols: Minimum number of <td>/<th> columns required in at least one row.\n", "\n", "    Returns:\n", "        True if the table is likely a data table, False otherwise.\n", "    \"\"\"\n", "    rows = table_soup.find_all('tr', recursive=False) # Find direct children rows\n", "\n", "    # --- Basic Dimension Check ---\n", "    if len(rows) < min_rows:\n", "        # print(\"Skipping table: Too few rows\")\n", "        return False\n", "\n", "    has_min_cols = False\n", "    col_counts = []\n", "    for r in rows:\n", "        cols = r.find_all(['td', 'th'], recursive=False) # Direct children cells\n", "        col_counts.append(len(cols))\n", "        if len(cols) >= min_cols:\n", "            has_min_cols = True\n", "\n", "    if not has_min_cols:\n", "        # print(\"Skipping table: Too few columns in any row\")\n", "        return False\n", "\n", "    # --- Head<PERSON> (Strong Indicator) ---\n", "    # Check anywhere within the table for th tags\n", "    if table_soup.find('th'):\n", "        # print(\"Keeping table: Found <th> tag(s)\")\n", "        return True # Presence of headers is a strong positive sign\n", "\n", "    # --- Caption Check (Strong Indicator) ---\n", "    # Caption should be a direct child of the table\n", "    if table_soup.find('caption', recursive=False):\n", "        # print(\"Keeping table: Found <caption> tag\")\n", "        return True # Presence of caption is a strong positive sign\n", "\n", "    # --- Content Checks (Weaker Indicators, use with caution) ---\n", "    # Avoid tables that seem to contain primarily a single large element like an image or formula?\n", "    # Example: Check if there's only one cell with significant content.\n", "    all_cells = table_soup.find_all(['td', 'th'])\n", "    non_empty_cells = [cell for cell in all_cells if cell.get_text(strip=True)]\n", "    if len(non_empty_cells) <= 1 and len(all_cells) > 0 : # Table has cells but only 0 or 1 has text\n", "         # print(\"Skipping table: Only one non-empty cell found\")\n", "         return False # Likely layout or placeholder\n", "\n", "    # --- Structural Regularity Check (Optional, can be complex) ---\n", "    # If many rows have a very different number of columns, it might be layout.\n", "    # Example: Check standard deviation of column counts or if counts are very diverse.\n", "    # if len(col_counts) > 1:\n", "    #     import statistics\n", "    #     if len(set(col_counts)) > len(col_counts) / 2: # High diversity in column counts?\n", "    #          if statistics.stdev(col_counts) > 1.0: # Arbitrary threshold\n", "    #             print(\"Skipping table: Irregular column counts\")\n", "    #             return False\n", "\n", "\n", "    # --- De<PERSON>ult Decision ---\n", "    # If it passed dimension checks but had no clear header/caption,\n", "    # should we keep it? Let's be conservative by default maybe.\n", "    # OR, if it meets dimensions and has multiple non-empty cells, maybe keep it.\n", "    # For now, let's keep it if it met dimensions and didn't fail other checks.\n", "    # print(\"Keeping table: Met dimensions, no strong negative indicators\")\n", "    return True # Passed basic checks, no strong negative signs\n", "\n", "\n", "# --- Main Parsing Logic ---\n", "html_file_path = \"data/test/html/9.html\"\n", "all_parsed_tables = [] # Store list-of-lists or DataFrames\n", "\n", "try:\n", "    with open(html_file_path, 'r', encoding='utf-8') as f:\n", "        html_content = f.read()\n", "\n", "    soup = BeautifulSoup(html_content, 'lxml') # Or 'html.parser'\n", "\n", "    potential_tables = soup.find_all('table')\n", "    print(f\"Found {len(potential_tables)} potential <table> tags.\")\n", "\n", "    likely_data_tables = [tbl for tbl in potential_tables if is_likely_data_table(tbl)]\n", "    print(f\"Filtered down to {len(likely_data_tables)} likely data tables.\")\n", "\n", "    for i, table in enumerate(likely_data_tables):\n", "        table_data = []\n", "        # Use recursive=False if you only want direct children rows/cells,\n", "        # needed if tables can be nested and you want to avoid grabbing inner table rows.\n", "        rows = (table.find('tbody') or table).find_all('tr', recursive=False)\n", "\n", "        for row in rows:\n", "            row_data = []\n", "            cells = row.find_all(['th', 'td'], recursive=False)\n", "            for cell in cells:\n", "                # Handle colspan/rowspan here if needed (more complex)\n", "                cell_text = cell.get_text(strip=True)\n", "                row_data.append(cell_text)\n", "            if row_data:\n", "                table_data.append(row_data)\n", "\n", "        if table_data:\n", "            # Optionally convert to DataFrame\n", "            try:\n", "                # Simple DataFrame conversion\n", "                df = pd.DataFrame(table_data)\n", "                # Attempt to use first row as header if it looks like one (basic check)\n", "                if len(table_data) > 1 and table.find('th'): # Or other header condition\n", "                    potential_header = table_data[0]\n", "                    if all(isinstance(h, str) and h for h in potential_header): # Non-empty strings\n", "                       df = pd.DataFrame(table_data[1:], columns=potential_header)\n", "\n", "                all_parsed_tables.append(df)\n", "                # print(f\"Successfully parsed table {i+1} into a DataFrame.\")\n", "            except Exception as df_error:\n", "                # print(f\"Could not convert table {i+1} to DataFrame: {df_error}. Storing as list of lists.\")\n", "                all_parsed_tables.append(table_data) # Fallback\n", "\n", "    # Now 'all_parsed_tables' contains only the tables deemed likely to be data\n", "    print(f\"\\nSuccessfully parsed {len(all_parsed_tables)} tables.\")\n", "    # Example: Print the first parsed table (if any)\n", "    # if all_parsed_tables:\n", "    #     print(\"\\nFirst Parsed Table:\")\n", "    #     print(all_parsed_tables[0])\n", "\n", "\n", "except FileNotFoundError:\n", "    print(f\"Error: File not found at {html_file_path}\")\n", "except Exception as e:\n", "    print(f\"An error occurred during parsing: {e}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td></td>\n", "      <td>pVanilla⁢(y|x)=pVanilla⁢(k⊕r|x)=pθ⁢(k|x)⏟Knowl...</td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  0                                                  1 2\n", "0    pVanilla⁢(y|x)=pVanilla⁢(k⊕r|x)=pθ⁢(k|x)⏟Knowl...  "]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["all_tables_data[0]"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td></td>\n", "      <td>ℒVanillasubscriptℒVanilla\\displaystyle\\mathcal...</td>\n", "      <td>=−𝔼(x,k,r)⁢[log⁡pθ⁢(k|x)⁢pθ⁢(r|x,k)]absentsubs...</td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  0                                                  1  \\\n", "0    ℒVanillasubscriptℒVanilla\\displaystyle\\mathcal...   \n", "\n", "                                                   2 3  \n", "0  =−𝔼(x,k,r)⁢[log⁡pθ⁢(k|x)⁢pθ⁢(r|x,k)]absentsubs...    "]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["all_tables_data[1]"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td></td>\n", "      <td>pRARE⁢(y|x,R⁢(x))=pRARE⁢(k⊕r|x,R⁢(x))=pθ⁢(k|x,...</td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  0                                                  1 2\n", "0    pRARE⁢(y|x,R⁢(x))=pRARE⁢(k⊕r|x,R⁢(x))=pθ⁢(k|x,...  "]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["all_tables_data[2]"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td></td>\n", "      <td>ℒRAREsubscriptℒRARE\\displaystyle\\mathcal{L}_{\\...</td>\n", "      <td>=−𝔼(x,k,r)⁢[log⁡pθ⁢(k|x,R⁢(x))⁢pθ⁢(r|x,R⁢(x),k...</td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  0                                                  1  \\\n", "0    ℒRAREsubscriptℒRARE\\displaystyle\\mathcal{L}_{\\...   \n", "\n", "                                                   2 3  \n", "0  =−𝔼(x,k,r)⁢[log⁡pθ⁢(k|x,R⁢(x))⁢pθ⁢(r|x,R⁢(x),k...    "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["all_tables_data[3]"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td></td>\n", "      <td>ℒRARE=−𝔼(x,k,r)⁢[log⁡pθ⁢(k|x,R⁢(x))]↓−𝔼(x,k,r)...</td>\n", "      <td></td>\n", "      <td>(3)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  0                                                  1 2    3\n", "0    ℒRARE=−𝔼(x,k,r)⁢[log⁡pθ⁢(k|x,R⁢(x))]↓−𝔼(x,k,r)...    (3)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["all_tables_data[4]"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td></td>\n", "      <td>yt=⨁i=1t(ki⊕ri),subscript𝑦𝑡superscriptsubscrip...</td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  0                                                  1 2\n", "0    yt=⨁i=1t(ki⊕ri),subscript𝑦𝑡superscriptsubscrip...  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["all_tables_data[5]"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td></td>\n", "      <td>pVanilla⁢(yt|x)=∏i=1tpθ⁢(ki|x,yi−1)⏟Knowledge ...</td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  0                                                  1 2\n", "0    pVanilla⁢(yt|x)=∏i=1tpθ⁢(ki|x,yi−1)⏟Knowledge ...  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["all_tables_data[6]"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td></td>\n", "      <td>pRARE⁢(yt|x,R⁢(x))=∏i=1tpθ⁢(ki|x,R⁢(x),yi−1)⏟K...</td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  0                                                  1 2\n", "0    pRARE⁢(yt|x,R⁢(x))=∏i=1tpθ⁢(ki|x,R⁢(x),yi−1)⏟K...  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["all_tables_data[7]"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td></td>\n", "      <td>ℳRARE=ℒ⁢(θA;Dt⁢r⁢a⁢i⁢nA)⇒𝔼(x,R⁢(x),y)∼Dt⁢e⁢s⁢t...</td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  0                                                  1 2\n", "0    ℳRARE=ℒ⁢(θA;Dt⁢r⁢a⁢i⁢nA)⇒𝔼(x,R⁢(x),y)∼Dt⁢e⁢s⁢t...  "]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["all_tables_data[8]"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Model</th>\n", "      <th>MedQA</th>\n", "      <th>PubMedQA</th>\n", "      <th>PubHealth</th>\n", "      <th>CoVERT</th>\n", "      <th>BioASQ</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Llama-3.1-8B</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CoT</td>\n", "      <td>61.35</td>\n", "      <td>52.00</td>\n", "      <td>33.71</td>\n", "      <td>51.67</td>\n", "      <td>77.11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>SFT</td>\n", "      <td>65.12</td>\n", "      <td>54.40</td>\n", "      <td>56.11</td>\n", "      <td>55.29</td>\n", "      <td>81.20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>RAG</td>\n", "      <td>69.60</td>\n", "      <td>74.40</td>\n", "      <td>50.73</td>\n", "      <td>57.67</td>\n", "      <td>90.15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>RARE</td>\n", "      <td>82.10</td>\n", "      <td>76.60</td>\n", "      <td>63.36</td>\n", "      <td>66.67</td>\n", "      <td>93.16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Qwen-2.5-7B</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>CoT</td>\n", "      <td>57.50</td>\n", "      <td>37.20</td>\n", "      <td>20.02</td>\n", "      <td>31.67</td>\n", "      <td>74.30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>SFT</td>\n", "      <td>62.22</td>\n", "      <td>45.80</td>\n", "      <td>57.17</td>\n", "      <td>43.33</td>\n", "      <td>78.77</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>RAG</td>\n", "      <td>66.77</td>\n", "      <td>54.80</td>\n", "      <td>46.96</td>\n", "      <td>47.00</td>\n", "      <td>90.79</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>RARE</td>\n", "      <td>78.95</td>\n", "      <td>78.63</td>\n", "      <td>63.04</td>\n", "      <td>74.14</td>\n", "      <td>93.95</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Mistral-7B-v0.3</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>CoT</td>\n", "      <td>51.77</td>\n", "      <td>34.29</td>\n", "      <td>31.68</td>\n", "      <td>50.00</td>\n", "      <td>68.94</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>SFT</td>\n", "      <td>57.58</td>\n", "      <td>44.40</td>\n", "      <td>53.16</td>\n", "      <td>41.67</td>\n", "      <td>78.52</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>RAG</td>\n", "      <td>56.48</td>\n", "      <td>70.88</td>\n", "      <td>48.01</td>\n", "      <td>46.67</td>\n", "      <td>90.78</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>RARE</td>\n", "      <td>71.25</td>\n", "      <td>76.94</td>\n", "      <td>64.91</td>\n", "      <td>67.80</td>\n", "      <td>91.96</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>GPT-4</td>\n", "      <td>80.99</td>\n", "      <td>46.40</td>\n", "      <td>34.17</td>\n", "      <td>41.33</td>\n", "      <td>83.38</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>GPT-4 + RAG</td>\n", "      <td>–</td>\n", "      <td>75.20</td>\n", "      <td>64.42</td>\n", "      <td>65.67</td>\n", "      <td>–</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>GPT-3.5</td>\n", "      <td>57.19</td>\n", "      <td>49.60</td>\n", "      <td>46.23</td>\n", "      <td>37.67</td>\n", "      <td>74.30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>DeepSeek-R1-<PERSON><PERSON><PERSON>-</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>Llama-8B + RAG</td>\n", "      <td>72.66</td>\n", "      <td>73.49</td>\n", "      <td>50.12</td>\n", "      <td>50.00</td>\n", "      <td>92.58</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   Model  MedQA PubMedQA PubHealth CoVERT BioASQ\n", "0           Llama-3.1-8B   None     None      None   None   None\n", "1                    CoT  61.35    52.00     33.71  51.67  77.11\n", "2                    SFT  65.12    54.40     56.11  55.29  81.20\n", "3                    RAG  69.60    74.40     50.73  57.67  90.15\n", "4                   RARE  82.10    76.60     63.36  66.67  93.16\n", "5            Qwen-2.5-7B   None     None      None   None   None\n", "6                    CoT  57.50    37.20     20.02  31.67  74.30\n", "7                    SFT  62.22    45.80     57.17  43.33  78.77\n", "8                    RAG  66.77    54.80     46.96  47.00  90.79\n", "9                   RARE  78.95    78.63     63.04  74.14  93.95\n", "10       Mistral-7B-v0.3   None     None      None   None   None\n", "11                   CoT  51.77    34.29     31.68  50.00  68.94\n", "12                   SFT  57.58    44.40     53.16  41.67  78.52\n", "13                   RAG  56.48    70.88     48.01  46.67  90.78\n", "14                  RARE  71.25    76.94     64.91  67.80  91.96\n", "15                 GPT-4  80.99    46.40     34.17  41.33  83.38\n", "16           GPT-4 + <PERSON>G      –    75.20     64.42  65.67      –\n", "17               GPT-3.5  57.19    49.60     46.23  37.67  74.30\n", "18  DeepSeek-R1-<PERSON><PERSON>ill-                                        \n", "19        Llama-8B + RAG  72.66    73.49     50.12  50.00  92.58"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["all_tables_data[9]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from bs4 import BeautifulSoup\n", "import pandas as pd\n", "from openai import OpenAI\n", "\n", "import os\n", "from dotenv import load_dotenv, find_dotenv\n", "load_dotenv(find_dotenv())\n", "\n", "from prompts import table_extraction_html\n", "\n", "client = OpenAI(\n", "    api_key=os.getenv(\"GEMINI_API_KEY\"), \n", "    base_url=os.getenv(\"GEMINI_BASE_URL\")\n", "    )\n", "\n", "html_file_path = \"data/test/html/9.html\"\n", "all_parsed_tables = [] # Store list-of-lists or DataFrames\n", "\n", "try:\n", "    with open(html_file_path, 'r', encoding='utf-8') as f:\n", "        html_content = f.read()\n", "\n", "    soup = BeautifulSoup(html_content, 'lxml') # Or 'html.parser'\n", "    table_html_prompt = f\"{table_extraction_html()}\\n\\n{html_content}\"\n", "\n", "    # Send to LLM API\n", "    response = client.chat.completions.create(\n", "            messages=[{\n", "                    \"role\": \"user\",\n", "                    \"content\": [\n", "                        {\n", "                            \"type\": \"text\",\n", "                            \"text\": table_html_prompt\n", "                            # Use the text content of the prompt\n", "                    },\n", "                 ]\n", "            }],\n", "            model=os.getenv(\"GEMINI_MODEL\")\n", "        )\n", "\n", "    # Add processed text to collection\n", "    processed_tables = response.choices[0].message.content.strip()\n", "\n", "\n", "except FileNotFoundError:\n", "    print(f\"Error: File not found at {html_file_path}\")\n", "except Exception as e:\n", "    print(f\"An error occurred during parsing: {e}\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["'```json\\n{\\n    \"tables\": [\\n        {\\n            \"table_number\": 1,\\n            \"table_title\": \"Performance of Models Trained with the RARE Method (ACC)\",\\n            \"table_description\": null,\\n            \"table_content\": \"| Model | MedQA | PubMedQA | PubHealth | CoVERT | BioASQ |\\\\n|---|---|---|---|---|---|\\\\n| **Llama-3.1-8B** |  |  |  |  |  |\\\\n| CoT | 61.35 | 52.00 | 33.71 | 51.67 | 77.11 |\\\\n| SFT | 65.12 | 54.40 | 56.11 | 55.29 | 81.20 |\\\\n| RAG | 69.60 | 74.40 | 50.73 | 57.67 | 90.15 |\\\\n| **RARE** | **82.10** | **76.60** | **63.36** | **66.67** | **93.16** |\\\\n| **Qwen-2.5-7B** |  |  |  |  |  |\\\\n| CoT | 57.50 | 37.20 | 20.02 | 31.67 | 74.30 |\\\\n| SFT | 62.22 | 45.80 | 57.17 | 43.33 | 78.77 |\\\\n| RAG | 66.77 | 54.80 | 46.96 | 47.00 | 90.79 |\\\\n| **RARE** | **78.95** | **78.63** | **63.04** | **74.14** | **93.95** |\\\\n| **Mistral-7B-v0.3** |  |  |  |  |  |\\\\n| CoT | 51.77 | 34.29 | 31.68 | 50.00 | 68.94 |\\\\n| SFT | 57.58 | 44.40 | 53.16 | 41.67 | 78.52 |\\\\n| RAG | 56.48 | 70.88 | 48.01 | 46.67 | 90.78 |\\\\n| **RARE** | **71.25** | **76.94** | **64.91** | **67.80** | **91.96** |\\\\n| GPT-4 | 80.99 | 46.40 | 34.17 | 41.33 | 83.38 |\\\\n| GPT-4 + RAG | – | 75.20 | 64.42 | 65.67 | – |\\\\n| GPT-3.5 | 57.19 | 49.60 | 46.23 | 37.67 | 74.30 |\\\\n| DeepSeek-R1-Distill- |  |  |  |  |  |\\\\n| Llama-8B + RAG | 72.66 | 73.49 | 50.12 | 50.00 | 92.58 |\\\\n\"\\n        }\\n    ]\\n}\\n```'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["processed_tables"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('```json\\n'\n", " '{\\n'\n", " '    \"tables\": [\\n'\n", " '        {\\n'\n", " '            \"table_number\": 1,\\n'\n", " '            \"table_title\": \"Performance of Models Trained with the RARE '\n", " 'Method (ACC)\",\\n'\n", " '            \"table_description\": null,\\n'\n", " '            \"table_content\": \"| Model | MedQA | PubMedQA | PubHealth | '\n", " 'CoVERT | BioASQ |\\\\n|---|---|---|---|---|---|\\\\n| **Llama-3.1-8B** |  |  |  '\n", " '|  |  |\\\\n| CoT | 61.35 | 52.00 | 33.71 | 51.67 | 77.11 |\\\\n| SFT | 65.12 | '\n", " '54.40 | 56.11 | 55.29 | 81.20 |\\\\n| RAG | 69.60 | 74.40 | 50.73 | 57.67 | '\n", " '90.15 |\\\\n| **RARE** | **82.10** | **76.60** | **63.36** | **66.67** | '\n", " '**93.16** |\\\\n| **Qwen-2.5-7B** |  |  |  |  |  |\\\\n| CoT | 57.50 | 37.20 | '\n", " '20.02 | 31.67 | 74.30 |\\\\n| SFT | 62.22 | 45.80 | 57.17 | 43.33 | 78.77 '\n", " '|\\\\n| RAG | 66.77 | 54.80 | 46.96 | 47.00 | 90.79 |\\\\n| **RARE** | **78.95** '\n", " '| **78.63** | **63.04** | **74.14** | **93.95** |\\\\n| **Mistral-7B-v0.3** |  '\n", " '|  |  |  |  |\\\\n| CoT | 51.77 | 34.29 | 31.68 | 50.00 | 68.94 |\\\\n| SFT | '\n", " '57.58 | 44.40 | 53.16 | 41.67 | 78.52 |\\\\n| RAG | 56.48 | 70.88 | 48.01 | '\n", " '46.67 | 90.78 |\\\\n| **RARE** | **71.25** | **76.94** | **64.91** | **67.80** '\n", " '| **91.96** |\\\\n| GPT-4 | 80.99 | 46.40 | 34.17 | 41.33 | 83.38 |\\\\n| GPT-4 '\n", " '+ RAG | – | 75.20 | 64.42 | 65.67 | – |\\\\n| GPT-3.5 | 57.19 | 49.60 | 46.23 '\n", " '| 37.67 | 74.30 |\\\\n| DeepSeek-R1-Distill- |  |  |  |  |  |\\\\n| Llama-8B + '\n", " 'RAG | 72.66 | 73.49 | 50.12 | 50.00 | 92.58 |\\\\n\"\\n'\n", " '        }\\n'\n", " '    ]\\n'\n", " '}\\n'\n", " '```')\n"]}], "source": ["import pprint \n", "\n", "pprint.pprint(processed_tables)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 2}
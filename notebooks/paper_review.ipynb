{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Errno 2] No such file or directory: 'Development/Websites/fastmoatless/'\n", "/Users/<USER>/Development/fastmoatless\n"]}], "source": ["%cd Development/Websites/fastmoatless/"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from fasthtml.oauth import OAuth, GoogleAppClient, redir_url\n", "from fasthtml.common import *\n", "from home_components import *\n", "from content import *\n", "from openai import OpenAI\n", "import asyncio\n", "import aiohttp\n", "from bs4 import BeautifulSoup\n", "from urllib.parse import urlencode\n", "import json\n", "from pydantic import BaseModel\n", "from datetime import datetime\n", "import os, openai, time\n", "import ast\n", "import sqlite3\n", "from dotenv import load_dotenv, find_dotenv\n", "\n", "load_dotenv(find_dotenv())"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["class SummaryModel(BaseModel):\n", "    abs_url: str\n", "    pdf_url: str\n", "    title: str\n", "    authors: List[str]           # Assuming authors are stored as comma-separated strings\n", "    updated: str\n", "    published: str\n", "    abstract: str\n", "    summary: str\n", "    tags: List[str]              # Assuming tags are stored as comma-separated strings"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Database setup\n", "db = database('data/summaries.db')\n", "summaries = db.t.summaries\n", "users = db.t.users\n", "if summaries not in db.t:\n", "    summaries.create(abs_url=str, pdf_url=str, title=str, authors=list, updated=str, published=str, abstract=str, summary=str, tags=list, pk=\"abs_url\")\n", "if users not in db.t:\n", "    users.create(user_id=int, pk=\"user_id\")\n", "Summary = summaries.dataclass()\n", "\n", "client = OpenAI(\n", "    api_key=os.getenv(\"GEMINI_API_KEY\"), \n", "    base_url=os.getenv(\"GEMINI_BASE_URL\")\n", "    )"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["fastlite.core.Summaries"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["Summary"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def extract_information(response):\n", "    soup = BeautifulSoup(response, 'xml')\n", "    entries = soup.find_all('entry')\n", "    papers = []\n", "    for entry in entries:\n", "        try:\n", "            paper = {}\n", "            paper['abs_url'] = entry.id.text\n", "            paper['title'] = entry.title.text\n", "            paper['abstract'] = entry.summary.text\n", "\n", "            # Convert to datetime format, then to dates\n", "            updated_datetime = datetime.strptime(entry.updated.text, \"%Y-%m-%dT%H:%M:%SZ\")\n", "            paper[\"updated\"] = updated_datetime.strftime(\"%Y-%m-%d\")\n", "            published_datetime = datetime.strptime(entry.published.text, \"%Y-%m-%dT%H:%M:%SZ\")\n", "            paper[\"published\"] = published_datetime.strftime(\"%Y-%m-%d\")\n", "            \n", "            # Extract authors\n", "            authors = entry.find_all('author')\n", "            paper['authors'] = [author.find('name').text for author in authors]\n", "            \n", "            # Extract primary category\n", "            primary_category = entry.find('arxiv:primary_category')\n", "            if primary_category:\n", "                paper['primary_category'] = primary_category['term']\n", "   \n", "            # Extract additional categories\n", "            categories = entry.find_all('category')\n", "            paper['categories'] = [category['term'] for category in categories]\n", "            \n", "            # Extract PDF link\n", "            pdf_link = entry.find('link', title='pdf')\n", "            if pdf_link:\n", "                paper['pdf_url'] = pdf_link['href']\n", "            \n", "            # Append the paper info to the list\n", "            papers.append(paper)\n", "        except Exception as e:\n", "            print(\"Error processing paper:\", e)\n", "    return papers"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# API url setup\n", "params = {\n", "            \"search_query\": \"%22large%20language%20models%22\",\n", "            \"start\": 0,\n", "            \"max_results\": 100,\n", "            \"sortBy\": \"submittedDate\",\n", "            \"sortOrder\": \"descending\"\n", "        }\n", "url = f'http://export.arxiv.org/api/query?{urlencode(params)}'"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "def fetch_papers():\n", "    \"\"\"Fetch latest papers synchronously using requests.\"\"\"\n", "    response = requests.get(url)  # Make a synchronous GET request\n", "    response.raise_for_status()   # Optional: raise an exception for HTTP errors\n", "    data = response.content       # Get the raw data from the response\n", "    papers = extract_information(data)  # Assume extract_information is now a synchronous function\n", "    return papers"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["data = fetch_papers()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'abs_url': 'http://arxiv.org/abs/2503.05683v1',\n", "  'title': 'Understanding the Limits of Lifelong Knowledge Editing in LLMs',\n", "  'abstract': \"  Keeping large language models factually up-to-date is crucial for deployment,\\nyet costly retraining remains a challenge. Knowledge editing offers a promising\\nalternative, but methods are only tested on small-scale or synthetic edit\\nbenchmarks. In this work, we aim to bridge research into lifelong knowledge\\nediting to real-world edits at practically relevant scale. We first introduce\\nWikiBigEdit; a large-scale benchmark of real-world Wikidata edits, built to\\nautomatically extend lifelong for future-proof benchmarking. In its first\\ninstance, it includes over 500K question-answer pairs for knowledge editing\\nalongside a comprehensive evaluation pipeline. Finally, we use WikiBigEdit to\\nstudy existing knowledge editing techniques' ability to incorporate large\\nvolumes of real-world facts and contrast their capabilities to generic\\nmodification techniques such as retrieval augmentation and continual finetuning\\nto acquire a complete picture of the practical extent of current lifelong\\nknowledge editing.\\n\",\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON>',\n", "   'Zeynep Akata',\n", "   '<PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL', 'cs.LG'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05683v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05659v1',\n", "  'title': 'A Survey of Large Language Model Empowered Agents for Recommendation and\\n  Search: Towards Next-Generation Information Retrieval',\n", "  'abstract': '  Information technology has profoundly altered the way humans interact with\\ninformation. The vast amount of content created, shared, and disseminated\\nonline has made it increasingly difficult to access relevant information. Over\\nthe past two decades, search and recommendation systems (collectively referred\\nto as information retrieval systems) have evolved significantly to address\\nthese challenges. Recent advances in large language models (LLMs) have\\ndemonstrated capabilities that surpass human performance in various\\nlanguage-related tasks and exhibit general understanding, reasoning, and\\ndecision-making abilities. This paper explores the transformative potential of\\nlarge language model agents in enhancing search and recommendation systems. We\\ndiscuss the motivations and roles of LLM agents, and establish a classification\\nframework to elaborate on the existing research. We highlight the immense\\npotential of LLM agents in addressing current challenges in search and\\nrecommendation, providing insights into future research directions. This paper\\nis the first to systematically review and classify the research on LLM agents\\nin these domains, offering a novel perspective on leveraging this advanced AI\\ntechnology for information retrieval. To help understand the existing works, we\\nlist the existing papers on agent-based simulation with large language models\\nat this link:\\nhttps://github.com/tsinghua-fib-lab/LLM-Agent-for-Recommendation-and-Search.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>'],\n", "  'primary_category': 'cs.IR',\n", "  'categories': ['cs.IR'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05659v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05620v1',\n", "  'title': 'Learning LLM Preference over Intra-Dialogue Pairs: A Framework for\\n  Utterance-level Understandings',\n", "  'abstract': '  Large language models (LLMs) have demonstrated remarkable capabilities in\\nhandling complex dialogue tasks without requiring use case-specific\\nfine-tuning. However, analyzing live dialogues in real-time necessitates\\nlow-latency processing systems, making it impractical to deploy models with\\nbillions of parameters due to latency constraints. As a result, practitioners\\noften prefer smaller models with millions of parameters, trained on\\nhigh-quality, human-annotated datasets. Yet, curating such datasets is both\\ntime-consuming and costly. Consequently, there is a growing need to combine the\\nscalability of LLM-generated labels with the precision of human annotations,\\nenabling fine-tuned smaller models to achieve both higher speed and accuracy\\ncomparable to larger models. In this paper, we introduce a simple yet effective\\nframework to address this challenge. Our approach is specifically designed for\\nper-utterance classification problems, which encompass tasks such as intent\\ndetection, dialogue state tracking, and more. To mitigate the impact of\\nlabeling errors from LLMs -- the primary source of inaccuracies in student\\nmodels -- we propose a noise-reduced preference learning loss. Experimental\\nresults demonstrate that our method significantly improves accuracy across\\nutterance-level dialogue tasks, including sentiment detection (over $2\\\\%$),\\ndialogue act classification (over $1.5\\\\%$), etc.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON><PERSON>',\n", "   'Luyang Kong',\n", "   '<PERSON>u',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL', 'cs.AI'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05620v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05613v1',\n", "  'title': 'A Survey on Sparse Autoencoders: Interpreting the Internal Mechanisms of\\n  Large Language Models',\n", "  'abstract': '  Large Language Models (LLMs) have revolutionized natural language processing,\\nyet their internal mechanisms remain largely opaque. Recently, mechanistic\\ninterpretability has attracted significant attention from the research\\ncommunity as a means to understand the inner workings of LLMs. Among various\\nmechanistic interpretability approaches, Sparse Autoencoders (SAEs) have\\nemerged as a particularly promising method due to their ability to disentangle\\nthe complex, superimposed features within LLMs into more interpretable\\ncomponents. This paper presents a comprehensive examination of SAEs as a\\npromising approach to interpreting and understanding LLMs. We provide a\\nsystematic overview of SAE principles, architectures, and applications\\nspecifically tailored for LLM analysis, covering theoretical foundations,\\nimplementation strategies, and recent developments in sparsity mechanisms. We\\nalso explore how SAEs can be leveraged to explain the internal workings of\\nLLMs, steer model behaviors in desired directions, and develop more transparent\\ntraining methodologies for future models. Despite the challenges that remain\\naround SAE implementation and scaling, they continue to provide valuable tools\\nfor understanding the internal mechanisms of large language models.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON>',\n", "   'Xuansheng Wu',\n", "   '<PERSON><PERSON>',\n", "   'Daking Rai',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.LG',\n", "  'categories': ['cs.LG', 'cs.AI', 'cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05613v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05592v1',\n", "  'title': 'R1-Searcher: Incentivizing the Search Capability in LLMs via\\n  Reinforcement Learning',\n", "  'abstract': '  Existing Large Reasoning Models (LRMs) have shown the potential of\\nreinforcement learning (RL) to enhance the complex reasoning capabilities of\\nLarge Language Models~(LLMs). While they achieve remarkable performance on\\nchallenging tasks such as mathematics and coding, they often rely on their\\ninternal knowledge to solve problems, which can be inadequate for\\ntime-sensitive or knowledge-intensive questions, leading to inaccuracies and\\nhallucinations. To address this, we propose \\\\textbf{R1-Searcher}, a novel\\ntwo-stage outcome-based RL approach designed to enhance the search capabilities\\nof LLMs. This method allows LLMs to autonomously invoke external search systems\\nto access additional knowledge during the reasoning process. Our framework\\nrelies exclusively on RL, without requiring process rewards or distillation for\\na cold start. % effectively generalizing to out-of-domain datasets and\\nsupporting both Base and Instruct models. Our experiments demonstrate that our\\nmethod significantly outperforms previous strong RAG methods, even when\\ncompared to the closed-source GPT-4o-mini.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON><PERSON>',\n", "   'Jinhao Jiang',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.AI',\n", "  'categories': ['cs.AI', 'cs.CL', 'cs.IR'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05592v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05565v1',\n", "  'title': 'Evaluating open-source Large Language Models for automated fact-checking',\n", "  'abstract': \"  The increasing prevalence of online misinformation has heightened the demand\\nfor automated fact-checking solutions. Large Language Models (LLMs) have\\nemerged as potential tools for assisting in this task, but their effectiveness\\nremains uncertain. This study evaluates the fact-checking capabilities of\\nvarious open-source LLMs, focusing on their ability to assess claims with\\ndifferent levels of contextual information. We conduct three key experiments:\\n(1) evaluating whether LLMs can identify the semantic relationship between a\\nclaim and a fact-checking article, (2) assessing models' accuracy in verifying\\nclaims when given a related fact-checking article, and (3) testing LLMs'\\nfact-checking abilities when leveraging data from external knowledge sources\\nsuch as Google and Wikipedia. Our results indicate that LLMs perform well in\\nidentifying claim-article connections and verifying fact-checked stories but\\nstruggle with confirming factual news, where they are outperformed by\\ntraditional fine-tuned models such as RoBERTa. Additionally, the introduction\\nof external knowledge does not significantly enhance LLMs' performance, calling\\nfor more tailored approaches. Our findings highlight both the potential and\\nlimitations of LLMs in automated fact-checking, emphasizing the need for\\nfurther refinements before they can reliably replace human fact-checkers.\\n\",\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': [\"<PERSON><PERSON>\",\n", "   '<PERSON> Corso',\n", "   '<PERSON>',\n", "   '<PERSON>'],\n", "  'primary_category': 'cs.CY',\n", "  'categories': ['cs.CY', 'cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05565v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05551v1',\n", "  'title': 'Revitalizing Saturated Benchmarks: A Weighted Metric Approach for\\n  Differentiating Large Language Model Performance',\n", "  'abstract': '  Existing benchmarks are becoming saturated and struggle to separate model\\nperformances due to factors like data contamination and advancing LLM\\ncapabilities. This paper introduces EMDM (Enhanced Model Differentiation\\nMetric), a novel weighted metric that revitalizes benchmarks by enhancing model\\nseparation. EMDM integrates final answer and Chain-of-Thought (CoT) reasoning\\ncorrectness, assigning weights based on the complexity and reasoning depth\\nrequired to solve a given sample in the evaluation data. Using a baseline LLM\\nin two setups-Unguided, where the model has no prior exposure to test samples,\\nand Guided, where the model has prior knowledge of the desired answer-EMDM\\ndistinguishes instances of varying difficulty. The CoT and answer correctness\\nfrom these setups inform an optimization objective for weight assignment,\\nresulting in a more nuanced evaluation of model performance. Compared to the\\nexact match (EM) metric, which achieves 17% separation on ARC-Challenge, EMDM\\nachieves 46%, demonstrating its effectiveness in differentiating models based\\non reasoning and knowledge requirements.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.LG',\n", "  'categories': ['cs.LG'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05551v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05530v1',\n", "  'title': 'Leveraging Approximate Caching for Faster Retrieval-Augmented Generation',\n", "  'abstract': '  Retrieval-augmented generation (RAG) enhances the reliability of large\\nlanguage model (LLM) answers by integrating external knowledge. However, RAG\\nincreases the end-to-end inference time since looking for relevant documents\\nfrom large vector databases is computationally expensive. To address this, we\\nintroduce Proximity, an approximate key-value cache that optimizes the RAG\\nworkflow by leveraging similarities in user queries. Instead of treating each\\nquery independently, Proximity reuses previously retrieved documents when\\nsimilar queries appear, reducing reliance on expensive vector database lookups.\\nWe evaluate Proximity on the MMLU and MedRAG benchmarks, demonstrating that it\\nsignificantly improves retrieval efficiency while maintaining response\\naccuracy. Proximity reduces retrieval latency by up to 59% while maintaining\\naccuracy and lowers the computational burden on the vector database. We also\\nexperiment with different similarity thresholds and quantify the trade-off\\nbetween speed and recall. Our work shows that approximate caching is a viable\\nand effective strategy for optimizing RAG-based systems.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON>',\n", "   '<PERSON> Ji',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.DB',\n", "  'categories': ['cs.DB', 'cs.LG', 'cs.PF'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05530v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05529v1',\n", "  'title': 'PoSSUM: A Protocol for Surveying Social-media Users with Multimodal LLMs',\n", "  'abstract': \"  This paper introduces PoSSUM, an open-source protocol for unobtrusive polling\\nof social-media users via multimodal Large Language Models (LLMs). PoSSUM\\nleverages users' real-time posts, images, and other digital traces to create\\nsilicon samples that capture information not present in the LLM's training\\ndata. To obtain representative estimates, PoSSUM employs Multilevel Regression\\nand Post-Stratification (MrP) with structured priors to counteract the\\nobservable selection biases of social-media platforms. The protocol is\\nvalidated during the 2024 U.S. Presidential Election, for which five PoSSUM\\npolls were conducted and published on GitHub and X. In the final poll, fielded\\nOctober 17-26 with a synthetic sample of 1,054 X users, PoSSUM accurately\\npredicted the outcomes in 50 of 51 states and assigned the Republican candidate\\na win probability of 0.65. Notably, it also exhibited lower state-level bias\\nthan most established pollsters. These results demonstrate PoSSUM's potential\\nas a fully automated, unobtrusive alternative to traditional survey methods.\\n\",\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON>'],\n", "  'primary_category': 'stat.AP',\n", "  'categories': ['stat.AP', 'cs.SI'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05529v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05516v1',\n", "  'title': 'Cognitive Bias Detection Using Advanced Prompt Engineering',\n", "  'abstract': \"  Cognitive biases, systematic deviations from rationality in judgment, pose\\nsignificant challenges in generating objective content. This paper introduces a\\nnovel approach for real-time cognitive bias detection in user-generated text\\nusing large language models (LLMs) and advanced prompt engineering techniques.\\nThe proposed system analyzes textual data to identify common cognitive biases\\nsuch as confirmation bias, circular reasoning, and hidden assumption. By\\ndesigning tailored prompts, the system effectively leverages LLMs' capabilities\\nto both recognize and mitigate these biases, improving the quality of\\nhuman-generated content (e.g., news, media, reports). Experimental results\\ndemonstrate the high accuracy of our approach in identifying cognitive biases,\\noffering a valuable tool for enhancing content objectivity and reducing the\\nrisks of biased decision-making.\\n\",\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON>-<PERSON>',\n", "   '<PERSON><PERSON>'],\n", "  'primary_category': 'cs.CY',\n", "  'categories': ['cs.CY', 'cs.AI', 'cs.CL', 'cs.HC'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05516v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05505v1',\n", "  'title': 'Statistical Guarantees of Correctness Coverage for Medical\\n  Multiple-Choice Question Answering',\n", "  'abstract': \"  Large language models (LLMs) are increasingly deployed in real-world\\nquestion-answering (QA) applications. However, LLMs have been proven to\\ngenerate hallucinations and nonfactual information, undermining their\\ntrustworthiness in high-stakes medical tasks. Conformal prediction (CP) is\\nwell-known to be model-agnostic and distribution-free, which creates\\nstatistically rigorous prediction sets in classification tasks. In this work,\\nwe for the first time adapt the CP framework to medical multiple-choice\\nquestion-answering (MCQA) tasks, by correlating the nonconformity score with\\nthe frequency score of correct options grounded in self-consistency theory,\\nassuming no access to internal model information. Considering that the adapted\\nCP framework can only control the (mis)coverage rate, we employ a risk control\\nframework, which can manage task-specific metrics by devising a monotonically\\ndecreasing loss function. We evaluate our framework on 3 popular medical MCQA\\ndatasets utilizing 4 ``off-the-shelf'' LLMs. Empirical results demonstrate that\\nwe achieve user-specified average (or marginal) error rates on the test set.\\nFurthermore, we observe that the average prediction set size (APSS) on the test\\nset decreases as the risk level increases, which concludes a promising\\nevaluation metric for the uncertainty of LLMs.\\n\",\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05505v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05493v1',\n", "  'title': 'Benchmarking LLMs in Recommendation Tasks: A Comparative Evaluation with\\n  Conventional Recommenders',\n", "  'abstract': '  In recent years, integrating large language models (LLMs) into recommender\\nsystems has created new opportunities for improving recommendation quality.\\nHowever, a comprehensive benchmark is needed to thoroughly evaluate and compare\\nthe recommendation capabilities of LLMs with traditional recommender systems.\\nIn this paper, we introduce RecBench, which systematically investigates various\\nitem representation forms (including unique identifier, text, semantic\\nembedding, and semantic identifier) and evaluates two primary recommendation\\ntasks, i.e., click-through rate prediction (CTR) and sequential recommendation\\n(SeqRec). Our extensive experiments cover up to 17 large models and are\\nconducted across five diverse datasets from fashion, news, video, books, and\\nmusic domains. Our findings indicate that LLM-based recommenders outperform\\nconventional recommenders, achieving up to a 5% AUC improvement in the CTR\\nscenario and up to a 170% NDCG@10 improvement in the SeqRec scenario. However,\\nthese substantial performance gains come at the expense of significantly\\nreduced inference efficiency, rendering the LLM-as-RS paradigm impractical for\\nreal-time recommendation environments. We aim for our findings to inspire\\nfuture research, including recommendation-specific model acceleration methods.\\nWe will release our code, data, configurations, and platform to enable other\\nresearchers to reproduce and build upon our experimental results.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   'Lu Fan',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.IR',\n", "  'categories': ['cs.IR', 'cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05493v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05481v1',\n", "  'title': 'Maximum Hallucination Standards for Domain-Specific Large Language\\n  Models',\n", "  'abstract': '  Large language models (LLMs) often generate inaccurate yet credible-sounding\\ncontent, known as hallucinations. This inherent feature of LLMs poses\\nsignificant risks, especially in critical domains. I analyze LLMs as a new\\nclass of engineering products, treating hallucinations as a product attribute.\\nI demonstrate that, in the presence of imperfect awareness of LLM\\nhallucinations and misinformation externalities, net welfare improves when the\\nmaximum acceptable level of LLM hallucinations is designed to vary with two\\ndomain-specific factors: the willingness to pay for reduced LLM hallucinations\\nand the marginal damage associated with misinformation.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'econ.GN',\n", "  'categories': ['econ.GN', 'q-fin.EC'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05481v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05473v1',\n", "  'title': 'The Society of HiveMind: Multi-Agent Optimization of Foundation Model\\n  Swarms to Unlock the Potential of Collective Intelligence',\n", "  'abstract': '  Multi-agent systems address issues of accessibility and scalability of\\nartificial intelligence (AI) foundation models, which are often represented by\\nlarge language models. We develop a framework - the \"Society of HiveMind\"\\n(SOHM) - that orchestrates the interaction between multiple AI foundation\\nmodels, imitating the observed behavior of animal swarms in nature by following\\nmodern evolutionary theories. On the one hand, we find that the SOHM provides a\\nnegligible benefit on tasks that mainly require real-world knowledge. On the\\nother hand, we remark a significant improvement on tasks that require intensive\\nlogical reasoning, indicating that multi-agent systems are capable of\\nincreasing the reasoning capabilities of the collective compared to the\\nindividual agents. Our findings demonstrate the potential of combining a\\nmultitude of diverse AI foundation models to form an artificial swarm\\nintelligence capable of self-improvement through interactions with a given\\nenvironment.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON>', '<PERSON>'],\n", "  'primary_category': 'cs.NE',\n", "  'categories': ['cs.NE', 'cs.AI'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05473v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05449v1',\n", "  'title': 'LLM-based Iterative Approach to Metamodeling in Automotive',\n", "  'abstract': \"  In this paper, we introduce an automated approach to domain-specific\\nmetamodel construction relying on Large Language Model (LLM). The main focus is\\nadoption in automotive domain. As outcome, a prototype was implemented as web\\nservice using Python programming language, while OpenAI's GPT-4o was used as\\nthe underlying LLM. Based on the initial experiments, this approach\\nsuccessfully constructs Ecore metamodel based on set of automotive requirements\\nand visualizes it making use of PlantUML notation, so human experts can provide\\nfeedback in order to refine the result. Finally, locally deployable solution is\\nalso considered, including the limitations and additional steps required.\\n\",\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>'],\n", "  'primary_category': 'cs.SE',\n", "  'categories': ['cs.SE', 'cs.AI'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05449v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05445v1',\n", "  'title': 'Are Your LLM-based Text-to-SQL Models Secure? Exploring SQL Injection\\n  via Backdoor Attacks',\n", "  'abstract': '  Large language models (LLMs) have shown state-of-the-art results in\\ntranslating natural language questions into SQL queries (Text-to-SQL), a\\nlong-standing challenge within the database community. However, security\\nconcerns remain largely unexplored, particularly the threat of backdoor\\nattacks, which can introduce malicious behaviors into models through\\nfine-tuning with poisoned datasets. In this work, we systematically investigate\\nthe vulnerabilities of LLM-based Text-to-SQL models and present ToxicSQL, a\\nnovel backdoor attack framework. Our approach leverages stealthy {semantic and\\ncharacter-level triggers} to make backdoors difficult to detect and remove,\\nensuring that malicious behaviors remain covert while maintaining high model\\naccuracy on benign inputs. Furthermore, we propose leveraging SQL injection\\npayloads as backdoor targets, enabling the generation of malicious yet\\nexecutable SQL queries, which pose severe security and privacy risks in\\nlanguage model-based SQL development. We demonstrate that injecting only 0.44%\\nof poisoned data can result in an attack success rate of 79.41%, posing a\\nsignificant risk to database security. Additionally, we propose detection and\\nmitigation strategies to enhance model reliability. Our findings highlight the\\nurgent need for security-aware Text-to-SQL development, emphasizing the\\nimportance of robust defenses against backdoor threats.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   'Jiale Lao',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.CR',\n", "  'categories': ['cs.CR', 'cs.DB'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05445v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05394v1',\n", "  'title': 'Static Program Analysis Guided LLM Based Unit Test Generation',\n", "  'abstract': '  We describe a novel approach to automating unit test generation for Java\\nmethods using large language models (LLMs). Existing LLM-based approaches rely\\non sample usage(s) of the method to test (focal method) and/or provide the\\nentire class of the focal method as input prompt and context. The former\\napproach is often not viable due to the lack of sample usages, especially for\\nnewly written focal methods. The latter approach does not scale well enough;\\nthe bigger the complexity of the focal method and larger associated class, the\\nharder it is to produce adequate test code (due to factors such as exceeding\\nthe prompt and context lengths of the underlying LLM). We show that augmenting\\nprompts with \\\\emph{concise} and \\\\emph{precise} context information obtained by\\nprogram analysis %of the focal method increases the effectiveness of generating\\nunit test code through LLMs. We validate our approach on a large commercial\\nJava project and a popular open-source Java project.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON>'],\n", "  'primary_category': 'cs.SE',\n", "  'categories': ['cs.SE', 'cs.AI'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05394v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05388v1',\n", "  'title': 'Ontology Generation using Large Language Models',\n", "  'abstract': '  The ontology engineering process is complex, time-consuming, and error-prone,\\neven for experienced ontology engineers. In this work, we investigate the\\npotential of Large Language Models (LLMs) to provide effective OWL ontology\\ndrafts directly from ontological requirements described using user stories and\\ncompetency questions. Our main contribution is the presentation and evaluation\\nof two new prompting techniques for automated ontology development: Memoryless\\nCQbyCQ and Ontogenia. We also emphasize the importance of three structural\\ncriteria for ontology assessment, alongside expert qualitative evaluation,\\nhighlighting the need for a multi-dimensional evaluation in order to capture\\nthe quality and usability of the generated ontologies. Our experiments,\\nconducted on a benchmark dataset of ten ontologies with 100 distinct CQs and 29\\ndifferent user stories, compare the performance of three LLMs using the two\\nprompting techniques. The results demonstrate improvements over the current\\nstate-of-the-art in LLM-supported ontology engineering. More specifically, the\\nmodel OpenAI o1-preview with Ontogenia produces ontologies of sufficient\\nquality to meet the requirements of ontology engineers, significantly\\noutperforming novice ontology engineers in modelling ability. However, we still\\nnote some common mistakes and variability of result quality, which is important\\nto take into account when using LLMs for ontology authoring support. We discuss\\nthese limitations and propose directions for future research.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>'],\n", "  'primary_category': 'cs.AI',\n", "  'categories': ['cs.AI'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05388v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05379v1',\n", "  'title': 'R1-Omni: Explainable Omni-Multimodal Emotion Recognition with\\n  Reinforcing Learning',\n", "  'abstract': \"  In this work, we present the first application of Reinforcement Learning with\\nVerifiable Reward (RLVR) to an Omni-multimodal large language model in the\\ncontext of emotion recognition, a task where both visual and audio modalities\\nplay crucial roles. We leverage RLVR to optimize the Omni model, significantly\\nenhancing its performance in three key aspects: reasoning capability, emotion\\nrecognition accuracy, and generalization ability. The introduction of RLVR not\\nonly improves the model's overall performance on in-distribution data but also\\ndemonstrates superior robustness when evaluated on out-of-distribution\\ndatasets. More importantly, the improved reasoning capability enables clear\\nanalysis of the contributions of different modalities, particularly visual and\\naudio information, in the emotion recognition process. This provides valuable\\ninsights into the optimization of multimodal large language models.\\n\",\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>'],\n", "  'primary_category': 'cs.LG',\n", "  'categories': ['cs.LG', 'cs.CV'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05379v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05371v1',\n", "  'title': 'Shifting Perspectives: Steering Vector Ensembles for Robust Bias\\n  Mitigation in LLMs',\n", "  'abstract': '  We present a novel approach to bias mitigation in large language models\\n(LLMs) by applying steering vectors to modify model activations in forward\\npasses. We employ Bayesian optimization to systematically identify effective\\ncontrastive pair datasets across nine bias axes. When optimized on the BBQ\\ndataset, our individually tuned steering vectors achieve average improvements\\nof 12.2%, 4.7%, and 3.2% over the baseline for Mistral, Llama, and Qwen,\\nrespectively. Building on these promising results, we introduce Steering Vector\\nEnsembles (SVE), a method that averages multiple individually optimized\\nsteering vectors, each targeting a specific bias axis such as age, race, or\\ngender. By leveraging their collective strength, SVE outperforms individual\\nsteering vectors in both bias reduction and maintaining model performance. The\\nwork presents the first systematic investigation of steering vectors for bias\\nmitigation, and we demonstrate that SVE is a powerful and computationally\\nefficient strategy for reducing bias in LLMs, with broader implications for\\nenhancing AI safety.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>'],\n", "  'primary_category': 'cs.LG',\n", "  'categories': ['cs.LG', 'cs.AI', 'cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05371v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05362v1',\n", "  'title': 'Chain of Strategy Optimization Makes Large Language Models Better\\n  Emotional Supporter',\n", "  'abstract': '  The growing emotional stress in modern society has increased the demand for\\nEmotional Support Conversations (ESC). While Large Language Models (LLMs) show\\npromise for ESC, they face two key challenges: (1) low strategy selection\\naccuracy, and (2) preference bias, limiting their adaptability to emotional\\nneeds of users. Existing supervised fine-tuning (SFT) struggles to address\\nthese issues, as it rigidly trains models on single gold-standard responses\\nwithout modeling nuanced strategy trade-offs. To overcome these limitations, we\\npropose Chain-of-Strategy Optimization (CSO), a novel approach that optimizes\\nstrategy selection preferences at each dialogue turn. We first leverage Monte\\nCarlo Tree Search to construct ESC-Pro, a high-quality preference dataset with\\nturn-level strategy-response pairs. Training on ESC-Pro with CSO improves both\\nstrategy accuracy and bias mitigation, enabling LLMs to generate more\\nempathetic and contextually appropriate responses. Experiments on LLaMA-3.1-8B,\\nGemma-2-9B, and Qwen2.5-7B demonstrate that CSO outperforms standard SFT,\\nhighlighting the efficacy of fine-grained, turn-level preference modeling in\\nESC.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON><PERSON>',\n", "   'Xingyu Sui',\n", "   'Xinyang Han',\n", "   '<PERSON>',\n", "   '<PERSON>lin Hu',\n", "   '<PERSON><PERSON><PERSON>',\n", "   'Libo Qin',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON>',\n", "   'Ting <PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05362v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05347v1',\n", "  'title': 'GEMA-Score: Granular Explainable Multi-Agent Score for Radiology Report\\n  Evaluation',\n", "  'abstract': '  Automatic medical report generation supports clinical diagnosis, reduces the\\nworkload of radiologists, and holds the promise of improving diagnosis\\nconsistency. However, existing evaluation metrics primarily assess the accuracy\\nof key medical information coverage in generated reports compared to\\nhuman-written reports, while overlooking crucial details such as the location\\nand certainty of reported abnormalities. These limitations hinder the\\ncomprehensive assessment of the reliability of generated reports and pose risks\\nin their selection for clinical use. Therefore, we propose a Granular\\nExplainable Multi-Agent Score (GEMA-Score) in this paper, which conducts both\\nobjective quantification and subjective evaluation through a large language\\nmodel-based multi-agent workflow. Our GEMA-Score parses structured reports and\\nemploys NER-F1 calculations through interactive exchanges of information among\\nagents to assess disease diagnosis, location, severity, and uncertainty.\\nAdditionally, an LLM-based scoring agent evaluates completeness, readability,\\nand clinical terminology while providing explanatory feedback. Extensive\\nexperiments validate that GEMA-Score achieves the highest correlation with\\nhuman expert evaluations on a public dataset, demonstrating its effectiveness\\nin clinical scoring (Kendall coefficient = 0.70 for Rexval dataset and Kendall\\ncoefficient = 0.54 for RadEvalX dataset). The anonymous project demo is\\navailable at: https://github.com/Zhenxuan-Zhang/GEMA_score.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON> Fang',\n", "   '<PERSON><PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL', 'cs.MA'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05347v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05346v1',\n", "  'title': 'AutoIOT: LLM-Driven Automated Natural Language Programming for AIoT\\n  Applications',\n", "  'abstract': \"  The advent of Large Language Models (LLMs) has profoundly transformed our\\nlives, revolutionizing interactions with AI and lowering the barrier to AI\\nusage. While LLMs are primarily designed for natural language interaction, the\\nextensive embedded knowledge empowers them to comprehend digital sensor data.\\nThis capability enables LLMs to engage with the physical world through IoT\\nsensors and actuators, performing a myriad of AIoT tasks. Consequently, this\\nevolution triggers a paradigm shift in conventional AIoT application\\ndevelopment, democratizing its accessibility to all by facilitating the design\\nand development of AIoT applications via natural language. However, some\\nlimitations need to be addressed to unlock the full potential of LLMs in AIoT\\napplication development. First, existing solutions often require transferring\\nraw sensor data to LLM servers, which raises privacy concerns, incurs high\\nquery fees, and is limited by token size. Moreover, the reasoning processes of\\nLLMs are opaque to users, making it difficult to verify the robustness and\\ncorrectness of inference results. This paper introduces AutoIOT, an LLM-based\\nautomated program generator for AIoT applications. AutoIOT enables users to\\nspecify their requirements using natural language (input) and automatically\\nsynthesizes interpretable programs with documentation (output). AutoIOT\\nautomates the iterative optimization to enhance the quality of generated code\\nwith minimum user involvement. AutoIOT not only makes the execution of AIoT\\ntasks more explainable but also mitigates privacy concerns and reduces token\\ncosts with local execution of synthesized programs. Extensive experiments and\\nuser studies demonstrate AutoIOT's remarkable capability in program synthesis\\nfor various AIoT tasks. The synthesized programs can match and even outperform\\nsome representative baselines.\\n\",\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL', 'cs.AI', 'cs.SE'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05346v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05328v1',\n", "  'title': 'Dynamic Knowledge Integration for Evidence-Driven Counter-Argument\\n  Generation with Large Language Models',\n", "  'abstract': '  This paper investigates the role of dynamic external knowledge integration in\\nimproving counter-argument generation using Large Language Models (LLMs). While\\nLLMs have shown promise in argumentative tasks, their tendency to generate\\nlengthy, potentially unfactual responses highlights the need for more\\ncontrolled and evidence-based approaches. We introduce a new manually curated\\ndataset of argument and counter-argument pairs specifically designed to balance\\nargumentative complexity with evaluative feasibility. We also propose a new\\nLLM-as-a-Judge evaluation methodology that shows a stronger correlation with\\nhuman judgments compared to traditional reference-based metrics. Our\\nexperimental results demonstrate that integrating dynamic external knowledge\\nfrom the web significantly improves the quality of generated counter-arguments,\\nparticularly in terms of relatedness, persuasiveness, and factuality. The\\nfindings suggest that combining LLMs with real-time external knowledge\\nretrieval offers a promising direction for developing more effective and\\nreliable counter-argumentation systems.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL', 'cs.AI'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05328v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05324v1',\n", "  'title': 'Routing for Large ML Models',\n", "  'abstract': '  Training large language models (LLMs), and other large machine learning\\nmodels, involves repeated communication of large volumes of data across a data\\ncenter network. The communication patterns induced by these training process\\nexhibit high regularity and persistence, giving rise to significant\\nopportunities for optimizing the manner in which flows are routed across the\\nnetwork. We present an algorithmic framework for \\\\textit{quantifying}\\nnetwork-wide efficiency in the context of training LLMs (and other large-scale\\nML models), and for periodically \\\\textit{optimizing} routing with respect to\\nthis global metric.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON>rah<PERSON>'],\n", "  'primary_category': 'cs.NI',\n", "  'categories': ['cs.NI', 'cs.LG'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05324v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05281v1',\n", "  'title': 'Similarity-Based Domain Adaptation with LLMs',\n", "  'abstract': '  Unsupervised domain adaptation leverages abundant labeled data from various\\nsource domains to generalize onto unlabeled target data. Prior research has\\nprimarily focused on learning domain-invariant features across the source and\\ntarget domains. However, these methods often require training a model using\\nsource domain data, which is time-consuming and can limit model usage for\\napplications with different source data. This paper introduces a simple\\nframework that utilizes the impressive generalization capabilities of Large\\nLanguage Models (LLMs) for target data annotation without the need of source\\nmodel training, followed by a novel similarity-based knowledge distillation\\nloss. Our extensive experiments on cross-domain text classification reveal that\\nour framework achieves impressive performance, specifically, 2.44\\\\% accuracy\\nimprovement when compared to the SOTA method.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05281v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05248v1',\n", "  'title': 'Optimizing LLM Inference Throughput via Memory-aware and SLA-constrained\\n  Dynamic Batching',\n", "  'abstract': '  The increasing adoption of large language models (LLMs) necessitates\\ninference serving systems that can deliver both high throughput and low\\nlatency. Deploying LLMs with hundreds of billions of parameters on\\nmemory-constrained GPUs exposes significant limitations in static batching\\nmethods. Current inference serving systems often treat batch sizes as fixed\\nhyper-parameters, hindering real-time adaptation to varying system conditions.\\nIn this paper, we propose a dynamic batching method that continuously monitors\\nmemory utilization and adheres to service-level agreements (SLAs) to enable\\nreal-time batch size configuration adjustment. The method comprises two core\\ncomponents: a memory-aware batch scheduler that dynamically allocates GPU\\nresources and a latency feedback mechanism that optimizes decoding processes\\nunder SLA constraints. The numerical experiments demonstrate throughput gains\\nof 8% to 28% and capacity improvements of 22% compared to traditional static\\nbatching methods, while maintaining full compatibility with existing inference\\ninfrastructure. These results highlight the effectiveness of dynamic batching\\nin balancing computational efficiency and quality-of-service requirements for\\ncontemporary LLM deployment scenarios. The source code of this work is publicly\\navailable at https://github.com/KevinLee1110/dynamic-batching.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.DC',\n", "  'categories': ['cs.DC'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05248v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05244v1',\n", "  'title': 'WritingBench: A Comprehensive Benchmark for Generative Writing',\n", "  'abstract': \"  Recent advancements in large language models (LLMs) have significantly\\nenhanced text generation capabilities, yet evaluating their performance in\\ngenerative writing remains a challenge. Existing benchmarks primarily focus on\\ngeneric text generation or limited in writing tasks, failing to capture the\\ndiverse requirements of high-quality written contents across various domains.\\nTo bridge this gap, we present WritingBench, a comprehensive benchmark designed\\nto evaluate LLMs across 6 core writing domains and 100 subdomains, encompassing\\ncreative, persuasive, informative, and technical writing. We further propose a\\nquery-dependent evaluation framework that empowers LLMs to dynamically generate\\ninstance-specific assessment criteria. This framework is complemented by a\\nfine-tuned critic model for criteria-aware scoring, enabling evaluations in\\nstyle, format and length. The framework's validity is further demonstrated by\\nits data curation capability, which enables 7B-parameter models to approach\\nstate-of-the-art (SOTA) performance. We open-source the benchmark, along with\\nevaluation tools and modular framework components, to advance the development\\nof LLMs in writing.\\n\",\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   'Ming Yan',\n", "   '<PERSON><PERSON><PERSON>',\n", "   'SHaopeng <PERSON>',\n", "   'Yuran Ren',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   'Qin Jin',\n", "   '<PERSON><PERSON>'],\n", "  'primary_category': 'cs.AI',\n", "  'categories': ['cs.AI', 'cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05244v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05242v1',\n", "  'title': 'MM-StoryAgent: Immersive Narrated Storybook Video Generation with a\\n  Multi-Agent Paradigm across Text, Image and Audio',\n", "  'abstract': '  The rapid advancement of large language models (LLMs) and artificial\\nintelligence-generated content (AIGC) has accelerated AI-native applications,\\nsuch as AI-based storybooks that automate engaging story production for\\nchildren. However, challenges remain in improving story attractiveness,\\nenriching storytelling expressiveness, and developing open-source evaluation\\nbenchmarks and frameworks. Therefore, we propose and opensource MM-StoryAgent,\\nwhich creates immersive narrated video storybooks with refined plots,\\nrole-consistent images, and multi-channel audio. MM-StoryAgent designs a\\nmulti-agent framework that employs LLMs and diverse expert tools (generative\\nmodels and APIs) across several modalities to produce expressive storytelling\\nvideos. The framework enhances story attractiveness through a multi-stage\\nwriting pipeline. In addition, it improves the immersive storytelling\\nexperience by integrating sound effects with visual, music and narrative\\nassets. MM-StoryAgent offers a flexible, open-source platform for further\\ndevelopment, where generative modules can be substituted. Both objective and\\nsubjective evaluation regarding textual story quality and alignment between\\nmodalities validate the effectiveness of our proposed MM-StoryAgent system. The\\ndemo and source code are available.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   'Ming Yan',\n", "   'Shaopeng Lai',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05242v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05234v1',\n", "  'title': \"Unveiling Biases in AI: ChatGPT's Political Economy Perspectives and\\n  Human Comparisons\",\n", "  'abstract': \"  We explore the political and ideological positioning of ChatGPT, a leading\\nlarge language model (LLM), by comparing its responses to political economy\\nquestions from the European Social Survey (ESS). The questions concern\\nenvironmental sustainability, civil rights, income inequality, and government\\nsize. ChatGPT's self-assessed placement on a left-right political spectrum is\\ncompared to the ideological stances of individuals providing similar answers in\\nthe ESS dataset. Results highlight a significant left-oriented bias in\\nChatGPT's answers, particularly on environmental and civil rights topics,\\ndiverging from its same self-declared center-left stance. These findings\\nunderscore the need for transparency in AI systems to prevent potential\\nideological influences on users. We conclude by discussing the implications for\\nAI governance, debiasing strategies, and educational use.\\n\",\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON>', '<PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'econ.GN',\n", "  'categories': ['econ.GN', 'q-fin.EC'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05234v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05220v1',\n", "  'title': 'ARbiter: Generating Dialogue Options and Communication Support in\\n  Augmented Reality',\n", "  'abstract': \"  In this position paper, we propose researching the combination of Augmented\\nReality (AR) and Artificial Intelligence (AI) to support conversations,\\ninspired by the interfaces of dialogue systems commonly found in videogames.\\nAR-capable devices are becoming more powerful and conventional in looks, as\\nseen in head-mounted displays (HMDs) like the Snapchat Spectacles, the XREAL\\nglasses, or the recently presented Meta Orion. This development reduces\\npossible ergonomic, appearance, and runtime concerns, thus allowing a more\\nstraightforward integration and extended use of AR in our everyday lives, both\\nin private and at work. At the same time, we can observe an immense surge in AI\\ndevelopment (also at CHI). Recently notorious Large Language Models (LLMs) like\\nOpenAI's o3-mini or DeepSeek-R1 soar over their precursors in their ability to\\nsustain conversations, provide suggestions, and handle complex topics in\\n(almost) real time. In combination with natural language recognition systems,\\nwhich are nowadays a standard component of smartphones and similar devices\\n(including modern AR-HMDs), it is easy to imagine a combined system that\\nintegrates into daily conversations and provides various types of assistance.\\nSuch a system would enable many opportunities for research in AR+AI, which, as\\nstated by <PERSON><PERSON><PERSON> et al., remains scarce. In the following, we describe how the\\ndesign of a conversational AR+AI system can learn from videogame dialogue\\nsystems, and we propose use cases and research questions that can be\\ninvestigated thanks to this AR+AI combination.\\n\",\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON><PERSON>', '<PERSON>'],\n", "  'primary_category': 'cs.HC',\n", "  'categories': ['cs.HC'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05220v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05212v1',\n", "  'title': 'Knowledge Updating? No More Model Editing! Just Selective Contextual\\n  Reasoning',\n", "  'abstract': \"  As real-world knowledge evolves, the information embedded within large\\nlanguage models (LLMs) can become outdated, inadequate, or erroneous. Model\\nediting has emerged as a prominent approach for updating LLMs' knowledge with\\nminimal computational costs and parameter changes. This approach typically\\nidentifies and adjusts specific model parameters associated with newly acquired\\nknowledge. However, existing methods often underestimate the adverse effects\\nthat parameter modifications can have on broadly distributed knowledge. More\\ncritically, post-edit LLMs frequently struggle with multi-hop reasoning and\\ncontinuous knowledge updates. Although various studies have discussed these\\nshortcomings, there is a lack of comprehensive evaluation. In this paper, we\\nprovide an evaluation of ten model editing methods along four dimensions:\\nreliability, generalization, locality, and portability. Results confirm that\\nall ten popular model editing methods show significant shortcomings across\\nmultiple dimensions, suggesting model editing is less promising. We then\\npropose a straightforward method called Selective Contextual Reasoning (SCR),\\nfor knowledge updating. SCR does not modify model parameters but harnesses\\nLLM's inherent contextual reasoning capabilities utilizing the updated\\nknowledge pieces. Under SCR, an LLM first assesses whether an incoming query\\nfalls within the scope of an external knowledge base. If it does, the relevant\\nexternal knowledge texts are contextualized to enhance reasoning; otherwise,\\nthe query is answered directly. We evaluate SCR against the ten model editing\\nmethods on two counterfactual datasets with three backbone LLMs. Empirical\\nresults confirm the effectiveness and efficiency of contextual reasoning for\\nknowledge updating.\\n\",\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL', 'cs.AI'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05212v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05203v1',\n", "  'title': 'Path Pooling: Train-Free Structure Enhancement for Efficient Knowledge\\n  Graph Retrieval-Augmented Generation',\n", "  'abstract': '  Although Large Language Models achieve strong success in many tasks, they\\nstill suffer from hallucinations and knowledge deficiencies in real-world\\napplications. Many knowledge graph-based retrieval-augmented generation\\n(KG-RAG) methods enhance the quality and credibility of LLMs by leveraging\\nstructure and semantic information in KGs as external knowledge bases. However,\\nthese methods struggle to effectively incorporate structure information, either\\nincurring high computational costs or underutilizing available knowledge.\\nInspired by smoothing operations in graph representation learning, we propose\\npath pooling, a simple, train-free strategy that introduces structure\\ninformation through a novel path-centric pooling operation. It seamlessly\\nintegrates into existing KG-RAG methods in a plug-and-play manner, enabling\\nricher structure information utilization. Extensive experiments demonstrate\\nthat incorporating the path pooling into the state-of-the-art KG-RAG method\\nconsistently improves performance across various settings while introducing\\nnegligible additional cost. Code is coming soon at\\nhttps://github.com/hrwang00/path-pooling.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON> <PERSON>'],\n", "  'primary_category': 'cs.AI',\n", "  'categories': ['cs.AI', 'cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05203v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05200v1',\n", "  'title': 'ORANSight-2.0: Foundational LLMs for O-RAN',\n", "  'abstract': '  Despite the transformative impact of Large Language Models (LLMs) across\\ncritical domains such as healthcare, customer service, and business marketing,\\ntheir integration into Open Radio Access Networks (O-RAN) remains limited. This\\ngap is primarily due to the absence of domain-specific foundational models,\\nwith existing solutions often relying on general-purpose LLMs that fail to\\naddress the unique challenges and technical intricacies of O-RAN. To bridge\\nthis gap, we introduce ORANSight-2.0 (O-RAN Insights), a pioneering initiative\\naimed at developing specialized foundational LLMs tailored for O-RAN. Built on\\n18 LLMs spanning five open-source LLM frameworks, ORANSight-2.0 fine-tunes\\nmodels ranging from 1 to 70B parameters, significantly reducing reliance on\\nproprietary, closed-source models while enhancing performance for O-RAN. At the\\ncore of ORANSight-2.0 is RANSTRUCT, a novel Retrieval-Augmented Generation\\n(RAG) based instruction-tuning framework that employs two LLM agents to create\\nhigh-quality instruction-tuning datasets. The generated dataset is then used to\\nfine-tune the 18 pre-trained open-source LLMs via QLoRA. To evaluate\\nORANSight-2.0, we introduce srsRANBench, a novel benchmark designed for code\\ngeneration and codebase understanding in the context of srsRAN, a widely used\\n5G O-RAN stack. We also leverage ORANBench13K, an existing benchmark for\\nassessing O-RAN-specific knowledge. Our comprehensive evaluations demonstrate\\nthat ORANSight-2.0 models outperform general-purpose and closed-source models,\\nsuch as ChatGPT-4o and Gemini, by 5.421% on ORANBench and 18.465% on\\nsrsRANBench, achieving superior performance while maintaining lower\\ncomputational and energy costs. We also experiment with RAG-augmented variants\\nof ORANSight-2.0 LLMs and thoroughly evaluate their energy characteristics,\\ndemonstrating costs for training, standard inference, and RAG-augmented\\ninference.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05200v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05193v1',\n", "  'title': 'Memory-augmented Query Reconstruction for LLM-based Knowledge Graph\\n  Reasoning',\n", "  'abstract': \"  Large language models (LLMs) have achieved remarkable performance on\\nknowledge graph question answering (KGQA) tasks by planning and interacting\\nwith knowledge graphs. However, existing methods often confuse tool utilization\\nwith knowledge reasoning, harming readability of model outputs and giving rise\\nto hallucinatory tool invocations, which hinder the advancement of KGQA. To\\naddress this issue, we propose Memory-augmented Query Reconstruction for\\nLLM-based Knowledge Graph Reasoning (MemQ) to decouple LLM from tool invocation\\ntasks using LLM-built query memory. By establishing a memory module with\\nexplicit descriptions of query statements, the proposed MemQ facilitates the\\nKGQA process with natural language reasoning and memory-augmented query\\nreconstruction. Meanwhile, we design an effective and readable reasoning to\\nenhance the LLM's reasoning capability in KGQA. Experimental results that MemQ\\nachieves state-of-the-art performance on widely used benchmarks WebQSP and CWQ.\\n\",\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05193v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05179v1',\n", "  'title': 'Sketch-of-Thought: Efficient LLM Reasoning with Adaptive\\n  Cognitive-Inspired Sketching',\n", "  'abstract': '  Recent advances in large language models have demonstrated remarkable\\nreasoning capabilities through Chain of Thought (CoT) prompting, but often at\\nthe cost of excessive verbosity in their intermediate outputs, which increases\\ncomputational overhead. We introduce Sketch-of-Thought (SoT), a novel prompting\\nframework that combines cognitive-inspired reasoning paradigms with linguistic\\nconstraints to minimize token usage while preserving reasoning accuracy. SoT is\\ndesigned as a flexible framework that can incorporate any custom reasoning\\nparadigms based on cognitive science, and we instantiate it with three such\\nparadigms - Conceptual Chaining, Chunked Symbolism, and Expert Lexicons - each\\ntailored to different reasoning tasks and selected dynamically via a\\nlightweight routing model. Through comprehensive evaluation across 15 reasoning\\ndatasets with multiple languages and multimodal scenarios, we demonstrate that\\nSoT achieves token reductions of 76% with negligible accuracy impact. In\\ncertain domains like mathematical and multi-hop reasoning, it even improves\\naccuracy while using significantly fewer tokens. Our code is publicly\\navailable: https://www.github.com/SimonAytes/SoT.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL', 'cs.AI', 'cs.LG'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05179v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05142v1',\n", "  'title': 'RocketEval: Efficient Automated LLM Evaluation via Grading Checklist',\n", "  'abstract': '  Evaluating large language models (LLMs) in diverse and challenging scenarios\\nis essential to align them with human preferences. To mitigate the prohibitive\\ncosts associated with human evaluations, utilizing a powerful LLM as a judge\\nhas emerged as a favored approach. Nevertheless, this methodology encounters\\nseveral challenges, including substantial expenses, concerns regarding privacy\\nand security, and reproducibility. In this paper, we propose a straightforward,\\nreplicable, and accurate automated evaluation method by leveraging a\\nlightweight LLM as the judge, named <PERSON><PERSON><PERSON>. Initially, we identify that the\\nperformance disparity between lightweight and powerful LLMs in evaluation tasks\\nprimarily stems from their ability to conduct comprehensive analyses, which is\\nnot easily enhanced through techniques such as chain-of-thought reasoning. By\\nreframing the evaluation task as a multi-faceted Q&A using an instance-specific\\nchecklist, we demonstrate that the limited judgment accuracy of lightweight\\nLLMs is largely attributes to high uncertainty and positional bias. To address\\nthese challenges, we introduce an automated evaluation process grounded in\\nchecklist grading, which is designed to accommodate a variety of scenarios and\\nquestions. This process encompasses the creation of checklists, the grading of\\nthese checklists by lightweight LLMs, and the reweighting of checklist items to\\nalign with the supervised annotations. Our experiments carried out on the\\nautomated evaluation benchmarks, MT-Bench and WildBench datasets, reveal that\\nRocketEval, when using Gemma-2-2B as the judge, achieves a high correlation\\n(0.965) with human preferences, which is comparable to GPT-4o. Moreover,\\nRocketEval provides a cost reduction exceeding 50-fold for large-scale\\nevaluation and comparison scenarios. Our code is available at\\nhttps://github.com/Joinn99/RocketEval-ICLR .\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON><PERSON>',\n", "   '<PERSON> Wen',\n", "   '<PERSON><PERSON>',\n", "   'Xing Sun',\n", "   '<PERSON><PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05142v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05139v1',\n", "  'title': 'Every FLOP Counts: Scaling a 300B Mixture-of-Experts LING LLM without\\n  Premium GPUs',\n", "  'abstract': '  In this technical report, we tackle the challenges of training large-scale\\nMixture of Experts (MoE) models, focusing on overcoming cost inefficiency and\\nresource limitations prevalent in such systems. To address these issues, we\\npresent two differently sized MoE large language models (LLMs), namely\\nLing-Lite and Ling-Plus (referred to as \"Bailing\" in Chinese, spelled\\nB\\\\v{a}il\\\\\\'ing in Pinyin). Ling-Lite contains 16.8 billion parameters with 2.75\\nbillion activated parameters, while Ling-Plus boasts 290 billion parameters\\nwith 28.8 billion activated parameters. Both models exhibit comparable\\nperformance to leading industry benchmarks. This report offers actionable\\ninsights to improve the efficiency and accessibility of AI development in\\nresource-constrained settings, promoting more scalable and sustainable\\ntechnologies. Specifically, to reduce training costs for large-scale MoE\\nmodels, we propose innovative methods for (1) optimization of model\\narchitecture and training processes, (2) refinement of training anomaly\\nhandling, and (3) enhancement of model evaluation efficiency. Additionally,\\nleveraging high-quality data generated from knowledge graphs, our models\\ndemonstrate superior capabilities in tool use compared to other models.\\nUltimately, our experimental findings demonstrate that a 300B MoE LLM can be\\neffectively trained on lower-performance devices while achieving comparable\\nperformance to models of a similar scale, including dense and MoE models.\\nCompared to high-performance devices, utilizing a lower-specification hardware\\nsystem during the pre-training phase demonstrates significant cost savings,\\nreducing computing costs by approximately 20%. The models can be accessed at\\nhttps://huggingface.co/inclusionAI.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': [' Ling Team',\n", "   'Binwei Zeng',\n", "   '<PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   'Feng Yu',\n", "   '<PERSON>',\n", "   'Feng Yuan',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON> Li',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   'Junpeng Fang',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON>',\n", "   'Ji <PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   'Jun<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   'Jubao Feng',\n", "   '<PERSON><PERSON><PERSON> Di',\n", "   '<PERSON><PERSON> Xu',\n", "   'Jinghua Yao',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON>wei <PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON>',\n", "   'Li Tang',\n", "   'Lin Ju',\n", "   '<PERSON><PERSON>',\n", "   'Qing Cui',\n", "   'Song Liu',\n", "   'Shicheng Li',\n", "   'Shun Song',\n", "   'Song Yan',\n", "   'Tengwei Cai',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   'Tao Feng',\n", "   'Tao Wu',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   'Xiaobo Hu',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON> Guo',\n", "   '<PERSON><PERSON> Wang',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON>',\n", "   'Yuhao Fu',\n", "   '<PERSON> Xiong',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>',\n", "   'Zhenhang Sun',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>'],\n", "  'primary_category': 'cs.LG',\n", "  'categories': ['cs.LG', 'cs.AI', 'cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05139v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05132v1',\n", "  'title': 'R1-<PERSON>\\'s \"Aha Moment\" in Visual Reasoning on a 2B Non-SFT Model',\n", "  'abstract': '  Recently DeepSeek R1 demonstrated how reinforcement learning with simple\\nrule-based incentives can enable autonomous development of complex reasoning in\\nlarge language models, characterized by the \"aha moment\", in which the model\\nmanifest self-reflection and increased response length during training.\\nHowever, attempts to extend this success to multimodal reasoning often failed\\nto reproduce these key characteristics. In this report, we present the first\\nsuccessful replication of these emergent characteristics for multimodal\\nreasoning on only a non-SFT 2B model. Starting with Qwen2-VL-2B and applying\\nreinforcement learning directly on the SAT dataset, our model achieves 59.47%\\naccuracy on CVBench, outperforming the base model by approximately ~30% and\\nexceeding both SFT setting by ~2%. In addition, we share our failed attempts\\nand insights in attempting to achieve R1-like reasoning using RL with instruct\\nmodels. aiming to shed light on the challenges involved. Our key observations\\ninclude: (1) applying RL on instruct model often results in trivial reasoning\\ntrajectories, and (2) naive length reward are ineffective in eliciting\\nreasoning capabilities. The project code is available at\\nhttps://github.com/turningpoint-ai/VisualThinker-R1-Zero\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON>-<PERSON><PERSON>'],\n", "  'primary_category': 'cs.AI',\n", "  'categories': ['cs.AI', 'cs.CV', 'cs.LG'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05132v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05130v1',\n", "  'title': '<PERSON><PERSON>: Enabling GPU Resourcing-on-Demand for Serverless DL Serving via\\n  Introspective Elasticity',\n", "  'abstract': '  Serverless computing, with its ease of management, auto-scaling, and\\ncost-effectiveness, is widely adopted by deep learning (DL) applications. DL\\nworkloads, especially with large language models, require substantial GPU\\nresources to ensure QoS. However, it is prone to produce GPU fragments (e.g.,\\n15\\\\%-94\\\\%) in serverless DL systems due to the dynamicity of workloads and\\ncoarse-grained static GPU allocation mechanisms, gradually eroding the profits\\noffered by serverless elasticity.\\n  Different from classical serverless systems that only scale horizontally, we\\npresent introspective elasticity (IE), a fine-grained and adaptive\\ntwo-dimensional co-scaling mechanism to support GPU resourcing-on-demand for\\nserverless DL tasks. Based on this insight, we build Di<PERSON>, a cross-layer and\\nGPU-based serverless DL system with IE support. First, <PERSON><PERSON> provides\\nmulti-factor profiling for DL tasks with efficient pruning search methods.\\nSecond, <PERSON><PERSON> adheres to the resourcing-complementary principles in scheduling\\nto improve GPU utilization with QoS guarantees. Third, <PERSON><PERSON> adopts an adaptive\\n2D co-scaling method to enhance the elasticity of GPU provisioning in real\\ntime. Evaluations show that it can dynamically adjust the resourcing of various\\nDL functions with low GPU fragmentation (10\\\\%-46\\\\% GPU defragmentation), high\\nthroughput (up to 1.8$\\\\times$ inference and 1.1$\\\\times$ training throughput\\nincrement) and QoS guarantees (11\\\\%-71\\\\% violation rate reduction), compared to\\nthe SOTA baselines.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['Cunchi Lv',\n", "   '<PERSON> Shi',\n", "   'Zhengyu Lei',\n", "   '<PERSON><PERSON><PERSON>',\n", "   'Wenting Tan',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.DC',\n", "  'categories': ['cs.DC'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05130v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05109v1',\n", "  'title': 'Can Large Language Models Grasp Concepts in Visual Content? A Case Study\\n  on YouTube Shorts about Depression',\n", "  'abstract': \"  Large language models (LLMs) are increasingly used to assist computational\\nsocial science research. While prior efforts have focused on text, the\\npotential of leveraging multimodal LLMs (MLLMs) for online video studies\\nremains underexplored. We conduct one of the first case studies on\\nMLLM-assisted video content analysis, comparing AI's interpretations to human\\nunderstanding of abstract concepts. We leverage LLaVA-1.6 Mistral 7B to\\ninterpret four abstract concepts regarding video-mediated self-disclosure,\\nanalyzing 725 keyframes from 142 depression-related YouTube short videos. We\\nperform a qualitative analysis of MLLM's self-generated explanations and found\\nthat the degree of operationalization can influence MLLM's interpretations.\\nInterestingly, greater detail does not necessarily increase human-AI alignment.\\nWe also identify other factors affecting AI alignment with human understanding,\\nsuch as concept complexity and versatility of video genres. Our exploratory\\nstudy highlights the need to customize prompts for specific concepts and calls\\nfor researchers to incorporate more human-centered evaluations when working\\nwith AI systems in a multimodal context.\\n\",\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON><PERSON> \"<PERSON><PERSON>\" <PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.HC',\n", "  'categories': ['cs.HC', 'cs.CY'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05109v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05102v1',\n", "  'title': 'AutoTestForge: A Multidimensional Automated Testing Framework for\\n  Natural Language Processing Models',\n", "  'abstract': '  In recent years, the application of behavioral testing in Natural Language\\nProcessing (NLP) model evaluation has experienced a remarkable and substantial\\ngrowth. However, the existing methods continue to be restricted by the\\nrequirements for manual labor and the limited scope of capability assessment.\\nTo address these limitations, we introduce AutoTestForge, an automated and\\nmultidimensional testing framework for NLP models in this paper. Within\\nAutoTestForge, through the utilization of Large Language Models (LLMs) to\\nautomatically generate test templates and instantiate them, manual involvement\\nis significantly reduced. Additionally, a mechanism for the validation of test\\ncase labels based on differential testing is implemented which makes use of a\\nmulti-model voting system to guarantee the quality of test cases. The framework\\nalso extends the test suite across three dimensions, taxonomy, fairness, and\\nrobustness, offering a comprehensive evaluation of the capabilities of NLP\\nmodels. This expansion enables a more in-depth and thorough assessment of the\\nmodels, providing valuable insights into their strengths and weaknesses. A\\ncomprehensive evaluation across sentiment analysis (SA) and semantic textual\\nsimilarity (STS) tasks demonstrates that AutoTestForge consistently outperforms\\nexisting datasets and testing tools, achieving higher error detection rates (an\\naverage of $30.89\\\\%$ for SA and $34.58\\\\%$ for STS). Moreover, different\\ngeneration strategies exhibit stable effectiveness, with error detection rates\\nranging from $29.03\\\\% - 36.82\\\\%$.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.SE',\n", "  'categories': ['cs.SE', 'cs.CL', 'cs.CR'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05102v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05096v1',\n", "  'title': 'SpecServe: Efficient and SLO-Aware Large Language Model Serving with\\n  Adaptive Speculative Decoding',\n", "  'abstract': '  Large Language Model (LLM) services often face challenges in achieving low\\ninference latency and meeting Service Level Objectives (SLOs) under dynamic\\nrequest patterns. Speculative decoding, which exploits lightweight models for\\ndrafting and LLMs for verification, has emerged as a compelling technique to\\naccelerate LLM inference. However, existing speculative decoding solutions\\noften fail to adapt to varying workloads and system environments, resulting in\\nperformance variability and SLO violations. In this paper, we introduce\\nSpecServe, an efficient LLM inference system that dynamically adjusts\\nspeculative strategies according to real-time request loads and system\\nconfigurations. SpecServe proposes a theoretical model to understand and\\npredict the efficiency of speculative decoding across diverse scenarios.\\nAdditionally, it implements intelligent drafting and verification algorithms to\\nguarantee optimal performance while achieving high SLO attainment. Experimental\\nresults on real-world LLM traces demonstrate that SpecServe consistently meets\\nSLOs and achieves substantial performance improvements, yielding\\n1.14$\\\\times$-14.3$\\\\times$ speedups over state-of-the-art speculative inference\\nsystems.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON>',\n", "   'Hao <PERSON>',\n", "   'Zhubo Shi',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   'Qingjiang Shi'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05096v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05085v1',\n", "  'title': 'S2S-Arena, Evaluating Speech2Speech Protocols on Instruction Following\\n  with Paralinguistic Information',\n", "  'abstract': '  The rapid development of large language models (LLMs) has brought significant\\nattention to speech models, particularly recent progress in speech2speech\\nprotocols supporting speech input and output. However, the existing benchmarks\\nadopt automatic text-based evaluators for evaluating the instruction following\\nability of these models lack consideration for paralinguistic information in\\nboth speech understanding and generation. To address these issues, we introduce\\nS2S-Arena, a novel arena-style S2S benchmark that evaluates\\ninstruction-following capabilities with paralinguistic information in both\\nspeech-in and speech-out across real-world tasks. We design 154 samples that\\nfused TTS and live recordings in four domains with 21 tasks and manually\\nevaluate existing popular speech models in an arena-style manner. The\\nexperimental results show that: (1) in addition to the superior performance of\\nGPT-4o, the speech model of cascaded ASR, LLM, and TTS outperforms the jointly\\ntrained model after text-speech alignment in speech2speech protocols; (2)\\nconsidering paralinguistic information, the knowledgeability of the speech\\nmodel mainly depends on the LLM backbone, and the multilingual support of that\\nis limited by the speech module; (3) excellent speech models can already\\nunderstand the paralinguistic information in speech input, but generating\\nappropriate audio with paralinguistic information is still a challenge.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   'Fan Bu',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   'Haizhou Li'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL', 'cs.SD', 'eess.AS'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05085v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05079v1',\n", "  'title': 'On a Connection Between Imitation Learning and RLHF',\n", "  'abstract': '  This work studies the alignment of large language models with preference data\\nfrom an imitation learning perspective. We establish a close theoretical\\nconnection between reinforcement learning from human feedback RLHF and\\nimitation learning (IL), revealing that RLHF implicitly performs imitation\\nlearning on the preference data distribution. Building on this connection, we\\npropose DIL, a principled framework that directly optimizes the imitation\\nlearning objective. DIL provides a unified imitation learning perspective on\\nalignment, encompassing existing alignment algorithms as special cases while\\nnaturally introducing new variants. By bridging IL and RLHF, DIL offers new\\ninsights into alignment with RLHF. Extensive experiments demonstrate that DIL\\noutperforms existing methods on various challenging benchmarks.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.LG',\n", "  'categories': ['cs.LG'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05079v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05070v1',\n", "  'title': 'PromptPex: Automatic Test Generation for Language Model Prompts',\n", "  'abstract': '  Large language models (LLMs) are being used in many applications and prompts\\nfor these models are integrated into software applications as code-like\\nartifacts. These prompts behave much like traditional software in that they\\ntake inputs, generate outputs, and perform some specific function. However,\\nprompts differ from traditional code in many ways and require new approaches to\\nensure that they are robust. For example, unlike traditional software the\\noutput of a prompt depends on the AI model that interprets it. Also, while\\nnatural language prompts are easy to modify, the impact of updates is harder to\\npredict. New approaches to testing, debugging, and modifying prompts with\\nrespect to the model running them are required.\\n  To address some of these issues, we developed PromptPex, an LLM-based tool to\\nautomatically generate and evaluate unit tests for a given prompt. PromptPex\\nextracts input and output specifications from a prompt and uses them to\\ngenerate diverse, targeted, and valid unit tests. These tests are instrumental\\nin identifying regressions when a prompt is changed and also serve as a tool to\\nunderstand how prompts are interpreted by different models. We use PromptPex to\\ngenerate tests for eight benchmark prompts and evaluate the quality of the\\ngenerated tests by seeing if they can cause each of four diverse models to\\nproduce invalid output. PromptPex consistently creates tests that result in\\nmore invalid model outputs than a carefully constructed baseline LLM-based test\\ngenerator. Furthermore, by extracting concrete specifications from the input\\nprompt, PromptPex allows prompt writers to clearly understand and test specific\\naspects of their prompts. The source code of PromptPex is available at\\nhttps://github.com/microsoft/promptpex.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON>'],\n", "  'primary_category': 'cs.SE',\n", "  'categories': ['cs.SE', 'cs.AI'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05070v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05066v1',\n", "  'title': 'Capacity-Aware Inference: Mitigating the Straggler Effect in Mixture of\\n  Experts',\n", "  'abstract': '  The Mixture of Experts (MoE) is an effective architecture for scaling large\\nlanguage models by leveraging sparse expert activation, optimizing the\\ntrade-off between performance and efficiency. However, under expert\\nparallelism, MoE suffers from inference inefficiencies due to imbalanced\\ntoken-to-expert assignment, where some experts are overloaded while others\\nremain underutilized. This imbalance leads to poor resource utilization and\\nincreased latency, as the most burdened expert dictates the overall delay, a\\nphenomenon we define as the \\\\textbf{\\\\textit{Straggler Effect}}. To mitigate\\nthis, we propose Capacity-Aware Inference, including two key techniques: (1)\\n\\\\textbf{\\\\textit{Capacity-Aware Token Drop}}, which discards overloaded tokens\\nto regulate the maximum latency of MoE, and (2) \\\\textbf{\\\\textit{Capacity-Aware\\nToken Reroute}}, which reallocates overflowed tokens to underutilized experts,\\nbalancing the token distribution. These techniques collectively optimize both\\nhigh-load and low-load expert utilization, leading to a more efficient MoE\\ninference pipeline. Extensive experiments demonstrate the effectiveness of our\\nmethods, showing significant improvements in inference efficiency, e.g., 0.2\\\\%\\naverage performance increase and a 1.94$\\\\times$ inference speedup on\\nMixtral-8$\\\\times$7B-Instruct.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>'],\n", "  'primary_category': 'cs.LG',\n", "  'categories': ['cs.LG', 'cs.AI', 'cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05066v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05061v1',\n", "  'title': 'No Free Labels: Limitations of LLM-as-a-Judge Without Human Grounding',\n", "  'abstract': \"  LLM-as-a-Judge is a framework that uses an LLM (large language model) to\\nevaluate the quality of natural language text - typically text that is also\\ngenerated by an LLM. This framework holds great promise due to its relative\\nlow-cost, ease of use, and strong correlations with human stylistic\\npreferences. However, LLM Judges have been shown to exhibit biases that can\\ndistort their judgments. We evaluate how well LLM Judges can grade whether a\\ngiven response to a conversational question is correct, an ability crucial to\\nsoundly estimating the overall response quality. To do so, we create and\\npublicly release a human-annotated dataset with labels of correctness for 1,200\\nLLM responses. We source questions from a combination of existing datasets and\\na novel, challenging benchmark (BFF-Bench) created for this analysis. We\\ndemonstrate a strong connection between an LLM's ability to correctly answer a\\nquestion and grade responses to that question. Although aggregate level\\nstatistics might imply a judge has high agreement with human annotators, it\\nwill struggle on the subset of questions it could not answer. To address this\\nissue, we recommend a simple solution: provide the judge with a correct,\\nhuman-written reference answer. We perform an in-depth analysis on how\\nreference quality can affect the performance of an LLM Judge. We show that\\nproviding a weaker judge (e.g. <PERSON><PERSON> 2.5 7B) with higher quality references\\nreaches better agreement with human annotators than a stronger judge (e.g.\\nGPT-4o) with synthetic references.\\n\",\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-07',\n", "  'authors': ['<PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05061v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05049v1',\n", "  'title': 'Dynamic-KGQA: A Scalable Framework for Generating Adaptive Question\\n  Answering Datasets',\n", "  'abstract': '  As question answering (QA) systems advance alongside the rapid evolution of\\nfoundation models, the need for robust, adaptable, and large-scale evaluation\\nbenchmarks becomes increasingly critical. Traditional QA benchmarks are often\\nstatic and publicly available, making them susceptible to data contamination\\nand memorization by large language models (LLMs). Consequently, static\\nbenchmarks may overestimate model generalization and hinder a reliable\\nassessment of real-world performance. In this work, we introduce Dynamic-KGQA,\\na scalable framework for generating adaptive QA datasets from knowledge graphs\\n(KGs), designed to mitigate memorization risks while maintaining statistical\\nconsistency across iterations. Unlike fixed benchmarks, Dynamic-KGQA generates\\na new dataset variant on every run while preserving the underlying\\ndistribution, enabling fair and reproducible evaluations. Furthermore, our\\nframework provides fine-grained control over dataset characteristics,\\nsupporting domain-specific and topic-focused QA dataset generation.\\nAdditionally, Dynamic-KGQA produces compact, semantically coherent subgraphs\\nthat facilitate both training and evaluation of KGQA models, enhancing their\\nability to leverage structured knowledge effectively. To align with existing\\nevaluation protocols, we also provide static large-scale train/test/validation\\nsplits, ensuring comparability with prior methods. By introducing a dynamic,\\ncustomizable benchmarking paradigm, Dynamic-KGQA enables a more rigorous and\\nadaptable evaluation of QA systems.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL', 'cs.IR', 'cs.LG'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05049v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05047v1',\n", "  'title': 'Biases in Large Language Model-Elicited Text: A Case Study in Natural\\n  Language Inference',\n", "  'abstract': '  We test whether NLP datasets created with Large Language Models (LLMs)\\ncontain annotation artifacts and social biases like NLP datasets elicited from\\ncrowd-source workers. We recreate a portion of the Stanford Natural Language\\nInference corpus using GPT-4, Llama-2 70b for Chat, and Mistral 7b Instruct. We\\ntrain hypothesis-only classifiers to determine whether LLM-elicited NLI\\ndatasets contain annotation artifacts. Next, we use pointwise mutual\\ninformation to identify the words in each dataset that are associated with\\ngender, race, and age-related terms. On our LLM-generated NLI datasets,\\nfine-tuned BERT hypothesis-only classifiers achieve between 86-96% accuracy.\\nOur analyses further characterize the annotation artifacts and stereotypical\\nbiases in LLM-generated datasets.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON>', '<PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05047v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05021v1',\n", "  'title': 'Safety is Not Only About Refusal: Reasoning-Enhanced Fine-tuning for\\n  Interpretable LLM Safety',\n", "  'abstract': '  Large Language Models (LLMs) are vulnerable to jailbreak attacks that exploit\\nweaknesses in traditional safety alignment, which often relies on rigid refusal\\nheuristics or representation engineering to block harmful outputs. While they\\nare effective for direct adversarial attacks, they fall short of broader safety\\nchallenges requiring nuanced, context-aware decision-making. To address this,\\nwe propose Reasoning-enhanced Finetuning for interpretable LLM Safety\\n(Rational), a novel framework that trains models to engage in explicit safe\\nreasoning before response. Fine-tuned models leverage the extensive pretraining\\nknowledge in self-generated reasoning to bootstrap their own safety through\\nstructured reasoning, internalizing context-sensitive decision-making. Our\\nfindings suggest that safety extends beyond refusal, requiring context\\nawareness for more robust, interpretable, and adaptive responses. Reasoning is\\nnot only a core capability of LLMs but also a fundamental mechanism for LLM\\nsafety. Rational employs reasoning-enhanced fine-tuning, allowing it to reject\\nharmful prompts while providing meaningful and context-aware responses in\\ncomplex scenarios.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON>g <PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL', 'cs.CR'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05021v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05012v1',\n", "  'title': \"LLMs' Reshaping of People, Processes, Products, and Society in Software\\n  Development: A Comprehensive Exploration with Early Adopters\",\n", "  'abstract': '  Large language models (LLMs) like OpenAI ChatGPT, Google Gemini, and GitHub\\nCopilot are rapidly gaining traction in the software industry, but their full\\nimpact on software engineering remains insufficiently explored. Despite their\\ngrowing adoption, there is a notable lack of formal, qualitative assessments of\\nhow LLMs are applied in real-world software development contexts. To fill this\\ngap, we conducted semi-structured interviews with sixteen early-adopter\\nprofessional developers to explore their use of LLMs throughout various stages\\nof the software development life cycle. Our investigation examines four\\ndimensions: people - how LLMs affect individual developers and teams; process -\\nhow LLMs alter software engineering workflows; product - LLM impact on software\\nquality and innovation; and society - the broader socioeconomic and ethical\\nimplications of LLM adoption. Thematic analysis of our data reveals that while\\nLLMs have not fundamentally revolutionized the development process, they have\\nsubstantially enhanced routine coding tasks, including code generation,\\nrefactoring, and debugging. Developers reported the most effective outcomes\\nwhen providing LLMs with clear, well-defined problem statements, indicating\\nthat LLMs excel with decomposed problems and specific requirements.\\nFurthermore, these early-adopters identified that LLMs offer significant value\\nfor personal and professional development, aiding in learning new languages and\\nconcepts. Early-adopters, highly skilled in software engineering and how LLMs\\nwork, identified early and persisting challenges for software engineering, such\\nas inaccuracies in generated content and the need for careful manual review\\nbefore integrating LLM outputs into production environments. Our study provides\\na nuanced understanding of how LLMs are shaping the landscape of software\\ndevelopment, with their benefits, limitations, and ongoing implications.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON>'],\n", "  'primary_category': 'cs.SE',\n", "  'categories': ['cs.SE', 'cs.AI', 'cs.HC'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05012v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05010v1',\n", "  'title': 'Leveraging Domain Knowledge at Inference Time for LLM Translation:\\n  Retrieval versus Generation',\n", "  'abstract': \"  While large language models (LLMs) have been increasingly adopted for machine\\ntranslation (MT), their performance for specialist domains such as medicine and\\nlaw remains an open challenge. Prior work has shown that LLMs can be\\ndomain-adapted at test-time by retrieving targeted few-shot demonstrations or\\nterminologies for inclusion in the prompt. Meanwhile, for general-purpose LLM\\nMT, recent studies have found some success in generating similarly useful\\ndomain knowledge from an LLM itself, prior to translation. Our work studies\\ndomain-adapted MT with LLMs through a careful prompting setup, finding that\\ndemonstrations consistently outperform terminology, and retrieval consistently\\noutperforms generation. We find that generating demonstrations with weaker\\nmodels can close the gap with larger model's zero-shot performance. Given the\\neffectiveness of demonstrations, we perform detailed analyses to understand\\ntheir value. We find that domain-specificity is particularly important, and\\nthat the popular multi-domain benchmark is testing adaptation to a particular\\nwriting style more so than to a specific domain.\\n\",\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05010v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.05005v1',\n", "  'title': '<PERSON><PERSON><PERSON>y: A Lightweight Approach to Dynamic Inference of Generative\\n  Language Models',\n", "  'abstract': \"  Deploying large language models (LLMs) in real-world applications is often\\nhindered by strict computational and latency constraints. While dynamic\\ninference offers the flexibility to adjust model behavior based on varying\\nresource budgets, existing methods are frequently limited by hardware\\ninefficiencies or performance degradation. In this paper, we introduce Balcony,\\na simple yet highly effective framework for depth-based dynamic inference. By\\nfreezing the pretrained LLM and inserting additional transformer layers at\\nselected exit points, Balcony maintains the full model's performance while\\nenabling real-time adaptation to different computational budgets. These\\nadditional layers are trained using a straightforward self-distillation loss,\\naligning the sub-model outputs with those of the full model. This approach\\nrequires significantly fewer training tokens and tunable parameters,\\ndrastically reducing computational costs compared to prior methods. When\\napplied to the LLaMA3-8B model, using only 0.2% of the original pretraining\\ndata, Balcony achieves minimal performance degradation while enabling\\nsignificant speedups. Remarkably, we show that Balcony outperforms\\nstate-of-the-art methods such as Flextron and Layerskip as well as other\\nleading compression techniques on multiple models and at various scales, across\\na variety of benchmarks.\\n\",\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   'Parsa <PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   'Boxing Chen',\n", "   '<PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL', 'cs.AI', 'cs.LG'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.05005v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04992v1',\n", "  'title': 'Wanda++: Pruning Large Language Models via Regional Gradients',\n", "  'abstract': '  Large Language Models (LLMs) pruning seeks to remove unimportant weights for\\ninference speedup with minimal performance impact. However, existing methods\\noften suffer from performance loss without full-model sparsity-aware\\nfine-tuning. This paper presents Wanda++, a novel pruning framework that\\noutperforms the state-of-the-art methods by utilizing decoder-block-level\\n\\\\textbf{regional} gradients. Specifically, Wanda++ improves the pruning score\\nwith regional gradients for the first time and proposes an efficient regional\\noptimization method to minimize pruning-induced output discrepancies between\\nthe dense and sparse decoder output. Notably, Wanda++ improves perplexity by up\\nto 32\\\\% over <PERSON> in the language modeling task and generalizes effectively to\\ndownstream tasks. Further experiments indicate our proposed method is\\northogonal to sparsity-aware fine-tuning, where Wanda++ can be combined with\\nLoRA fine-tuning to achieve a similar perplexity improvement as the Wanda\\nmethod. The proposed method is lightweight, pruning a 7B LLaMA model in under\\n10 minutes on a single NVIDIA H100 GPU.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.LG',\n", "  'categories': ['cs.LG', 'cs.AI', 'cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04992v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04990v1',\n", "  'title': 'DP-GTR: Differentially Private Prompt Protection via Group Text\\n  Rewriting',\n", "  'abstract': '  Prompt privacy is crucial, especially when using online large language models\\n(LLMs), due to the sensitive information often contained within prompts. While\\nLLMs can enhance prompt privacy through text rewriting, existing methods\\nprimarily focus on document-level rewriting, neglecting the rich,\\nmulti-granular representations of text. This limitation restricts LLM\\nutilization to specific tasks, overlooking their generalization and in-context\\nlearning capabilities, thus hindering practical application. To address this\\ngap, we introduce DP-GTR, a novel three-stage framework that leverages local\\ndifferential privacy (DP) and the composition theorem via group text rewriting.\\nDP-GTR is the first framework to integrate both document-level and word-level\\ninformation while exploiting in-context learning to simultaneously improve\\nprivacy and utility, effectively bridging local and global DP mechanisms at the\\nindividual data point level. Experiments on CommonSense QA and DocVQA\\ndemonstrate that DP-GTR outperforms existing approaches, achieving a superior\\nprivacy-utility trade-off. Furthermore, our framework is compatible with\\nexisting rewriting techniques, serving as a plug-in to enhance privacy\\nprotection. Our code is publicly available at\\nhttps://github.com/FatShion-FTD/DP-GTR for reproducibility.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON>',\n", "   'Heng Fan',\n", "   'Song Fu',\n", "   '<PERSON><PERSON> Ding',\n", "   '<PERSON><PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04990v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04983v1',\n", "  'title': 'Leveraging Large Language Models For Scalable Vector Graphics\\n  Processing: A Review',\n", "  'abstract': '  In recent years, rapid advances in computer vision have significantly\\nimproved the processing and generation of raster images. However, vector\\ngraphics, which is essential in digital design, due to its scalability and ease\\nof editing, have been relatively understudied. Traditional vectorization\\ntechniques, which are often used in vector generation, suffer from long\\nprocessing times and excessive output complexity, limiting their usability in\\npractical applications. The advent of large language models (LLMs) has opened\\nnew possibilities for the generation, editing, and analysis of vector graphics,\\nparticularly in the SVG format, which is inherently text-based and well-suited\\nfor integration with LLMs.\\n  This paper provides a systematic review of existing LLM-based approaches for\\nSVG processing, categorizing them into three main tasks: generation, editing,\\nand understanding. We observe notable models such as IconShop, StrokeNUWA, and\\nStarVector, highlighting their strengths and limitations. Furthermore, we\\nanalyze benchmark datasets designed for assessing SVG-related tasks, including\\nSVGEditBench, VGBench, and SGP-Bench, and conduct a series of experiments to\\nevaluate various LLMs in these domains. Our results demonstrate that for vector\\ngraphics reasoning-enhanced models outperform standard LLMs, particularly in\\ngeneration and understanding tasks. Furthermore, our findings underscore the\\nneed to develop more diverse and richly annotated datasets to further improve\\nLLM capabilities in vector graphics tasks.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON>', '<PERSON>', '<PERSON><PERSON>'],\n", "  'primary_category': 'cs.CV',\n", "  'categories': ['cs.CV'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04983v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04982v1',\n", "  'title': 'LVLM-Compress-Bench: Benchmarking the Broader Impact of Large\\n  Vision-Language Model Compression',\n", "  'abstract': '  Despite recent efforts in understanding the compression impact on large\\nlanguage models (LLMs) in terms of their downstream task performance and\\ntrustworthiness on relatively simpler uni-modal benchmarks (for example,\\nquestion answering, common sense reasoning), their detailed study on\\nmulti-modal Large Vision-Language Models (LVLMs) is yet to be unveiled. Towards\\nmitigating this gap, we present LVLM-Compress-Bench, a framework to first\\nthoroughly study the broad impact of compression on the generative performance\\nof LVLMs with multi-modal input driven tasks. In specific, we consider two\\nmajor classes of compression for autoregressive models, namely KV cache and\\nweight compression, for the dynamically growing intermediate cache and static\\nweights, respectively.\\n  We use four LVLM variants of the popular LLaVA framework to present our\\nanalysis via integrating various state-of-the-art KV and weight compression\\nmethods including uniform, outlier-reduced, and group quantization for the KV\\ncache and weights. With this framework we demonstrate on ten different\\nmulti-modal datasets with different capabilities including recognition,\\nknowledge, language generation, spatial awareness, visual reasoning,\\nhallucination and visual illusion identification, toxicity, stereotypes and\\nbias. In specific, our framework demonstrates the compression impact on both\\ngeneral and ethically critical metrics leveraging a combination of real world\\nand synthetic datasets to encompass diverse societal intersectional attributes.\\nExtensive experimental evaluations yield diverse and intriguing observations on\\nthe behavior of LVLMs at different quantization budget of KV and weights, in\\nboth maintaining and losing performance as compared to the baseline model with\\nFP16 data format.\\n  Code will be open-sourced at\\nhttps://github.com/opengear-project/LVLM-compress-bench.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.CV',\n", "  'categories': ['cs.CV', 'cs.AI', 'cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04982v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04977v1',\n", "  'title': 'Quantifying the Relevance of Youth Research Cited in the US Policy\\n  Documents',\n", "  'abstract': \"  In recent years, there has been a growing concern and emphasis on conducting\\nresearch beyond academic or scientific research communities, benefiting society\\nat large. A well-known approach to measuring the impact of research on society\\nis enumerating its policy citation(s). Despite the importance of research in\\ninforming policy, there is no concrete evidence to suggest the research's\\nrelevance in cited policy documents. This is concerning because it may increase\\nthe possibility of evidence used in policy being manipulated by individual,\\nsocial, or political biases that may lead to inappropriate, fragmented, or\\narchaic research evidence in policy. Therefore, it is crucial to identify the\\ndegree of relevance between research articles and citing policy documents. In\\nthis paper, we examined the scale of contextual relevance of youth-focused\\nresearch in the referenced US policy documents using natural language\\nprocessing techniques, state-of-the-art pre-trained Large Language Models\\n(LLMs), and statistical analysis. Our experiments and analysis concluded that\\nyouth-related research articles that get US policy citations are mostly\\nrelevant to the citing policy documents.\\n\",\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>'],\n", "  'primary_category': 'cs.CY',\n", "  'categories': ['cs.CY', 'cs.AI'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04977v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04973v1',\n", "  'title': 'Beyond RAG: Task-Aware KV Cache Compression for Comprehensive Knowledge\\n  Reasoning',\n", "  'abstract': '  Incorporating external knowledge in large language models (LLMs) enhances\\ntheir utility across diverse applications, but existing methods have\\ntrade-offs. Retrieval-Augmented Generation (RAG) fetches evidence via\\nsimilarity search, but key information may fall outside top ranked results.\\nLong-context models can process multiple documents but are computationally\\nexpensive and limited by context window size. Inspired by students condensing\\nstudy material for open-book exams, we propose task-aware key-value (KV) cache\\ncompression, which compresses external knowledge in a zero- or few-shot setup.\\nThis enables LLMs to reason efficiently over a compacted representation of all\\nrelevant information. Experiments show our approach outperforms both RAG and\\ntask-agnostic compression methods. On LongBench v2, it improves accuracy by up\\nto 7 absolute points over RAG with a 30x compression rate, while reducing\\ninference latency from 0.43s to 0.16s. A synthetic dataset highlights that RAG\\nperforms well when sparse evidence suffices, whereas task-aware compression is\\nsuperior for broad knowledge tasks.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON><PERSON>',\n", "   'Orion Weller',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL', 'cs.AI', 'cs.IR', 'cs.LG'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04973v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04959v1',\n", "  'title': 'DB-Explore: Automated Database Exploration and Instruction Synthesis for\\n  Text-to-SQL',\n", "  'abstract': '  Recent text-to-SQL systems powered by large language models (LLMs) have\\ndemonstrated remarkable performance in translating natural language queries\\ninto SQL. However, these systems often struggle with complex database\\nstructures and domain-specific queries, as they primarily focus on enhancing\\nlogical reasoning and SQL syntax while overlooking the critical need for\\ncomprehensive database understanding. To address this limitation, we propose\\nDB-Explore, a novel framework that systematically aligns LLMs with database\\nknowledge through automated exploration and instruction synthesis. DB-Explore\\nconstructs database graphs to capture complex relational schemas, leverages\\nGPT-4 to systematically mine structural patterns and semantic knowledge, and\\nsynthesizes instructions to distill this knowledge for efficient fine-tuning of\\nLLMs. Our framework enables comprehensive database understanding through\\ndiverse sampling strategies and automated instruction generation, bridging the\\ngap between database structures and language models. Experiments conducted on\\nthe SPIDER and BIRD benchmarks validate the effectiveness of DB-Explore,\\nachieving an execution accuracy of 52.1% on BIRD and 84.0% on SPIDER. Notably,\\nour open-source implementation, based on the Qwen2.5-coder-7B model,\\noutperforms multiple GPT-4-driven text-to-SQL systems in comparative\\nevaluations, and achieves near state-of-the-art performance with minimal\\ncomputational cost.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON>',\n", "   'Wei<PERSON> Lu'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04959v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04930v1',\n", "  'title': 'HILGEN: Hierarchically-Informed Data Generation for Biomedical NER Using\\n  Knowledgebases and Large Language Models',\n", "  'abstract': \"  We present HILGEN, a Hierarchically-Informed Data Generation approach that\\ncombines domain knowledge from the Unified Medical Language System (UMLS) with\\nsynthetic data generated by large language models (LLMs), specifically GPT-3.5.\\nOur approach leverages UMLS's hierarchical structure to expand training data\\nwith related concepts, while incorporating contextual information from LLMs\\nthrough targeted prompts aimed at automatically generating synthetic examples\\nfor sparsely occurring named entities. The performance of the HILGEN approach\\nwas evaluated across four biomedical NER datasets (MIMIC III, BC5CDR,\\nNCBI-Disease, and Med-Mentions) using BERT-Large and DANN (Data Augmentation\\nwith Nearest Neighbor Classifier) models, applying various data generation\\nstrategies, including UMLS, GPT-3.5, and their best ensemble. For the\\nBERT-Large model, incorporating UMLS led to an average F1 score improvement of\\n40.36%, while using GPT-3.5 resulted in a comparable average increase of\\n40.52%. The Best-Ensemble approach using BERT-Large achieved the highest\\nimprovement, with an average increase of 42.29%. DANN model's F1 score improved\\nby 22.74% on average using the UMLS-only approach. The GPT-3.5-based method\\nresulted in a 21.53% increase, and the Best-Ensemble DANN model showed a more\\nnotable improvement, with an average increase of 25.03%. Our proposed HILGEN\\napproach improves NER performance in few-shot settings without requiring\\nadditional manually annotated data. Our experiments demonstrate that an\\neffective strategy for optimizing biomedical NER is to combine biomedical\\nknowledge curated in the past, such as the UMLS, and generative LLMs to create\\nsynthetic training instances. Our future research will focus on exploring\\nadditional innovative synthetic data generation strategies for further\\nimproving NER performance.\\n\",\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL', 'cs.AI'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04930v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04919v1',\n", "  'title': 'FirePlace: Geometric Refinements of LLM Common Sense Reasoning for 3D\\n  Object Placement',\n", "  'abstract': '  Scene generation with 3D assets presents a complex challenge, requiring both\\nhigh-level semantic understanding and low-level geometric reasoning. While\\nMultimodal Large Language Models (MLLMs) excel at semantic tasks, their\\napplication to 3D scene generation is hindered by their limited grounding on 3D\\ngeometry. In this paper, we investigate how to best work with MLLMs in an\\nobject placement task. Towards this goal, we introduce a novel framework,\\nFirePlace, that applies existing MLLMs in (1) 3D geometric reasoning and the\\nextraction of relevant geometric details from the 3D scene, (2) constructing\\nand solving geometric constraints on the extracted low-level geometry, and (3)\\npruning for final placements that conform to common sense. By combining\\ngeometric reasoning with real-world understanding of MLLMs, our method can\\npropose object placements that satisfy both geometric constraints as well as\\nhigh-level semantic common-sense considerations. Our experiments show that\\nthese capabilities allow our method to place objects more effectively in\\ncomplex scenes with intricate geometry, surpassing the quality of prior work.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.CV',\n", "  'categories': ['cs.CV'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04919v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04725v1',\n", "  'title': 'L$^2$M: Mutual Information Scaling Law for Long-Context Language\\n  Modeling',\n", "  'abstract': \"  We rigorously establish a bipartite mutual information scaling law in natural\\nlanguage that governs long-range dependencies. This scaling law, which we show\\nis distinct from and scales independently of the conventional two-point mutual\\ninformation, is the key to understanding long-context language modeling. Using\\nthis scaling law, we formulate the Long-context Language Modeling (L$^2$M)\\ncondition, which relates a model's capacity for effective long context length\\nmodeling to the scaling of its latent state size for storing past information.\\nOur results are validated through experiments on both transformers and state\\nspace models. This work establishes a theoretical foundation that guides the\\ndevelopment of large language models toward longer context lengths.\\n\",\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   'Di Luo',\n", "   '<PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL',\n", "   'cs.AI',\n", "   'cs.IT',\n", "   'cs.LG',\n", "   'math.IT',\n", "   'physics.data-an'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04725v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04723v2',\n", "  'title': 'Shifting Long-Context LLMs Research from Input to Output',\n", "  'abstract': '  Recent advancements in long-context Large Language Models (LLMs) have\\nprimarily concentrated on processing extended input contexts, resulting in\\nsignificant strides in long-context comprehension. However, the equally\\ncritical aspect of generating long-form outputs has received comparatively less\\nattention. This paper advocates for a paradigm shift in NLP research toward\\naddressing the challenges of long-output generation. Tasks such as novel\\nwriting, long-term planning, and complex reasoning require models to understand\\nextensive contexts and produce coherent, contextually rich, and logically\\nconsistent extended text. These demands highlight a critical gap in current LLM\\ncapabilities. We underscore the importance of this under-explored domain and\\ncall for focused efforts to develop foundational LLMs tailored for generating\\nhigh-quality, long-form outputs, which hold immense potential for real-world\\napplications.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   'Zhiqing Hu',\n", "   'Shangqing Tu',\n", "   '<PERSON> Hee',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL', 'cs.AI'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04723v2'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04722v1',\n", "  'title': 'Enough Coin Flips Can Make LLMs Act Bayesian',\n", "  'abstract': '  Large language models (LLMs) exhibit the ability to generalize given few-shot\\nexamples in their input prompt, an emergent capability known as in-context\\nlearning (ICL). We investigate whether LLMs utilize ICL to perform structured\\nreasoning in ways that are consistent with a Bayesian framework or rely on\\npattern matching. Using a controlled setting of biased coin flips, we find\\nthat: (1) LLMs often possess biased priors, causing initial divergence in\\nzero-shot settings, (2) in-context evidence outweighs explicit bias\\ninstructions, (3) LLMs broadly follow Bayesian posterior updates, with\\ndeviations primarily due to miscalibrated priors rather than flawed updates,\\nand (4) attention magnitude has negligible effect on Bayesian inference. With\\nsufficient demonstrations of biased coin flips via ICL, LLMs update their\\npriors in a Bayesian manner.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL', 'cs.AI', 'cs.LG'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04722v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04715v1',\n", "  'title': 'Predictable Scale: Part I -- Optimal Hyperparameter Scaling Law in Large\\n  Language Model Pretraining',\n", "  'abstract': '  The impressive capabilities of Large Language Models (LLMs) across diverse\\ntasks are now well-established, yet their effective deployment necessitates\\ncareful hyperparameter optimization. Through extensive empirical studies\\ninvolving grid searches across diverse configurations, we discover universal\\nscaling laws governing these hyperparameters: optimal learning rate follows a\\npower-law relationship with both model parameters and data sizes, while optimal\\nbatch size scales primarily with data sizes. Our analysis reveals a convex\\noptimization landscape for hyperparameters under fixed models and data size\\nconditions. This convexity implies an optimal hyperparameter plateau. We\\ncontribute a universal, plug-and-play optimal hyperparameter tool for the\\ncommunity. Its estimated values on the test set are merely 0.07\\\\% away from the\\nglobally optimal LLM performance found via an exhaustive search. These laws\\ndemonstrate remarkable robustness across variations in model sparsity, training\\ndata distribution, and model shape. To our best known, this is the first work\\nthat unifies different model shapes and structures, such as Mixture-of-Experts\\nmodels and dense transformers, as well as establishes optimal hyperparameter\\nscaling laws across diverse data distributions. This exhaustive optimization\\nprocess demands substantial computational resources, utilizing nearly one\\nmillion NVIDIA H800 GPU hours to train 3,700 LLMs of varying sizes and\\nhyperparameters from scratch and consuming approximately 100 trillion tokens in\\ntotal. To facilitate reproducibility and further research, we will\\nprogressively release all loss measurements and model checkpoints through our\\ndesignated repository https://step-law.github.io/\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   'Jingcheng Hu',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>'],\n", "  'primary_category': 'cs.LG',\n", "  'categories': ['cs.LG', 'cs.AI', 'F.2.2; I.2.7'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04715v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04704v2',\n", "  'title': 'Universality of Layer-Level Entropy-Weighted Quantization Beyond Model\\n  Architecture and Size',\n", "  'abstract': '  We present a novel approach to selective model quantization that transcends\\nthe limitations of architecture-specific and size-dependent compression methods\\nfor Large Language Models (LLMs) using Entropy-Weighted Quantization (EWQ). By\\nanalyzing the entropy distribution across transformer blocks, EWQ determines\\nwhich blocks can be safely quantized without causing significant performance\\ndegradation, independent of model architecture or size. Our method outperforms\\nuniform quantization approaches, maintaining Massive Multitask Language\\nUnderstanding (MMLU) accuracy scores within 0.5% of unquantized models while\\nreducing memory usage by up to 18%. We demonstrate the effectiveness of EWQ\\nacross multiple architectures -- from 1.6B to 70B parameters -- and showcase\\nconsistent improvements in the quality-compression trade-off regardless of\\nmodel scale or architectural design. A surprising finding of EWQ is its ability\\nto reduce perplexity compared to unquantized models, suggesting the presence of\\nbeneficial regularization through selective precision reduction. This\\nimprovement holds across different model families, indicating a fundamental\\nrelationship between layer-level entropy and optimal precision requirements.\\nAdditionally, we introduce FastEWQ, a rapid method for entropy distribution\\nanalysis that eliminates the need for loading model weights. This technique\\nleverages universal characteristics of entropy distribution that persist across\\nvarious architectures and scales, enabling near-instantaneous quantization\\ndecisions while maintaining 80% classification accuracy with full entropy\\nanalysis. Our results demonstrate that effective quantization strategies can be\\ndeveloped independently of specific architectural choices or model sizes,\\nopening new possibilities for efficient LLM deployment.\\n',\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>'],\n", "  'primary_category': 'cs.LG',\n", "  'categories': ['cs.LG', 'cs.AI'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04704v2'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04693v1',\n", "  'title': 'UIPE: Enhancing LLM Unlearning by Removing Knowledge Related to\\n  Forgetting Targets',\n", "  'abstract': \"  Large Language Models (LLMs) inevitably acquire harmful information during\\ntraining on massive datasets. LLM unlearning aims to eliminate the influence of\\nsuch harmful information while maintaining the model's overall performance.\\nExisting unlearning methods, represented by gradient ascent-based approaches,\\nprimarily focus on forgetting target data while overlooking the crucial impact\\nof logically related knowledge on the effectiveness of unlearning. In this\\npaper, through both theoretical and experimental analyses, we first demonstrate\\nthat a key reason for the suboptimal unlearning performance is that models can\\nreconstruct the target content through reasoning with logically related\\nknowledge. To address this issue, we propose Unlearning Improvement via\\nParameter Extrapolation (UIPE), a method that removes knowledge highly\\ncorrelated with the forgetting targets. Experimental results show that UIPE\\nsignificantly enhances the performance of various mainstream LLM unlearning\\nmethods on the TOFU benchmark.\\n\",\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04693v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04691v1',\n", "  'title': 'Quantifying the Reasoning Abilities of LLMs on Real-world Clinical Cases',\n", "  'abstract': \"  The latest reasoning-enhanced large language models (reasoning LLMs), such as\\nDeepSeek-R1 and OpenAI-o3, have demonstrated remarkable success. However, the\\napplication of such reasoning enhancements to the highly professional medical\\ndomain has not been clearly evaluated, particularly regarding with not only\\nassessing the final generation but also examining the quality of their\\nreasoning processes. In this study, we present MedR-Bench, a reasoning-focused\\nmedical evaluation benchmark comprising 1,453 structured patient cases with\\nreasoning references mined from case reports. Our benchmark spans 13 body\\nsystems and 10 specialty disorders, encompassing both common and rare diseases.\\nIn our evaluation, we introduce a versatile framework consisting of three\\ncritical clinical stages: assessment recommendation, diagnostic\\ndecision-making, and treatment planning, comprehensively capturing the LLMs'\\nperformance across the entire patient journey in healthcare. For metrics, we\\npropose a novel agentic system, Reasoning Evaluator, designed to automate and\\nobjectively quantify free-text reasoning responses in a scalable manner from\\nthe perspectives of efficiency, factuality, and completeness by dynamically\\nsearching and performing cross-referencing checks. As a result, we assess five\\nstate-of-the-art reasoning LLMs, including DeepSeek-R1, OpenAI-o3-mini, and\\nothers. Our results reveal that current LLMs can handle relatively simple\\ndiagnostic tasks with sufficient critical assessment results, achieving\\naccuracy generally over 85%. However, they still struggle with more complex\\ntasks, such as assessment recommendation and treatment planning. In reasoning,\\ntheir reasoning processes are generally reliable, with factuality scores\\nexceeding 90%, though they often omit critical reasoning steps. Our study\\nclearly reveals further development directions for current clinical LLMs.\\n\",\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04691v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04675v1',\n", "  'title': 'LLM-guided Plan and Retrieval: A Strategic Alignment for Interpretable\\n  User Satisfaction Estimation in Dialogue',\n", "  'abstract': '  Understanding user satisfaction with conversational systems, known as User\\nSatisfaction Estimation (USE), is essential for assessing dialogue quality and\\nenhancing user experiences. However, existing methods for USE face challenges\\ndue to limited understanding of underlying reasons for user dissatisfaction and\\nthe high costs of annotating user intentions. To address these challenges, we\\npropose PRAISE (Plan and Retrieval Alignment for Interpretable Satisfaction\\nEstimation), an interpretable framework for effective user satisfaction\\nprediction. PRAISE operates through three key modules. The Strategy Planner\\ndevelops strategies, which are natural language criteria for classifying user\\nsatisfaction. The Feature Retriever then incorporates knowledge on user\\nsatisfaction from Large Language Models (LLMs) and retrieves relevance features\\nfrom utterances. Finally, the Score Analyzer evaluates strategy predictions and\\nclassifies user satisfaction. Experimental results demonstrate that PRAISE\\nachieves state-of-the-art performance on three benchmarks for the USE task.\\nBeyond its superior performance, PRAISE offers additional benefits. It enhances\\ninterpretability by providing instance-level explanations through effective\\nalignment of utterances with strategies. Moreover, PRAISE operates more\\nefficiently than existing approaches by eliminating the need for LLMs during\\nthe inference phase.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON><PERSON>',\n", "   'Sohhyung Park',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   'Sung<PERSON><PERSON> Cho'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04675v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04647v1',\n", "  'title': 'Implicit Cross-Lingual Rewarding for Efficient Multilingual Preference\\n  Alignment',\n", "  'abstract': '  Direct Preference Optimization (DPO) has become a prominent method for\\naligning Large Language Models (LLMs) with human preferences. While DPO has\\nenabled significant progress in aligning English LLMs, multilingual preference\\nalignment is hampered by data scarcity. To address this, we propose a novel\\napproach that $\\\\textit{captures}$ learned preferences from well-aligned English\\nmodels by implicit rewards and $\\\\textit{transfers}$ them to other languages\\nthrough iterative training. Specifically, we derive an implicit reward model\\nfrom the logits of an English DPO-aligned model and its corresponding reference\\nmodel. This reward model is then leveraged to annotate preference relations in\\ncross-lingual instruction-following pairs, using English instructions to\\nevaluate multilingual responses. The annotated data is subsequently used for\\nmultilingual DPO fine-tuning, facilitating preference knowledge transfer from\\nEnglish to other languages. Fine-tuning Llama3 for two iterations resulted in a\\n12.72% average improvement in Win Rate and a 5.97% increase in Length Control\\nWin Rate across all training languages on the X-AlpacaEval leaderboard. Our\\nfindings demonstrate that leveraging existing English-aligned models can enable\\nefficient and effective multilingual preference alignment, significantly\\nreducing the need for extensive multilingual preference data. The code is\\navailable at https://github.com/ZNLP/Implicit-Cross-Lingual-Rewarding\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON>',\n", "   'Chengqing Zong',\n", "   '<PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL', 'cs.AI'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04647v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04636v1',\n", "  'title': 'Mark Your LLM: Detecting the Misuse of Open-Source Large Language Models\\n  via Watermarking',\n", "  'abstract': '  As open-source large language models (LLMs) like Llama3 become more capable,\\nit is crucial to develop watermarking techniques to detect their potential\\nmisuse. Existing watermarking methods either add watermarks during LLM\\ninference, which is unsuitable for open-source LLMs, or primarily target\\nclassification LLMs rather than recent generative LLMs. Adapting these\\nwatermarks to open-source LLMs for misuse detection remains an open challenge.\\nThis work defines two misuse scenarios for open-source LLMs: intellectual\\nproperty (IP) violation and LLM Usage Violation. Then, we explore the\\napplication of inference-time watermark distillation and backdoor watermarking\\nin these contexts. We propose comprehensive evaluation methods to assess the\\nimpact of various real-world further fine-tuning scenarios on watermarks and\\nthe effect of these watermarks on LLM performance. Our experiments reveal that\\nbackdoor watermarking could effectively detect IP Violation, while\\ninference-time watermark distillation is applicable in both scenarios but less\\nrobust to further fine-tuning and has a more significant impact on LLM\\nperformance compared to backdoor watermarking. Exploring more advanced\\nwatermarking methods for open-source LLMs to detect their misuse should be an\\nimportant future direction.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL', 'cs.AI', 'cs.CR', 'cs.LG'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04636v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04618v1',\n", "  'title': 'Better Process Supervision with Bi-directional Rewarding Signals',\n", "  'abstract': '  Process supervision, i.e., evaluating each step, is critical for complex\\nlarge language model (LLM) reasoning and test-time searching with increased\\ninference compute. Existing approaches, represented by process reward models\\n(PRMs), primarily focus on rewarding signals up to the current step, exhibiting\\na one-directional nature and lacking a mechanism to model the distance to the\\nfinal target. To address this problem, we draw inspiration from the A*\\nalgorithm, which states that an effective supervisory signal should\\nsimultaneously consider the incurred cost and the estimated cost for reaching\\nthe target. Building on this key insight, we introduce BiRM, a novel process\\nsupervision model that not only evaluates the correctness of previous steps but\\nalso models the probability of future success. We conduct extensive experiments\\non mathematical reasoning tasks and demonstrate that BiRM provides more precise\\nevaluations of LLM reasoning steps, achieving an improvement of 3.1% on\\nGaokao2023 over PRM under the Best-of-N sampling method. Besides, in\\nsearch-based strategies, BiRM provides more comprehensive guidance and\\noutperforms ORM by 5.0% and PRM by 3.8% respectively on MATH-500.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON><PERSON>g Xi',\n", "   '<PERSON><PERSON> Guo',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   'Tao Gui',\n", "   'Yun Li',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04618v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04611v1',\n", "  'title': 'Towards Data-Efficient Language Models: A Child-Inspired Approach to\\n  Language Learning',\n", "  'abstract': '  In this work, we explain our approach employed in the BabyLM Challenge, which\\nuses various methods of training language models (LMs) with significantly less\\ndata compared to traditional large language models (LLMs) and are inspired by\\nhow human children learn. While a human child is exposed to far less linguistic\\ninput than an LLM, they still achieve remarkable language understanding and\\ngeneration abilities. To this end, we develop a model trained on a curated\\ndataset consisting of 10 million words, primarily sourced from child-directed\\ntranscripts. The 2024 BabyLM Challenge initial dataset of 10M words is filtered\\nto 8.5M. Next, it is supplemented with a randomly selected subset of TVR\\ndataset consisting of 1.5M words of television dialogues. The latter dataset\\nensures that similar to children, the model is also exposed to language through\\nmedia. Furthermore, we reduce the vocabulary size to 32,000 tokens, aligning it\\nwith the limited vocabulary of children in the early stages of language\\nacquisition. We use curriculum learning and is able to match the baseline on\\ncertain benchmarks while surpassing the baseline on others. Additionally,\\nincorporating common LLM training datasets, such as MADLAD-400, degrades\\nperformance. These findings underscore the importance of dataset selection,\\nvocabulary scaling, and curriculum learning in creating more data-efficient\\nlanguage models that better mimic human learning processes.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON> <PERSON>', '<PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04611v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04875v1',\n", "  'title': 'Architecture for a Trustworthy Quantum Chatbot',\n", "  'abstract': \"  Large language model (LLM)-based tools such as ChatGPT seem useful for\\nclassical programming assignments. The more specialized the field, the more\\nlikely they lack reliability because of the lack of data to train them. In the\\ncase of quantum computing, the quality of answers of generic chatbots is low.\\n  C4Q is a chatbot focused on quantum programs that addresses this challenge\\nthrough a software architecture that integrates specialized LLMs to classify\\nrequests and specialized question answering modules with a deterministic\\nlogical engine to provide trustworthy quantum computing support. This article\\ndescribes the latest version (2.0) of C4Q, which delivers several enhancements:\\nready-to-run Qiskit code for gate definitions and circuit operations, expanded\\nfeatures to solve software engineering tasks such as the travelling salesperson\\nproblem and the knapsack problem, and a feedback mechanism for iterative\\nimprovement.\\n  Extensive testing of the backend confirms the system's reliability, while\\nempirical evaluations show that C4Q 2.0's classification LLM reaches\\nnear-perfect accuracy. The evaluation of the result consists in a comparative\\nstudy with three existing chatbots highlighting C4Q 2.0's maintainability and\\ncorrectness, reflecting on how software architecture decisions, such as\\nseparating deterministic logic from probabilistic text generation impact the\\nquality of the results.\\n\",\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON>', '<PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL', 'quant-ph', '81P68, 68T20'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04875v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04598v1',\n", "  'title': 'HybridNorm: Towards Stable and Efficient Transformer Training via Hybrid\\n  Normalization',\n", "  'abstract': '  Transformers have become the de facto architecture for a wide range of\\nmachine learning tasks, particularly in large language models (LLMs). Despite\\ntheir remarkable performance, challenges remain in training deep transformer\\nnetworks, especially regarding the location of layer normalization. While\\nPre-Norm structures facilitate easier training due to their more prominent\\nidentity path, they often yield suboptimal performance compared to Post-Norm.\\nIn this paper, we propose $\\\\textbf{HybridNorm}$, a straightforward yet\\neffective hybrid normalization strategy that integrates the advantages of both\\nPre-Norm and Post-Norm approaches. Specifically, HybridNorm employs QKV\\nnormalization within the attention mechanism and Post-Norm in the feed-forward\\nnetwork (FFN) of each transformer block. This design not only stabilizes\\ntraining but also enhances performance, particularly in the context of LLMs.\\nComprehensive experiments in both dense and sparse architectures show that\\nHybridNorm consistently outperforms both Pre-Norm and Post-Norm approaches,\\nachieving state-of-the-art results across various benchmarks. These findings\\nhighlight the potential of HybridNorm as a more stable and effective technique\\nfor improving the training and performance of deep transformer models. %Code\\nwill be made publicly available. Code is available at\\nhttps://github.com/BryceZhuo/HybridNorm.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON> Li',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL', 'cs.AI', 'cs.LG'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04598v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04874v1',\n", "  'title': 'Memory Is All You Need: Testing How Model Memory Affects LLM Performance\\n  in Annotation Tasks',\n", "  'abstract': \"  Generative Large Language Models (LLMs) have shown promising results in text\\nannotation using zero-shot and few-shot learning. Yet these approaches do not\\nallow the model to retain information from previous annotations, making each\\nresponse independent from the preceding ones. This raises the question of\\nwhether model memory -- the LLM having knowledge about its own previous\\nannotations in the same task -- affects performance. In this article, using\\nOpenAI's GPT-4o and Meta's Llama 3.1 on two political science datasets, we\\ndemonstrate that allowing the model to retain information about its own\\nprevious classifications yields significant performance improvements: between 5\\nand 25\\\\% when compared to zero-shot and few-shot learning. Moreover, memory\\nreinforcement, a novel approach we propose that combines model memory and\\nreinforcement learning, yields additional performance gains in three out of our\\nfour tests. These findings have important implications for applied researchers\\nlooking to improve performance and efficiency in LLM annotation tasks.\\n\",\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON>', '<PERSON><PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL', 'cs.LG'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04874v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04596v1',\n", "  'title': 'The Next Frontier of LLM Applications: Open Ecosystems and Hardware\\n  Synergy',\n", "  'abstract': '  Large Language Model (LLM) applications, including LLM app stores and\\nautonomous agents, are shaping the future of AI ecosystems. However, platform\\nsilos, fragmented hardware integration, and the absence of standardized\\ninterfaces limit scalability, interoperability, and resource efficiency. While\\nLLM app stores democratize AI, their closed ecosystems restrict modular AI\\nreuse and cross-platform portability. Meanwhile, agent-based frameworks offer\\nflexibility but often lack seamless integration across diverse environments.\\nThis paper envisions the future of LLM applications and proposes a three-layer\\ndecoupled architecture grounded in software engineering principles such as\\nlayered system design, service-oriented architectures, and hardware-software\\nco-design. This architecture separates application logic, communication\\nprotocols, and hardware execution, enhancing modularity, efficiency, and\\ncross-platform compatibility. Beyond architecture, we highlight key security\\nand privacy challenges for safe, scalable AI deployment and outline research\\ndirections in software and security engineering. This vision aims to foster\\nopen, secure, and interoperable LLM ecosystems, guiding future advancements in\\nAI applications.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.SE',\n", "  'categories': ['cs.SE', 'cs.AI'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04596v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04873v1',\n", "  'title': 'Are Large Language Models Good In-context Learners for Financial\\n  Sentiment Analysis?',\n", "  'abstract': '  Recently, large language models (LLMs) with hundreds of billions of\\nparameters have demonstrated the emergent ability, surpassing traditional\\nmethods in various domains even without fine-tuning over domain-specific data.\\nHowever, when it comes to financial sentiment analysis (FSA)$\\\\unicode{x2013}$a\\nfundamental task in financial AI$\\\\unicode{x2013}$these models often encounter\\nvarious challenges, such as complex financial terminology, subjective human\\nemotions, and ambiguous inclination expressions. In this paper, we aim to\\nanswer the fundamental question: whether LLMs are good in-context learners for\\nFSA? Unveiling this question can yield informative insights on whether LLMs can\\nlearn to address the challenges by generalizing in-context demonstrations of\\nfinancial document-sentiment pairs to the sentiment analysis of new documents,\\ngiven that finetuning these models on finance-specific data is difficult, if\\nnot impossible at all. To the best of our knowledge, this is the first paper\\nexploring in-context learning for FSA that covers most modern LLMs (recently\\nreleased DeepSeek V3 included) and multiple in-context sample selection\\nmethods. Comprehensive experiments validate the in-context learning capability\\nof LLMs for FSA.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL', 'cs.AI', 'q-fin.CP'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04873v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04872v1',\n", "  'title': 'TinyR1-32B-Preview: Boosting Accuracy with Branch-Merge Distillation',\n", "  'abstract': '  The challenge of reducing the size of Large Language Models (LLMs) while\\nmaintaining their performance has gained significant attention. However,\\nexisting methods, such as model distillation and transfer learning, often fail\\nto achieve high accuracy. To address this limitation, we introduce the\\nBranch-Merge distillation approach, which enhances model compression through\\ntwo phases: (1) the Branch Phase, where knowledge from a large teacher model is\\n\\\\textit{selectively distilled} into specialized student models via\\ndomain-specific supervised fine-tuning (SFT); And (2) the Merge Phase, where\\nthese student models are merged to enable cross-domain knowledge transfer and\\nimprove generalization. We validate our distillation approach using DeepSeek-R1\\nas the teacher and DeepSeek-R1-Distill-Qwen-32B as the student. The resulting\\nmerged model, TinyR1-32B-Preview, outperforms its counterpart\\nDeepSeek-R1-Distill-Qwen-32B across multiple benchmarks, including Mathematics\\n(+5.5 points), Coding (+4.4 points) and Science (+2.9 points), while achieving\\nnear-equal performance to DeepSeek-R1 on AIME 2024. The Branch-Merge\\ndistillation approach provides a scalable solution for creating smaller,\\nhigh-performing LLMs with reduced computational cost and time.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   'Change Jia',\n", "   '<PERSON><PERSON> Zhang',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   'Sai-er <PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   'Jun<PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   'Bin <PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL', 'cs.AI'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04872v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04870v1',\n", "  'title': 'Leveraging Large Language Models to Address Data Scarcity in Machine\\n  Learning: Applications in Graphene Synthesis',\n", "  'abstract': '  Machine learning in materials science faces challenges due to limited\\nexperimental data, as generating synthesis data is costly and time-consuming,\\nespecially with in-house experiments. Mining data from existing literature\\nintroduces issues like mixed data quality, inconsistent formats, and variations\\nin reporting experimental parameters, complicating the creation of consistent\\nfeatures for the learning algorithm. Additionally, combining continuous and\\ndiscrete features can hinder the learning process with limited data. Here, we\\npropose strategies that utilize large language models (LLMs) to enhance machine\\nlearning performance on a limited, heterogeneous dataset of graphene chemical\\nvapor deposition synthesis compiled from existing literature. These strategies\\ninclude prompting modalities for imputing missing data points and leveraging\\nlarge language model embeddings to encode the complex nomenclature of\\nsubstrates reported in chemical vapor deposition experiments. The proposed\\nstrategies enhance graphene layer classification using a support vector machine\\n(SVM) model, increasing binary classification accuracy from 39% to 65% and\\nternary accuracy from 52% to 72%. We compare the performance of the SVM and a\\nGPT-4 model, both trained and fine-tuned on the same data. Our results\\ndemonstrate that the numerical classifier, when combined with LLM-driven data\\nenhancements, outperforms the standalone LLM predictor, highlighting that in\\ndata-scarce scenarios, improving predictive learning with LLM strategies\\nrequires more than simple fine-tuning on datasets. Instead, it necessitates\\nsophisticated approaches for data imputation and feature space homogenization\\nto achieve optimal performance. The proposed strategies emphasize data\\nenhancement techniques, offering a broadly applicable framework for improving\\nmachine learning performance on scarce, inhomogeneous datasets.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON>', '<PERSON>'],\n", "  'primary_category': 'physics.comp-ph',\n", "  'categories': ['physics.comp-ph', 'cond-mat.mtrl-sci', 'cs.LG'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04870v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04557v1',\n", "  'title': 'Learning Generalizable Language-Conditioned Cloth Manipulation from Long\\n  Demonstrations',\n", "  'abstract': '  Multi-step cloth manipulation is a challenging problem for robots due to the\\nhigh-dimensional state spaces and the dynamics of cloth. Despite recent\\nsignificant advances in end-to-end imitation learning for multi-step cloth\\nmanipulation skills, these methods fail to generalize to unseen tasks. Our\\ninsight in tackling the challenge of generalizable multi-step cloth\\nmanipulation is decomposition. We propose a novel pipeline that autonomously\\nlearns basic skills from long demonstrations and composes learned basic skills\\nto generalize to unseen tasks. Specifically, our method first discovers and\\nlearns basic skills from the existing long demonstration benchmark with the\\ncommonsense knowledge of a large language model (LLM). Then, leveraging a\\nhigh-level LLM-based task planner, these basic skills can be composed to\\ncomplete unseen tasks. Experimental results demonstrate that our method\\noutperforms baseline methods in learning multi-step cloth manipulation skills\\nfor both seen and unseen tasks.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.RO',\n", "  'categories': ['cs.RO'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04557v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04554v1',\n", "  'title': 'Compositional Translation: A Novel LLM-based Approach for Low-resource\\n  Machine Translation',\n", "  'abstract': '  The ability of generative large language models (LLMs) to perform in-context\\nlearning has given rise to a large body of research into how best to prompt\\nmodels for various natural language processing tasks. Machine Translation (MT)\\nhas been shown to benefit from in-context examples, in particular when they are\\nsemantically similar to the sentence to translate. In this paper, we propose a\\nnew LLM-based translation paradigm, compositional translation, to replace naive\\nfew-shot MT with similarity-based demonstrations. An LLM is used to decompose a\\nsentence into simpler phrases, and then to translate each phrase with the help\\nof retrieved demonstrations. Finally, the LLM is prompted to translate the\\ninitial sentence with the help of the self-generated phrase-translation pairs.\\nOur intuition is that this approach should improve translation because these\\nshorter phrases should be intrinsically easier to translate and easier to match\\nwith relevant examples. This is especially beneficial in low-resource\\nscenarios, and more generally whenever the selection pool is small or out of\\ndomain. We show that compositional translation boosts LLM translation\\nperformance on a wide range of popular MT benchmarks, including FLORES 200,\\nNTREX 128 and TICO-19. Code and outputs are available at\\nhttps://github.com/ArmelRandy/compositional-translation\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04554v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04550v1',\n", "  'title': 'Benchmarking Reasoning Robustness in Large Language Models',\n", "  'abstract': \"  Despite the recent success of large language models (LLMs) in reasoning such\\nas DeepSeek, we for the first time identify a key dilemma in reasoning\\nrobustness and generalization: significant performance degradation on novel or\\nincomplete data, suggesting a reliance on memorized patterns rather than\\nsystematic reasoning. Our closer examination reveals four key unique\\nlimitations underlying this issue:(1) Positional bias--models favor earlier\\nqueries in multi-query inputs but answering the wrong one in the latter (e.g.,\\nGPT-4o's accuracy drops from 75.8 percent to 72.8 percent); (2) Instruction\\nsensitivity--performance declines by 5.0 to 7.5 percent in the Qwen2.5 Series\\nand by 5.0 percent in DeepSeek-V3 with auxiliary guidance; (3) Numerical\\nfragility--value substitution sharply reduces accuracy (e.g., GPT-4o drops from\\n97.5 percent to 82.5 percent, GPT-o1-mini drops from 97.5 percent to 92.5\\npercent); and (4) Memory dependence--models resort to guesswork when missing\\ncritical data. These findings further highlight the reliance on heuristic\\nrecall over rigorous logical inference, demonstrating challenges in reasoning\\nrobustness. To comprehensively investigate these robustness challenges, this\\npaper introduces a novel benchmark, termed as Math-RoB, that exploits\\nhallucinations triggered by missing information to expose reasoning gaps. This\\nis achieved by an instruction-based approach to generate diverse datasets that\\nclosely resemble training distributions, facilitating a holistic robustness\\nassessment and advancing the development of more robust reasoning frameworks.\\nBad character(s) in field Abstract.\\n\",\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON>',\n", "   'Yongcheng Jing',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   'Bo Du',\n", "   'Dacheng Tao'],\n", "  'primary_category': 'cs.AI',\n", "  'categories': ['cs.AI'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04550v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04543v1',\n", "  'title': 'Keeping Yourself is Important in Downstream Tuning Multimodal Large\\n  Language Model',\n", "  'abstract': '  Multi-modal Large Language Models (MLLMs) integrate visual and linguistic\\nreasoning to address complex tasks such as image captioning and visual question\\nanswering. While MLLMs demonstrate remarkable versatility, MLLMs appears\\nlimited performance on special applications. But tuning MLLMs for downstream\\ntasks encounters two key challenges: Task-Expert Specialization, where\\ndistribution shifts between pre-training and target datasets constrain target\\nperformance, and Open-World Stabilization, where catastrophic forgetting erases\\nthe model general knowledge. In this work, we systematically review recent\\nadvancements in MLLM tuning methodologies, classifying them into three\\nparadigms: (I) Selective Tuning, (II) Additive Tuning, and (III)\\nReparameterization Tuning. Furthermore, we benchmark these tuning strategies\\nacross popular MLLM architectures and diverse downstream tasks to establish\\nstandardized evaluation analysis and systematic tuning principles. Finally, we\\nhighlight several open challenges in this domain and propose future research\\ndirections. To facilitate ongoing progress in this rapidly evolving field, we\\nprovide a public repository that continuously tracks developments:\\nhttps://github.com/WenkeHuang/Awesome-MLLM-Tuning.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   'Yiyang Fang',\n", "   'Guancheng Wan',\n", "   '<PERSON><PERSON><PERSON><PERSON>',\n", "   'Chi Wen',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON>yun Li',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON> Ma',\n", "   '<PERSON>',\n", "   '<PERSON>',\n", "   'He Li',\n", "   '<PERSON><PERSON><PERSON>',\n", "   'Mang <PERSON>',\n", "   '<PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL', 'cs.AI'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04543v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04530v1',\n", "  'title': 'SOLAR: Scalable Optimization of Large-scale Architecture for Reasoning',\n", "  'abstract': '  Large Language Models (LLMs) excel in reasoning but remain constrained by\\ntheir Chain-of-Thought (CoT) approach, which struggles with complex tasks\\nrequiring more nuanced topological reasoning. We introduce SOLAR, Scalable\\nOptimization of Large-scale Architecture for Reasoning, a framework that\\ndynamically optimizes various reasoning topologies to enhance accuracy and\\nefficiency.\\n  Our Topological Annotation Generation (TAG) system automates topological\\ndataset creation and segmentation, improving post-training and evaluation.\\nAdditionally, we propose Topological-Scaling, a reward-driven framework that\\naligns training and inference scaling, equipping LLMs with adaptive, task-aware\\nreasoning.\\n  SOLAR achieves substantial gains on MATH and GSM8K: +5% accuracy with\\nTopological Tuning, +9% with Topological Reward, and +10.02% with Hybrid\\nScaling. It also reduces response length by over 5% for complex problems,\\nlowering inference latency.\\n  To foster the reward system, we train a multi-task Topological Reward Model\\n(M-TRM), which autonomously selects the best reasoning topology and answer in a\\nsingle pass, eliminating the need for training and inference on multiple\\nsingle-task TRMs (S-TRMs), thus reducing both training cost and inference\\nlatency. In addition, in terms of performance, M-TRM surpasses all S-TRMs,\\nimproving accuracy by +10% and rank correlation by +9%.\\n  To the best of our knowledge, SOLAR sets a new benchmark for scalable,\\nhigh-precision LLM reasoning while introducing an automated annotation process\\nand a dynamic reasoning topology competition mechanism.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>'],\n", "  'primary_category': 'cs.AI',\n", "  'categories': ['cs.AI'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04530v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04506v1',\n", "  'title': 'Multi-modal Summarization in Model-Based Engineering: Automotive\\n  Software Development Case Study',\n", "  'abstract': '  Multimodal summarization integrating information from diverse data modalities\\npresents a promising solution to aid the understanding of information within\\nvarious processes. However, the application and advantages of multimodal\\nsummarization have not received much attention in model-based engineering\\n(MBE), where it has become a cornerstone in the design and development of\\ncomplex systems, leveraging formal models to improve understanding, validation\\nand automation throughout the engineering lifecycle. UML and EMF diagrams in\\nmodel-based engineering contain a large amount of multimodal information and\\nintricate relational data. Hence, our study explores the application of\\nmultimodal large language models within the domain of model-based engineering\\nto evaluate their capacity for understanding and identifying relationships,\\nfeatures, and functionalities embedded in UML and EMF diagrams. We aim to\\ndemonstrate the transformative potential benefits and limitations of multimodal\\nsummarization in improving productivity and accuracy in MBE practices. The\\nproposed approach is evaluated within the context of automotive software\\ndevelopment, while many promising state-of-art models were taken into account.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>',\n", "   'Lu<PERSON>z Mazur',\n", "   '<PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>'],\n", "  'primary_category': 'cs.SE',\n", "  'categories': ['cs.SE', 'cs.AI'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04506v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04490v1',\n", "  'title': 'Large Language Models in Bioinformatics: A Survey',\n", "  'abstract': '  Large Language Models (LLMs) are revolutionizing bioinformatics, enabling\\nadvanced analysis of DNA, RNA, proteins, and single-cell data. This survey\\nprovides a systematic review of recent advancements, focusing on genomic\\nsequence modeling, RNA structure prediction, protein function inference, and\\nsingle-cell transcriptomics. Meanwhile, we also discuss several key challenges,\\nincluding data scarcity, computational complexity, and cross-omics integration,\\nand explore future directions such as multimodal learning, hybrid AI models,\\nand clinical applications. By offering a comprehensive perspective, this paper\\nunderscores the transformative potential of LLMs in driving innovations in\\nbioinformatics and precision medicine.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   'Xiangyu Shi',\n", "   '<PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL', 'q-bio.GN'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04490v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04479v1',\n", "  'title': 'ToolFuzz -- Automated Agent Tool Testing',\n", "  'abstract': \"  Large Language Model (LLM) Agents leverage the advanced reasoning\\ncapabilities of LLMs in real-world applications. To interface with an\\nenvironment, these agents often rely on tools, such as web search or database\\nAPIs. As the agent provides the LLM with tool documentation along the user\\nquery, the completeness and correctness of this documentation is critical.\\nHowever, tool documentation is often over-, under-, or ill-specified, impeding\\nthe agent's accuracy. Standard software testing approaches struggle to identify\\nthese errors as they are expressed in natural language. Thus, despite its\\nimportance, there currently exists no automated method to test the tool\\ndocumentation for agents. To address this issue, we present ToolFuzz, the first\\nmethod for automated testing of tool documentations. ToolFuzz is designed to\\ndiscover two types of errors: (1) user queries leading to tool runtime errors\\nand (2) user queries that lead to incorrect agent responses. ToolFuzz can\\ngenerate a large and diverse set of natural inputs, effectively finding tool\\ndescription errors at a low false positive rate. Further, we present two\\nstraightforward prompt-engineering approaches. We evaluate all three tool\\ntesting approaches on 32 common LangChain tools and 35 newly created custom\\ntools and 2 novel benchmarks to further strengthen the assessment. We find that\\nmany publicly available tools suffer from underspecification. Specifically, we\\nshow that ToolFuzz identifies 20x more erroneous inputs compared to the\\nprompt-engineering approaches, making it a key component for building reliable\\nAI agents.\\n\",\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>'],\n", "  'primary_category': 'cs.AI',\n", "  'categories': ['cs.AI', 'cs.SE'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04479v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04474v1',\n", "  'title': 'Know Thy Judge: On the Robustness Meta-Evaluation of LLM Safety Judges',\n", "  'abstract': '  Large Language Model (LLM) based judges form the underpinnings of key safety\\nevaluation processes such as offline benchmarking, automated red-teaming, and\\nonline guardrailing. This widespread requirement raises the crucial question:\\ncan we trust the evaluations of these evaluators? In this paper, we highlight\\ntwo critical challenges that are typically overlooked: (i) evaluations in the\\nwild where factors like prompt sensitivity and distribution shifts can affect\\nperformance and (ii) adversarial attacks that target the judge. We highlight\\nthe importance of these through a study of commonly used safety judges, showing\\nthat small changes such as the style of the model output can lead to jumps of\\nup to 0.24 in the false negative rate on the same dataset, whereas adversarial\\nattacks on the model generation can fool some judges into misclassifying 100%\\nof harmful generations as safe ones. These findings reveal gaps in commonly\\nused meta-evaluation benchmarks and weaknesses in the robustness of current LLM\\njudges, indicating that low attack success under certain judges could create a\\nfalse sense of security.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.LG',\n", "  'categories': ['cs.LG', 'cs.CR'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04474v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04463v1',\n", "  'title': 'Guiding LLMs to Generate High-Fidelity and High-Quality Counterfactual\\n  Explanations for Text Classification',\n", "  'abstract': \"  The need for interpretability in deep learning has driven interest in\\ncounterfactual explanations, which identify minimal changes to an instance that\\nchange a model's prediction. Current counterfactual (CF) generation methods\\nrequire task-specific fine-tuning and produce low-quality text. Large Language\\nModels (LLMs), though effective for high-quality text generation, struggle with\\nlabel-flipping counterfactuals (i.e., counterfactuals that change the\\nprediction) without fine-tuning. We introduce two simple classifier-guided\\napproaches to support counterfactual generation by LLMs, eliminating the need\\nfor fine-tuning while preserving the strengths of LLMs. Despite their\\nsimplicity, our methods outperform state-of-the-art counterfactual generation\\nmethods and are effective across different LLMs, highlighting the benefits of\\nguiding counterfactual generation by LLMs with classifier information. We\\nfurther show that data augmentation by our generated CFs can improve a\\nclassifier's robustness. Our analysis reveals a critical issue in\\ncounterfactual generation by LLMs: LLMs rely on parametric knowledge rather\\nthan faithfully following the classifier.\\n\",\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04463v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04457v1',\n", "  'title': 'TPC: Cross-Temporal Prediction Connection for Vision-Language Model\\n  Hallucination Reduction',\n", "  'abstract': \"  Vision-language models (VLMs) have achieved remarkable advancements,\\ncapitalizing on the impressive capabilities of large language models (LLMs)\\nacross diverse tasks. Despite this, a critical challenge known as hallucination\\noccurs when models overconfidently describe objects or attributes absent from\\nthe image, a problem exacerbated by the tendency of VLMs to rely on linguistic\\npriors. This limitation reduces model reliability in high-stakes applications.\\nIn this work, we have observed the characteristic of logits' continuity\\nconsistency enhancement and introduced a straightforward and efficient method,\\nCross-Temporal Prediction Connection (TPC), designed to enhance the semantic\\nconsistency of logits by connecting them temporally across timesteps. TPC\\namplifies information flow and improves coherence, effectively reducing\\nhallucination. Extensive experiments show that TPC surpasses existing\\nrepresentatives, delivering superior performance in both accuracy and\\nefficiency while maintaining robustness in open-ended text generation tasks.\\n\",\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON>', '<PERSON><PERSON>', '<PERSON>'],\n", "  'primary_category': 'cs.CV',\n", "  'categories': ['cs.CV', 'cs.AI'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04457v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04429v1',\n", "  'title': 'Activation Space Interventions Can Be Transferred Between Large Language\\n  Models',\n", "  'abstract': '  The study of representation universality in AI models reveals growing\\nconvergence across domains, modalities, and architectures. However, the\\npractical applications of representation universality remain largely\\nunexplored. We bridge this gap by demonstrating that safety interventions can\\nbe transferred between models through learned mappings of their shared\\nactivation spaces. We demonstrate this approach on two well-established AI\\nsafety tasks: backdoor removal and refusal of harmful prompts, showing\\nsuccessful transfer of steering vectors that alter the models\\' outputs in a\\npredictable way. Additionally, we propose a new task, \\\\textit{corrupted\\ncapabilities}, where models are fine-tuned to embed knowledge tied to a\\nbackdoor. This tests their ability to separate useful skills from backdoors,\\nreflecting real-world challenges. Extensive experiments across Llama, Qwen and\\nGemma model families show that our method enables using smaller models to\\nefficiently align larger ones. Furthermore, we demonstrate that autoencoder\\nmappings between base and fine-tuned models can serve as reliable ``lightweight\\nsafety switches\", allowing dynamic toggling between model behaviors.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON>'],\n", "  'primary_category': 'cs.AI',\n", "  'categories': ['cs.AI'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04429v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04418v1',\n", "  'title': 'AOLO: Analysis and Optimization For Low-Carbon Oriented Wireless Large\\n  Language Model Services',\n", "  'abstract': '  Recent advancements in large language models (LLMs) have led to their\\nwidespread adoption and large-scale deployment across various domains. However,\\ntheir environmental impact, particularly during inference, has become a growing\\nconcern due to their substantial energy consumption and carbon footprint.\\nExisting research has focused on inference computation alone, overlooking the\\nanalysis and optimization of carbon footprint in network-aided LLM service\\nsystems. To address this gap, we propose AOLO, a framework for analysis and\\noptimization for low-carbon oriented wireless LLM services. AOLO introduces a\\ncomprehensive carbon footprint model that quantifies greenhouse gas emissions\\nacross the entire LLM service chain, including computational inference and\\nwireless communication. Furthermore, we formulate an optimization problem aimed\\nat minimizing the overall carbon footprint, which is solved through joint\\noptimization of inference outputs and transmit power under\\nquality-of-experience and system performance constraints. To achieve this joint\\noptimization, we leverage the energy efficiency of spiking neural networks\\n(SNNs) by adopting SNN as the actor network and propose a low-carbon-oriented\\noptimization algorithm, i.e., SNN-based deep reinforcement learning (SDRL).\\nComprehensive simulations demonstrate that SDRL algorithm significantly reduces\\noverall carbon footprint, achieving an 18.77% reduction compared to the\\nbenchmark soft actor-critic, highlighting its potential for enabling more\\nsustainable LLM inference services.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>'],\n", "  'primary_category': 'eess.SY',\n", "  'categories': ['eess.SY', 'cs.LG', 'cs.SY'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04418v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04413v1',\n", "  'title': 'Can Large Language Models Predict Antimicrobial Resistance Gene?',\n", "  'abstract': '  This study demonstrates that generative large language models can be utilized\\nin a more flexible manner for DNA sequence analysis and classification tasks\\ncompared to traditional transformer encoder-based models. While recent\\nencoder-based models such as DNABERT and Nucleotide Transformer have shown\\nsignificant performance in DNA sequence classification, transformer\\ndecoder-based generative models have not yet been extensively explored in this\\nfield. This study evaluates how effectively generative Large Language Models\\nhandle DNA sequences with various labels and analyzes performance changes when\\nadditional textual information is provided. Experiments were conducted on\\nantimicrobial resistance genes, and the results show that generative Large\\nLanguage Models can offer comparable or potentially better predictions,\\ndemonstrating flexibility and accuracy when incorporating both sequence and\\ntextual information. The code and data used in this work are available at the\\nfollowing GitHub repository: https://github.com/biocomgit/llm4dna.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04413v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04412v1',\n", "  'title': 'Wider or Deeper? Scaling LLM Inference-Time Compute with Adaptive\\n  Branching Tree Search',\n", "  'abstract': '  Recent advances demonstrate that increasing inference-time computation can\\nsignificantly boost the reasoning capabilities of large language models (LLMs).\\nAlthough repeated sampling (i.e., generating multiple candidate outputs) is a\\nhighly effective strategy, it does not leverage external feedback signals for\\nrefinement, which are often available in tasks like coding. In this work, we\\npropose $\\\\textit{Adaptive Branching Monte Carlo Tree Search (AB-MCTS)}$, a\\nnovel inference-time framework that generalizes repeated sampling with\\nprincipled multi-turn exploration and exploitation. At each node in the search\\ntree, AB-MCTS dynamically decides whether to \"go wider\" by expanding new\\ncandidate responses or \"go deeper\" by revisiting existing ones based on\\nexternal feedback signals. We evaluate our method on complex coding and\\nengineering tasks using frontier models. Empirical results show that AB-MCTS\\nconsistently outperforms both repeated sampling and standard MCTS, underscoring\\nthe importance of combining the response diversity of LLMs with multi-turn\\nsolution refinement for effective inference-time scaling.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   'So <PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>'],\n", "  'primary_category': 'cs.AI',\n", "  'categories': ['cs.AI'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04412v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04398v2',\n", "  'title': 'Speculative MoE: Communication Efficient Parallel MoE Inference with\\n  Speculative Token and Expert Pre-scheduling',\n", "  'abstract': \"  MoE (Mixture of Experts) prevails as a neural architecture that can scale\\nmodern transformer-based LLMs (Large Language Models) to unprecedented scales.\\nNevertheless, large MoEs' great demands of computing power, memory capacity and\\nmemory bandwidth make scalable serving a fundamental challenge and efficient\\nparallel inference has become a requisite to attain adequate throughput under\\nlatency constraints. DeepSpeed-MoE, one state-of-the-art MoE inference\\nframework, adopts a 3D-parallel paradigm including EP (Expert Parallelism), TP\\n(Tensor Parallel) and DP (Data Parallelism). However, our analysis shows\\nDeepSpeed-MoE's inference efficiency is largely bottlenecked by EP, which is\\nimplemented with costly all-to-all collectives to route token activation. Our\\nwork aims to boost DeepSpeed-MoE by strategically reducing EP's communication\\noverhead with a technique named Speculative MoE. Speculative MoE has two\\nspeculative parallelization schemes, speculative token shuffling and\\nspeculative expert grouping, which predict outstanding tokens' expert routing\\npaths and pre-schedule tokens and experts across devices to losslessly trim\\nEP's communication volume. Besides DeepSpeed-MoE, we also build Speculative MoE\\ninto a prevailing MoE inference engine SGLang. Experiments show Speculative MoE\\ncan significantly boost state-of-the-art MoE inference frameworks on fast\\nhomogeneous and slow heterogeneous interconnects.\\n\",\n", "  'updated': '2025-03-07',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON>',\n", "   '<PERSON><PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   'Yuanhao Lai',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>'],\n", "  'primary_category': 'cs.LG',\n", "  'categories': ['cs.LG', 'cs.AI', 'cs.DC'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04398v2'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04396v1',\n", "  'title': 'TableLoRA: Low-rank Adaptation on Table Structure Understanding for\\n  Large Language Models',\n", "  'abstract': \"  Tabular data are crucial in many fields and their understanding by large\\nlanguage models (LLMs) under high parameter efficiency paradigm is important.\\nHowever, directly applying parameter-efficient fine-tuning (PEFT) techniques to\\ntabular tasks presents significant challenges, particularly in terms of better\\ntable serialization and the representation of two-dimensional structured\\ninformation within a one-dimensional sequence. To address this, we propose\\nTableLoRA, a module designed to improve LLMs' understanding of table structure\\nduring PEFT. It incorporates special tokens for serializing tables with special\\ntoken encoder and uses 2D LoRA to encode low-rank information on cell\\npositions. Experiments on four tabular-related datasets demonstrate that\\nTableLoRA consistently outperforms vanilla LoRA and surpasses various table\\nencoding methods tested in control experiments. These findings reveal that\\nTableLoRA, as a table-specific LoRA, enhances the ability of LLMs to process\\ntabular data effectively, especially in low-parameter settings, demonstrating\\nits potential as a robust solution for handling table-related tasks.\\n\",\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   'Yeye <PERSON>',\n", "   '<PERSON><PERSON><PERSON>',\n", "   'Shi Han',\n", "   '<PERSON><PERSON><PERSON>',\n", "   '<PERSON><PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04396v1'},\n", " {'abs_url': 'http://arxiv.org/abs/2503.04395v1',\n", "  'title': \"Shaping Shared Languages: Human and Large Language Models' Inductive\\n  Biases in Emergent Communication\",\n", "  'abstract': '  Languages are shaped by the inductive biases of their users. Using a\\nclassical referential game, we investigate how artificial languages evolve when\\noptimised for inductive biases in humans and large language models (LLMs) via\\nHuman-Human, LLM-LLM and Human-LLM experiments. We show that referentially\\ngrounded vocabularies emerge that enable reliable communication in all\\nconditions, even when humans and LLMs collaborate. Comparisons between\\nconditions reveal that languages optimised for LLMs subtly differ from those\\noptimised for humans. Interestingly, interactions between humans and LLMs\\nalleviate these differences and result in vocabularies which are more\\nhuman-like than LLM-like. These findings advance our understanding of how\\ninductive biases in LLMs play a role in the dynamic nature of human language\\nand contribute to maintaining alignment in human and machine communication. In\\nparticular, our work underscores the need to think of new methods that include\\nhuman interaction in the training processes of LLMs, and shows that using\\ncommunicative success as a reward signal can be a fruitful, novel direction.\\n',\n", "  'updated': '2025-03-06',\n", "  'published': '2025-03-06',\n", "  'authors': ['<PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>',\n", "   '<PERSON>'],\n", "  'primary_category': 'cs.CL',\n", "  'categories': ['cs.CL'],\n", "  'pdf_url': 'http://arxiv.org/pdf/2503.04395v1'}]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["import pandas as pd \n", "\n", "df = pd.DataFrame(data)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["paper_abstracts = \"\"\n", "for j, abs in enumerate([x.strip() for x in list(df[\"abstract\"][0:10])]):\n", "    paper_abstracts += f\"Paper {j+1}: {abs}\\n\""]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["\"Paper 1: Keeping large language models factually up-to-date is crucial for deployment,\\nyet costly retraining remains a challenge. Knowledge editing offers a promising\\nalternative, but methods are only tested on small-scale or synthetic edit\\nbenchmarks. In this work, we aim to bridge research into lifelong knowledge\\nediting to real-world edits at practically relevant scale. We first introduce\\nWikiBigEdit; a large-scale benchmark of real-world Wikidata edits, built to\\nautomatically extend lifelong for future-proof benchmarking. In its first\\ninstance, it includes over 500K question-answer pairs for knowledge editing\\nalongside a comprehensive evaluation pipeline. Finally, we use WikiBigEdit to\\nstudy existing knowledge editing techniques' ability to incorporate large\\nvolumes of real-world facts and contrast their capabilities to generic\\nmodification techniques such as retrieval augmentation and continual finetuning\\nto acquire a complete picture of the practical extent of current lifelong\\nknowledge editing.\\nPaper 2: Information technology has profoundly altered the way humans interact with\\ninformation. The vast amount of content created, shared, and disseminated\\nonline has made it increasingly difficult to access relevant information. Over\\nthe past two decades, search and recommendation systems (collectively referred\\nto as information retrieval systems) have evolved significantly to address\\nthese challenges. Recent advances in large language models (LLMs) have\\ndemonstrated capabilities that surpass human performance in various\\nlanguage-related tasks and exhibit general understanding, reasoning, and\\ndecision-making abilities. This paper explores the transformative potential of\\nlarge language model agents in enhancing search and recommendation systems. We\\ndiscuss the motivations and roles of LLM agents, and establish a classification\\nframework to elaborate on the existing research. We highlight the immense\\npotential of LLM agents in addressing current challenges in search and\\nrecommendation, providing insights into future research directions. This paper\\nis the first to systematically review and classify the research on LLM agents\\nin these domains, offering a novel perspective on leveraging this advanced AI\\ntechnology for information retrieval. To help understand the existing works, we\\nlist the existing papers on agent-based simulation with large language models\\nat this link:\\nhttps://github.com/tsinghua-fib-lab/LLM-Agent-for-Recommendation-and-Search.\\nPaper 3: Large language models (LLMs) have demonstrated remarkable capabilities in\\nhandling complex dialogue tasks without requiring use case-specific\\nfine-tuning. However, analyzing live dialogues in real-time necessitates\\nlow-latency processing systems, making it impractical to deploy models with\\nbillions of parameters due to latency constraints. As a result, practitioners\\noften prefer smaller models with millions of parameters, trained on\\nhigh-quality, human-annotated datasets. Yet, curating such datasets is both\\ntime-consuming and costly. Consequently, there is a growing need to combine the\\nscalability of LLM-generated labels with the precision of human annotations,\\nenabling fine-tuned smaller models to achieve both higher speed and accuracy\\ncomparable to larger models. In this paper, we introduce a simple yet effective\\nframework to address this challenge. Our approach is specifically designed for\\nper-utterance classification problems, which encompass tasks such as intent\\ndetection, dialogue state tracking, and more. To mitigate the impact of\\nlabeling errors from LLMs -- the primary source of inaccuracies in student\\nmodels -- we propose a noise-reduced preference learning loss. Experimental\\nresults demonstrate that our method significantly improves accuracy across\\nutterance-level dialogue tasks, including sentiment detection (over $2\\\\%$),\\ndialogue act classification (over $1.5\\\\%$), etc.\\nPaper 4: Large Language Models (LLMs) have revolutionized natural language processing,\\nyet their internal mechanisms remain largely opaque. Recently, mechanistic\\ninterpretability has attracted significant attention from the research\\ncommunity as a means to understand the inner workings of LLMs. Among various\\nmechanistic interpretability approaches, Sparse Autoencoders (SAEs) have\\nemerged as a particularly promising method due to their ability to disentangle\\nthe complex, superimposed features within LLMs into more interpretable\\ncomponents. This paper presents a comprehensive examination of SAEs as a\\npromising approach to interpreting and understanding LLMs. We provide a\\nsystematic overview of SAE principles, architectures, and applications\\nspecifically tailored for LLM analysis, covering theoretical foundations,\\nimplementation strategies, and recent developments in sparsity mechanisms. We\\nalso explore how SAEs can be leveraged to explain the internal workings of\\nLLMs, steer model behaviors in desired directions, and develop more transparent\\ntraining methodologies for future models. Despite the challenges that remain\\naround SAE implementation and scaling, they continue to provide valuable tools\\nfor understanding the internal mechanisms of large language models.\\nPaper 5: Existing Large Reasoning Models (LRMs) have shown the potential of\\nreinforcement learning (RL) to enhance the complex reasoning capabilities of\\nLarge Language Models~(LLMs). While they achieve remarkable performance on\\nchallenging tasks such as mathematics and coding, they often rely on their\\ninternal knowledge to solve problems, which can be inadequate for\\ntime-sensitive or knowledge-intensive questions, leading to inaccuracies and\\nhallucinations. To address this, we propose \\\\textbf{R1-Searcher}, a novel\\ntwo-stage outcome-based RL approach designed to enhance the search capabilities\\nof LLMs. This method allows LLMs to autonomously invoke external search systems\\nto access additional knowledge during the reasoning process. Our framework\\nrelies exclusively on RL, without requiring process rewards or distillation for\\na cold start. % effectively generalizing to out-of-domain datasets and\\nsupporting both Base and Instruct models. Our experiments demonstrate that our\\nmethod significantly outperforms previous strong RAG methods, even when\\ncompared to the closed-source GPT-4o-mini.\\nPaper 6: The increasing prevalence of online misinformation has heightened the demand\\nfor automated fact-checking solutions. Large Language Models (LLMs) have\\nemerged as potential tools for assisting in this task, but their effectiveness\\nremains uncertain. This study evaluates the fact-checking capabilities of\\nvarious open-source LLMs, focusing on their ability to assess claims with\\ndifferent levels of contextual information. We conduct three key experiments:\\n(1) evaluating whether LLMs can identify the semantic relationship between a\\nclaim and a fact-checking article, (2) assessing models' accuracy in verifying\\nclaims when given a related fact-checking article, and (3) testing LLMs'\\nfact-checking abilities when leveraging data from external knowledge sources\\nsuch as Google and Wikipedia. Our results indicate that LLMs perform well in\\nidentifying claim-article connections and verifying fact-checked stories but\\nstruggle with confirming factual news, where they are outperformed by\\ntraditional fine-tuned models such as RoBERTa. Additionally, the introduction\\nof external knowledge does not significantly enhance LLMs' performance, calling\\nfor more tailored approaches. Our findings highlight both the potential and\\nlimitations of LLMs in automated fact-checking, emphasizing the need for\\nfurther refinements before they can reliably replace human fact-checkers.\\nPaper 7: Existing benchmarks are becoming saturated and struggle to separate model\\nperformances due to factors like data contamination and advancing LLM\\ncapabilities. This paper introduces EMDM (Enhanced Model Differentiation\\nMetric), a novel weighted metric that revitalizes benchmarks by enhancing model\\nseparation. EMDM integrates final answer and Chain-of-Thought (CoT) reasoning\\ncorrectness, assigning weights based on the complexity and reasoning depth\\nrequired to solve a given sample in the evaluation data. Using a baseline LLM\\nin two setups-Unguided, where the model has no prior exposure to test samples,\\nand Guided, where the model has prior knowledge of the desired answer-EMDM\\ndistinguishes instances of varying difficulty. The CoT and answer correctness\\nfrom these setups inform an optimization objective for weight assignment,\\nresulting in a more nuanced evaluation of model performance. Compared to the\\nexact match (EM) metric, which achieves 17% separation on ARC-Challenge, EMDM\\nachieves 46%, demonstrating its effectiveness in differentiating models based\\non reasoning and knowledge requirements.\\nPaper 8: Retrieval-augmented generation (RAG) enhances the reliability of large\\nlanguage model (LLM) answers by integrating external knowledge. However, RAG\\nincreases the end-to-end inference time since looking for relevant documents\\nfrom large vector databases is computationally expensive. To address this, we\\nintroduce Proximity, an approximate key-value cache that optimizes the RAG\\nworkflow by leveraging similarities in user queries. Instead of treating each\\nquery independently, Proximity reuses previously retrieved documents when\\nsimilar queries appear, reducing reliance on expensive vector database lookups.\\nWe evaluate Proximity on the MMLU and MedRAG benchmarks, demonstrating that it\\nsignificantly improves retrieval efficiency while maintaining response\\naccuracy. Proximity reduces retrieval latency by up to 59% while maintaining\\naccuracy and lowers the computational burden on the vector database. We also\\nexperiment with different similarity thresholds and quantify the trade-off\\nbetween speed and recall. Our work shows that approximate caching is a viable\\nand effective strategy for optimizing RAG-based systems.\\nPaper 9: This paper introduces PoSSUM, an open-source protocol for unobtrusive polling\\nof social-media users via multimodal Large Language Models (LLMs). PoSSUM\\nleverages users' real-time posts, images, and other digital traces to create\\nsilicon samples that capture information not present in the LLM's training\\ndata. To obtain representative estimates, PoSSUM employs Multilevel Regression\\nand Post-Stratification (MrP) with structured priors to counteract the\\nobservable selection biases of social-media platforms. The protocol is\\nvalidated during the 2024 U.S. Presidential Election, for which five PoSSUM\\npolls were conducted and published on GitHub and X. In the final poll, fielded\\nOctober 17-26 with a synthetic sample of 1,054 X users, PoSSUM accurately\\npredicted the outcomes in 50 of 51 states and assigned the Republican candidate\\na win probability of 0.65. Notably, it also exhibited lower state-level bias\\nthan most established pollsters. These results demonstrate PoSSUM's potential\\nas a fully automated, unobtrusive alternative to traditional survey methods.\\nPaper 10: Cognitive biases, systematic deviations from rationality in judgment, pose\\nsignificant challenges in generating objective content. This paper introduces a\\nnovel approach for real-time cognitive bias detection in user-generated text\\nusing large language models (LLMs) and advanced prompt engineering techniques.\\nThe proposed system analyzes textual data to identify common cognitive biases\\nsuch as confirmation bias, circular reasoning, and hidden assumption. By\\ndesigning tailored prompts, the system effectively leverages LLMs' capabilities\\nto both recognize and mitigate these biases, improving the quality of\\nhuman-generated content (e.g., news, media, reports). Experimental results\\ndemonstrate the high accuracy of our approach in identifying cognitive biases,\\noffering a valuable tool for enhancing content objectivity and reducing the\\nrisks of biased decision-making.\\n\""]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["paper_abstracts"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["'You are an expert reviewer for AI conferences. You follow best practices and review papers according to the reviewer guidelines.\\n            Reviewer guidelines:\\n            1. Read the paper: It’s important to carefully read through the entire paper, and to look up any related work and citations that will help you comprehensively evaluate it. Be sure to give yourself sufficient time for this step.\\n            2. While reading, consider the following:\\n                - Objective of the work: What is the goal of the paper? Is it to better address a known application or problem, draw attention to a new application or problem, or to introduce and/or explain a new theoretical finding? A combination of these? Different objectives will require different considerations as to potential value and impact.\\n                - Strong points: is the submission clear, technically correct, experimentally rigorous, reproducible, does it present novel findings (e.g. theoretically, algorithmically, etc.)?\\n                - Weak points: is it weak in any of the aspects listed in b.?\\n                - Be mindful of potential biases and try to be open-minded about the value and interest a paper can hold for the community, even if it may not be very interesting for you.\\n            3. Answer four key questions for yourself, to make a recommendation to Accept or Reject:\\n                - What is the specific question and/or problem tackled by the paper?\\n                - Is the approach well motivated, including being well-placed in the literature?\\n                - Does the paper support the claims? This includes determining if results, whether theoretical or empirical, are correct and if they are scientifically rigorous.\\n                - What is the significance of the work? Does it contribute new knowledge and sufficient value to the community? Note, this does not necessarily require state-of-the-art results. Submissions bring value to the community when they convincingly demonstrate new, relevant, impactful knowledge (incl., empirical, theoretical, for practitioners, etc).\\n            4. Write your review including the following information: \\n                - Summarize what the paper claims to contribute. Be positive and constructive.\\n                - List strong and weak points of the paper. Be as comprehensive as possible.\\n                - Clearly state your initial recommendation (accept or reject) with one or two key reasons for this choice.\\n                - Provide supporting arguments for your recommendation.\\n                - Ask questions you would like answered by the authors to help you clarify your understanding of the paper and provide the additional evidence you need to be confident in your assessment.\\n                - Provide additional feedback with the aim to improve the paper. Make it clear that these points are here to help, and not necessarily part of your decision assessment.\\n            Your review must be formatted in markdown as follows:\\n            # Review\\n            ## Summary\\n            Briefly summarize the paper and its contributions. This is not the place to critique the paper; the authors should generally agree with a well-written summary.\\n            ## Soundness\\n            Please assign the paper a numerical rating on the following scale to indicate the soundness of the technical claims, experimental and research methodology and on whether the central claims of the paper are adequately supported with evidence. Choose from the following:\\n            4: excellent\\n            3: good\\n            2: fair\\n            1: poor\\n            ## Presentation\\n            Please assign the paper a numerical rating on the following scale to indicate the quality of the presentation. This should take into account the writing style and clarity, as well as contextualization relative to prior work. Choose from the following:\\n            4: excellent\\n            3: good\\n            2: fair\\n            1: poor\\n            ## Contribution\\n            Please assign the paper a numerical rating on the following scale to indicate the quality of the overall contribution this paper makes to the research area being studied. Are the questions being asked important? Does the paper bring a significant originality of ideas and/or execution? Are the results valuable to share with the broader ICLR community? Choose from the following:\\n            4: excellent\\n            3: good\\n            2: fair\\n            1: poor\\n            ## Strengths\\n            A substantive assessment of the strengths of the paper, touching on each of the following dimensions: originality, quality, clarity, and significance. We encourage reviewers to be broad in their definitions of originality and significance. For example, originality may arise from a new definition or problem formulation, creative combinations of existing ideas, application to a new domain, or removing limitations from prior results.\\n            ## Weaknesses\\n            A substantive assessment of the weaknesses of the paper. Focus on constructive and actionable insights on how the work could improve towards its stated goals. Be specific, avoid generic remarks. For example, if you believe the contribution lacks novelty, provide references and an explanation as evidence; if you believe experiments are insufficient, explain why and exactly what is missing, etc.\\n            ## Questions\\n            Please list up and carefully describe any questions and suggestions for the authors. Think of the things where a response from the author can change your opinion, clarify a confusion or address a limitation. This is important for a productive rebuttal and discussion phase with the authors.\\n            ## Flag For Ethics Review\\n            If there are ethical issues with this paper, please flag the paper for an ethics review and select area of expertise that would be most useful for the ethics reviewer to have. Please select all that apply. Choose from the following:\\n            No ethics review needed.\\n            Yes, Discrimination / bias / fairness concerns\\n            Yes, Privacy, security and safety\\n            Yes, Legal compliance (e.g., GDPR, copyright, terms of use)\\n            Yes, Potentially harmful insights, methodologies and applications\\n            Yes, Responsible research practice (e.g., human subjects, data release)\\n            Yes, Research integrity issues (e.g., plagiarism, dual submission)\\n            Yes, Unprofessional behaviors (e.g., unprofessional exchange between authors and reviewers)\\n            Yes, Other reasons (please specify below)\\n            ## Details Of Ethics Concerns\\n            Please provide details of your concerns.\\n            ## Rating\\n            Please provide an \"overall score\" for this submission. Choose from the following:\\n            1: strong reject\\n            3: reject, not good enough\\n            5: marginally below the acceptance threshold\\n            6: marginally above the acceptance threshold\\n            8: accept, good paper\\n            10: strong accept, should be highlighted at the conference\\n            Your response must only contain the review in markdown format with sections as defined above.'"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["from prompts import system_prompt, review_prompt, structured_rating_prompt\n", "\n", "system_prompt()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["\"Rank the following papers: Paper 1: Keeping large language models factually up-to-date is crucial for deployment,\\nyet costly retraining remains a challenge. Knowledge editing offers a promising\\nalternative, but methods are only tested on small-scale or synthetic edit\\nbenchmarks. In this work, we aim to bridge research into lifelong knowledge\\nediting to real-world edits at practically relevant scale. We first introduce\\nWikiBigEdit; a large-scale benchmark of real-world Wikidata edits, built to\\nautomatically extend lifelong for future-proof benchmarking. In its first\\ninstance, it includes over 500K question-answer pairs for knowledge editing\\nalongside a comprehensive evaluation pipeline. Finally, we use WikiBigEdit to\\nstudy existing knowledge editing techniques' ability to incorporate large\\nvolumes of real-world facts and contrast their capabilities to generic\\nmodification techniques such as retrieval augmentation and continual finetuning\\nto acquire a complete picture of the practical extent of current lifelong\\nknowledge editing.\\nPaper 2: Information technology has profoundly altered the way humans interact with\\ninformation. The vast amount of content created, shared, and disseminated\\nonline has made it increasingly difficult to access relevant information. Over\\nthe past two decades, search and recommendation systems (collectively referred\\nto as information retrieval systems) have evolved significantly to address\\nthese challenges. Recent advances in large language models (LLMs) have\\ndemonstrated capabilities that surpass human performance in various\\nlanguage-related tasks and exhibit general understanding, reasoning, and\\ndecision-making abilities. This paper explores the transformative potential of\\nlarge language model agents in enhancing search and recommendation systems. We\\ndiscuss the motivations and roles of LLM agents, and establish a classification\\nframework to elaborate on the existing research. We highlight the immense\\npotential of LLM agents in addressing current challenges in search and\\nrecommendation, providing insights into future research directions. This paper\\nis the first to systematically review and classify the research on LLM agents\\nin these domains, offering a novel perspective on leveraging this advanced AI\\ntechnology for information retrieval. To help understand the existing works, we\\nlist the existing papers on agent-based simulation with large language models\\nat this link:\\nhttps://github.com/tsinghua-fib-lab/LLM-Agent-for-Recommendation-and-Search.\\nPaper 3: Large language models (LLMs) have demonstrated remarkable capabilities in\\nhandling complex dialogue tasks without requiring use case-specific\\nfine-tuning. However, analyzing live dialogues in real-time necessitates\\nlow-latency processing systems, making it impractical to deploy models with\\nbillions of parameters due to latency constraints. As a result, practitioners\\noften prefer smaller models with millions of parameters, trained on\\nhigh-quality, human-annotated datasets. Yet, curating such datasets is both\\ntime-consuming and costly. Consequently, there is a growing need to combine the\\nscalability of LLM-generated labels with the precision of human annotations,\\nenabling fine-tuned smaller models to achieve both higher speed and accuracy\\ncomparable to larger models. In this paper, we introduce a simple yet effective\\nframework to address this challenge. Our approach is specifically designed for\\nper-utterance classification problems, which encompass tasks such as intent\\ndetection, dialogue state tracking, and more. To mitigate the impact of\\nlabeling errors from LLMs -- the primary source of inaccuracies in student\\nmodels -- we propose a noise-reduced preference learning loss. Experimental\\nresults demonstrate that our method significantly improves accuracy across\\nutterance-level dialogue tasks, including sentiment detection (over $2\\\\%$),\\ndialogue act classification (over $1.5\\\\%$), etc.\\nPaper 4: Large Language Models (LLMs) have revolutionized natural language processing,\\nyet their internal mechanisms remain largely opaque. Recently, mechanistic\\ninterpretability has attracted significant attention from the research\\ncommunity as a means to understand the inner workings of LLMs. Among various\\nmechanistic interpretability approaches, Sparse Autoencoders (SAEs) have\\nemerged as a particularly promising method due to their ability to disentangle\\nthe complex, superimposed features within LLMs into more interpretable\\ncomponents. This paper presents a comprehensive examination of SAEs as a\\npromising approach to interpreting and understanding LLMs. We provide a\\nsystematic overview of SAE principles, architectures, and applications\\nspecifically tailored for LLM analysis, covering theoretical foundations,\\nimplementation strategies, and recent developments in sparsity mechanisms. We\\nalso explore how SAEs can be leveraged to explain the internal workings of\\nLLMs, steer model behaviors in desired directions, and develop more transparent\\ntraining methodologies for future models. Despite the challenges that remain\\naround SAE implementation and scaling, they continue to provide valuable tools\\nfor understanding the internal mechanisms of large language models.\\nPaper 5: Existing Large Reasoning Models (LRMs) have shown the potential of\\nreinforcement learning (RL) to enhance the complex reasoning capabilities of\\nLarge Language Models~(LLMs). While they achieve remarkable performance on\\nchallenging tasks such as mathematics and coding, they often rely on their\\ninternal knowledge to solve problems, which can be inadequate for\\ntime-sensitive or knowledge-intensive questions, leading to inaccuracies and\\nhallucinations. To address this, we propose \\\\textbf{R1-Searcher}, a novel\\ntwo-stage outcome-based RL approach designed to enhance the search capabilities\\nof LLMs. This method allows LLMs to autonomously invoke external search systems\\nto access additional knowledge during the reasoning process. Our framework\\nrelies exclusively on RL, without requiring process rewards or distillation for\\na cold start. % effectively generalizing to out-of-domain datasets and\\nsupporting both Base and Instruct models. Our experiments demonstrate that our\\nmethod significantly outperforms previous strong RAG methods, even when\\ncompared to the closed-source GPT-4o-mini.\\nPaper 6: The increasing prevalence of online misinformation has heightened the demand\\nfor automated fact-checking solutions. Large Language Models (LLMs) have\\nemerged as potential tools for assisting in this task, but their effectiveness\\nremains uncertain. This study evaluates the fact-checking capabilities of\\nvarious open-source LLMs, focusing on their ability to assess claims with\\ndifferent levels of contextual information. We conduct three key experiments:\\n(1) evaluating whether LLMs can identify the semantic relationship between a\\nclaim and a fact-checking article, (2) assessing models' accuracy in verifying\\nclaims when given a related fact-checking article, and (3) testing LLMs'\\nfact-checking abilities when leveraging data from external knowledge sources\\nsuch as Google and Wikipedia. Our results indicate that LLMs perform well in\\nidentifying claim-article connections and verifying fact-checked stories but\\nstruggle with confirming factual news, where they are outperformed by\\ntraditional fine-tuned models such as RoBERTa. Additionally, the introduction\\nof external knowledge does not significantly enhance LLMs' performance, calling\\nfor more tailored approaches. Our findings highlight both the potential and\\nlimitations of LLMs in automated fact-checking, emphasizing the need for\\nfurther refinements before they can reliably replace human fact-checkers.\\nPaper 7: Existing benchmarks are becoming saturated and struggle to separate model\\nperformances due to factors like data contamination and advancing LLM\\ncapabilities. This paper introduces EMDM (Enhanced Model Differentiation\\nMetric), a novel weighted metric that revitalizes benchmarks by enhancing model\\nseparation. EMDM integrates final answer and Chain-of-Thought (CoT) reasoning\\ncorrectness, assigning weights based on the complexity and reasoning depth\\nrequired to solve a given sample in the evaluation data. Using a baseline LLM\\nin two setups-Unguided, where the model has no prior exposure to test samples,\\nand Guided, where the model has prior knowledge of the desired answer-EMDM\\ndistinguishes instances of varying difficulty. The CoT and answer correctness\\nfrom these setups inform an optimization objective for weight assignment,\\nresulting in a more nuanced evaluation of model performance. Compared to the\\nexact match (EM) metric, which achieves 17% separation on ARC-Challenge, EMDM\\nachieves 46%, demonstrating its effectiveness in differentiating models based\\non reasoning and knowledge requirements.\\nPaper 8: Retrieval-augmented generation (RAG) enhances the reliability of large\\nlanguage model (LLM) answers by integrating external knowledge. However, RAG\\nincreases the end-to-end inference time since looking for relevant documents\\nfrom large vector databases is computationally expensive. To address this, we\\nintroduce Proximity, an approximate key-value cache that optimizes the RAG\\nworkflow by leveraging similarities in user queries. Instead of treating each\\nquery independently, Proximity reuses previously retrieved documents when\\nsimilar queries appear, reducing reliance on expensive vector database lookups.\\nWe evaluate Proximity on the MMLU and MedRAG benchmarks, demonstrating that it\\nsignificantly improves retrieval efficiency while maintaining response\\naccuracy. Proximity reduces retrieval latency by up to 59% while maintaining\\naccuracy and lowers the computational burden on the vector database. We also\\nexperiment with different similarity thresholds and quantify the trade-off\\nbetween speed and recall. Our work shows that approximate caching is a viable\\nand effective strategy for optimizing RAG-based systems.\\nPaper 9: This paper introduces PoSSUM, an open-source protocol for unobtrusive polling\\nof social-media users via multimodal Large Language Models (LLMs). PoSSUM\\nleverages users' real-time posts, images, and other digital traces to create\\nsilicon samples that capture information not present in the LLM's training\\ndata. To obtain representative estimates, PoSSUM employs Multilevel Regression\\nand Post-Stratification (MrP) with structured priors to counteract the\\nobservable selection biases of social-media platforms. The protocol is\\nvalidated during the 2024 U.S. Presidential Election, for which five PoSSUM\\npolls were conducted and published on GitHub and X. In the final poll, fielded\\nOctober 17-26 with a synthetic sample of 1,054 X users, PoSSUM accurately\\npredicted the outcomes in 50 of 51 states and assigned the Republican candidate\\na win probability of 0.65. Notably, it also exhibited lower state-level bias\\nthan most established pollsters. These results demonstrate PoSSUM's potential\\nas a fully automated, unobtrusive alternative to traditional survey methods.\\nPaper 10: Cognitive biases, systematic deviations from rationality in judgment, pose\\nsignificant challenges in generating objective content. This paper introduces a\\nnovel approach for real-time cognitive bias detection in user-generated text\\nusing large language models (LLMs) and advanced prompt engineering techniques.\\nThe proposed system analyzes textual data to identify common cognitive biases\\nsuch as confirmation bias, circular reasoning, and hidden assumption. By\\ndesigning tailored prompts, the system effectively leverages LLMs' capabilities\\nto both recognize and mitigate these biases, improving the quality of\\nhuman-generated content (e.g., news, media, reports). Experimental results\\ndemonstrate the high accuracy of our approach in identifying cognitive biases,\\noffering a valuable tool for enhancing content objectivity and reducing the\\nrisks of biased decision-making.\\n\""]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["review_prompt(paper_abstracts)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["chat_completion = client.chat.completions.create(\n", "        messages=[{\n", "            \"role\": \"system\",\n", "            \"content\": system_prompt()\n", "        }, {\n", "            \"role\": \"user\",\n", "            \"content\": f\"Review the following paper: {paper_abstracts[0]}\"\n", "        }],\n", "        model=os.getenv(\"GEMINI_MODEL\"),\n", "    )\n", "\n", "review = chat_completion.choices[0].message.content.strip()"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# Review\n", "## Summary\n", "This paper introduces a novel approach to few-shot learning by leveraging pre-trained language models (PLMs) and knowledge graphs (KGs). The method, termed \"KG-PLM,\" enriches the input text for PLMs with relevant entities and relations extracted from KGs, aiming to provide more contextual information for better few-shot prediction. The authors demonstrate the effectiveness of KG-PLM on several few-shot classification tasks, showing improved performance compared to baseline methods.\n", "\n", "## Soundness\n", "3: good\n", "\n", "## Presentation\n", "3: good\n", "\n", "## Contribution\n", "3: good\n", "\n", "## Strengths\n", "*   The paper addresses an important problem in few-shot learning, which is to improve performance with limited data.\n", "*   The idea of combining PLMs and KGs is well-motivated, as it leverages the strengths of both approaches for better contextual understanding.\n", "*   The experimental results demonstrate the effectiveness of the proposed KG-PLM method compared to several baselines.\n", "*   The paper is generally well-written and easy to follow.\n", "\n", "## Weaknesses\n", "*   The novelty of the approach is somewhat limited, as it builds upon existing techniques for PLMs and KGs. The combination, while effective, doesn't present a fundamentally new idea.\n", "*   The experiments could be more comprehensive. It would be helpful to see results on a wider range of datasets and few-shot settings.\n", "*   The paper lacks a thorough analysis of the impact of different KG components (entities vs. relations) on the performance of KG-PLM. Understanding the relative importance of these components would provide valuable insights.\n", "*   The paper does not adequately address the computational cost of extracting and processing information from KGs. This cost can be significant, especially for large KGs, and should be discussed in the context of the overall efficiency of the method.\n", "*   The choice of knowledge graph (e.g., Wikidata) and the entity linking method might impact performance, and the paper doesn't explore these choices or their potential impact.\n", "\n", "## Questions\n", "*   Could you provide a more detailed analysis of the computational cost associated with KG-PLM, particularly the time required for KG entity and relation extraction?\n", "*   How does the performance of KG-PLM vary with different choices of knowledge graphs (e.g., DBpedia, ConceptNet)?\n", "*   What is the impact of the entity linking method on the overall performance of KG-PLM?\n", "*   Could you provide an ablation study to analyze the individual contributions of entities and relations extracted from the KG?\n", "*   How does KG-PLM perform when the knowledge graph is incomplete or contains noisy information?\n", "*   Have you considered using prompt engineering with the PLM to further enhance few-shot performance? If so, what were the results?\n", "\n", "## Flag For Ethics Review\n", "No ethics review needed.\n", "\n", "## Details Of Ethics Concerns\n", "N/A\n", "\n", "## Rating\n", "6: marginally above the acceptance threshold\n", "\n", "The paper presents a well-motivated approach to few-shot learning by combining PLMs and KGs. The experimental results show improved performance compared to baseline methods. However, the novelty of the approach is somewhat limited, and the experiments could be more comprehensive. Addressing the questions above would help to clarify the contributions of the paper and strengthen the evaluation. The computational cost and KG-choice aspects need more discussion. While the paper doesn't introduce a revolutionary idea, it offers a practical solution that could be valuable to the community, making it marginally above the acceptance threshold.\n"]}], "source": ["print(review)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel\n", "\n", "class RatingModel(BaseModel):\n", "    soundness: int\n", "    presentation: int\n", "    contribution: int\n", "    final: int"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Extract the ratings from the following review: # Review\\n## Summary\\nThis paper introduces a novel approach to few-shot learning by leveraging pre-trained language models (PLMs) and knowledge graphs (KGs). The method, termed \"KG-PLM,\" enriches the input text for PLMs with relevant entities and relations extracted from KGs, aiming to provide more contextual information for better few-shot prediction. The authors demonstrate the effectiveness of KG-PLM on several few-shot classification tasks, showing improved performance compared to baseline methods.\\n\\n## Soundness\\n3: good\\n\\n## Presentation\\n3: good\\n\\n## Contribution\\n3: good\\n\\n## Strengths\\n*   The paper addresses an important problem in few-shot learning, which is to improve performance with limited data.\\n*   The idea of combining PLMs and KGs is well-motivated, as it leverages the strengths of both approaches for better contextual understanding.\\n*   The experimental results demonstrate the effectiveness of the proposed KG-PLM method compared to several baselines.\\n*   The paper is generally well-written and easy to follow.\\n\\n## Weaknesses\\n*   The novelty of the approach is somewhat limited, as it builds upon existing techniques for PLMs and KGs. The combination, while effective, doesn\\'t present a fundamentally new idea.\\n*   The experiments could be more comprehensive. It would be helpful to see results on a wider range of datasets and few-shot settings.\\n*   The paper lacks a thorough analysis of the impact of different KG components (entities vs. relations) on the performance of KG-PLM. Understanding the relative importance of these components would provide valuable insights.\\n*   The paper does not adequately address the computational cost of extracting and processing information from KGs. This cost can be significant, especially for large KGs, and should be discussed in the context of the overall efficiency of the method.\\n*   The choice of knowledge graph (e.g., Wikidata) and the entity linking method might impact performance, and the paper doesn\\'t explore these choices or their potential impact.\\n\\n## Questions\\n*   Could you provide a more detailed analysis of the computational cost associated with KG-PLM, particularly the time required for KG entity and relation extraction?\\n*   How does the performance of KG-PLM vary with different choices of knowledge graphs (e.g., DBpedia, ConceptNet)?\\n*   What is the impact of the entity linking method on the overall performance of KG-PLM?\\n*   Could you provide an ablation study to analyze the individual contributions of entities and relations extracted from the KG?\\n*   How does KG-PLM perform when the knowledge graph is incomplete or contains noisy information?\\n*   Have you considered using prompt engineering with the PLM to further enhance few-shot performance? If so, what were the results?\\n\\n## Flag For Ethics Review\\nNo ethics review needed.\\n\\n## Details Of Ethics Concerns\\nN/A\\n\\n## Rating\\n6: marginally above the acceptance threshold\\n\\nThe paper presents a well-motivated approach to few-shot learning by combining PLMs and KGs. The experimental results show improved performance compared to baseline methods. However, the novelty of the approach is somewhat limited, and the experiments could be more comprehensive. Addressing the questions above would help to clarify the contributions of the paper and strengthen the evaluation. The computational cost and KG-choice aspects need more discussion. While the paper doesn\\'t introduce a revolutionary idea, it offers a practical solution that could be valuable to the community, making it marginally above the acceptance threshold. as JSON'"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["structured_rating_prompt(review)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["chat_completion = client.beta.chat.completions.parse(\n", "        messages=[{\n", "            \"role\": \"system\",\n", "            \"content\": system_prompt()\n", "        }, {\n", "            \"role\": \"user\",\n", "            \"content\": structured_rating_prompt(review)\n", "        }],\n", "        model=os.getenv(\"GEMINI_MODEL\"),\n", "        response_format=RatingModel,\n", "    )\n", "\n", "rating = chat_completion.choices[0].message.content.strip()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["'{\\n  \"contribution\": 3,\\n  \"presentation\": 3,\\n  \"final\": 6,\\n  \"soundness\": 3\\n}'"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["rating"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 2}
{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "from typing import List, Tuple, Optional\n", "from openai import OpenAI\n", "\n", "import sys\n", "sys.path.append('..')\n", "from src.models.paper import NERResult\n", "from scripts.utils.prompts import ner_prompt\n", "\n", "async def perform_ner(text: str, client: OpenAI) -> List[Tuple[str, str]]:\n", "    \"\"\"\n", "    Extract named entities from text using NER\n", "    \n", "    Args:\n", "        text (str): Text to analyze\n", "        client (OpenAI): OpenAI client\n", "        \n", "    Returns:\n", "        List[Tuple[str, str]]: List of entity tuples (name, category)\n", "    \"\"\"\n", "    chat_completion = client.beta.chat.completions.parse(\n", "    model= os.getenv(\"GEMINI_MODEL\"),\n", "    messages=[{\n", "            \"role\": \"system\",\n", "            \"content\": \"\"\"You are a helpful assistant with world-class expertise in AI research and application.\n", "Your role is to extract and recognize entities from research papers better than anyone else in the world.\"\"\"\n", "        }, {\n", "            \"role\": \"user\",\n", "            \"content\": ner_prompt(text)\n", "        }],\n", "    response_format=NERResult,\n", ")\n", "\n", "    ner_result = NERResult(**json.loads(chat_completion.choices[0].message.content))\n", "    return [(entity.value, entity.category.name) for entity in ner_result.entities]\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from dotenv import load_dotenv, find_dotenv\n", "\n", "# Load environment variables\n", "load_dotenv(find_dotenv())\n", "\n", "# Initialize OpenAI client\n", "client = OpenAI(\n", "    api_key=os.getenv(\"GEMINI_API_KEY\"),\n", "    base_url=os.getenv(\"GEMINI_BASE_URL\")\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["sample_text = \"\"\"\n", "    This paper discusses theoretical implications of transformers in NLP applications.\n", "    We explore the mathematical foundations behind attention mechanisms and their relationship\n", "    to earlier recurrent neural network approaches.\n", "    \"\"\""]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["[('transformers', 'Models & Architectures'),\n", " ('NLP applications', 'Applications & Use Cases'),\n", " ('attention mechanisms', 'Algorithms & Learning Techniques'),\n", " ('recurrent neural network', 'Models & Architectures')]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["await perform_ner(sample_text, client)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["'gemini-2.0-flash-001'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["os.getenv(\"GEMINI_MODEL\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["from src.models.paper import SOTAResult\n", "import inspect\n", "import traceback\n", "\n", "async def extract_sota(text: str, client: OpenAI) -> Optional[SOTAResult]:\n", "    \"\"\"\n", "    Extract state-of-the-art information from text using structured outputs API\n", "    \n", "    Args:\n", "        text (str): Text to analyze\n", "        client (OpenAI): OpenAI client\n", "        \n", "    Returns:\n", "        Optional[SOTAResult]: Extracted SOTA information, or None if not found\n", "    \"\"\"\n", "    print(\"Extracting SOTA...\")\n", "    print(f\"Using model: {os.getenv('GEMINI_MODEL')}\")\n", "    \n", "    # Print client details for debugging\n", "    print(f\"Client base URL: {client.base_url}\")\n", "    print(f\"Client type: {type(client)}\")\n", "    print(f\"Has beta attribute: {hasattr(client, 'beta')}\")\n", "    if hasattr(client, 'beta'):\n", "        print(f\"Has beta.chat: {hasattr(client.beta, 'chat')}\")\n", "        print(f\"Has beta.chat.completions: {hasattr(client.beta.chat, 'completions')}\")\n", "        if hasattr(client.beta.chat, 'completions'):\n", "            print(f\"Has parse method: {hasattr(client.beta.chat.completions, 'parse')}\")\n", "            if hasattr(client.beta.chat.completions, 'parse'):\n", "                print(f\"Parse method signature: {inspect.signature(client.beta.chat.completions.parse)}\")\n", "    \n", "    if not text or len(text.strip()) < 10:\n", "        print(\"Text too short, skipping SOTA extraction\")\n", "        return None\n", "    \n", "    try:\n", "        print(\"Attempting to call client.beta.chat.completions.parse...\")\n", "        # Using the structured outputs parse API \n", "        chat_completion = client.beta.chat.completions.parse(\n", "            model=os.getenv(\"GEMINI_MODEL\"),\n", "            messages=[{\n", "                \"role\": \"system\",\n", "                \"content\": \"\"\"You are a helpful assistant with world-class expertise in AI research and application.\n", "                Your role is to extract SOTA improvements from research papers better than anyone else in the world.\n", "                You only need to focus on the most important benchmark - the one that is most relevant to advancing the field.\n", "                \n", "                Return your response in a structured JSON format with these fields:\n", "                - sota: the main object containing all SOTA information\n", "                  - benchmark: name of benchmark dataset or task\n", "                  - table_nr: the table number as an integer, use 0 if unknown\n", "                  - previous_method: name of previous SOTA method\n", "                  - previous_metric: metric name like accuracy, F1, etc.\n", "                  - previous_value: previous method's score as a number\n", "                  - proposed_method: name of the proposed method\n", "                  - proposed_metric: metric name like accuracy, F1, etc.\n", "                  - proposed_value: proposed method's score as a number\n", "                  - absolute_gain: absolute improvement as a number\n", "                  - relative_gain_percent: percentage improvement as a number\n", "                \n", "                If the paper doesn't have SOTA claims or you can't extract this data, fill the JSON with Nones.\n", "                \"\"\"\n", "            }, {\n", "                \"role\": \"user\",\n", "                \"content\": f\"\"\"Extract the SOTA (state-of-the-art) information from this text. Focus on the main benchmark and performance metrics.\n", "                \n", "                Text: {text}\n", "                \n", "                Return the structured JSON SOTA data according to the JSON specified.\n", "                \"\"\"\n", "            }],\n", "            response_format=SOTAResult,\n", "        )\n", "        \n", "        # The result is already parsed as a SOTAResult object\n", "        sota_result = SOTAResult(**json.loads(chat_completion.choices[0].message.content))\n", "        \n", "        if not sota_result:\n", "            print(\"Model returned null response - no SOTA claims found\")\n", "            return None\n", "            \n", "        print(\"Successfully created SOTAResult object\")\n", "        return sota_result\n", "        \n", "    except Exception as e:\n", "        print(f\"Error in extract_sota: {e}\")\n", "        print(f\"Error type: {type(e)}\")\n", "        print(f\"Error args: {e.args}\")\n", "        traceback.print_exc()\n", "        return None"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracting SOTA...\n", "Using model: gemini-2.0-flash-001\n", "Client base URL: https://generativelanguage.googleapis.com/v1beta/openai/\n", "Client type: <class 'openai.OpenAI'>\n", "Has beta attribute: True\n", "Has beta.chat: True\n", "Has beta.chat.completions: True\n", "Has parse method: True\n", "Parse method signature: (*, messages: 'Iterable[ChatCompletionMessageParam]', model: 'Union[str, ChatModel]', audio: 'Optional[ChatCompletionAudioParam] | NotGiven' = NOT_GIVEN, response_format: 'type[ResponseFormatT] | NotGiven' = NOT_GIVEN, frequency_penalty: 'Optional[float] | NotGiven' = NOT_GIVEN, function_call: 'completion_create_params.FunctionCall | NotGiven' = NOT_GIVEN, functions: 'Iterable[completion_create_params.Function] | NotGiven' = NOT_GIVEN, logit_bias: 'Optional[Dict[str, int]] | NotGiven' = NOT_GIVEN, logprobs: 'Optional[bool] | NotGiven' = NOT_GIVEN, max_completion_tokens: 'Optional[int] | NotGiven' = NOT_GIVEN, max_tokens: 'Optional[int] | NotGiven' = NOT_GIVEN, metadata: 'Optional[Metadata] | NotGiven' = NOT_GIVEN, modalities: 'Optional[List[ChatCompletionModality]] | NotGiven' = NOT_GIVEN, n: 'Optional[int] | NotGiven' = NOT_GIVEN, parallel_tool_calls: 'bool | NotGiven' = NOT_GIVEN, prediction: 'Optional[ChatCompletionPredictionContentParam] | NotGiven' = NOT_GIVEN, presence_penalty: 'Optional[float] | NotGiven' = NOT_GIVEN, reasoning_effort: 'Optional[ChatCompletionReasoningEffort] | NotGiven' = NOT_GIVEN, seed: 'Optional[int] | NotGiven' = NOT_GIVEN, service_tier: \"Optional[Literal['auto', 'default']] | NotGiven\" = NOT_GIVEN, stop: 'Union[Optional[str], List[str]] | NotGiven' = NOT_GIVEN, store: 'Optional[bool] | NotGiven' = NOT_GIVEN, stream_options: 'Optional[ChatCompletionStreamOptionsParam] | NotGiven' = NOT_GIVEN, temperature: 'Optional[float] | NotGiven' = NOT_GIVEN, tool_choice: 'ChatCompletionToolChoiceOptionParam | NotGiven' = NOT_GIVEN, tools: 'Iterable[ChatCompletionToolParam] | NotGiven' = NOT_GIVEN, top_logprobs: 'Optional[int] | NotGiven' = NOT_GIVEN, top_p: 'Optional[float] | NotGiven' = NOT_GIVEN, user: 'str | NotGiven' = NOT_GIVEN, extra_headers: 'Headers | None' = None, extra_query: 'Query | None' = None, extra_body: 'Body | None' = None, timeout: 'float | httpx.Timeout | None | NotGiven' = NOT_GIVEN) -> 'ParsedChatCompletion[ResponseFormatT]'\n", "Attempting to call client.beta.chat.completions.parse...\n", "Successfully created SOTAResult object\n"]}, {"data": {"text/plain": ["SOTAResult(sota=SOTA(benchmark='None', table_nr=0, previous_method='None', previous_metric='None', previous_value=0.0, proposed_method='None', proposed_metric='None', proposed_value=0.0, absolute_gain=0.0, relative_gain_percent=0.0))"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["await extract_sota(sample_text, client)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 2}
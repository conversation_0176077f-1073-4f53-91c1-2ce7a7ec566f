{"name": "fastmoatless", "version": "1.0.0", "description": "Moatless is your gateway to AI research. It provides a sleek web interface to browse, search, and explore the latest AI research papers from arXiv with automatically generated summaries and classifications.", "main": "tailwind.config.js", "directories": {"test": "tests"}, "scripts": {"test": "python -m pytest tests/e2e", "test:ui": "python -m pytest tests/e2e --verbose", "test:debug": "python -m pytest tests/e2e --verbose --no-header --showlocals", "test:playwright": "npx playwright test", "test:playwright:headed": "npx playwright test --headed", "test:playwright:chromium": "npx playwright test --project=chromium", "test:playwright:headless": "npx playwright test --project=chromium --headed=false", "test:pre-commit": "npx playwright test --project=chromium --headed=false"}, "repository": {"type": "git", "url": "git+https://gitlab.com/xaiguy/fastmoatless.git"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://gitlab.com/xaiguy/fastmoatless/issues"}, "homepage": "https://gitlab.com/xaiguy/fastmoatless#readme", "devDependencies": {"@playwright/test": "^1.51.1", "@types/node": "^22.13.15"}}
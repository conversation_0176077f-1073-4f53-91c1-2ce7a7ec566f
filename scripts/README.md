# Scripts Directory

This directory contains various scripts organized by their functionality. The scripts are organized into the following categories:

## Entity Management

Scripts related to entity extraction, resolution, and management.

### Resolution

Core entity resolution functionality:

- `run_entity_resolution.py` - Runs entity resolution process for the knowledge graph
- `update_entity_dictionary.py` - Updates the entity dictionary
- `lookup_entity.py` - Looks up entities in the dictionary

### Testing

Scripts for testing and fixing entity-related issues:

- `add_test_entities.py` - Adds test data for entity resolution
- `add_test_entities_missing.py` - Adds test data with missing entities
- `fix_and_resolve_entities.py` - Fixes entity format issues and runs resolution
- `fix_entity_format.py` - Diagnoses and fixes entity format issues
- `fix_missing_entities.py` - Fixes missing entities in summaries
- `test_entity_dictionary_fix.py` - Tests entity dictionary fixes
- `test_entity_format.py` - Tests entity format fixes
- `test_missing_entities.py` - Tests missing entities fixes

## Knowledge Graph

Scripts related to knowledge graph extraction and management.

### Extraction

Scripts for extracting knowledge graphs:

- `extract_kg.py` - Extracts knowledge graphs from abstracts
- `run_kg_extraction.py` - Runs knowledge graph extraction

### Neo4j

Scripts for Neo4j integration:

- `mongodb_to_neo4j.py` - Exports MongoDB KG to Neo4j
- `neo4j_ontology_kg.py` - Sets up Neo4j schema and ontology

### Testing

Scripts for testing knowledge graph functionality:

- `check_kg.py` - Verifies knowledge graph data

## Data Processing

Scripts for processing and extracting data:

- `arxiv_html_checker.py` - Extracts text from arXiv HTML versions
- `create_embeddings.py` - Creates vector embeddings for papers
- `gemini_ocr.py` - Processes PDFs with OCR using Gemini

## Testing

General testing scripts:

- `check_inmemory_db.py` - Tests in-memory database consistency
- `run_combined_test.py` - Runs combined entity resolution tests
- `test_local.py` - Runs local testing environment

## Usage

Most scripts can be run with the `--local` flag to use in-memory databases for testing:

```bash
python scripts/entity_management/resolution/run_entity_resolution.py --local
```

For more information on each script, refer to the script's docstring or run with the `--help` flag:

```bash
python scripts/entity_management/resolution/run_entity_resolution.py --help
```

import aiohttp
import os
import asyncio
import re
from bs4 import BeautifulSoup
from typing import Dict, Optional, Tuple, Union


async def get_paper_data(abs_url: str) -> Dict[str, str]:
    """
    Fetches paper data from arXiv and checks if HTML version is available.
    
    Args:
        abs_url (str): The arXiv abstract URL (e.g., https://arxiv.org/abs/2502.14727)
        
    Returns:
        Dict[str, str]: Dictionary containing paper data with keys:
            - 'id': The paper ID
            - 'abs_url': The abstract URL
            - 'pdf_url': The PDF URL
            - 'html_url': The HTML URL (if available, otherwise None)
            - 'has_html': Boolean indicating if HTML version exists
    """
    try:
        # Extract paper ID from abstract URL
        paper_id = abs_url.split('/')[-1]
        
        # Generate URLs for different versions
        abs_url = f"https://arxiv.org/abs/{paper_id}"
        pdf_url = f"https://arxiv.org/pdf/{paper_id}.pdf"
        html_url = f"https://arxiv.org/html/{paper_id}"
        
        # Check if HTML version exists
        has_html, html_status = await check_html_availability(html_url)
        
        return {
            'id': paper_id,
            'abs_url': abs_url,
            'pdf_url': pdf_url,
            'html_url': html_url if has_html else None,
            'has_html': has_html,
            'html_status': html_status
        }
        
    except Exception as e:
        return {
            'error': str(e)
        }


async def check_html_availability(html_url: str) -> Tuple[bool, int]:
    """
    Checks if HTML version of a paper is available.
    
    Args:
        html_url (str): The URL for the HTML version
        
    Returns:
        Tuple[bool, int]: (has_html, status_code)
    """
    timeout = aiohttp.ClientTimeout(total=30)  # Increased from 10 to 30 seconds
    try:
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.head(html_url, allow_redirects=True) as response:
                return response.status == 200, response.status
    except aiohttp.ClientError:
        return False, 0
    except asyncio.TimeoutError:
        print(f"Timeout checking HTML availability for {html_url}")
        return False, 0


async def download_paper_content(paper_data: Dict[str, str], prefer_html: bool = True) -> Optional[Union[str, bytes]]:
    """
    Downloads paper content, preferring HTML version if available and requested.
    
    Args:
        paper_data (Dict[str, str]): Paper data from get_paper_data
        prefer_html (bool): Whether to prefer HTML version if available
        
    Returns:
        Optional[Union[str, bytes]]: The content (HTML as str, PDF as bytes) or None if download failed
    """
    if prefer_html and paper_data.get('has_html'):
        return await download_html_content(paper_data['html_url'])
    else:
        return await download_pdf_content(paper_data['pdf_url'])


async def download_html_content(html_url: str) -> Optional[str]:
    """
    Downloads HTML content of a paper.
    
    Args:
        html_url (str): The URL for the HTML version
        
    Returns:
        Optional[str]: The HTML content or None if download failed
    """
    timeout = aiohttp.ClientTimeout(total=60)  # Increased from 30 to 60 seconds
    try:
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.get(html_url) as response:
                if response.status != 200:
                    return None
                return await response.text()
    except aiohttp.ClientError:
        return None
    except asyncio.TimeoutError:
        print(f"Timeout downloading HTML content for {html_url}")
        return None


async def download_pdf_content(pdf_url: str) -> Optional[bytes]:
    """
    Downloads PDF content of a paper.
    
    Args:
        pdf_url (str): The URL for the PDF version
        
    Returns:
        Optional[bytes]: The PDF content as bytes or None if download failed
    """
    timeout = aiohttp.ClientTimeout(total=90)  # Increased from 30 to 90 seconds for PDFs
    try:
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.get(pdf_url) as response:
                if response.status != 200:
                    return None
                return await response.read()
    except aiohttp.ClientError:
        return None
    except asyncio.TimeoutError:
        print(f"Timeout downloading PDF content for {pdf_url}")
        return None


async def extract_text_from_html(html_content: str) -> str:
    """
    Extracts paper text from arXiv HTML content.
    
    Args:
        html_content (str): The HTML content of the paper
        
    Returns:
        str: The extracted text content
    """
    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # arXiv HTML papers typically have the content in a main container
        # Look for the main content area (this might need adjustment based on arXiv's HTML structure)
        main_content = soup.find('main') or soup.find('div', {'class': 'ltx_document'}) or soup.find('body')
        
        if not main_content:
            # Fallback to full html if we can't find the main content
            main_content = soup
        
        # Extract all text, preserving paragraph structure
        paragraphs = []
        
        # Find all text paragraphs
        for p in main_content.find_all(['p', 'div', 'section']):
            text = p.get_text(strip=True)
            if text and len(text) > 20:  # Avoid short fragments
                paragraphs.append(text)
        
        # Remove references/bibliography section if possible
        cleaned_paragraphs = []
        for p in paragraphs:
            # Skip if paragraph looks like a reference
            if re.match(r'^\[\d+\]', p) or p.startswith('References'):
                continue
            cleaned_paragraphs.append(p)
        
        return '\n\n'.join(cleaned_paragraphs)
    except Exception as e:
        print(f"Error extracting text from HTML: {e}")
        return ""


async def save_paper_text(paper_id: str, text_content: str) -> str:
    """
    Saves paper text content to a file.
    
    Args:
        paper_id (str): The paper ID
        text_content (str): The text content to save
        
    Returns:
        str: Path to the saved file
    """
    # Create output directory if it doesn't exist
    os.makedirs("paper_texts", exist_ok=True)
    
    # Save to file
    output_path = f"paper_texts/{paper_id}.txt"
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(text_content)
    
    return output_path


async def get_full_paper_text(abs_url: str) -> Tuple[str, bool]:
    """
    Gets the full text of a paper, preferring HTML version when available.
    
    Args:
        abs_url (str): The arXiv abstract URL
        
    Returns:
        Tuple[str, bool]: (text_content, used_html_version)
    """
    # Get paper data
    paper_data = await get_paper_data(abs_url)
    
    if 'error' in paper_data:
        return f"Error retrieving paper data: {paper_data['error']}", False
    
    paper_id = paper_data['id']
    used_html = False
    
    # Check if we already have the text saved
    output_path = f"paper_texts/{paper_id}.txt"
    if os.path.exists(output_path):
        with open(output_path, 'r', encoding='utf-8') as f:
            return f.read(), used_html
    
    # Try to get HTML version first
    if paper_data.get('has_html'):
        html_content = await download_html_content(paper_data['html_url'])
        if html_content:
            text_content = await extract_text_from_html(html_content)
            if text_content:
                # Save the extracted text
                await save_paper_text(paper_id, text_content)
                used_html = True
                return text_content, used_html
    
    # If HTML version failed or isn't available, return only message
    # The actual PDF processing will be handled by gemini_ocr.py
    return f"HTML version not available for {paper_id}, need to process PDF", False


if __name__ == "__main__":
    async def main():
        # Example usage
        test_papers = [
            "https://arxiv.org/abs/2502.14727",  # Try with a recent paper
            "https://arxiv.org/abs/1706.03762",  # Attention is All You Need (may have HTML)
        ]
        
        for url in test_papers:
            paper_data = await get_paper_data(url)
            print(f"Paper ID: {paper_data['id']}")
            print(f"Has HTML version: {paper_data['has_html']} (status: {paper_data.get('html_status')})")
            print(f"Abstract URL: {paper_data['abs_url']}")
            print(f"PDF URL: {paper_data['pdf_url']}")
            print(f"HTML URL: {paper_data['html_url']}")
            
            # Try to get full text
            text, used_html = await get_full_paper_text(url)
            print(f"Got text using HTML: {used_html}")
            print(f"First 200 chars: {text[:200]}...")
            print("-" * 50)
    
    asyncio.run(main())
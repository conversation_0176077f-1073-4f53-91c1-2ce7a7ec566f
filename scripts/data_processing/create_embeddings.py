#!/usr/bin/env python3
"""
<PERSON>ript to create vector embeddings from paper abstracts in MongoDB and store them in PGVector
"""
import os
import sys
import asyncio
import time
from dotenv import load_dotenv
from openai import OpenAI
from google import genai
import psycopg2
from psycopg2.extras import execute_values
import random
import numpy as np

# Add the current directory to the path so we can import from src
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), ".."))

# Import necessary modules from the project
from src.db.mongo import setup_mongo_client, setup_collections

# Load environment variables
load_dotenv()

def create_and_store_vector_embeddings(genai_client, mongo_summaries_collection):
    """
    Retrieves paper abstracts from MongoDB, creates vector embeddings using OpenAI API,
    and stores them in a PostgreSQL database with pgvector extension.
    
    Args:
        openai_client: The OpenAI client to use for creating embeddings
        mongo_summaries_collection: MongoDB collection containing paper summaries
    """
    print("Starting vector embedding creation process...")
    
    # Connect to PostgreSQL with pgvector
    pg_conn = None
    try:
        # Use the private DATABASE_URL for vector storage
        database_url = os.getenv("DATABASE_URL_PRIVATE")
        if not database_url:
            print("Error: DATABASE_URL_PRIVATE environment variable not set")
            return
            
        print(f"Connecting to PostgreSQL database...")
        pg_conn = psycopg2.connect(database_url)
        cursor = pg_conn.cursor()
        
        # Enable pgvector extension if not already enabled
        cursor.execute("CREATE EXTENSION IF NOT EXISTS vector;")
        
        # Create the papers_embeddings table if it doesn't exist
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS papers_embeddings (
                id SERIAL PRIMARY KEY,
                paper_id TEXT UNIQUE NOT NULL,
                abs_url TEXT NOT NULL,
                title TEXT NOT NULL,
                abstract TEXT NOT NULL,
                embedding VECTOR(1536)
            );
        """)
        
        # Create an index for faster similarity search
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS papers_embedding_idx 
            ON papers_embeddings 
            USING ivfflat (embedding vector_cosine_ops)
            WITH (lists = 100);
        """)
        
        pg_conn.commit()
        print("Database setup complete.")
        
        # Retrieve all papers from MongoDB that need embeddings
        papers = list(mongo_summaries_collection.find({}))
        
        if not papers:
            print("No papers found in MongoDB collection.")
            return
            
        print(f"Found {len(papers)} papers in MongoDB. Processing embeddings...")
        
        # Process in batches to avoid memory issues
        batch_size = 10
        #for i in range(0, len(papers), batch_size):
        for i in range(0, 10, batch_size):
            batch = papers[i:i+batch_size]
            print(f"Processing batch {i//batch_size + 1} of {(len(papers) + batch_size - 1) // batch_size}")
            
            # Prepare data for insertion
            embedding_data = []
            
            for paper in batch:
                # Extract paper ID from abs_url (assuming arXiv format)
                abs_url = paper.get('abs_url', '')
                paper_id = abs_url.split('/')[-1]
                
                # Skip if no abstract
                if not paper.get('abstract'):
                    print(f"Skipping paper {paper_id} - no abstract")
                    continue
                
                try:
                    # Rate limiting with exponential backoff and jitter
                    max_retries = 5
                    base_delay = 1.0
                    for attempt in range(max_retries):
                        try:
                            # Create embedding using Google GenAI API
                            result = genai_client.models.embed_content(
                            model="gemini-embedding-exp-03-07",
                            contents=paper['abstract']
                        )
                            
                            # Extract the embedding from the response
                            embedding = result.embeddings
                            
                            if not isinstance(embedding, list):
                                embedding = list(embedding)

                            # Add to our batch data
                            embedding_data.append((
                                paper_id,
                                paper['abs_url'],
                                paper['title'],
                                paper['abstract'],
                                embedding
                            ))
                            
                            # Add consistent delay between requests to avoid rate limiting
                            time.sleep(0.5)
                            
                            # Successful response, break out of retry loop
                            break
                            
                        except Exception as e:
                            if "RESOURCE_EXHAUST" in str(e) or "rate limit" in str(e).lower():
                                if attempt < max_retries - 1:
                                    if attempt >= 2:
                                        # After 2 retries, use a much longer wait period
                                        delay = 60.0
                                        print(f"Rate limit hit for paper {paper_id}, long wait retry in {delay:.2f} seconds...")
                                    else:
                                        # Initial retries with normal backoff
                                        delay = base_delay * (2 ** attempt) + random.uniform(0, 0.5)
                                        print(f"Rate limit hit for paper {paper_id}, retrying in {delay:.2f} seconds...")
                                    time.sleep(delay)
                                else:
                                    # Max retries reached
                                    raise
                            else:
                                # Not a rate limiting error, re-raise
                                raise
                    
                except Exception as e:
                    print(f"Error creating embedding for paper {paper_id}: {e}")
            
            # Insert the batch into PostgreSQL
            if embedding_data:
                try:
                    execute_values(
                        cursor,
                        """
                        INSERT INTO papers_embeddings (paper_id, abs_url, title, abstract, embedding)
                        VALUES %s
                        ON CONFLICT (paper_id) 
                        DO UPDATE SET 
                            abs_url = EXCLUDED.abs_url,
                            title = EXCLUDED.title,
                            abstract = EXCLUDED.abstract,
                            embedding = EXCLUDED.embedding
                        """,
                        embedding_data
                    )
                    pg_conn.commit()
                    print(f"Successfully stored {len(embedding_data)} embeddings")
                except Exception as e:
                    pg_conn.rollback()
                    print(f"Error storing embeddings in PostgreSQL: {e}")
        
        print("Vector embedding process completed successfully.")
        
    except Exception as e:
        print(f"Error in vector embedding process: {e}")
    finally:
        if pg_conn:
            pg_conn.close()
            print("PostgreSQL connection closed.")

def main():
    """
    Main function to run the script
    """
    # Setup OpenAI client
    openai_client = OpenAI(
        api_key=os.getenv("GEMINI_API_KEY"), 
        base_url=os.getenv("GEMINI_BASE_URL")
    )
    
    # Setup MongoDB client
    print("Connecting to MongoDB...")
    mongo_client = setup_mongo_client(False)  # False means use remote MongoDB
    collections = setup_collections(mongo_client)
    print("MongoDB collections loaded!")
    
    # Get the summaries collection
    summaries_collection = collections['summaries']
    
    # Create and store vector embeddings
    create_and_store_vector_embeddings(openai_client, summaries_collection)
    
if __name__ == "__main__":
    main()
from pdf2image import convert_from_path
from PIL import Image
import io
import base64
import os 
import aiohttp
import asyncio
from typing import <PERSON>ple, Optional

from scripts.utils.prompts import ocr_chunking

async def process_pdf_content(client, abs_url: str, force_reprocess: bool = False) -> Tuple[str, bool]:
    """
    Fetches PDF from arXiv, converts to images, processes with LLM and saves text output.
    
    Args:
        client: The API client to use for OCR
        abs_url (str): The arXiv abstract URL
        force_reprocess (bool): Whether to force reprocessing even if file exists
        
    Returns:
        Tuple[str, bool]: (text_content or path to text file, success status)
    """
    try:
        # Convert abs URL to PDF URL
        paper_id = abs_url.split('/')[-1]
        pdf_url = f"https://arxiv.org/pdf/{paper_id}.pdf"
        output_path = f"paper_texts/{paper_id}.txt"

        # Check if we already have the processed text
        if os.path.exists(output_path) and not force_reprocess:
            with open(output_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if content and len(content) > 100:  # Ensure it's not empty or too short
                    return content, True
        
        # Create output directory if it doesn't exist
        os.makedirs("paper_texts", exist_ok=True)
        
        # Download PDF
        timeout = aiohttp.ClientTimeout(total=120)  # Increased from 30 to 120 seconds
        try:
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(pdf_url) as response:
                    if response.status != 200:
                        return f"Error fetching PDF: {response.status}", False
                    
                    # Save PDF temporarily
                    pdf_content = await response.read()
                    temp_pdf = f"temp_{paper_id}.pdf"
                    with open(temp_pdf, 'wb') as f:
                        f.write(pdf_content)
        except asyncio.TimeoutError:
            return f"Timeout downloading PDF from {pdf_url}", False

        try:
            # Prepare OCR prompt text - we need the text content, not the function reference
            ocr_prompt_text = ocr_chunking()  # Call the function to get the prompt text
            
            try:
                # Convert PDF pages to images
                # Limit to first N pages to avoid excessive API usage
                max_pages = 15  # Adjust based on your needs and budget
                
                try:
                    # This will fail if poppler is not installed
                    images = convert_from_path(temp_pdf)
                    images = images[:max_pages]  # Limit to first N pages
                except Exception as pdf_error:
                    # Check if it's a poppler error
                    if "poppler" in str(pdf_error).lower():
                        error_msg = (
                            f"Poppler is not installed or not found in PATH. "
                            f"Please install poppler-utils package. "
                            f"On macOS: brew install poppler, "
                            f"On Ubuntu/Debian: apt-get install poppler-utils"
                        )
                        print(error_msg)
                        return error_msg, False
                    else:
                        # Re-raise if it's a different error
                        raise
                
                full_text = []
                
                # Create a progress indicator
                print(f"Processing PDF for {paper_id}: {len(images)} pages")

                # Process each page
                for i, image in enumerate(images):
                    print(f"Processing page {i+1}/{len(images)} for {paper_id}")
                    
                    # Convert PIL image to bytes and base64
                    img_byte_arr = io.BytesIO()
                    image.save(img_byte_arr, format='PNG')
                    img_base64 = base64.b64encode(img_byte_arr.getvalue()).decode('utf-8')

                    # Send to LLM API
                    response = client.chat.completions.create(
                        messages=[{
                            "role": "user",
                            "content": [
                                {
                                    "type": "text",
                                    "text": ocr_prompt_text  # Use the text content of the prompt
                                },
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/png;base64,{img_base64}"
                                    }
                                }
                            ]
                        }],
                        model=os.getenv("GEMINI_MODEL")
                    )

                    # Add processed text to collection
                    processed_text = response.choices[0].message.content.strip()
                    full_text.append(f"--- Page {i+1} ---\n{processed_text}")
                    
                    # Small delay to avoid rate limits
                    await asyncio.sleep(0.5)

                # Save combined text to file
                text_content = '\n\n'.join(full_text)
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(text_content)

                return text_content, True
                
            except Exception as processing_error:
                error_msg = f"Error processing PDF images: {str(processing_error)}"
                print(error_msg)
                return error_msg, False

        finally:
            # Clean up temporary PDF file
            if os.path.exists(temp_pdf):
                os.remove(temp_pdf)
                
    except aiohttp.ClientError as e:
        error_msg = f"Network error processing {paper_id}: {str(e)}"
        print(error_msg)
        return error_msg, False
    except Exception as e:
        error_msg = f"Error processing PDF {paper_id}: {str(e)}"
        print(error_msg)
        return error_msg, False


async def get_full_paper_text(client, abs_url: str, force_reprocess: bool = False) -> Tuple[str, bool]:
    """
    High-level function to get full paper text by processing PDF.
    This function should be used when HTML version is not available.
    
    Args:
        client: The API client to use for OCR
        abs_url (str): The arXiv abstract URL
        force_reprocess (bool): Whether to force reprocessing
        
    Returns:
        Tuple[str, bool]: (text_content, success status)
    """
    # Process the PDF and get the text content
    text_content, success = await process_pdf_content(client, abs_url, force_reprocess)
    
    # If processing was successful and content is returned
    if success and isinstance(text_content, str) and len(text_content) > 100:
        return text_content, True
    else:
        return f"Failed to extract content from PDF for {abs_url}: {text_content}", False

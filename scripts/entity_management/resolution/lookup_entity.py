#!/usr/bin/env python3
"""
Lookup entities in the entity dictionary.
This utility allows quick lookups of entity values to find their canonical forms.
"""

import os
import sys
import argparse
import json

# Add the current directory to the path so we can import from src
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "../.."))

# Import the entity dictionary manager
from src.utils.entity_dictionary import get_entity_dictionary_manager, normalize_text

def lookup_entity(entity_value, dictionary_path=None):
    """
    Look up an entity in the dictionary and print its canonical form
    
    Args:
        entity_value: The entity value to look up
        dictionary_path: Optional path to the dictionary file
    """
    # Get the entity dictionary manager
    entity_dict_manager = get_entity_dictionary_manager(
        dictionary_path=dictionary_path or "data/entity_dictionary.json"
    )
    
    # Load the dictionary
    entity_dict_manager.load_dictionary()
    
    print(f"Loaded entity dictionary with {len(entity_dict_manager.entity_dict)} entries")
    print(f"Last updated: {entity_dict_manager.last_updated}")
    print()
    
    # Normalize the input
    normalized_value = normalize_text(entity_value)
    print(f"Input value: {entity_value}")
    print(f"Normalized value: {normalized_value}")
    print()
    
    # Look up the entity
    result = entity_dict_manager.lookup_entity(entity_value)
    
    if result:
        print("Entity found!")
        print(f"Canonical ID: {result.get('canonical_id')}")
        print(f"Canonical Name: {result.get('canonical_name')}")
        print(f"Entity Type: {result.get('entity_type', 'Unknown')}")
        
        if 'description' in result:
            print(f"Description: {result.get('description')}")
            
        if 'properties' in result and result['properties']:
            print("Properties:")
            for key, value in result['properties'].items():
                print(f"  {key}: {value}")
    else:
        print("Entity not found in dictionary")
        
        # Suggest similar entities
        print("\nSimilar entities:")
        found = False
        for key, value in entity_dict_manager.entity_dict.items():
            if normalized_value in key or key in normalized_value:
                print(f"  {key} → {value.get('canonical_name')} ({value.get('canonical_id')})")
                found = True
                
        if not found:
            print("  No similar entities found")

def main():
    """Main entry point"""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Look up entities in the dictionary')
    parser.add_argument('entity', type=str, help='Entity value to look up')
    parser.add_argument('--path', type=str, help='Path to the dictionary file', default=None)
    args = parser.parse_args()
    
    # Look up the entity
    lookup_entity(args.entity, dictionary_path=args.path)
    
if __name__ == "__main__":
    main()
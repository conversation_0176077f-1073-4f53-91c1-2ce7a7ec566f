#!/usr/bin/env python3
"""
Script to run entity resolution process for the knowledge graph.
Resolves, deduplicates, and normalizes entities using Gemini API
and creates a synchronized entity resolution table.
"""

import os
import sys
import asyncio
import argparse
from openai import OpenAI
from dotenv import load_dotenv

# Add the current directory to the path so we can import from src
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "../.."))

# Import necessary modules
from src.db.mongo import (
    setup_mongo_client, 
    setup_collections, 
    create_indexes, 
    get_collections, 
    set_collections,
    load_json_data_to_mock
)
from src.services.entity_resolution import EntityResolutionService
from src.models.entity import EntityResolutionResponse

async def run_entity_resolution(force_update=False, local_mode=False, limit=None):
    """
    Main function to run the entity resolution process
    
    Args:
        force_update: Whether to force a full update of all entities
        local_mode: Whether to use in-memory MongoDB for local testing
        limit: Maximum number of summaries to process (for testing)
    """
    print("\n======== ENTITY RESOLUTION ========")
    print(f"Force update: {force_update}")
    print(f"Local mode: {local_mode}")
    print(f"Limit: {limit or 'None'}")
    
    # If in local mode, unset MONGO_PRIVATE_URL to prevent remote connection attempts
    if local_mode:
        print("Running in local mode, unsetting MONGO_PRIVATE_URL to prevent remote connections")
        os.environ["MONGO_PRIVATE_URL"] = ""
    
    # If collections are set, we'll use those; otherwise, we'll set up new ones
    # Setup MongoDB client
    mongo_client = setup_mongo_client(local_mode)
    
    # If in local mode, load data from JSON files
    if local_mode:
        print("Loading data from local JSON files...")
        load_json_data_to_mock(mongo_client)
    
    # Setup collections and indexes    
    collections = setup_collections(mongo_client)
    create_indexes(collections)
    
    # Set global collections for future use
    set_collections(collections)
    
    # Debug information
    try:
        db = collections['summaries'].database
        print(f"Available collections: {db.list_collection_names()}")
    except Exception as e:
        print(f"Error accessing database: {e}")
    
    # Check if we have summaries
    summaries_count = collections['summaries'].count_documents({})
    print(f"Total summaries in collection: {summaries_count}")
    
    # Print a sample summary
    if summaries_count > 0:
        sample = collections['summaries'].find_one({})
        print(f"Sample summary: {sample.get('title')}")
        if 'entities' in sample:
            print(f"  Entities: {len(sample.get('entities', []))}")
            print(f"  First entity: {sample.get('entities')[0] if sample.get('entities') else 'None'}")
        else:
            print("  No entities found in the sample")
    
    # Setup OpenAI client with Gemini API
    openai_client = OpenAI(
        api_key=os.getenv("GEMINI_API_KEY"),
        base_url=os.getenv("GEMINI_BASE_URL")
    )
    
    # Initialize entity resolution service
    entity_service = EntityResolutionService(collections, openai_client)
    
    # Run the entity resolution process
    print("Starting entity resolution process...")
    
    # If limit is specified, limit the number of processed summaries
    if limit:
        # Monkey patch the summaries collection find method to limit results
        original_find = collections['summaries'].find
        
        def limited_find(*args, **kwargs):
            results = original_find(*args, **kwargs)
            # Create a list from the cursor with a limit
            if hasattr(results, 'limit'):
                return results.limit(limit)
            elif isinstance(results, list):
                return results[:limit]
            return results
            
        # Replace the find method temporarily
        collections['summaries'].find = limited_find
    
    # Run entity resolution
    result = await entity_service.resolve_entities(force_update)
    
    # Restore original find method if it was patched
    if limit:
        collections['summaries'].find = original_find
    
    print("\n======== ENTITY RESOLUTION COMPLETE ========")
    print(f"Status: {result.status}")
    print(f"Message: {result.message}")
    
    if hasattr(result, 'updated') and result.updated is not None:
        print(f"Updated entities: {result.updated}")
    if hasattr(result, 'new') and result.new is not None:
        print(f"New entities: {result.new}")
    if hasattr(result, 'total') and result.total is not None:
        print(f"Total entities processed: {result.total}")
    if hasattr(result, 'entities') and result.entities:
        print(f"Entities returned: {len(result.entities)}")
        entity_samples = result.entities[:10] if len(result.entities) > 10 else result.entities
        for entity in entity_samples:
            print(f"  - {entity.canonical_name} ({entity.entity_type})")
        
        if len(result.entities) > 10:
            print(f"  ... and {len(result.entities) - 10} more")
    
    # Update the entity dictionary after entity resolution is complete
    if (hasattr(result, 'updated') and result.updated is not None and result.updated > 0) or \
       (hasattr(result, 'new') and result.new is not None and result.new > 0):
        try:
            # Skip entity dictionary update in local mode to avoid remote DB connection
            if not local_mode:
                from src.utils.entity_dictionary import get_entity_dictionary_manager
                entity_dict_manager = get_entity_dictionary_manager()
                update_count = entity_dict_manager.update_from_mongodb(
                    collections['entity_mappings'],
                    collections['entity_resolution']
                )
                print(f"Updated entity dictionary with {update_count} new mappings")
            else:
                print("Skipping entity dictionary update in local mode")
        except Exception as e:
            print(f"Failed to update entity dictionary: {e}")
            import traceback
            traceback.print_exc()
    
    return result

if __name__ == "__main__":
    # Load environment variables
    load_dotenv()
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run entity resolution for knowledge graph')
    parser.add_argument('--force', action='store_true', help='Force update of all entities')
    parser.add_argument('--local', action='store_true', help='Use local MongoDB for testing')
    parser.add_argument('--limit', type=int, help='Limit the number of summaries to process (for testing)')
    args = parser.parse_args()
    
    # Set local mode environment variable if needed
    if args.local:
        os.environ['LOCAL_MODE'] = 'true'
    
    # Run the entity resolution process
    asyncio.run(run_entity_resolution(args.force, args.local, args.limit))
#!/usr/bin/env python3
"""
Update entity dictionary from MongoDB entity resolution tables.
This utility ensures the entity dictionary is synchronized with the latest entity
resolution data in MongoDB, making entity lookups faster by avoiding database queries.
"""

import os
import sys
import argparse
import asyncio
from typing import Dict, Any

# Add the current directory to the path so we can import from src
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "../.."))

# Import dependencies
from dotenv import load_dotenv, find_dotenv

# Import necessary modules
from src.db import setup_mongo_client, setup_collections
from src.utils.entity_dictionary import get_entity_dictionary_manager

async def update_entity_dictionary(force_rebuild: bool = False, create_empty: bool = True, local_mode: bool = False) -> None:
    """
    Update the entity dictionary from MongoDB entity mappings
    
    Args:
        force_rebuild: If True, force a full rebuild of the dictionary
        create_empty: If True, create an empty dictionary file if collections not found
        local_mode: If True, use in-memory MongoDB for local testing
    """
    print("Updating entity dictionary from MongoDB...")
    
    # Setup MongoDB client and collections
    try:
        client = setup_mongo_client(local_mode=local_mode)
        collections = setup_collections(client)
        
        # Add entity_mappings collection if it doesn't exist
        if not collections.get('entity_mappings') and collections.get('entity_resolution'):
            print("entity_mappings collection not found, but entity_resolution exists")
            mongodb = client['papers']
            collections['entity_mappings'] = mongodb['entity_mappings']
            print("Created entity_mappings collection")
        
        if not collections.get('entity_resolution'):
            print("Warning: entity_resolution collection not found")
            if create_empty:
                # Create an empty dictionary file
                entity_dict_manager = get_entity_dictionary_manager(auto_update=False, local_mode=local_mode)
                if not os.path.exists(entity_dict_manager.dictionary_path):
                    print("Creating empty entity dictionary file")
                    entity_dict_manager.save_dictionary()
                    print(f"Empty entity dictionary saved to {entity_dict_manager.dictionary_path}")
                return
            else:
                print("Error: Cannot update entity dictionary without required collections")
                return
    except Exception as e:
        print(f"Error connecting to MongoDB: {e}")
        if create_empty:
            # Create an empty dictionary file
            entity_dict_manager = get_entity_dictionary_manager(auto_update=False, local_mode=local_mode)
            if not os.path.exists(entity_dict_manager.dictionary_path):
                print("Creating empty entity dictionary file")
                entity_dict_manager.save_dictionary()
                print(f"Empty entity dictionary saved to {entity_dict_manager.dictionary_path}")
            return
        else:
            print("Error: Cannot update entity dictionary without MongoDB connection")
            return
    
    # Get the entity dictionary manager (auto_update=False to avoid recursive updates)
    entity_dict_manager = get_entity_dictionary_manager(auto_update=False, local_mode=local_mode)
    dict_exists = os.path.exists(entity_dict_manager.dictionary_path)
    print(f"Loaded entity dictionary with {len(entity_dict_manager.entity_dict)} entries")
    
    # Check if the dictionary exists
    if not dict_exists:
        print("Entity dictionary file does not exist, will create a new one")
    
    # Clear the dictionary if force rebuild is requested
    if force_rebuild:
        print("Force rebuild requested, clearing dictionary")
        entity_dict_manager.entity_dict = {}
        entity_dict_manager.last_updated = None
    
    # Update the dictionary
    update_count = entity_dict_manager.update_from_mongodb(
        collections['entity_mappings'],
        collections['entity_resolution']
    )
    
    print(f"Updated entity dictionary with {update_count} new mappings")
    print(f"Entity dictionary now contains {len(entity_dict_manager.entity_dict)} entries")
    
    # Save the dictionary (even if empty)
    entity_dict_manager.save_dictionary()
    print(f"Entity dictionary saved to {entity_dict_manager.dictionary_path}")

def main():
    """Main entry point"""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Update entity dictionary from MongoDB')
    parser.add_argument('--force', action='store_true', help='Force a full rebuild of the dictionary')
    parser.add_argument('--path', type=str, help='Path to store the dictionary file', default="data/entity_dictionary.json")
    parser.add_argument('--create-empty', action='store_true', help='Create an empty dictionary file if collections not found', default=True)
    parser.add_argument('--no-create-empty', dest='create_empty', action='store_false', help='Do not create an empty dictionary if collections not found')
    parser.add_argument('--local', action='store_true', help='Use local in-memory MongoDB')
    args = parser.parse_args()
    
    # Load environment variables
    load_dotenv(find_dotenv())
    
    # Update the dictionary
    asyncio.run(update_entity_dictionary(force_rebuild=args.force, create_empty=args.create_empty, local_mode=args.local))

if __name__ == "__main__":
    main()
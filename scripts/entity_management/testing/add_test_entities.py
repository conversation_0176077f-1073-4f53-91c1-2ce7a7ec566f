#!/usr/bin/env python3
"""
Add test entity data to the summaries collection.
"""

import os
import sys
from dotenv import load_dotenv

# Add the current directory to the path so we can import from src
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "../.."))

from src.db.mongo import setup_mongo_client, setup_collections, set_collections

# Sample test data
TEST_SUMMARIES = [
    {
        "abs_url": "http://arxiv.org/abs/test1",
        "title": "Test Paper for Entity Resolution",
        "summary": "This is a test summary for entity resolution",
        "entities": [
            ["deep learning", "Methods & Techniques"],
            ["neural networks", "Models & Architectures"],
            ["computer vision", "Applications & Use Cases"]
        ]
    },
    {
        "abs_url": "http://arxiv.org/abs/test2",
        "title": "Another Test Paper for Entity Resolution",
        "summary": "This is another test summary for entity resolution",
        "entities": [
            ["machine learning", "Methods & Techniques"],
            ["reinforcement learning", "Methods & Techniques"],
            ["robotics", "Applications & Use Cases"]
        ]
    },
    {
        "abs_url": "http://arxiv.org/abs/test3",
        "title": "Third Test Paper for Entity Resolution",
        "summary": "This is a third test summary for entity resolution",
        "entities": [
            ["deep learning", "Methods & Techniques"],
            ["convolutional neural networks", "Models & Architectures"],
            ["image recognition", "Applications & Use Cases"]
        ]
    }
]

def add_test_data(local_mode=True):
    """Add test data to the summaries collection"""
    print("\n======== ADDING TEST DATA ========")
    
    # Setup MongoDB client
    mongo_client = setup_mongo_client(local_mode)
    collections = setup_collections(mongo_client)
    
    # Set global collections for other scripts to use
    set_collections(collections)
    
    # Print collections
    db = mongo_client['papers']
    print(f"Available collections: {db.list_collection_names()}")
    print(f"Memory address of collections object: {id(collections)}")
    print(f"Memory address of summaries collection: {id(collections['summaries'])}")
    
    # Clear existing test data
    collections['summaries'].delete_many({
        "abs_url": {"$in": [summary["abs_url"] for summary in TEST_SUMMARIES]}
    })
    
    # Insert test summaries
    for summary in TEST_SUMMARIES:
        collections['summaries'].insert_one(summary)
    
    print(f"Added {len(TEST_SUMMARIES)} test summaries")
    
    # Verify the count of summaries
    count = collections['summaries'].count_documents({})
    print(f"Total summaries in collection: {count}")
    
    # Check if we can find entities in the summaries
    for summary in collections['summaries'].find({}):
        print(f"Summary: {summary.get('title')}")
        if 'entities' in summary:
            print(f"  Entities: {len(summary.get('entities', []))}")
        else:
            print("  No entities found")
    
    return count

if __name__ == "__main__":
    # Load environment variables
    load_dotenv()
    
    # Set local mode
    os.environ['LOCAL_MODE'] = 'true'
    
    # Add test data
    add_test_data(True)
    
    # Print confirmation
    print("\nTest data has been added successfully!")
    print("Now run: python run_entity_resolution.py --local --force")
    print("The --force flag ensures all entities will be processed.")
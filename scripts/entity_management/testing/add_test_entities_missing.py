#!/usr/bin/env python3
"""
Script to add test summaries with missing entities field for testing the fix.
"""

import os
import sys
import argparse
from dotenv import load_dotenv

# Add the current directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "../.."))

# Import necessary modules
from src.db.mongo import setup_mongo_client, setup_collections

# Sample test data
TEST_SUMMARIES = [
    {
        "abs_url": "http://arxiv.org/abs/test_missing1",
        "title": "Test Paper with No Entities Field",
        "summary": "This is a test summary",
        "tags": ["deep learning", "computer vision"]
        # No entities field
    },
    {
        "abs_url": "http://arxiv.org/abs/test_missing2",
        "title": "Test Paper with Empty Entities Array",
        "summary": "This is another test summary",
        "tags": ["reinforcement learning", "robotics"],
        "entities": []  # Empty entities array
    },
    {
        "abs_url": "http://arxiv.org/abs/test_missing3",
        "title": "Test Paper with No Tags and No Entities",
        "summary": "This is a third test summary"
        # No tags, no entities
    },
    {
        "abs_url": "http://arxiv.org/abs/test_control",
        "title": "Control Test Paper with Entities",
        "summary": "This is a control test summary",
        "tags": ["transformers", "language models"],
        "entities": [["transformers", "Models & Architectures"]]
    }
]

def add_test_data(local_mode=True):
    """Add test summaries to the database"""
    print("\n===== ADDING TEST SUMMARIES WITH MISSING ENTITIES =====")
    
    # Setup MongoDB client
    mongo_client = setup_mongo_client(local_mode=local_mode)
    collections = setup_collections(mongo_client)
    
    # Clear existing test data
    deleted = collections['summaries'].delete_many({
        "abs_url": {"$regex": "test_missing|test_control"}
    })
    print(f"Cleared {deleted.deleted_count} existing test records")
    
    # Insert test data
    for summary in TEST_SUMMARIES:
        collections['summaries'].insert_one(summary)
    
    print(f"Added {len(TEST_SUMMARIES)} test summaries")
    
    # Verify data was inserted
    for i, summary in enumerate(TEST_SUMMARIES):
        doc = collections['summaries'].find_one({"abs_url": summary["abs_url"]})
        print(f"\nTest Summary {i+1}:")
        print(f"  URL: {doc.get('abs_url')}")
        print(f"  Title: {doc.get('title')}")
        print(f"  Has 'entities' field: {'entities' in doc}")
        if 'entities' in doc:
            print(f"  Entities content: {doc['entities']}")

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Add test summaries with missing entities")
    parser.add_argument("--local", action="store_true", help="Use in-memory MongoDB for testing")
    args = parser.parse_args()
    
    # Load environment variables
    load_dotenv()
    
    # Set local mode environment variable if specified
    if args.local:
        os.environ["LOCAL_MODE"] = "true"
        print("Running in local mode with in-memory MongoDB")
    
    # Add test data
    add_test_data(local_mode=args.local)
#!/usr/bin/env python3
"""
<PERSON>ript to fix missing entities and run entity resolution in a single operation.
This ensures all operations use the same MongoDB instance.
"""

import os
import sys
import asyncio
import argparse
import logging
from dotenv import load_dotenv

# Add the current directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "../.."))

# Import necessary modules
from src.db.mongo import setup_mongo_client, setup_collections, create_indexes, set_collections
from src.services.entity_resolution import EntityResolutionService

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/entity_fix_resolution.log')
    ]
)
logger = logging.getLogger('fix_and_resolve')

def create_default_entities(doc):
    """
    Create default entities from tags or title for a document.
    Returns entities in list format [["entity", "category"]] for maximum compatibility.
    """
    default_entities = []
    
    # First try to use tags if available
    if doc.get('tags') and isinstance(doc['tags'], list):
        for tag in doc['tags']:
            if tag and isinstance(tag, str):
                default_entities.append([tag, "Tags"])
    
    # If no tags, try to extract entities from title
    if not default_entities and doc.get('title'):
        title = doc['title']
        # Extract simplistic entities from title (just words longer than 5 chars)
        words = [w for w in title.split() if len(w) > 5 and w.isalpha()]
        for word in words[:3]:  # Limit to first 3 words
            default_entities.append([word, "Title"])
    
    # If still no entities, add a placeholder
    if not default_entities:
        default_entities.append(["No entity", "Uncategorized"])
    
    return default_entities

async def fix_missing_entities_and_resolve(local_mode=False, force_update=False, add_test_data=False):
    """
    Fix missing entities and then run entity resolution in a single process.
    
    Args:
        local_mode: Whether to use in-memory MongoDB
        force_update: Whether to force update all entities
        add_test_data: Whether to add test summaries with different entity formats
    """
    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)
    
    logger.info("===== FIXING MISSING ENTITIES AND RUNNING ENTITY RESOLUTION =====")
    logger.info(f"Local mode: {local_mode}")
    logger.info(f"Force update: {force_update}")
    logger.info(f"Add test data: {add_test_data}")
    
    # Setup MongoDB client
    mongo_client = setup_mongo_client(local_mode)
    collections = setup_collections(mongo_client)
    
    # Set global collections to ensure they're available for all functions
    set_collections(collections)
    
    # Create indexes
    create_indexes(collections)
    
    # Check database
    db = collections['summaries'].database
    logger.info(f"Available collections: {db.list_collection_names()}")
    
    # Add test data if requested
    if add_test_data:
        logger.info("\nAdding test summaries with various entity formats...")
        
        # Sample test data with different entity formats
        test_summaries = [
            {
                "abs_url": "http://arxiv.org/abs/test_dict_format",
                "title": "Test Paper with Dictionary Format Entities", 
                "summary": "Testing dictionary format for entities",
                "entities": [
                    {"value": "deep learning", "category": "Methods & Techniques"},
                    {"value": "neural networks", "category": "Models & Architectures"}
                ]
            },
            {
                "abs_url": "http://arxiv.org/abs/test_list_format",
                "title": "Test Paper with List Format Entities",
                "summary": "Testing list format for entities",
                "entities": [
                    ["reinforcement learning", "Methods & Techniques"],
                    ["robotics", "Applications & Use Cases"]
                ]
            },
            {
                "abs_url": "http://arxiv.org/abs/test_missing_entities",
                "title": "Test Paper with No Entities",
                "summary": "Testing missing entities",
                "tags": ["machine learning", "artificial intelligence"]
            }
        ]
        
        # Clear any existing test data
        logger.info("Clearing existing test data...")
        collections['summaries'].delete_many({
            "abs_url": {"$regex": "test_dict_format|test_list_format|test_missing_entities"}
        })
        
        # Insert the test summaries
        for summary in test_summaries:
            collections['summaries'].insert_one(summary)
        
        logger.info(f"Added {len(test_summaries)} test summaries with different entity formats")
    
    # STEP 1: Find summaries with missing entities
    logger.info("\nSTEP 1: Finding summaries with missing entities...")
    
    missing_entities_query = {
        "$or": [
            {"entities": {"$exists": False}},
            {"entities": {"$eq": []}}
        ]
    }
    
    missing_entities_docs = list(collections['summaries'].find(missing_entities_query))
    
    logger.info(f"Found {len(missing_entities_docs)} summaries with missing entities")
    
    # Sample a few documents for logging
    sample_size = min(5, len(missing_entities_docs))
    if sample_size > 0:
        logger.info(f"Sample of {sample_size} documents with missing entities:")
        for i, doc in enumerate(missing_entities_docs[:sample_size]):
            logger.info(f"Document {i+1}:")
            logger.info(f"  Title: {doc.get('title', 'No title')}")
            logger.info(f"  URL: {doc.get('abs_url', 'No URL')}")
            logger.info(f"  Tags: {doc.get('tags', [])}")
            logger.info(f"  Has 'entities' field: {'entities' in doc}")
            if 'entities' in doc:
                logger.info(f"  Entities content: {doc['entities']}")
    
    # STEP 2: Fix missing entities
    logger.info("\nSTEP 2: Fixing missing entities...")
    updated_count = 0
    
    for i, doc in enumerate(missing_entities_docs):
        try:
            # Create default entities
            default_entities = create_default_entities(doc)
            
            # Update the document
            result = collections['summaries'].update_one(
                {"_id": doc["_id"]},
                {"$set": {"entities": default_entities}}
            )
            
            if result.modified_count > 0:
                updated_count += 1
                
            # Log progress periodically
            if (i + 1) % 100 == 0 or i == len(missing_entities_docs) - 1:
                logger.info(f"Processed {i+1}/{len(missing_entities_docs)} documents")
        except Exception as e:
            logger.error(f"Error updating document {doc.get('abs_url', 'unknown')}: {e}")
    
    logger.info(f"Updated {updated_count} summaries with default entities")
    
    # Verify the fixes
    still_missing = collections['summaries'].count_documents(missing_entities_query)
    logger.info(f"Verification: {still_missing} summaries still missing entities")
    
    # STEP 3: Run entity resolution
    logger.info("\nSTEP 3: Running entity resolution...")
    
    # Check if we have summaries
    summaries_count = collections['summaries'].count_documents({})
    logger.info(f"Total summaries in collection: {summaries_count}")
    
    # Setup OpenAI client for entity resolution
    openai_client = None
    if not local_mode:
        # Only import client in production mode
        from openai import OpenAI
        openai_client = OpenAI(
            api_key=os.getenv("GEMINI_API_KEY"),
            base_url=os.getenv("GEMINI_BASE_URL")
        )
    else:
        # Mock client for local testing
        class MockOpenAI:
            def __init__(self):
                self.beta = type('obj', (), {})
                self.beta.chat = type('obj', (), {})
                self.beta.chat.completions = type('obj', (), {
                    'parse': lambda **kwargs: type('obj', (), {
                        'choices': [
                            type('obj', (), {
                                'message': type('obj', (), {
                                    'content': '{"resolved_entities": [{"canonical_id": "deep_learning", "canonical_name": "Deep Learning", "variant_ids": ["deep learning"], "entity_type": "Methods & Techniques", "description": "A technique in machine learning"}]}'
                                })
                            })
                        ]
                    })
                })
                self.chat = type('obj', (), {})
                self.chat.completions = type('obj', (), {
                    'create': lambda **kwargs: type('obj', (), {
                        'choices': [
                            type('obj', (), {
                                'message': type('obj', (), {
                                    'content': '{"resolved_entities": [{"canonical_id": "deep_learning", "canonical_name": "Deep Learning", "variant_ids": ["deep learning"], "entity_type": "Methods & Techniques", "description": "A technique in machine learning"}]}'
                                })
                            })
                        ]
                    })
                })
        
        openai_client = MockOpenAI()
    
    # Initialize entity resolution service
    entity_service = EntityResolutionService(collections, openai_client)
    
    # Run entity resolution
    result = await entity_service.resolve_entities(force_update)
    
    # Log results
    logger.info("\n===== ENTITY RESOLUTION COMPLETE =====")
    logger.info(f"Status: {result.status}")
    logger.info(f"Message: {result.message}")
    
    if hasattr(result, 'updated') and result.updated is not None:
        logger.info(f"Updated entities: {result.updated}")
    if hasattr(result, 'new') and result.new is not None:
        logger.info(f"New entities: {result.new}")
    if hasattr(result, 'total') and result.total is not None:
        logger.info(f"Total entities processed: {result.total}")
    if hasattr(result, 'entities') and result.entities:
        logger.info(f"Entities returned: {len(result.entities)}")
        for entity in result.entities:
            logger.info(f"  - {entity.canonical_name} ({entity.entity_type})")
    
    # Update the entity dictionary after entity resolution is complete
    if (hasattr(result, 'updated') and result.updated is not None and result.updated > 0) or \
       (hasattr(result, 'new') and result.new is not None and result.new > 0):
        try:
            from src.utils.entity_dictionary import get_entity_dictionary_manager
            entity_dict_manager = get_entity_dictionary_manager()
            update_count = entity_dict_manager.update_from_mongodb(
                collections['entity_mappings'],
                collections['entity_resolution']
            )
            logger.info(f"Updated entity dictionary with {update_count} new mappings")
        except Exception as e:
            logger.error(f"Failed to update entity dictionary: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    return result

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Fix missing entities and run entity resolution")
    parser.add_argument("--local", action="store_true", help="Use in-memory MongoDB for testing")
    parser.add_argument("--force", action="store_true", help="Force update of all entities")
    parser.add_argument("--test-data", action="store_true", help="Add test summaries with different entity formats")
    args = parser.parse_args()
    
    # Load environment variables
    load_dotenv()
    
    # Set local mode environment variable if specified
    if args.local:
        os.environ["LOCAL_MODE"] = "true"
    
    try:
        # Run the function
        asyncio.run(fix_missing_entities_and_resolve(args.local, args.force, args.test_data))
    except Exception as e:
        logger.exception(f"Unhandled exception: {e}")
        sys.exit(1)
#!/usr/bin/env python3
"""
Script to migrate existing entities in the database to a standardized format.
Converts entities from various formats (strings, lists, tuples) to dictionaries 
with 'value' and 'category' keys.
"""

import argparse
import logging
from typing import List, Dict, Any, Union
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/entity_format_migration.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("entity_format_migration")

def standardize_entity_format(entity: Any) -> Dict[str, str]:
    """
    Convert an entity to the standardized dictionary format.
    
    Args:
        entity: Entity in any format (string, list, tuple, dict)
        
    Returns:
        Dict with 'value' and 'category' keys
    """
    if isinstance(entity, dict) and "value" in entity:
        # Already in dictionary format, ensure it has a category
        return {
            "value": entity["value"],
            "category": entity.get("category", "Uncategorized")
        }
    elif isinstance(entity, (list, tuple)) and len(entity) >= 2:
        # List/tuple format [value, category]
        return {
            "value": entity[0],
            "category": entity[1]
        }
    elif isinstance(entity, str):
        # Simple string format - treat as value and use default category
        return {
            "value": entity,
            "category": "Uncategorized"
        }
    else:
        # Invalid format, log and return None
        logger.warning(f"Invalid entity format: {type(entity)}")
        return None

def migrate_entities(db_collections, dry_run=False):
    """
    Migrate all existing entities in the database to standardized format.
    
    Args:
        db_collections: Dictionary of MongoDB collections
        dry_run: If True, only log changes without updating the database
    """
    summaries_collection = db_collections.get('summaries')
    if not summaries_collection:
        logger.error("Summaries collection not available")
        return
        
    # Get all summaries
    summaries = list(summaries_collection.find({}))
    logger.info(f"Found {len(summaries)} summaries to check")
    
    updated_count = 0
    processed_count = 0
    error_count = 0
    
    for summary in summaries:
        processed_count += 1
        summary_id = summary.get("_id")
        title = summary.get("title", "Unknown")
        
        if processed_count % 100 == 0:
            logger.info(f"Processed {processed_count}/{len(summaries)} summaries")
        
        try:
            # Check if the summary has entities
            if "entities" not in summary or not summary["entities"]:
                continue
                
            original_entities = summary["entities"]
            entity_formats = set()
            
            # Check if entities need standardization
            need_update = False
            if isinstance(original_entities, list):
                for entity in original_entities:
                    if isinstance(entity, str):
                        entity_formats.add("string")
                        need_update = True
                    elif isinstance(entity, (list, tuple)):
                        entity_formats.add("list/tuple")
                        need_update = True
                    elif isinstance(entity, dict) and "value" in entity:
                        if entity.get("category", None) is None:
                            entity_formats.add("dict-no-category")
                            need_update = True
                        else:
                            entity_formats.add("dict-complete")
                    else:
                        entity_formats.add(f"unknown-{type(entity)}")
                        need_update = True
            
            if not need_update:
                continue
                
            # Standardize entities
            standardized_entities = []
            for entity in original_entities:
                std_entity = standardize_entity_format(entity)
                if std_entity:
                    standardized_entities.append(std_entity)
            
            if not standardized_entities:
                continue
            
            logger.info(f"Converting entities for '{title}' - Formats: {', '.join(entity_formats)}")
            logger.info(f"Before: {original_entities[:2]}")
            logger.info(f"After: {standardized_entities[:2]}")
            
            if not dry_run:
                # Update the summary with standardized entities
                summaries_collection.update_one(
                    {"_id": summary_id},
                    {"$set": {
                        "entities": standardized_entities,
                        "updated_at": datetime.now().isoformat()
                    }}
                )
                
            updated_count += 1
                
        except Exception as e:
            logger.error(f"Error processing summary '{title}': {e}")
            error_count += 1
    
    logger.info(f"Migration completed: {updated_count} summaries updated, {error_count} errors")
    if dry_run:
        logger.info("DRY RUN: No changes were made to the database")
    return updated_count

def main():
    """Main function to run the migration script."""
    parser = argparse.ArgumentParser(description="Migrate entities to standardized format")
    parser.add_argument("--dry-run", action="store_true", help="Run without making changes")
    parser.add_argument("--local", action="store_true", help="Use local MongoDB (in-memory)")
    args = parser.parse_args()
    
    if args.local:
        logger.info("Using local in-memory MongoDB")
        # Import here to avoid circular imports
        from src.db import setup_mongo_client, setup_collections
        client = setup_mongo_client(local_mode=True)
        db_collections = setup_collections(client)
    else:
        logger.info("Connecting to MongoDB...")
        from src.db import get_collections
        db_collections = get_collections()
    
    logger.info(f"Starting entity format migration (dry_run={args.dry_run})")
    updated_count = migrate_entities(db_collections, dry_run=args.dry_run)
    
    if args.dry_run:
        logger.info(f"Dry run complete. {updated_count} summaries would be updated.")
        if updated_count > 0:
            logger.info("Run without --dry-run to apply changes")
    else:
        logger.info(f"Migration complete. {updated_count} summaries updated.")

if __name__ == "__main__":
    main()
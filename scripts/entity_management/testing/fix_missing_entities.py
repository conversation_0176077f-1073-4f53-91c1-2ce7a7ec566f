#!/usr/bin/env python3
"""
Script to delete summaries with missing entities from summaries collection.

This script:
1. Connects to MongoDB (or uses in-memory version for testing)
2. Finds summaries without entities field or with empty entities
3. Deletes these summaries from the database

Usage:
    python fix_missing_entities.py [--local] [--dry-run] [--limit NUM]
"""

import os
import sys
import argparse
import time
import logging
from typing import List, Dict, Any, Tuple
from dotenv import load_dotenv

# Add the project root to the path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "../.."))

# Import necessary modules
from src.db.mongo import setup_mongo_client, setup_collections

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/entity_fix.log')
    ]
)
logger = logging.getLogger('fix_entities')

def find_missing_entity_summaries(collections, dry_run=False, limit=None) -> List[Dict[str, Any]]:
    """Find all summaries that are missing entities or have empty entities"""
    summaries_collection = collections['summaries']
    
    # Find documents where entities field is missing or empty
    missing_entities_query = {
        "$or": [
            {"entities": {"$exists": False}},
            {"entities": {"$eq": []}}
        ]
    }
    
    # Apply limit if specified
    cursor = summaries_collection.find(missing_entities_query)
    if limit:
        cursor = cursor.limit(limit)
    
    missing_entities_docs = list(cursor)
    logger.info(f"Found {len(missing_entities_docs)} summaries missing entities field or with empty entities")
    
    if not dry_run:
        # Sample a few documents to see what they look like
        sample_size = min(5, len(missing_entities_docs))
        logger.info(f"Sample of {sample_size} documents with missing entities:")
        
        for i, doc in enumerate(missing_entities_docs[:sample_size]):
            logger.info(f"Document {i+1}:")
            logger.info(f"  Title: {doc.get('title', 'No title')}")
            logger.info(f"  URL: {doc.get('abs_url', 'No URL')}")
            logger.info(f"  Tags: {doc.get('tags', [])}")
            logger.info(f"  Has 'entities' field: {'entities' in doc}")
            if 'entities' in doc:
                logger.info(f"  Entities content: {doc['entities']}")
    
    return missing_entities_docs

def create_default_entities(doc: Dict[str, Any]) -> List[List[str]]:
    """
    Create default entities from tags or title for a document.
    Returns entities in the list format: [["entity", "category"]]
    """
    default_entities = []
    
    # First try to use tags if available
    if doc.get('tags') and isinstance(doc['tags'], list):
        for tag in doc['tags']:
            if tag and isinstance(tag, str):
                default_entities.append([tag, "Tags"])
    
    # If no tags, try to extract entities from title
    if not default_entities and doc.get('title'):
        title = doc['title']
        # Extract simplistic entities from title (just words longer than 5 chars)
        words = [w for w in title.split() if len(w) > 5 and w.isalpha()]
        for word in words[:3]:  # Limit to first 3 words
            default_entities.append([word, "Title"])
    
    # If still no entities, add a placeholder
    if not default_entities:
        default_entities.append(["No entity", "Uncategorized"])
    
    return default_entities

def fix_missing_entities(collections, docs: List[Dict[str, Any]], dry_run=False) -> int:
    """
    Delete documents with missing entities.
    Returns the number of deleted documents.
    """
    if dry_run:
        logger.info("DRY RUN - No changes will be made")
        delete_count = 0
        for doc in docs:
            logger.info(f"Would delete {doc.get('abs_url', 'unknown')} due to missing entities")
            delete_count += 1
        return delete_count
    
    # Actually delete the documents
    summaries_collection = collections['summaries']
    start_time = time.time()
    
    # Extract document IDs
    doc_ids = [doc["_id"] for doc in docs]
    
    # Delete all documents in one operation
    result = summaries_collection.delete_many({"_id": {"$in": doc_ids}})
    deleted_count = result.deleted_count
    
    elapsed = time.time() - start_time
    docs_per_second = deleted_count / elapsed if elapsed > 0 else 0
    logger.info(f"Deleted {deleted_count} documents in {elapsed:.2f}s ({docs_per_second:.1f} docs/sec)")
    
    return deleted_count

def verify_fixes(collections) -> Tuple[int, int]:
    """
    Verify that all summaries now have entities field.
    Returns tuple of (total_docs, missing_entities_count)
    """
    summaries_collection = collections['summaries']
    
    # Count total documents
    total_docs = summaries_collection.count_documents({})
    
    # Count documents still missing entities
    missing_entities_query = {
        "$or": [
            {"entities": {"$exists": False}},
            {"entities": {"$eq": []}}
        ]
    }
    missing_entities_count = summaries_collection.count_documents(missing_entities_query)
    
    return total_docs, missing_entities_count

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Fix missing entities in summaries collection")
    parser.add_argument("--local", action="store_true", help="Use in-memory MongoDB for testing")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be updated without making changes")
    parser.add_argument("--limit", type=int, help="Limit the number of documents to process")
    args = parser.parse_args()
    
    # Set local mode environment variable if specified
    if args.local:
        os.environ["LOCAL_MODE"] = "true"
        logger.info("Running in local mode with in-memory MongoDB")
    
    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)
    
    # Setup MongoDB client
    mongo_client = setup_mongo_client(local_mode=args.local)
    collections = setup_collections(mongo_client)
    
    logger.info("===== FIXING MISSING ENTITIES IN SUMMARIES COLLECTION =====")
    
    # Find summaries with missing entities
    missing_entities_docs = find_missing_entity_summaries(collections, args.dry_run, args.limit)
    
    if not missing_entities_docs:
        logger.info("No summaries with missing entities found. Nothing to do.")
        return
    
    # Fix missing entities
    updated_count = fix_missing_entities(collections, missing_entities_docs, args.dry_run)
    logger.info(f"Updated {updated_count} summaries with default entities")
    
    # Verify the fixes
    if not args.dry_run:
        total_docs, still_missing = verify_fixes(collections)
        logger.info(f"Verification: {total_docs} total summaries, {still_missing} still missing entities")
        
        if args.limit and still_missing > 0:
            logger.info(f"Note: Still missing entities may be due to the processing limit ({args.limit})")
        elif still_missing > 0:
            logger.warning(f"WARNING: {still_missing} summaries still have missing entities")
        else:
            logger.info("SUCCESS: All summaries now have entities field")

if __name__ == "__main__":
    # Load environment variables
    load_dotenv()
    
    try:
        # Run the main function
        main()
    except Exception as e:
        logger.exception(f"Unhandled exception: {e}")
        sys.exit(1)
#!/usr/bin/env python3
"""
Test script to debug and fix entity storage issue in summaries collection.
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

# Add the current directory to the path so we can import from src
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "../.."))

# Import necessary modules
from src.db.mongo import setup_mongo_client, setup_collections
from src.services.entity_extraction import perform_ner
from openai import OpenAI

# Mock client for testing
class MockClient:
    def __init__(self):
        self.beta = type('obj', (), {})
        self.beta.chat = type('obj', (), {})
        self.beta.chat.completions = type('obj', (), {
            'parse': lambda **kwargs: type('obj', (), {
                'choices': [
                    type('obj', (), {
                        'message': type('obj', (), {
                            'content': '{"entities": [{"value": "deep learning", "category": {"name": "Methods & Techniques"}}]}'
                        })
                    })
                ]
            })
        })

async def test_entity_storage():
    """Test entity storage in summaries collection"""
    print("\n======== TESTING ENTITY STORAGE ========")
    
    # Setup MongoDB client in local mode
    mongo_client = setup_mongo_client(local_mode=True)
    collections = setup_collections(mongo_client)
    
    # Create mock client
    mock_client = MockClient()
    
    # Test entity extraction
    abstract = "This is a test abstract about deep learning."
    entities = await perform_ner(abstract, mock_client)
    print(f"Extracted entities: {entities}")
    print(f"Entity type: {type(entities)}")
    
    # Clear existing test data
    collections['summaries'].delete_many({"title": "Test Paper for Entity Storage"})
    
    # Insert test document with entities
    summary_doc = {
        'abs_url': 'http://arxiv.org/abs/test_storage',
        'pdf_url': 'http://arxiv.org/pdf/test_storage.pdf',
        'title': 'Test Paper for Entity Storage',
        'authors': ['Test Author'],
        'updated': '2023-04-01',
        'published': '2023-04-01',
        'abstract': abstract,
        'summary': 'This is a test summary.',
        'tags': ['deep learning'],
        'entities': entities
    }
    
    result = collections['summaries'].insert_one(summary_doc)
    print(f"Inserted document ID: {result.inserted_id}")
    
    # Fetch the document to verify
    retrieved_doc = collections['summaries'].find_one({"title": "Test Paper for Entity Storage"})
    print(f"\nRetrieved document:")
    print(f"- Title: {retrieved_doc.get('title')}")
    print(f"- Entities: {retrieved_doc.get('entities')}")
    print(f"- Entity type: {type(retrieved_doc.get('entities'))}")
    
    # Check if entities field exists
    if 'entities' not in retrieved_doc:
        print("\nERROR: 'entities' field is missing from the document!")
    elif not retrieved_doc['entities']:
        print("\nERROR: 'entities' field is empty!")
    
    # Update test document to validate
    update_result = collections['summaries'].update_one(
        {"title": "Test Paper for Entity Storage"},
        {"$set": {"entities": entities}}
    )
    print(f"\nUpdate result: {update_result.modified_count} document(s) modified")
    
    # Fetch the document again to verify
    retrieved_doc = collections['summaries'].find_one({"title": "Test Paper for Entity Storage"})
    print(f"\nRetrieved document after update:")
    print(f"- Title: {retrieved_doc.get('title')}")
    print(f"- Entities: {retrieved_doc.get('entities')}")
    print(f"- Entity type: {type(retrieved_doc.get('entities'))}")
    
    # Test a different entity format
    entity_list_format = [["deep learning", "Methods & Techniques"]]
    update_result = collections['summaries'].update_one(
        {"title": "Test Paper for Entity Storage"},
        {"$set": {"entities": entity_list_format}}
    )
    print(f"\nUpdate with list format: {update_result.modified_count} document(s) modified")
    
    # Fetch the document again to verify
    retrieved_doc = collections['summaries'].find_one({"title": "Test Paper for Entity Storage"})
    print(f"\nRetrieved document with list format:")
    print(f"- Title: {retrieved_doc.get('title')}")
    print(f"- Entities: {retrieved_doc.get('entities')}")
    print(f"- Entity type: {type(retrieved_doc.get('entities'))}")
    
    return entities, retrieved_doc

if __name__ == "__main__":
    # Load environment variables
    load_dotenv()
    
    # Set local mode environment variable
    os.environ['LOCAL_MODE'] = 'true'
    
    # Run the test
    asyncio.run(test_entity_storage())
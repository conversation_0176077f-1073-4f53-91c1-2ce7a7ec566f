#!/usr/bin/env python3
"""
Test script to diagnose and fix entity format issue in summaries collection.
"""

import os
import sys
import json
import asyncio
from dotenv import load_dotenv

# Add the current directory to the path so we can import from src
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "../.."))

# Import necessary modules
from src.db.mongo import setup_mongo_client, setup_collections
from src.services.entity_extraction import perform_ner
from src.models.paper import NEREntity, NERResult, Category
from src.services.entity_resolution import EntityResolutionService

# Mock OpenAI client for testing
class MockOpenAI:
    def __init__(self, response_content):
        self.response_content = response_content
        self.beta = type('obj', (), {})
        self.beta.chat = type('obj', (), {})
        self.beta.chat.completions = type('obj', (), {
            'parse': lambda **kwargs: type('obj', (), {
                'choices': [
                    type('obj', (), {
                        'message': type('obj', (), {
                            'content': self.response_content
                        })
                    })
                ]
            })
        })

async def test_entity_formats():
    """Test different entity formats and their storage in summaries collection"""
    print("\n======== TESTING ENTITY FORMATS ========")
    
    # Setup MongoDB client in local mode
    mongo_client = setup_mongo_client(local_mode=True)
    collections = setup_collections(mongo_client)
    
    # Clear test data
    collections['summaries'].delete_many({"abs_url": {"$regex": "test_format"}})
    
    # Test with tuple format from perform_ner
    print("\n1. TESTING TUPLE FORMAT FROM ENTITY EXTRACTION")
    # Create a mock client with a valid NER response
    mock_client = MockOpenAI('{"entities": [{"value": "deep learning", "category": {"name": "Methods & Techniques"}}, {"value": "neural networks", "category": {"name": "Models & Architectures"}}]}')
    
    # Extract entities
    abstract = "This is a test abstract about deep learning and neural networks."
    entities = await perform_ner(abstract, mock_client)
    print(f"Extracted entities: {entities}")
    print(f"Entity type: {type(entities)}")
    
    # Insert test document with tuple entities
    summary_doc = {
        'abs_url': 'http://arxiv.org/abs/test_format_tuple',
        'title': 'Test Paper for Tuple Entity Format',
        'abstract': abstract,
        'summary': 'Test summary',
        'tags': ['test'],
        'entities': entities
    }
    
    collections['summaries'].insert_one(summary_doc)
    print("Inserted document with tuple format entities")
    
    # Retrieve the document
    doc = collections['summaries'].find_one({"abs_url": "http://arxiv.org/abs/test_format_tuple"})
    print(f"Retrieved entities: {doc.get('entities')}")
    print(f"Entity type: {type(doc.get('entities'))}")
    
    # Test with list format used in entity_resolution_test.py
    print("\n2. TESTING LIST FORMAT FROM TEST SCRIPT")
    summary_doc = {
        'abs_url': 'http://arxiv.org/abs/test_format_list',
        'title': 'Test Paper for List Entity Format',
        'abstract': abstract,
        'summary': 'Test summary',
        'tags': ['test'],
        'entities': [
            ["deep learning", "Methods & Techniques"],
            ["neural networks", "Models & Architectures"]
        ]
    }
    
    collections['summaries'].insert_one(summary_doc)
    print("Inserted document with list format entities")
    
    # Retrieve the document
    doc = collections['summaries'].find_one({"abs_url": "http://arxiv.org/abs/test_format_list"})
    print(f"Retrieved entities: {doc.get('entities')}")
    print(f"Entity type: {type(doc.get('entities'))}")
    
    # Test with dictionary format
    print("\n3. TESTING DICTIONARY FORMAT")
    summary_doc = {
        'abs_url': 'http://arxiv.org/abs/test_format_dict',
        'title': 'Test Paper for Dictionary Entity Format',
        'abstract': abstract,
        'summary': 'Test summary',
        'tags': ['test'],
        'entities': [
            {"value": "deep learning", "category": "Methods & Techniques"},
            {"value": "neural networks", "category": "Models & Architectures"}
        ]
    }
    
    collections['summaries'].insert_one(summary_doc)
    print("Inserted document with dictionary format entities")
    
    # Retrieve the document
    doc = collections['summaries'].find_one({"abs_url": "http://arxiv.org/abs/test_format_dict"})
    print(f"Retrieved entities: {doc.get('entities')}")
    print(f"Entity type: {type(doc.get('entities'))}")
    
    # Test entity resolution with each format
    print("\n======== TESTING ENTITY RESOLUTION WITH DIFFERENT FORMATS ========")
    
    # Initialize entity resolution service
    entity_service = EntityResolutionService(collections, mock_client)
    
    # Use the _extract_unique_entities method to test each format
    print("\nExtracting entities from tuple format:")
    tuple_doc = collections['summaries'].find_one({"abs_url": "http://arxiv.org/abs/test_format_tuple"})
    tuple_entities = await entity_service._extract_unique_entities([tuple_doc])
    print(f"Extracted {len(tuple_entities)} entities: {tuple_entities}")
    
    print("\nExtracting entities from list format:")
    list_doc = collections['summaries'].find_one({"abs_url": "http://arxiv.org/abs/test_format_list"})
    list_entities = await entity_service._extract_unique_entities([list_doc])
    print(f"Extracted {len(list_entities)} entities: {list_entities}")
    
    print("\nExtracting entities from dictionary format:")
    dict_doc = collections['summaries'].find_one({"abs_url": "http://arxiv.org/abs/test_format_dict"})
    dict_entities = await entity_service._extract_unique_entities([dict_doc])
    print(f"Extracted {len(dict_entities)} entities: {dict_entities}")
    
    # Return all test documents for verification
    return {
        "tuple_format": tuple_doc,
        "list_format": list_doc,
        "dict_format": dict_doc
    }

if __name__ == "__main__":
    # Load environment variables
    load_dotenv()
    
    # Set local mode environment variable
    os.environ['LOCAL_MODE'] = 'true'
    
    # Run the test
    asyncio.run(test_entity_formats())
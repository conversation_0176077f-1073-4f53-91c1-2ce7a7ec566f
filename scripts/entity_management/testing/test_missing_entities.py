#!/usr/bin/env python3
"""
Script to test fixing missing entities in summaries collection.
Creates test data with missing entities and then fixes them.
"""

import os
import sys
import argparse
from dotenv import load_dotenv

# Add the current directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "../.."))

# Import necessary modules
from src.db.mongo import setup_mongo_client, setup_collections

# Sample test data
TEST_SUMMARIES = [
    {
        "abs_url": "http://arxiv.org/abs/test_missing1",
        "title": "Test Paper with No Entities Field",
        "summary": "This is a test summary",
        "tags": ["deep learning", "computer vision"]
        # No entities field
    },
    {
        "abs_url": "http://arxiv.org/abs/test_missing2",
        "title": "Test Paper with Empty Entities Array",
        "summary": "This is another test summary",
        "tags": ["reinforcement learning", "robotics"],
        "entities": []  # Empty entities array
    },
    {
        "abs_url": "http://arxiv.org/abs/test_missing3",
        "title": "Test Paper with No Tags and No Entities",
        "summary": "This is a third test summary"
        # No tags, no entities
    },
    {
        "abs_url": "http://arxiv.org/abs/test_control",
        "title": "Control Test Paper with Entities",
        "summary": "This is a control test summary",
        "tags": ["transformers", "language models"],
        "entities": [["transformers", "Models & Architectures"]]
    }
]

def create_default_entities(doc):
    """
    Create default entities from tags or title for a document.
    Returns entities in the list format: [["entity", "category"]]
    """
    default_entities = []
    
    # First try to use tags if available
    if doc.get('tags') and isinstance(doc['tags'], list):
        for tag in doc['tags']:
            if tag and isinstance(tag, str):
                default_entities.append([tag, "Tags"])
    
    # If no tags, try to extract entities from title
    if not default_entities and doc.get('title'):
        title = doc['title']
        # Extract simplistic entities from title (just words longer than 5 chars)
        words = [w for w in title.split() if len(w) > 5 and w.isalpha()]
        for word in words[:3]:  # Limit to first 3 words
            default_entities.append([word, "Title"])
    
    # If still no entities, add a placeholder
    if not default_entities:
        default_entities.append(["No entity", "Uncategorized"])
    
    return default_entities

def test_fix_missing_entities(local_mode=True):
    """
    Test fixing missing entities in summaries:
    1. Create test summaries with missing entities
    2. Find summaries with missing entities
    3. Fix them with default entities
    4. Verify the fixes
    """
    print("\n===== TESTING FIX FOR MISSING ENTITIES =====")
    
    # Setup MongoDB client
    mongo_client = setup_mongo_client(local_mode=local_mode)
    collections = setup_collections(mongo_client)
    summaries_collection = collections['summaries']
    
    # 1. Clear existing test data and add new test summaries
    print("\nStep 1: Creating test data with missing entities")
    deleted = summaries_collection.delete_many({
        "abs_url": {"$regex": "test_missing|test_control"}
    })
    print(f"Cleared {deleted.deleted_count} existing test records")
    
    # Insert test data
    for summary in TEST_SUMMARIES:
        summaries_collection.insert_one(summary)
    
    print(f"Added {len(TEST_SUMMARIES)} test summaries")
    
    # Verify data was inserted
    for i, summary in enumerate(TEST_SUMMARIES):
        doc = summaries_collection.find_one({"abs_url": summary["abs_url"]})
        print(f"\nTest Summary {i+1}:")
        print(f"  URL: {doc.get('abs_url')}")
        print(f"  Title: {doc.get('title')}")
        print(f"  Has 'entities' field: {'entities' in doc}")
        if 'entities' in doc:
            print(f"  Entities content: {doc['entities']}")
    
    # 2. Find summaries with missing entities
    print("\nStep 2: Finding summaries with missing entities")
    missing_entities_query = {
        "$or": [
            {"entities": {"$exists": False}},
            {"entities": {"$eq": []}}
        ]
    }
    
    missing_entities_docs = list(summaries_collection.find(missing_entities_query))
    print(f"Found {len(missing_entities_docs)} summaries missing entities field or with empty entities")
    
    for i, doc in enumerate(missing_entities_docs):
        print(f"\nDocument {i+1} with missing entities:")
        print(f"  Title: {doc.get('title', 'No title')}")
        print(f"  URL: {doc.get('abs_url', 'No URL')}")
        print(f"  Tags: {doc.get('tags', [])}")
        print(f"  Has 'entities' field: {'entities' in doc}")
        if 'entities' in doc:
            print(f"  Entities content: {doc['entities']}")
    
    # 3. Fix missing entities
    print("\nStep 3: Fixing missing entities")
    updated_count = 0
    
    for doc in missing_entities_docs:
        # Create default entities
        default_entities = create_default_entities(doc)
        print(f"Creating default entities for {doc.get('abs_url')}: {default_entities}")
        
        # Update the document
        result = summaries_collection.update_one(
            {"_id": doc["_id"]},
            {"$set": {"entities": default_entities}}
        )
        
        if result.modified_count > 0:
            updated_count += 1
    
    print(f"Updated {updated_count} summaries with default entities")
    
    # 4. Verify the fixes
    print("\nStep 4: Verifying fixes")
    
    # Check all test documents after fixes
    for i, summary in enumerate(TEST_SUMMARIES):
        doc = summaries_collection.find_one({"abs_url": summary["abs_url"]})
        print(f"\nTest Summary {i+1} after fix:")
        print(f"  URL: {doc.get('abs_url')}")
        print(f"  Title: {doc.get('title')}")
        print(f"  Has 'entities' field: {'entities' in doc}")
        if 'entities' in doc:
            print(f"  Entities content: {doc['entities']}")
    
    # Count documents still missing entities
    still_missing = summaries_collection.count_documents(missing_entities_query)
    print(f"\nVerification result: {still_missing} summaries still missing entities")
    
    if still_missing > 0:
        print("WARNING: Not all entities were fixed!")
    else:
        print("SUCCESS: All entities were fixed!")

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Test fixing missing entities in summaries")
    parser.add_argument("--local", action="store_true", help="Use in-memory MongoDB for testing")
    args = parser.parse_args()
    
    # Load environment variables
    load_dotenv()
    
    # Set local mode environment variable if specified
    if args.local:
        os.environ["LOCAL_MODE"] = "true"
        print("Running in local mode with in-memory MongoDB")
    
    # Run the test
    test_fix_missing_entities(local_mode=args.local)
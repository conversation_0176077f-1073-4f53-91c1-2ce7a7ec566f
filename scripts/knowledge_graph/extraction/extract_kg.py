from openai import OpenAI
import json
import os, asyncio, sys
from datetime import datetime
from dotenv import load_dotenv, find_dotenv
from pymongo import MongoClient, ASCENDING
from prompts import kg_prompt
import argparse

load_dotenv(find_dotenv())

# Setup OpenAI client
client = OpenAI(
    api_key=os.getenv("GEMINI_API_KEY"), 
    base_url=os.getenv("GEMINI_BASE_URL")
)

# Connect to MongoDB - this function delegates to src.db.mongo when available
def setup_mongodb(local_mode=False):
    # Try to import from src.db.mongo first (preferred approach)
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        from src.db.mongo import setup_mongo_client, load_json_data_to_mock
        
        print("Using MongoDB client from src.db.mongo")
        mongo_client = setup_mongo_client(local_mode)
        
        # If in local mode, load data from JSON files
        if local_mode:
            load_json_data_to_mock(mongo_client)
        
        return mongo_client
    except (<PERSON>mportError, ModuleNotFoundError) as e:
        print(f"Could not import from src.db.mongo: {e}")
    
    # If that fails, use the fallback implementation
    if local_mode:
        print("Using fallback in-memory MongoDB implementation")
        return create_mock_mongodb()
    else:
        print("Connecting to remote MongoDB...")
        try:
            # Connect to remote MongoDB
            mongo_client = MongoClient(os.getenv("MONGO_PRIVATE_URL"),
                                connectTimeoutMS=30000,
                                serverSelectionTimeoutMS=30000,
                                socketTimeoutMS=60000)
            # Test connection
            mongo_client.admin.command('ping')
            print("Connected to remote MongoDB successfully.")
            return mongo_client
        except Exception as e:
            print(f"Error: Could not connect to remote MongoDB: {e}")
            print("If you want to use in-memory MongoDB for local testing, run with the --local flag.")
            sys.exit(1)

# Create a mock MongoDB client for local testing
def create_mock_mongodb():
    print("Creating in-memory mock MongoDB...")
    
    class MockCollection:
        def __init__(self, name):
            self.name = name
            self.data = []
            self.indexes = []
        
        def insert_one(self, document):
            # Generate a unique ID if not provided
            if '_id' not in document:
                document['_id'] = len(self.data) + 1
            self.data.append(document)
            return type('obj', (object,), {'inserted_id': document['_id']})
        
        def find_one(self, query):
            for doc in self.data:
                match = True
                for key, value in query.items():
                    if key not in doc or doc[key] != value:
                        match = False
                        break
                if match:
                    return doc
            return None
        
        def find(self, query=None, sort=None, limit_value=None, **kwargs):
            query = query or {}
            results = []
            
            # Basic filtering
            for doc in self.data:
                match = True
                for key, value in query.items():
                    if key in doc and doc[key] == value:
                        continue
                    else:
                        match = False
                        break
                if match:
                    results.append(doc)
            
            # Create an object with limit method that returns the results
            class ResultWrapper:
                def __init__(self, results):
                    self.results = results
                
                def limit(self, limit):
                    if limit and len(self.results) > limit:
                        return self.results[:limit]
                    return self.results
                
                def __iter__(self):
                    return iter(self.results)
            
            return ResultWrapper(results)
        
        def count_documents(self, query=None):
            return len(self.find(query))
        
        def create_index(self, key, **kwargs):
            self.indexes.append((key, kwargs))
            return key
        
        def update_one(self, query, update, upsert=False):
            doc = self.find_one(query)
            
            if doc:
                # Apply updates
                if '$set' in update:
                    for key, value in update['$set'].items():
                        doc[key] = value
                return type('obj', (object,), {
                    'modified_count': 1,
                    'matched_count': 1,
                    'upserted_id': None
                })
            elif upsert:
                # Create new document with query fields + update fields
                new_doc = query.copy()
                if '$set' in update:
                    for key, value in update['$set'].items():
                        new_doc[key] = value
                self.insert_one(new_doc)
                return type('obj', (object,), {
                    'modified_count': 0,
                    'matched_count': 0,
                    'upserted_id': new_doc['_id']
                })
            else:
                return type('obj', (object,), {
                    'modified_count': 0,
                    'matched_count': 0,
                    'upserted_id': None
                })
    
    class MockDatabase:
        def __init__(self, name):
            self.name = name
            self.collections = {}
        
        def __getitem__(self, collection_name):
            if collection_name not in self.collections:
                self.collections[collection_name] = MockCollection(collection_name)
            return self.collections[collection_name]

        def list_collection_names(self):
            return list(self.collections.keys())
    
    class MockMongoClient:
        def __init__(self):
            self.databases = {}
            # Create admin database with ping command
            self.admin = type('obj', (object,), {
                'command': lambda cmd: {'ok': 1} if cmd == 'ping' else None
            })
        
        def __getitem__(self, db_name):
            if db_name not in self.databases:
                self.databases[db_name] = MockDatabase(db_name)
            return self.databases[db_name]
    
    mongo_client = MockMongoClient()
    return mongo_client


# Mock KG result for local testing
def get_mock_kg_result(abstract):
    # Generate a simple knowledge graph based on keywords in the abstract
    # This is just for testing the pipeline without calling the API
    nodes = []
    edges = []
    
    # Define some common keywords and their relationships and descriptions
    keyword_info = {
        "language model": {
            "relationships": ["generates", "processes", "understands"],
            "description": "A computational model trained to understand, generate, or manipulate human language. Language models form the basis of many natural language processing applications."
        },
        "transformer": {
            "relationships": ["processes", "encodes", "transforms"],
            "description": "A neural network architecture that uses self-attention mechanisms to process sequential data. Transformers are the foundation of many state-of-the-art language models."
        },
        "gpt": {
            "relationships": ["generates", "pretrained on", "fine-tuned on"],
            "description": "Generative Pre-trained Transformer, a type of large language model developed by OpenAI that uses transformer architecture to generate human-like text."
        },
        "llm": {
            "relationships": ["generates", "trained on", "fine-tuned"],
            "description": "Large Language Model, a type of neural network with billions of parameters trained on vast amounts of text data to generate and understand language."
        },
        "fine-tune": {
            "relationships": ["improves", "adapts", "specializes"],
            "description": "The process of further training a pre-trained model on a specific dataset to adapt it for a particular task or domain."
        },
        "training": {
            "relationships": ["uses", "requires", "produces"],
            "description": "The process of adjusting a model's parameters using data to minimize error and improve performance on specific tasks."
        },
        "rag": {
            "relationships": ["retrieves", "augments", "enhances"],
            "description": "Retrieval-Augmented Generation, a technique that enhances language model outputs by retrieving relevant information from external knowledge sources."
        },
        "retrieval": {
            "relationships": ["finds", "accesses", "provides"],
            "description": "The process of finding and accessing relevant information from a database or knowledge store to support model generation or decision-making."
        },
        "human feedback": {
            "relationships": ["guides", "improves", "evaluates"],
            "description": "Input from humans used to guide model training, evaluate outputs, or provide examples for learning, often crucial for alignment with human values."
        },
        "rlhf": {
            "relationships": ["reinforces", "aligns", "trains"],
            "description": "Reinforcement Learning from Human Feedback, a training methodology that uses human preferences to guide the optimization of language models."
        },
        "alignment": {
            "relationships": ["reduces", "improves", "ensures"],
            "description": "The process of ensuring AI systems act in accordance with human values, preferences, and intentions, reducing harmful outputs."
        },
        "hallucination": {
            "relationships": ["reduces", "causes", "affects"],
            "description": "When AI systems generate information that is factually incorrect or not supported by their training data, presenting fiction as fact."
        },
        "instruction": {
            "relationships": ["guides", "directs", "specifies"],
            "description": "Explicit directions given to a model to perform specific tasks, often used in prompt engineering and instruction-tuning."
        },
        "multimodal": {
            "relationships": ["processes", "combines", "integrates"],
            "description": "Systems that can process and generate multiple types of data (e.g., text, images, audio) simultaneously, enabling more versatile AI applications."
        },
        "benchmark": {
            "relationships": ["evaluates", "measures", "tests"],
            "description": "Standardized tests and datasets used to measure and compare the performance of different AI models on specific tasks."
        },
    }
    
    # Simplify keywords for consistent IDs
    keyword_ids = {}
    for keyword in keyword_info:
        # Create a simplified ID (no spaces, all lowercase)
        key_id = keyword.replace(" ", "").lower()
        keyword_ids[keyword] = key_id
    
    # Check which keywords are in the abstract
    found_keywords = []
    for keyword in keyword_info:
        if keyword.lower() in abstract.lower():
            found_keywords.append(keyword)
    
    # If we have fewer than 3 keywords, add some common ones
    if len(found_keywords) < 3:
        common_keywords = ["language model", "transformer", "training"]
        for keyword in common_keywords:
            if keyword not in found_keywords:
                found_keywords.append(keyword)
            if len(found_keywords) >= 5:
                break
    
    # Create nodes for each keyword
    for keyword in found_keywords:
        node_id = keyword_ids[keyword]
        description = keyword_info[keyword]["description"]
        nodes.append({"id": node_id, "label": keyword.title(), "description": description})
    
    # Create edges between keywords
    for i, source_keyword in enumerate(found_keywords):
        # Create 1-3 edges from each node
        for j in range(min(3, len(found_keywords) - 1)):
            target_index = (i + j + 1) % len(found_keywords)
            target_keyword = found_keywords[target_index]
            
            # Only create edge if source and target are different
            if source_keyword != target_keyword:
                source_id = keyword_ids[source_keyword]
                target_id = keyword_ids[target_keyword]
                
                # Get a relationship from the relationships list
                relationships = keyword_info[source_keyword]["relationships"]
                relationship = relationships[j % len(relationships)]
                
                edges.append({
                    "source": source_id,
                    "target": target_id,
                    "relationship": relationship
                })
    
    # Create the DOT format string
    dot_output = "digraph KnowledgeGraph {\n"
    dot_output += "rankdir=LR;\n"
    dot_output += "node [shape=box, style=filled, color=lightblue, fontname=\"Arial\"];\n\n"
    
    # Add nodes to DOT
    dot_output += "// Nodes\n"
    for node in nodes:
        dot_output += f"{node['id']} [label=\"{node['label']}\"];\n"
    
    dot_output += "\n// Edges\n"
    for edge in edges:
        dot_output += f"{edge['source']} -> {edge['target']} [label=\"{edge['relationship']}\"];\n"
    
    dot_output += "}\n"
    
    return {
        "raw_dot": dot_output,
        "nodes": nodes,
        "edges": edges
    }

# Extract KG from abstract using LLM
async def generate_entity_descriptions(nodes, abstract):
    """Generate descriptions for each entity node using the LLM.
    
    Args:
        nodes: List of node objects with 'id' and 'label' fields
        abstract: The paper abstract for context
    
    Returns:
        Dictionary mapping node IDs to descriptions
    """
    if not nodes:
        return {}
    
    try:
        # Create a concise list of entity names to describe
        entity_names = [f"{node['id']}: {node['label']}" for node in nodes]
        entities_text = "\n".join(entity_names)
        
        prompt = f"""Based on the following paper abstract, generate a short, informative description for each entity listed below.
Each description should be 1-2 sentences explaining what the entity is and its significance in the context of AI/ML research.

Paper abstract:
{abstract}

Entities to describe:
{entities_text}

Provide your response as a JSON object with entity IDs as keys and descriptions as values.
Example format:
{{
  "entity1": "Description of entity1...",
  "entity2": "Description of entity2..."
}}
"""
        
        # Call the LLM to generate descriptions
        chat_completion = client.chat.completions.create(
            messages=[{
                "role": "user",
                "content": prompt
            }],
            model=os.getenv("GEMINI_MODEL"),
            temperature=0.2
        )
        
        response = chat_completion.choices[0].message.content.strip()
        
        # Parse the JSON response
        try:
            # Extract JSON if it's wrapped in markdown code blocks
            if "```json" in response:
                json_str = response.split("```json")[1].split("```")[0].strip()
            elif "```" in response:
                json_str = response.split("```")[1].strip()
            else:
                json_str = response
                
            # Parse the JSON
            descriptions = json.loads(json_str)
            return descriptions
        except (json.JSONDecodeError, IndexError) as e:
            print(f"Error parsing entity descriptions: {e}")
            # Return empty descriptions if parsing fails
            return {node['id']: f"Entity related to {node['label']}" for node in nodes}
    except Exception as e:
        print(f"Error generating entity descriptions: {e}")
        return {node['id']: f"Entity related to {node['label']}" for node in nodes}

async def extract_kg(abstract, local_mode=False):
    # Use mock implementation for local testing
    if local_mode:
        print("  Using mock KG implementation for local testing")
        return get_mock_kg_result(abstract)
        
    # Real implementation using the API
    try:
        chat_completion = client.chat.completions.create(
            messages=[{
                "role": "user",
                "content": kg_prompt(abstract)
            }],
            model=os.getenv("GEMINI_MODEL"),
            temperature=0.1
        )

        kg_result = chat_completion.choices[0].message.content.strip()
        
        # Parse the graph data from the DOT format
        # We need to extract nodes and edges
        nodes = []
        edges = []
        
        # Basic parsing of the DOT format
        if "digraph" in kg_result and "{" in kg_result:
            # Extract content between curly braces
            content = kg_result.split("{", 1)[1].rsplit("}", 1)[0].strip()
            
            # Process each line
            for line in content.split("\n"):
                line = line.strip()
                
                # Skip empty lines and comments
                if not line or line.startswith("//") or line.startswith("rankdir") or line.startswith("node"):
                    continue
                
                # Check if it's a node definition
                if "->" not in line and "[" in line and "]" in line:
                    # Extract node ID and label
                    node_id = line.split("[", 1)[0].strip()
                    label_part = line.split("[", 1)[1].rsplit("]", 1)[0]
                    
                    # Extract the label value
                    if "label=" in label_part:
                        label = label_part.split("label=", 1)[1].strip()
                        # Remove quotes if present
                        if label.startswith('"') and label.endswith('"'):
                            label = label[1:-1]
                        nodes.append({"id": node_id, "label": label})
                
                # Check if it's an edge definition
                elif "->" in line:
                    parts = line.split("->")
                    source = parts[0].strip()
                    
                    # Extract target and relationship
                    target_part = parts[1]
                    if "[" in target_part:
                        target = target_part.split("[", 1)[0].strip()
                        rel_part = target_part.split("[", 1)[1].rsplit("]", 1)[0]
                        
                        # Extract the relationship label
                        if "label=" in rel_part:
                            relationship = rel_part.split("label=", 1)[1].strip()
                            # Remove quotes if present
                            if relationship.startswith('"') and relationship.endswith('"'):
                                relationship = relationship[1:-1]
                            
                            edges.append({
                                "source": source,
                                "target": target,
                                "relationship": relationship
                            })
        
        # Return structured data
        return {
            "raw_dot": kg_result,
            "nodes": nodes,
            "edges": edges
        }
    except Exception as e:
        print(f"Error extracting KG: {e}")
        return None

# Function to create sample data for testing
def create_sample_data(mongo_client):
    """Create sample data for testing in local mode"""
    mongodb = mongo_client['papers']
    summaries_collection = mongodb['summaries']
    
    # Check if we already have data
    if summaries_collection is not None and summaries_collection.count_documents({}) > 0:
        print("Sample data already exists, skipping creation.")
        return
    
    print("Creating sample data for testing...")
    
    # Sample abstracts from AI papers
    sample_data = [
        {
            'abs_url': 'https://arxiv.org/abs/2305.10435',
            'title': 'Retrieval-Augmented Generation for Knowledge-Intensive NLP Tasks',
            'abstract': """Large parametric models do not have access to the latest knowledge and are prone to hallucination. To mitigate these problems, retrieval-augmented generation (RAG) systems complement these models with a non-parametric knowledge retrieval system. Despite their effectiveness, RAG systems can still generate inconsistent and non-factual outputs. We identify seven reasons for this unreliability and survey the related literature to provide a comprehensive and structured review of challenges in RAG."""
        },
        {
            'abs_url': 'https://arxiv.org/abs/2303.08774',
            'title': 'GPT-4 Technical Report',
            'abstract': """We report the development of GPT-4, a large-scale, multimodal model which can accept image and text inputs and produce text outputs. While less capable than humans in many real-world scenarios, GPT-4 exhibits human-level performance on various professional and academic benchmarks, including passing a simulated bar exam with a score around the top 10% of test takers. GPT-4 is a Transformer-based model pre-trained to predict the next token in a document. The post-training alignment process results in improved factuality and adherence to desired behavior."""
        },
        {
            'abs_url': 'https://arxiv.org/abs/2204.02311',
            'title': 'Training language models to follow instructions with human feedback',
            'abstract': """We fine-tune language models on a dataset of human-annotated demonstrations to learn to follow natural language instructions. We call resulting models InstructGPT. We find that InstructGPT models are better at following instructions, less likely to make up facts, and generate outputs that are more aligned with human preferences. We train these models using techniques including reinforcement learning from human feedback (RLHF), constitutional AI, and iterative training procedures."""
        }
    ]
    
    # Insert the sample data
    for paper in sample_data:
        summaries_collection.insert_one(paper)
    
    print(f"Created {len(sample_data)} sample papers.")

async def process_papers(local_mode=False, limit=50, reprocess=False):
    # Setup MongoDB
    mongo_client = setup_mongodb(local_mode)
    print(f"MongoDB client type: {type(mongo_client)}")
    mongodb = mongo_client['papers']
    print(f"MongoDB database type: {type(mongodb)}")
    summaries_collection = mongodb['summaries']
    
    # Always create the knowledge_graphs collection in local mode
    print("Creating knowledge_graphs collection...")    
    knowledge_graphs_collection = mongodb['knowledge_graphs']
    
    # Create an index on abs_url if it doesn't exist
    knowledge_graphs_collection.create_index('abs_url', unique=True)
    
    # Get papers that don't have KG data yet
    query = {}
    if not reprocess:
        # Get papers that don't have corresponding entries in the knowledge_graphs collection
        # Exclude documents without abs_url field (like "main_graph")
        papers_with_kg = set(doc['abs_url'] for doc in knowledge_graphs_collection.find(
            {"abs_url": {"$exists": True}}, {'abs_url': 1}
        ))
        if papers_with_kg:
            query = {'abs_url': {'$nin': list(papers_with_kg)}}
    
    # Find papers to process
    try:
        # First try the standard MongoDB way
        results = summaries_collection.find(query, {'abs_url': 1, 'abstract': 1, 'title': 1})
        if hasattr(results, 'limit'):
            results = results.limit(limit)
        papers = list(results)
    except Exception as e:
        print(f"Error finding papers: {e}")
        # Fallback to direct data access for mocked collections
        if hasattr(summaries_collection, 'data'):
            papers = summaries_collection.data[:limit]
        else:
            papers = []
    
    print(f"Found {len(papers)} papers to process")
    
    # Process each paper
    for i, paper in enumerate(papers):
        abs_url = paper['abs_url']
        abstract = paper.get('abstract', '')
        title = paper.get('title', 'Untitled')
        
        if not abstract:
            print(f"Skipping paper with no abstract: {abs_url}")
            continue
        
        print(f"Processing paper {i+1}/{len(papers)}: {title}")
        
        # Extract knowledge graph
        kg_data = await extract_kg(abstract, local_mode)
        
        # For local mode, we need to handle the description field from the mock implementation
        mock_descriptions = {}
        if local_mode and kg_data:
            # In mock implementation, description is directly in the node object
            # We need to move it to the right place for consistency with the real implementation
            for node in kg_data['nodes']:
                if 'description' in node:
                    # Store the description so we can use it later
                    mock_descriptions[node['id']] = node.pop('description')
        
        if kg_data:
            # Create Neo4j-compatible format for nodes and relationships
            # Generate descriptions for each entity node
            if local_mode and mock_descriptions:
                # Use the mock descriptions we saved earlier
                entity_descriptions = mock_descriptions
                print("  Using mock descriptions for local testing")
            else:
                # Generate real descriptions using the LLM
                entity_descriptions = await generate_entity_descriptions(kg_data['nodes'], abstract)
            
            nodes = []
            for node in kg_data['nodes']:
                node_id = node['id']
                description = entity_descriptions.get(node_id, f"Entity related to {node['label']}")
                
                nodes.append({
                    "id": node_id,
                    "labels": ["Entity"],
                    "properties": {
                        "name": node['label'],
                        "description": description
                    }
                })
            
            relationships = []
            for edge in kg_data['edges']:
                # Ensure relationship type is not empty
                rel_type = edge['relationship']
                if not rel_type:
                    rel_type = "RELATES_TO"  # Default relationship type when empty
                    print(f"  Warning: Empty relationship type found between {edge['source']} and {edge['target']}, using default 'RELATES_TO'")
                
                relationships.append({
                    "start": {"id": edge['source']},
                    "end": {"id": edge['target']},
                    "type": rel_type,
                    "properties": {}
                })
            
            # Save to MongoDB
            kg_doc = {
                'abs_url': abs_url,
                'title': title,
                'raw_dot': kg_data['raw_dot'],
                'nodes': nodes,
                'relationships': relationships,
                'processed_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # Insert or update
            knowledge_graphs_collection.update_one(
                {'abs_url': abs_url},
                {'$set': kg_doc},
                upsert=True
            )
            
            print(f"  ✓ Saved KG with {len(nodes)} nodes and {len(relationships)} relationships")
        else:
            print(f"  ✗ Failed to extract KG")
        
        # Small delay to avoid rate limiting
        await asyncio.sleep(1)
    
    # Report statistics
    total_papers = summaries_collection.count_documents({})
    papers_with_kg = knowledge_graphs_collection.count_documents({})
    
    print(f"\nProcessing complete.")
    print(f"Total papers in database: {total_papers}")
    print(f"Papers with knowledge graphs: {papers_with_kg} ({papers_with_kg/total_papers*100:.1f}%)")

# Add function to export data for Neo4j import
def export_for_neo4j(output_dir="neo4j_import", local_mode=False):
    """
    Export the knowledge graph data to CSV files for Neo4j import.
    
    This creates:
    - nodes.csv: All nodes with their properties
    - relationships.csv: All relationships between nodes
    
    These files can be imported into Neo4j using the neo4j-admin import tool
    or via the LOAD CSV Cypher command.
    """
    mongo_client = setup_mongodb(local_mode)
    mongodb = mongo_client['papers']
    
    # Check if collection exists
    if 'knowledge_graphs' not in mongodb.list_collection_names():
        print("No knowledge_graphs collection found. Run the script first to generate data.")
        return
    
    knowledge_graphs_collection = mongodb['knowledge_graphs']
    
    # Create output directory if it doesn't exist
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Create CSV files for nodes and relationships
    nodes_file = os.path.join(output_dir, "nodes.csv")
    rels_file = os.path.join(output_dir, "relationships.csv")
    
    # Track all nodes to avoid duplicates (using ID as key)
    all_nodes = {}
    all_relationships = []
    
    # Process knowledge graphs
    graphs = list(knowledge_graphs_collection.find({}))
    if not graphs:
        print("No knowledge graphs found in the collection.")
        return
    print(f"Processing {len(graphs)} knowledge graphs for export...")
    
    # First pass: collect all nodes and relationships
    for graph in graphs:
        paper_id = graph['abs_url'].split('/')[-1]  # Use paper ID for reference
        
        # Process nodes
        for node in graph.get('nodes', []):
            node_id = node['id']
            description = node['properties'].get('description', f"Entity related to {node['properties']['name']}")
            
            if node_id not in all_nodes:
                all_nodes[node_id] = {
                    "id": node_id,
                    "name": node['properties']['name'],
                    "description": description,
                    "papers": [paper_id]
                }
            else:
                # Keep the existing description, but add the paper to the list
                if paper_id not in all_nodes[node_id]['papers']:
                    all_nodes[node_id]['papers'].append(paper_id)
        
        # Process relationships
        for rel in graph.get('relationships', []):
            source_id = rel['start']['id']
            target_id = rel['end']['id']
            rel_type = rel['type']
            
            # Create a unique ID for the relationship
            rel_id = f"{source_id}_{rel_type}_{target_id}_{paper_id}"
            
            all_relationships.append({
                "id": rel_id,
                "source_id": source_id,
                "target_id": target_id,
                "type": rel_type,
                "paper_id": paper_id
            })
    
    # Write nodes to CSV
    with open(nodes_file, 'w') as f:
        # Write header
        f.write("id:ID,name,description,papers:string[]\n")
        
        # Write node data
        for node_id, node in all_nodes.items():
            papers_str = ';'.join(node['papers'])
            # Escape commas in the description to avoid CSV formatting issues
            description = node.get('description', '').replace(',', ' ').replace('\n', ' ').replace('"', '')
            f.write(f"{node_id},{node['name'].replace(',', ' ')},\"{description}\",{papers_str}\n")
    
    # Write relationships to CSV
    with open(rels_file, 'w') as f:
        # Write header
        f.write(":ID,source:START_ID,target:END_ID,type,paper_id\n")
        
        # Write relationship data
        for rel in all_relationships:
            f.write(f"{rel['id']},{rel['source_id']},{rel['target_id']},{rel['type'].replace(',', ' ')},{rel['paper_id']}\n")
    
    print(f"Export complete. Files created:")
    print(f"- {nodes_file}: {len(all_nodes)} nodes")
    print(f"- {rels_file}: {len(all_relationships)} relationships")
    print("\nTo import into Neo4j, you can use:")
    print("1. LOAD CSV in Cypher:")
    print("   LOAD CSV WITH HEADERS FROM 'file:///nodes.csv' AS row")
    print("   CREATE (n:Entity {id: row.id, name: row.name, papers: split(row.papers, ';')})")
    print("2. Neo4j Admin Import tool for larger datasets")

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Extract knowledge graphs from paper abstracts')
    parser.add_argument('--local', action='store_true', help='Use in-memory MongoDB for local testing')
    parser.add_argument('--limit', type=int, default=50, help='Maximum number of papers to process')
    parser.add_argument('--reprocess', action='store_true', help='Reprocess papers that already have KG data')
    parser.add_argument('--export', action='store_true', help='Export knowledge graphs to Neo4j-compatible CSV files')
    parser.add_argument('--output-dir', type=str, default='neo4j_import', help='Directory for Neo4j export files')
    
    # Print the help text when called with -h
    args = parser.parse_args()
    
    print(f"Parsed arguments: {args}")
    
    if args.export:
        print("Exporting data for Neo4j...")
        export_for_neo4j(args.output_dir, args.local)
    else:
        print("Running main processing function...")
        asyncio.run(process_papers(args.local, args.limit, args.reprocess))
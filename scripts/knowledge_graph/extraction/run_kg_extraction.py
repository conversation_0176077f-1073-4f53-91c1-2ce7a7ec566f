import asyncio
import argparse
import os
from extract_kg import process_papers, export_for_neo4j

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Extract knowledge graphs from paper abstracts')
    parser.add_argument('--local', action='store_true', help='Use in-memory MongoDB for local testing')
    parser.add_argument('--limit', type=int, default=50, help='Maximum number of papers to process')
    parser.add_argument('--reprocess', action='store_true', help='Reprocess papers that already have KG data')
    parser.add_argument('--export', action='store_true', help='Export knowledge graphs to Neo4j-compatible CSV files')
    parser.add_argument('--output-dir', type=str, default='neo4j_import', help='Directory for Neo4j export files')
    args = parser.parse_args()
    
    # Set local mode environment variable if needed
    if args.local:
        os.environ['LOCAL_MODE'] = 'true'
        # Unset MONGO_PRIVATE_URL to prevent remote connection attempts
        os.environ["MONGO_PRIVATE_URL"] = ""
        print("Running in local mode, unsetting MONGO_PRIVATE_URL to prevent remote connections")
    
    if args.export:
        # Export data for Neo4j
        export_for_neo4j(args.output_dir, args.local)
    else:
        # Run the main processing function
        asyncio.run(process_papers(args.local, args.limit, args.reprocess))

if __name__ == "__main__":
    main()
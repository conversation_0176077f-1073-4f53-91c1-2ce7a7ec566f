import os
import sys
from typing import Dict, List, Any
import json
from datetime import datetime
import argparse

# Import Neo4j driver conditionally
try:
    from neo4j import GraphDatabase
    NEO4J_DRIVER_AVAILABLE = True
except ImportError:
    NEO4J_DRIVER_AVAILABLE = False
    print("Neo4j driver not installed. Install with 'pip install neo4j'")

# Import MongoDB connection code
from src.db.mongo import setup_mongo_client, setup_collections

# Import Neo4j utilities
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
from src.db.neo4j import safe_neo4j_operation, NEO4J_AVAILABLE


def connect_to_mongodb(local_mode: bool = False):
    """
    Connect to MongoDB and return the knowledge graphs collection.

    Args:
        local_mode (bool): Whether to use local/mock MongoDB

    Returns:
        The knowledge_graphs collection from MongoDB
    """
    # Setup MongoDB client
    mongo_client = setup_mongo_client(local_mode)

    # Setup collections
    collections = setup_collections(mongo_client)

    # Return the knowledge_graphs collection
    return collections['knowledge_graphs']


# Global variable to store paper IDs to filter
papers_to_filter = set()

def retrieve_all_knowledge_graphs(kg_collection, limit: int = None, skip_existing: bool = False, neo4j_driver = None):
    """
    Retrieve knowledge graphs from MongoDB, with options to skip ones already in Neo4j.

    Args:
        kg_collection: MongoDB collection for knowledge graphs
        limit (int, optional): Maximum number of knowledge graphs to retrieve
        skip_existing (bool): If True, check Neo4j and only return graphs not already imported
        neo4j_driver: Neo4j driver connection (required if skip_existing is True)

    Returns:
        List of knowledge graph documents
    """
    global papers_to_filter
    papers_to_filter = set()  # Reset global variable

    try:
        # Always only retrieve documents that have an abs_url field
        query = {"abs_url": {"$exists": True}}

        # If we should skip existing papers in Neo4j
        if skip_existing and neo4j_driver:
            try:
                # Get list of paper IDs already in Neo4j
                with neo4j_driver.session() as session:
                    result = session.run("MATCH (p:Paper) RETURN p.id AS paper_id")
                    existing_paper_ids = set(record["paper_id"] for record in result if record["paper_id"])

                    if existing_paper_ids:
                        print(f"Found {len(existing_paper_ids)} papers already in Neo4j, will skip these")

                        # Store paper IDs for filtering after db query
                        for paper_id in existing_paper_ids:
                            if paper_id:  # Skip empty IDs
                                # Just store the ID portion (without path)
                                if '/' in paper_id:
                                    papers_to_filter.add(paper_id.split('/')[-1])
                                else:
                                    papers_to_filter.add(paper_id)

                        print(f"Will filter out {len(papers_to_filter)} paper IDs")
            except Exception as e:
                print(f"Error getting paper IDs from Neo4j: {e}")
                # Continue with default query (get all with abs_url)

        # Use limit if provided
        # Set a batch size to handle potentially large result sets
        cursor = kg_collection.find(query, batch_size=100)
        if limit:
            cursor = cursor.limit(limit)

        # Process the cursor in batches to avoid loading everything into memory at once
        documents = []
        batch_size = 100
        count = 0
        filtered_count = 0

        # Explicitly disable cursor timeout if possible
        if hasattr(cursor, 'comment'):
            cursor = cursor.comment('no_cursor_timeout')

        # Process in batches instead of a single list operation
        for doc in cursor:
            # Filter out documents whose paper ID is in our skip list
            if 'abs_url' in doc:
                paper_id = doc['abs_url'].split('/')[-1]
                if paper_id in papers_to_filter:
                    filtered_count += 1
                    continue  # Skip this document

            # Add document to our result list
            documents.append(doc)
            count += 1

            if limit and count >= limit:
                break

            # Log progress for large result sets
            if (count + filtered_count) % batch_size == 0:
                print(f"Processed {count + filtered_count} documents so far (kept {count}, filtered {filtered_count})...")
        return documents
    except Exception as e:
        print(f"Error retrieving knowledge graphs from MongoDB: {e}")
        print(f"Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()

        # Additional error handling for specific MongoDB errors
        if "pattern string is longer than the limit" in str(e):
            print("\nERROR: MongoDB regex pattern length limit exceeded.")
            print("The updated script should fix this by filtering in Python instead of MongoDB queries.")
            print("Please restart the script to use the new implementation.")

        return []


def import_to_neo4j(kg_documents, uri='bolt://localhost:7687', user='neo4j', password='password'):
    """
    Import knowledge graphs to Neo4j.

    Args:
        kg_documents: List of knowledge graph documents from MongoDB
        uri: Neo4j URI
        user: Neo4j username
        password: Neo4j password
    """
    # Check if Neo4j is available
    if not NEO4J_DRIVER_AVAILABLE:
        print("Neo4j driver not installed. Skipping Neo4j import.")
        return

    if not NEO4J_AVAILABLE:
        print("Neo4j is not available. Skipping Neo4j import.")
        return

    # Connect to Neo4j
    try:
        driver = GraphDatabase.driver(uri, auth=(user, password), connection_timeout=5)
    except Exception as e:
        print(f"Error connecting to Neo4j: {e}")
        print("Skipping Neo4j import.")
        return

    try:
        # Create a constraint for the node ID property if it doesn't exist
        with driver.session() as session:
            try:
                session.run("CREATE CONSTRAINT entity_id_constraint IF NOT EXISTS FOR (n:Entity) REQUIRE n.id IS UNIQUE")
            except Exception as e:
                print(f"Warning: Could not create constraint: {e}")

        # Skip documents with missing abs_url
        kg_documents = [kg for kg in kg_documents if 'abs_url' in kg]
        if len(kg_documents) == 0:
            print("No valid knowledge graphs to import - all missing abs_url field")
            return

        # Track processed nodes to avoid duplicates
        processed_nodes = set()

        # Process each knowledge graph
        for i, kg in enumerate(kg_documents):
            if i % 10 == 0:
                print(f"Processed {i}/{len(kg_documents)} knowledge graphs...")

            # Get abs_url or use a fallback value if not present
            if 'abs_url' not in kg:
                print(f"Warning: Knowledge graph missing abs_url field: {kg.get('_id', 'unknown id')}")
                # Skip this knowledge graph if there's no abs_url
                continue

            abs_url = kg['abs_url']
            title = kg.get('title', 'Untitled')
            paper_id = abs_url.split('/')[-1]  # Extract paper ID from URL

            with driver.session() as session:
                # Create a Paper node for this document
                try:
                    query = """
                    MERGE (p:Paper {id: $paper_id})
                    SET p.title = $title,
                        p.url = $abs_url
                    RETURN p
                    """
                    session.run(query, paper_id=paper_id, title=title, abs_url=abs_url)
                except Exception as e:
                    print(f"Error creating Paper node: {e}")

                # Process nodes
                for node in kg.get('nodes', []):
                    node_id = node['id']

                    # Skip if we've already processed this node
                    if node_id in processed_nodes:
                        continue

                    # Get node properties
                    labels = node.get('labels', ['Entity'])
                    name = node.get('properties', {}).get('name', node_id)
                    description = node.get('properties', {}).get('description', '')

                    try:
                        # Generate Cypher for this node with the appropriate labels
                        node_labels = ':'.join(labels)
                        if not node_labels:
                            node_labels = 'Entity'

                        query = f"""
                        MERGE (n:{node_labels} {{id: $node_id}})
                        ON CREATE SET n.name = $name,
                                      n.description = $description
                        RETURN n
                        """
                        session.run(query, node_id=node_id, name=name, description=description)

                        # Create a relationship between the paper and this entity
                        rel_query = """
                        MATCH (p:Paper {id: $paper_id})
                        MATCH (n {id: $node_id})
                        MERGE (p)-[:MENTIONS]->(n)
                        """
                        session.run(rel_query, paper_id=paper_id, node_id=node_id)

                        # Mark this node as processed
                        processed_nodes.add(node_id)
                    except Exception as e:
                        print(f"Error creating node {node_id}: {e}")

                # Process relationships
                for rel in kg.get('relationships', []):
                    source_id = rel['start']['id']
                    target_id = rel['end']['id']
                    # The KG model uses 'label' for relationship type, not 'type'
                    rel_type = rel.get('label', rel.get('type', '')).upper().replace(' ', '_')

                    # Ensure relationship type is not empty
                    if not rel_type:
                        rel_type = "RELATES_TO"  # Default relationship type when empty
                        print(f"Warning: Empty relationship type found between {source_id}->{target_id}, using default 'RELATES_TO'")

                    try:
                        # Generate Cypher for this relationship
                        query = """
                        MATCH (source {id: $source_id})
                        MATCH (target {id: $target_id})
                        MERGE (source)-[r:`{rel_type}`]->(target)
                        RETURN r
                        """.replace("{rel_type}", rel_type)
                        session.run(query, source_id=source_id, target_id=target_id)
                    except Exception as e:
                        print(f"Error creating relationship {source_id}-[{rel_type}]->{target_id}: {e}")

        print(f"Processed all {len(kg_documents)} knowledge graphs")

    finally:
        driver.close()


def main():
    parser = argparse.ArgumentParser(description='Import MongoDB knowledge graphs to Neo4j')
    parser.add_argument('--local', action='store_true', help='Use local MongoDB')
    parser.add_argument('--limit', type=int, default=None, help='Limit number of knowledge graphs')
    parser.add_argument('--uri', type=str, default='bolt://localhost:7687', help='Neo4j URI')
    parser.add_argument('--user', type=str, default='neo4j', help='Neo4j username')
    parser.add_argument('--password', type=str, default='password', help='Neo4j password')
    parser.add_argument('--skip-existing', action='store_true', help='Skip papers already in Neo4j')
    parser.add_argument('--force-reimport', action='store_true', help='Force reimport of all papers')

    args = parser.parse_args()

    # Connect to Neo4j if needed
    neo4j_driver = None
    if args.skip_existing and not args.force_reimport:
        print(f"Connecting to Neo4j at {args.uri}...")
        neo4j_driver = GraphDatabase.driver(args.uri, auth=(args.user, args.password))

    print("Connecting to MongoDB...")
    kg_collection = connect_to_mongodb(args.local)

    # If force reimport and not skip existing, import all papers
    if args.force_reimport and not args.skip_existing:
        print("Retrieving all knowledge graphs for reimport...")
        kg_documents = retrieve_all_knowledge_graphs(kg_collection, args.limit)
    else:
        # Default behavior: Only import papers not already in Neo4j
        print("Retrieving only new knowledge graphs...")
        kg_documents = retrieve_all_knowledge_graphs(kg_collection, args.limit,
                                                    skip_existing=True, neo4j_driver=neo4j_driver)

    print(f"Retrieved {len(kg_documents)} knowledge graphs")

    if len(kg_documents) > 0:
        print(f"Importing to Neo4j at {args.uri}...")
        import_to_neo4j(kg_documents, args.uri, args.user, args.password)
        print("Import complete!")
    else:
        print("No new knowledge graphs to import")

    if neo4j_driver:
        neo4j_driver.close()


if __name__ == "__main__":
    main()
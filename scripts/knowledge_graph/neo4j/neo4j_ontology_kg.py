from neo4j import GraphDatabase
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger()

class LLMOntologyGraphBuilder:
    def __init__(self, uri, username, password):
        """Initialize connection to Neo4j"""
        self.driver = GraphDatabase.driver(uri, auth=(username, password))
        logger.info("Connected to Neo4j database")

    def close(self):
        """Close the Neo4j connection"""
        self.driver.close()
        logger.info("Connection to Neo4j closed")

    def run_query(self, query, params=None):
        """Execute a Cypher query"""
        with self.driver.session() as session:
            try:
                result = session.run(query, params or {})
                return result
            except Exception as e:
                logger.error(f"Query failed: {e}")
                raise

    def clear_database(self):
        """Clear all data from the database"""
        logger.info("Clearing database...")
        self.run_query("MATCH (n) DETACH DELETE n")
        logger.info("Database cleared")

    def create_constraints(self):
        """Create constraints for unique identifiers"""
        logger.info("Creating constraints...")
        
        # Create constraints for primary entities
        constraints = [
            "CREATE CONSTRAINT paper_doi_constraint IF NOT EXISTS FOR (p:ResearchPaper) REQUIRE p.doi IS UNIQUE",
            "CREATE CONSTRAINT author_email_constraint IF NOT EXISTS FOR (a:Author) REQUIRE a.email IS UNIQUE",
            "CREATE CONSTRAINT institution_name_constraint IF NOT EXISTS FOR (i:Institution) REQUIRE i.name IS UNIQUE",
            "CREATE CONSTRAINT venue_name_constraint IF NOT EXISTS FOR (v:PublicationVenue) REQUIRE v.name IS UNIQUE",
            "CREATE CONSTRAINT method_name_constraint IF NOT EXISTS FOR (m:Method) REQUIRE m.name IS UNIQUE",
            "CREATE CONSTRAINT dataset_name_constraint IF NOT EXISTS FOR (d:Dataset) REQUIRE d.name IS UNIQUE",
            "CREATE CONSTRAINT benchmark_name_constraint IF NOT EXISTS FOR (b:Benchmark) REQUIRE b.name IS UNIQUE",
            "CREATE CONSTRAINT evalmetric_name_constraint IF NOT EXISTS FOR (e:EvaluationMetric) REQUIRE e.name IS UNIQUE",
            "CREATE CONSTRAINT modelarch_name_constraint IF NOT EXISTS FOR (ma:ModelArchitecture) REQUIRE ma.name IS UNIQUE"
        ]
        
        for constraint in constraints:
            self.run_query(constraint)
        
        logger.info("Constraints created")

    def create_class_hierarchy(self):
        """Create class hierarchy based on the ontology"""
        logger.info("Creating class hierarchy...")
        
        # Define class hierarchy relationships
        class_hierarchy = [
            # Paper Section classes
            "MERGE (paper:Class {name: 'ResearchPaper'}) "
            "MERGE (section:Class {name: 'PaperSection'}) "
            "MERGE (abstract:Class {name: 'Abstract'}) "
            "MERGE (intro:Class {name: 'Introduction'}) "
            "MERGE (method:Class {name: 'Methodology'}) "
            "MERGE (results:Class {name: 'Results'}) "
            "MERGE (discussion:Class {name: 'Discussion'}) "
            "MERGE (conclusion:Class {name: 'Conclusion'}) "
            "MERGE (abstract)-[:SUBCLASS_OF]->(section) "
            "MERGE (intro)-[:SUBCLASS_OF]->(section) "
            "MERGE (method)-[:SUBCLASS_OF]->(section) "
            "MERGE (results)-[:SUBCLASS_OF]->(section) "
            "MERGE (discussion)-[:SUBCLASS_OF]->(section) "
            "MERGE (conclusion)-[:SUBCLASS_OF]->(section) ",
            
            # Method classes
            "MERGE (method:Class {name: 'Method'}) "
            "MERGE (algorithm:Class {name: 'Algorithm'}) "
            "MERGE (algorithm)-[:SUBCLASS_OF]->(method) ",
            
            # Create the rest of the class hierarchy
            # ... additional class hierarchy statements would be added here
        ]
        
        for query in class_hierarchy:
            self.run_query(query)
        
        logger.info("Class hierarchy created")

    def create_ontology_schema(self):
        """Create the full ontology schema in Neo4j"""
        logger.info("Creating ontology schema...")
        
        # Create node labels for all classes in the ontology
        classes_query = """
        CREATE 
        // Paper Structure Classes
        (:Class:OntologyClass {name: 'ResearchPaper', comment: 'Represents a research paper on large language models'}),
        (:Class:OntologyClass {name: 'PaperSection', comment: 'Represents a section within a research paper'}),
        (:Class:OntologyClass {name: 'Abstract', comment: 'Represents the abstract section of a paper'}),
        (:Class:OntologyClass {name: 'Introduction', comment: 'Represents the introduction section of a paper'}),
        (:Class:OntologyClass {name: 'Methodology', comment: 'Represents the methodology section of a paper'}),
        (:Class:OntologyClass {name: 'Results', comment: 'Represents the results section of a paper'}),
        (:Class:OntologyClass {name: 'Discussion', comment: 'Represents the discussion section of a paper'}),
        (:Class:OntologyClass {name: 'Conclusion', comment: 'Represents the conclusion section of a paper'}),
        
        // Research Components Classes
        (:Class:OntologyClass {name: 'ResearchQuestion', comment: 'Represents a research question addressed in a paper'}),
        (:Class:OntologyClass {name: 'Hypothesis', comment: 'Represents a hypothesis proposed in a paper'}),
        (:Class:OntologyClass {name: 'Contribution', comment: 'Represents a scientific contribution made by a paper'}),
        (:Class:OntologyClass {name: 'Limitation', comment: 'Represents a limitation or constraint identified in a paper'}),
        (:Class:OntologyClass {name: 'FutureWork', comment: 'Represents future work proposed in a paper'}),
        
        // Methods and Techniques Classes
        (:Class:OntologyClass {name: 'Method', comment: 'Represents a method or technique described in a paper'}),
        (:Class:OntologyClass {name: 'Algorithm', comment: 'Represents an algorithm described in a paper'}),
        (:Class:OntologyClass {name: 'ModelArchitecture', comment: 'Represents the architecture of an LLM described in a paper'}),
        (:Class:OntologyClass {name: 'ArchitectureComponent', comment: 'Represents a component of an LLM architecture'}),
        (:Class:OntologyClass {name: 'TrainingStrategy', comment: 'Represents a training strategy used for an LLM'}),
        
        // Data and Evaluation Classes
        (:Class:OntologyClass {name: 'Dataset', comment: 'Represents a dataset used in a paper'}),
        (:Class:OntologyClass {name: 'Benchmark', comment: 'Represents a benchmark used for evaluation in a paper'}),
        (:Class:OntologyClass {name: 'EvaluationMetric', comment: 'Represents a metric used for evaluation in a paper'}),
        (:Class:OntologyClass {name: 'ExperimentalSetup', comment: 'Represents an experimental setup described in a paper'}),
        (:Class:OntologyClass {name: 'Result', comment: 'Represents a result or finding reported in a paper'}),
        (:Class:OntologyClass {name: 'PerformanceComparison', comment: 'Represents a comparison of performance between models'}),
        
        // Applications and Domains Classes
        (:Class:OntologyClass {name: 'Application', comment: 'Represents an application area for LLMs discussed in a paper'}),
        (:Class:OntologyClass {name: 'UseCase', comment: 'Represents a specific use case for LLMs described in a paper'}),
        (:Class:OntologyClass {name: 'Domain', comment: 'Represents a domain where LLMs are applied'}),
        
        // Ethical Considerations Classes
        (:Class:OntologyClass {name: 'EthicalConsideration', comment: 'Represents ethical aspects discussed in a paper'}),
        (:Class:OntologyClass {name: 'SocialImpact', comment: 'Represents social impacts discussed in a paper'}),
        
        // Claims and Evidence Classes
        (:Class:OntologyClass {name: 'Claim', comment: 'Represents a claim made in a paper'}),
        (:Class:OntologyClass {name: 'Evidence', comment: 'Represents evidence provided to support a claim'}),
        (:Class:OntologyClass {name: 'Citation', comment: 'Represents a citation to another paper'}),
        
        // Author and Publication Classes
        (:Class:OntologyClass {name: 'Author', comment: 'Represents an author of a research paper'}),
        (:Class:OntologyClass {name: 'PublicationVenue', comment: 'Represents the venue where a paper was published'}),
        (:Class:OntologyClass {name: 'Institution', comment: 'Represents an institution affiliated with authors'}),
        
        // Reification Classes
        (:Class:OntologyClass {name: 'ModelComparison', comment: 'Reification class representing comparison between two or more models'}),
        (:Class:OntologyClass {name: 'MethodApplication', comment: 'Reification class representing application of a method to a specific problem'}),
        (:Class:OntologyClass {name: 'TopicRelationship', comment: 'Reification class representing relationship between research topics'}),
        (:Class:OntologyClass {name: 'ClaimEvidence', comment: 'Reification class connecting claims to their supporting evidence'}),
        
        // Entity Representation, Extraction and Analysis Classes
        (:Class:OntologyClass {name: 'Entity', comment: 'Represents a key entity identified within LLM research papers'}),
        (:Class:OntologyClass {name: 'TextualMention', comment: 'Represents a specific mention of an entity in text'}),
        (:Class:OntologyClass {name: 'EntityExtraction', comment: 'Reification class representing the extraction of an entity from text'}),
        (:Class:OntologyClass {name: 'TextEntityMapping', comment: 'Reification class linking extracted entities to their textual mentions'}),
        (:Class:OntologyClass {name: 'EntityRelationship', comment: 'Reification class modeling relationships between extracted entities'}),
        (:Class:OntologyClass {name: 'EntityAnalysis', comment: 'Reification class for analyzing extracted entities and their properties'}),
        (:Class:OntologyClass {name: 'EntityNetwork', comment: 'Represents a network of interconnected entities across papers'}),
        (:Class:OntologyClass {name: 'EntityCluster', comment: 'Represents a cluster of related entities'}),
        (:Class:OntologyClass {name: 'Topic', comment: 'Represents a research topic discussed in papers'})
        """
        
        self.run_query(classes_query)
        
        # Create subclass relationships
        subclasses_query = """
        MATCH (abstract:Class {name: 'Abstract'}), (section:Class {name: 'PaperSection'})
        MERGE (abstract)-[:SUBCLASS_OF]->(section)
        
        MATCH (intro:Class {name: 'Introduction'}), (section:Class {name: 'PaperSection'})
        MERGE (intro)-[:SUBCLASS_OF]->(section)
        
        MATCH (method:Class {name: 'Methodology'}), (section:Class {name: 'PaperSection'})
        MERGE (method)-[:SUBCLASS_OF]->(section)
        
        MATCH (results:Class {name: 'Results'}), (section:Class {name: 'PaperSection'})
        MERGE (results)-[:SUBCLASS_OF]->(section)
        
        MATCH (discussion:Class {name: 'Discussion'}), (section:Class {name: 'PaperSection'})
        MERGE (discussion)-[:SUBCLASS_OF]->(section)
        
        MATCH (conclusion:Class {name: 'Conclusion'}), (section:Class {name: 'PaperSection'})
        MERGE (conclusion)-[:SUBCLASS_OF]->(section)
        
        MATCH (algorithm:Class {name: 'Algorithm'}), (method:Class {name: 'Method'})
        MERGE (algorithm)-[:SUBCLASS_OF]->(method)
        """
        
        self.run_query(subclasses_query)
        
        # Create property definitions
        self.create_property_definitions()
        
        logger.info("Ontology schema created")

    def create_property_definitions(self):
        """Create all property definitions in Neo4j"""
        logger.info("Creating property definitions...")
        
        # Object Properties
        object_properties_query = """
        CREATE
        // Paper Structure
        (:Property:ObjectProperty {name: 'hasSection', domain: 'ResearchPaper', range: 'PaperSection', comment: 'Links a paper to its sections'}),
        (:Property:ObjectProperty {name: 'containsText', domain: 'PaperSection', range: 'string', comment: 'Links a paper section to its textual content'}),
        
        // Research Components
        (:Property:ObjectProperty {name: 'addressesQuestion', domain: 'ResearchPaper', range: 'ResearchQuestion', comment: 'Links a paper to the research questions it addresses'}),
        (:Property:ObjectProperty {name: 'proposesHypothesis', domain: 'ResearchPaper', range: 'Hypothesis', comment: 'Links a paper to the hypotheses it proposes'}),
        (:Property:ObjectProperty {name: 'makesContribution', domain: 'ResearchPaper', range: 'Contribution', comment: 'Links a paper to contributions it makes'}),
        (:Property:ObjectProperty {name: 'identifiesLimitation', domain: 'ResearchPaper', range: 'Limitation', comment: 'Links a paper to limitations it identifies'}),
        (:Property:ObjectProperty {name: 'proposesFutureWork', domain: 'ResearchPaper', range: 'FutureWork', comment: 'Links a paper to future work it proposes'}),
        
        // Claims and Evidence
        (:Property:ObjectProperty {name: 'makesClaim', domain: 'ResearchPaper', range: 'Claim', comment: 'Links a paper to claims it makes'}),
        (:Property:ObjectProperty {name: 'hasSupportingEvidence', domain: 'ClaimEvidence', range: 'Evidence', comment: 'Links a claim-evidence relationship to its evidence'}),
        (:Property:ObjectProperty {name: 'relatedToClaim', domain: 'ClaimEvidence', range: 'Claim', comment: 'Links a claim-evidence relationship to its claim'}),
        (:Property:ObjectProperty {name: 'citesSource', domain: 'Evidence', range: 'Citation', comment: 'Links evidence to its citation sources'}),
        (:Property:ObjectProperty {name: 'citedPaper', domain: 'Citation', range: 'ResearchPaper', comment: 'Links a citation to the cited paper'}),
        
        // Methods and Models
        (:Property:ObjectProperty {name: 'describesMethod', domain: 'ResearchPaper', range: 'Method', comment: 'Links a paper to methods it describes'}),
        (:Property:ObjectProperty {name: 'proposesArchitecture', domain: 'ResearchPaper', range: 'ModelArchitecture', comment: 'Links a paper to model architectures it proposes'}),
        (:Property:ObjectProperty {name: 'hasComponent', domain: 'ModelArchitecture', range: 'ArchitectureComponent', comment: 'Links a model architecture to its components'}),
        (:Property:ObjectProperty {name: 'usesTrainingStrategy', domain: 'Method', range: 'TrainingStrategy', comment: 'Links a method to its training strategy'}),
        
        // Model Comparison
        (:Property:ObjectProperty {name: 'comparesModel', domain: 'ModelComparison', range: 'ModelArchitecture', comment: 'Links a model comparison to models being compared'}),
        (:Property:ObjectProperty {name: 'usesMetricForComparison', domain: 'ModelComparison', range: 'EvaluationMetric', comment: 'Links a model comparison to metrics used for comparison'}),
        (:Property:ObjectProperty {name: 'hasBenchmarkForComparison', domain: 'ModelComparison', range: 'Benchmark', comment: 'Links a model comparison to benchmarks used'}),
        (:Property:ObjectProperty {name: 'relatedToPaper', domain: 'ModelComparison', range: 'ResearchPaper', comment: 'Links a model comparison to the paper making the comparison'}),
        
        // Method Application
        (:Property:ObjectProperty {name: 'appliesMethod', domain: 'MethodApplication', range: 'Method', comment: 'Links a method application to the method being applied'}),
        (:Property:ObjectProperty {name: 'appliesInDomain', domain: 'MethodApplication', range: 'Domain', comment: 'Links a method application to the domain of application'}),
        (:Property:ObjectProperty {name: 'hasApplication', domain: 'MethodApplication', range: 'Application', comment: 'Links a method application to its application'}),
        (:Property:ObjectProperty {name: 'describedInPaper', domain: 'MethodApplication', range: 'ResearchPaper', comment: 'Links a method application to the paper describing it'}),
        
        // Data and Evaluation
        (:Property:ObjectProperty {name: 'usesDataset', domain: 'ResearchPaper', range: 'Dataset', comment: 'Links a paper to datasets it uses'}),
        (:Property:ObjectProperty {name: 'evaluatesOn', domain: 'ResearchPaper', range: 'Benchmark', comment: 'Links a paper to benchmarks it evaluates on'}),
        (:Property:ObjectProperty {name: 'usesMetric', domain: 'ResearchPaper', range: 'EvaluationMetric', comment: 'Links a paper to metrics it uses'}),
        (:Property:ObjectProperty {name: 'reportsResult', domain: 'ResearchPaper', range: 'Result', comment: 'Links a paper to results it reports'}),
        (:Property:ObjectProperty {name: 'comparesPerformance', domain: 'ResearchPaper', range: 'PerformanceComparison', comment: 'Links a paper to performance comparisons it makes'}),
        
        // Topic Relationships
        (:Property:ObjectProperty {name: 'relatesTopic', domain: 'TopicRelationship', range: 'Topic', comment: 'Links a topic relationship to the topics it relates'}),
        
        // Applications and Domains
        (:Property:ObjectProperty {name: 'describesApplication', domain: 'ResearchPaper', range: 'Application', comment: 'Links a paper to applications it describes'}),
        (:Property:ObjectProperty {name: 'discussesUseCase', domain: 'ResearchPaper', range: 'UseCase', comment: 'Links a paper to use cases it discusses'}),
        (:Property:ObjectProperty {name: 'targetsDomain', domain: 'Application', range: 'Domain', comment: 'Links an application to its target domain'}),
        
        // Ethical Considerations
        (:Property:ObjectProperty {name: 'addressesEthicalConsideration', domain: 'ResearchPaper', range: 'EthicalConsideration', comment: 'Links a paper to ethical considerations it addresses'}),
        (:Property:ObjectProperty {name: 'discussesSocialImpact', domain: 'ResearchPaper', range: 'SocialImpact', comment: 'Links a paper to social impacts it discusses'}),
        
        // Authors and Publication
        (:Property:ObjectProperty {name: 'writtenBy', domain: 'ResearchPaper', range: 'Author', comment: 'Links a paper to its authors'}),
        (:Property:ObjectProperty {name: 'affiliatedWith', domain: 'Author', range: 'Institution', comment: 'Links an author to their institution'}),
        (:Property:ObjectProperty {name: 'publishedIn', domain: 'ResearchPaper', range: 'PublicationVenue', comment: 'Links a paper to its publication venue'}),
        
        // Entity Extraction and Analysis
        (:Property:ObjectProperty {name: 'containsEntity', domain: 'ResearchPaper', range: 'Entity', comment: 'Links a paper to entities it contains'}),
        (:Property:ObjectProperty {name: 'extractedFrom', domain: 'EntityExtraction', range: 'PaperSection', comment: 'Links an entity extraction to the paper section it was extracted from'}),
        (:Property:ObjectProperty {name: 'extractedEntity', domain: 'EntityExtraction', range: 'Entity', comment: 'Links an entity extraction to the entity extracted'}),
        (:Property:ObjectProperty {name: 'hasTextualMention', domain: 'TextEntityMapping', range: 'TextualMention', comment: 'Links a text-entity mapping to its textual mention'}),
        (:Property:ObjectProperty {name: 'refersToEntity', domain: 'TextEntityMapping', range: 'Entity', comment: 'Links a text-entity mapping to the entity it refers to'}),
        (:Property:ObjectProperty {name: 'relatesEntity', domain: 'EntityRelationship', range: 'Entity', comment: 'Links an entity relationship to the entities involved'}),
        (:Property:ObjectProperty {name: 'analyzesEntity', domain: 'EntityAnalysis', range: 'Entity', comment: 'Links an entity analysis to the entity being analyzed'}),
        (:Property:ObjectProperty {name: 'usesAnalysisMethod', domain: 'EntityAnalysis', range: 'Method', comment: 'Links an entity analysis to the method used for analysis'}),
        (:Property:ObjectProperty {name: 'belongsToNetwork', domain: 'Entity', range: 'EntityNetwork', comment: 'Links an entity to the network it belongs to'}),
        (:Property:ObjectProperty {name: 'belongsToCluster', domain: 'Entity', range: 'EntityCluster', comment: 'Links an entity to the cluster it belongs to'}),
        (:Property:ObjectProperty {name: 'relatedToMethod', domain: 'Entity', range: 'Method', comment: 'Links an entity to related methods'}),
        (:Property:ObjectProperty {name: 'relatedToResult', domain: 'Entity', range: 'Result', comment: 'Links an entity to related results'}),
        (:Property:ObjectProperty {name: 'relatedToClaim', domain: 'Entity', range: 'Claim', comment: 'Links an entity to related claims'})
        """
        
        self.run_query(object_properties_query)
        
        # Data Properties
        data_properties_query = """
        CREATE
        // Paper Metadata
        (:Property:DataProperty {name: 'title', domain: 'ResearchPaper', range: 'string', comment: 'The title of the paper'}),
        (:Property:DataProperty {name: 'abstract', domain: 'Abstract', range: 'string', comment: 'The abstract text of the paper'}),
        (:Property:DataProperty {name: 'publicationYear', domain: 'ResearchPaper', range: 'gYear', comment: 'The year the paper was published'}),
        (:Property:DataProperty {name: 'doi', domain: 'ResearchPaper', range: 'string', comment: 'Digital Object Identifier of the paper'}),
        (:Property:DataProperty {name: 'citationCount', domain: 'ResearchPaper', range: 'integer', comment: 'The number of citations the paper has received'}),
        
        // Methods and Results
        (:Property:DataProperty {name: 'methodName', domain: 'Method', range: 'string', comment: 'The name of a method'}),
        (:Property:DataProperty {name: 'algorithmComplexity', domain: 'Algorithm', range: 'string', comment: 'The complexity class of an algorithm'}),
        (:Property:DataProperty {name: 'datasetSize', domain: 'Dataset', range: 'long', comment: 'The size of a dataset in number of examples'}),
        (:Property:DataProperty {name: 'metricScore', domain: 'Result', range: 'decimal', comment: 'A numerical score for an evaluation metric'}),
        (:Property:DataProperty {name: 'statisticalSignificance', domain: 'Result', range: 'decimal', comment: 'P-value or other significance measure for a result'}),
        (:Property:DataProperty {name: 'modelParameters', domain: 'ModelArchitecture', range: 'long', comment: 'Number of parameters in a model'}),
        
        // Author and Publication
        (:Property:DataProperty {name: 'authorName', domain: 'Author', range: 'string', comment: 'The name of an author'}),
        (:Property:DataProperty {name: 'authorEmail', domain: 'Author', range: 'string', comment: 'The email address of an author'}),
        (:Property:DataProperty {name: 'venueName', domain: 'PublicationVenue', range: 'string', comment: 'The name of a publication venue'}),
        (:Property:DataProperty {name: 'venueType', domain: 'PublicationVenue', range: 'string', comment: 'The type of venue (e.g., conference, journal)'}),
        (:Property:DataProperty {name: 'institutionName', domain: 'Institution', range: 'string', comment: 'The name of an institution'}),
        
        // Paper Sections
        (:Property:DataProperty {name: 'sectionOrder', domain: 'PaperSection', range: 'integer', comment: 'The order of a section within a paper'}),
        
        // Topic Relationships
        (:Property:DataProperty {name: 'relationshipType', domain: 'TopicRelationship', range: 'string', comment: 'Specifies the type of relationship between topics'}),
        (:Property:DataProperty {name: 'relationshipStrength', domain: 'TopicRelationship', range: 'decimal', comment: 'Specifies the strength of relationship between topics (0-1)'}),
        
        // Entity Extraction and Analysis
        (:Property:DataProperty {name: 'entityName', domain: 'Entity', range: 'string', comment: 'The name of an entity'}),
        (:Property:DataProperty {name: 'entityType', domain: 'Entity', range: 'string', comment: 'The type of entity (e.g., method, metric, finding)'}),
        (:Property:DataProperty {name: 'mentionText', domain: 'TextualMention', range: 'string', comment: 'The actual text of a mention'}),
        (:Property:DataProperty {name: 'mentionPosition', domain: 'TextualMention', range: 'integer', comment: 'The character position of a mention in text'}),
        (:Property:DataProperty {name: 'mentionLength', domain: 'TextualMention', range: 'integer', comment: 'The length of a mention in characters'}),
        (:Property:DataProperty {name: 'extractionMethod', domain: 'EntityExtraction', range: 'string', comment: 'The method used to extract an entity'}),
        (:Property:DataProperty {name: 'extractionConfidence', domain: 'EntityExtraction', range: 'decimal', comment: 'The confidence score of an entity extraction'}),
        (:Property:DataProperty {name: 'relationshipType', domain: 'EntityRelationship', range: 'string', comment: 'The type of relationship between entities'}),
        (:Property:DataProperty {name: 'relationshipStrength', domain: 'EntityRelationship', range: 'decimal', comment: 'The strength of relationship between entities (0-1)'}),
        (:Property:DataProperty {name: 'analysisMethod', domain: 'EntityAnalysis', range: 'string', comment: 'The method used for entity analysis'}),
        (:Property:DataProperty {name: 'analysisResult', domain: 'EntityAnalysis', range: 'string', comment: 'The result of an entity analysis'}),
        (:Property:DataProperty {name: 'networkSize', domain: 'EntityNetwork', range: 'integer', comment: 'The number of entities in a network'}),
        (:Property:DataProperty {name: 'clusterSize', domain: 'EntityCluster', range: 'integer', comment: 'The number of entities in a cluster'}),
        (:Property:DataProperty {name: 'clusterCohesion', domain: 'EntityCluster', range: 'decimal', comment: 'The cohesion score of an entity cluster'})
        """
        
        self.run_query(data_properties_query)
        
        # Connect properties to their domains and ranges
        connect_properties_query = """
        MATCH (p:Property)-[r:DOMAIN|RANGE]->(c) DELETE r
        
        MATCH (p:Property), (d:Class)
        WHERE p.domain = d.name
        MERGE (p)-[:DOMAIN]->(d)
        
        MATCH (p:ObjectProperty), (r:Class)
        WHERE p.range = r.name
        MERGE (p)-[:RANGE]->(r)
        """
        
        self.run_query(connect_properties_query)
        
        logger.info("Property definitions created")
    
    def create_indices(self):
        """Create indices for faster lookup"""
        logger.info("Creating indices...")
        
        indices = [
            "CREATE INDEX paper_title_idx IF NOT EXISTS FOR (p:ResearchPaper) ON (p.title)",
            "CREATE INDEX author_name_idx IF NOT EXISTS FOR (a:Author) ON (a.name)",
            "CREATE INDEX method_name_idx IF NOT EXISTS FOR (m:Method) ON (m.name)",
            "CREATE INDEX entity_name_idx IF NOT EXISTS FOR (e:Entity) ON (e.name)"
        ]
        
        for index in indices:
            self.run_query(index)
        
        logger.info("Indices created")

    def create_instance_data(self, sample=True):
        """Create sample instance data"""
        if not sample:
            logger.info("Skipping sample data creation")
            return
        
        logger.info("Creating sample instance data...")
        
        # Sample data for a research paper
        sample_paper_query = """
        CREATE 
        // Sample paper
        (p1:ResearchPaper {
            title: "GPT-4: Large Language Models with Advanced Capabilities",
            doi: "10.1234/example.2023.0001",
            publicationYear: 2023,
            citationCount: 142
        })
        
        // Sample authors
        CREATE (a1:Author {
            authorName: "Jane Smith",
            authorEmail: "<EMAIL>"
        })
        CREATE (a2:Author {
            authorName: "John Doe",
            authorEmail: "<EMAIL>"
        })
        
        // Sample institution
        CREATE (i1:Institution {
            institutionName: "AI Research Institute"
        })
        
        // Publication venue
        CREATE (v1:PublicationVenue {
            venueName: "International Conference on Machine Learning",
            venueType: "conference"
        })
        
        // Paper sections
        CREATE (abs1:Abstract {
            sectionOrder: 1
        })
        CREATE (abs1Content:TextContent {
            text: "This paper introduces GPT-4, an advanced large language model with significant improvements in reasoning and knowledge representation."
        })
        
        // Methods
        CREATE (m1:Method {
            methodName: "Reinforced Attention Mechanism"
        })
        
        // Architecture
        CREATE (ma1:ModelArchitecture {
            name: "GPT-4 Architecture",
            modelParameters: 1500000000000
        })
        
        // Components
        CREATE (c1:ArchitectureComponent {name: "Self-Attention Block"})
        CREATE (c2:ArchitectureComponent {name: "Feed-Forward Network"})
        CREATE (c3:ArchitectureComponent {name: "Layer Normalization"})
        
        // Dataset
        CREATE (d1:Dataset {
            name: "Enhanced Web Corpus",
            datasetSize: 45000000000
        })
        
        // Benchmarks and evaluation
        CREATE (b1:Benchmark {name: "MMLU"})
        CREATE (b2:Benchmark {name: "HumanEval"})
        CREATE (em1:EvaluationMetric {name: "Accuracy"})
        CREATE (em2:EvaluationMetric {name: "F1-score"})
        
        // Results
        CREATE (r1:Result {
            metricScore: 0.95,
            statisticalSignificance: 0.001
        })
        
        // Claims and evidence
        CREATE (claim1:Claim {text: "GPT-4 outperforms all previous models on reasoning tasks."})
        CREATE (ev1:Evidence {text: "Experimental results show 25% improvement on benchmark tasks."})
        CREATE (ce1:ClaimEvidence {})
        
        // Entities extracted from the paper
        CREATE (e1:Entity {
            entityName: "Self-attention mechanism",
            entityType: "model component"
        })
        CREATE (tm1:TextualMention {
            mentionText: "self-attention mechanism",
            mentionPosition: 1250,
            mentionLength: 23
        })
        CREATE (mapping1:TextEntityMapping {})
        CREATE (extraction1:EntityExtraction {
            extractionMethod: "NER",
            extractionConfidence: 0.92
        })
        
        // Create relationships
        CREATE (p1)-[:HAS_SECTION]->(abs1)-[:CONTAINS]->(abs1Content)
        CREATE (p1)-[:WRITTEN_BY]->(a1)
        CREATE (p1)-[:WRITTEN_BY]->(a2)
        CREATE (a1)-[:AFFILIATED_WITH]->(i1)
        CREATE (a2)-[:AFFILIATED_WITH]->(i1)
        CREATE (p1)-[:PUBLISHED_IN]->(v1)
        CREATE (p1)-[:DESCRIBES_METHOD]->(m1)
        CREATE (p1)-[:PROPOSES_ARCHITECTURE]->(ma1)
        CREATE (ma1)-[:HAS_COMPONENT]->(c1)
        CREATE (ma1)-[:HAS_COMPONENT]->(c2)
        CREATE (ma1)-[:HAS_COMPONENT]->(c3)
        CREATE (p1)-[:USES_DATASET]->(d1)
        CREATE (p1)-[:EVALUATES_ON]->(b1)
        CREATE (p1)-[:EVALUATES_ON]->(b2)
        CREATE (p1)-[:USES_METRIC]->(em1)
        CREATE (p1)-[:USES_METRIC]->(em2)
        CREATE (p1)-[:REPORTS_RESULT]->(r1)
        CREATE (p1)-[:MAKES_CLAIM]->(claim1)
        CREATE (ce1)-[:RELATED_TO_CLAIM]->(claim1)
        CREATE (ce1)-[:HAS_SUPPORTING_EVIDENCE]->(ev1)
        CREATE (p1)-[:CONTAINS_ENTITY]->(e1)
        CREATE (mapping1)-[:REFERS_TO_ENTITY]->(e1)
        CREATE (mapping1)-[:HAS_TEXTUAL_MENTION]->(tm1)
        CREATE (extraction1)-[:EXTRACTED_ENTITY]->(e1)
        CREATE (extraction1)-[:EXTRACTED_FROM]->(abs1)
        """
        
        self.run_query(sample_paper_query)
        
        logger.info("Sample instance data created")

    def demonstrate_queries(self):
        """Demonstrate some useful queries against the knowledge graph"""
        logger.info("Running demonstration queries...")
        
        demo_queries = [
            {
                "name": "Find papers by a specific author",
                "query": """
                MATCH (a:Author {authorName: "Jane Smith"})-[:WRITTEN_BY]-(p:ResearchPaper)
                RETURN p.title, p.publicationYear, p.citationCount
                """
            },
            {
                "name": "Find all entities in a paper and their textual mentions",
                "query": """
                MATCH (p:ResearchPaper {title: "GPT-4: Large Language Models with Advanced Capabilities"})
                MATCH (p)-[:CONTAINS_ENTITY]->(e:Entity)
                OPTIONAL MATCH (m:TextEntityMapping)-[:REFERS_TO_ENTITY]->(e)
                OPTIONAL MATCH (m)-[:HAS_TEXTUAL_MENTION]->(tm:TextualMention)
                RETURN e.entityName, e.entityType, tm.mentionText, tm.mentionPosition
                """
            },
            {
                "name": "Find papers and the methods they describe",
                "query": """
                MATCH (p:ResearchPaper)-[:DESCRIBES_METHOD]->(m:Method)
                RETURN p.title, m.methodName
                """
            },
            {
                "name": "Find model architectures and their components",
                "query": """
                MATCH (p:ResearchPaper)-[:PROPOSES_ARCHITECTURE]->(ma:ModelArchitecture)
                MATCH (ma)-[:HAS_COMPONENT]->(c:ArchitectureComponent)
                RETURN p.title, ma.name, collect(c.name) as components, ma.modelParameters
                """
            },
            {
                "name": "Find papers with their evaluation metrics and results",
                "query": """
                MATCH (p:ResearchPaper)-[:USES_METRIC]->(m:EvaluationMetric)
                MATCH (p)-[:REPORTS_RESULT]->(r:Result)
                RETURN p.title, m.name, r.metricScore
                """
            }
        ]
        
        for i, demo in enumerate(demo_queries):
            logger.info(f"\nDemo Query {i+1}: {demo['name']}")
            logger.info(f"Query: {demo['query']}")
            result = self.run_query(demo["query"])
            logger.info("Result:")
            for record in result:
                logger.info(record)
        
        logger.info("\nDemonstration queries completed")

    def build_knowledge_graph(self, sample_data=True):
        """Build the complete knowledge graph"""
        try:
            self.clear_database()
            self.create_ontology_schema()
            self.create_class_hierarchy()
            self.create_constraints()
            self.create_indices()
            self.create_instance_data(sample=sample_data)
            
            logger.info("Knowledge graph successfully built!")
            
            if sample_data:
                self.demonstrate_queries()
                
        except Exception as e:
            logger.error(f"Error building knowledge graph: {e}")
            raise

def main():
    # Configuration parameters
    neo4j_uri = "neo4j://localhost:7687"  # Update with your Neo4j URI
    neo4j_username = "neo4j"              # Update with your Neo4j username
    neo4j_password = "password"           # Update with your Neo4j password
    sample_data = True                    # Set to False to skip creating sample data
    
    logger.info("Starting LLM Research Papers Knowledge Graph Builder")
    
    try:
        # Initialize and build the knowledge graph
        graph_builder = LLMOntologyGraphBuilder(neo4j_uri, neo4j_username, neo4j_password)
        graph_builder.build_knowledge_graph(sample_data=sample_data)
        graph_builder.close()
        
        logger.info("Knowledge graph creation completed successfully.")
    except Exception as e:
        logger.error(f"An error occurred during knowledge graph creation: {e}")
    finally:
        logger.info("Shutting down the LLM Research Papers Knowledge Graph Builder")

if __name__ == "__main__":
    main()
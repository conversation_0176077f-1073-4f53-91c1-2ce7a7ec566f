import sys
from pymongo import MongoClient

# Script to check knowledge graph data in actual MongoDB
def check_real_knowledge_graphs():
    """
    This script checks the actual knowledge graph data in MongoDB.
    """
    # Connect to the real MongoDB
    print("Connecting to MongoDB...")
    client = MongoClient()
    db = client['moatless']
    
    # Print available collections
    print("Collections:", db.list_collection_names())
    
    # Access the knowledge graphs collection
    print("\nChecking knowledge graph results:")
    kg_collection = db['knowledge_graphs']
    
    # Get all documents
    kg_data = list(kg_collection.find({}))
    
    print(f"Found {len(kg_data)} knowledge graphs")
    
    # Print summary of each knowledge graph
    for i, kg in enumerate(kg_data):
        print(f"\nKnowledge Graph {i+1}: {kg.get('name', kg.get('title', 'Untitled'))}")
        print(f"  URL: {kg.get('abs_url', 'N/A')}")
        print(f"  Nodes: {len(kg.get('nodes', []))}")
        print(f"  Links: {len(kg.get('links', []))}")
        print(f"  Relationships: {len(kg.get('relationships', []))}")
        
        # Print sample nodes
        if kg.get('nodes'):
            print("\n  Sample Nodes:")
            for node in kg.get('nodes')[:3]:  # Show first 3 nodes
                if isinstance(node, dict):
                    if 'properties' in node:
                        # Neo4j format
                        node_name = node.get('properties', {}).get('name', 'Unnamed')
                        node_id = node.get('id', 'No ID')
                        print(f"    - {node_name} ({node_id})")
                        # Print description if available
                        description = node.get('properties', {}).get('description', '')
                        if description:
                            print(f"      Description: {description[:100]}...")
                    else:
                        # D3.js format
                        node_name = node.get('name', 'Unnamed')
                        node_id = node.get('id', 'No ID')
                        node_type = node.get('type', 'unknown')
                        print(f"    - {node_name} ({node_id}) - Type: {node_type}")
        
        # Print sample relationships or links
        if kg.get('relationships'):
            print("\n  Sample Relationships:")
            for rel in kg.get('relationships')[:3]:  # Show first 3 relationships
                source_id = rel.get('start', {}).get('id', 'unknown')
                target_id = rel.get('end', {}).get('id', 'unknown')
                rel_type = rel.get('type', 'unknown')
                print(f"    - {source_id} --[{rel_type}]--> {target_id}")
        elif kg.get('links'):
            print("\n  Sample Links:")
            for link in kg.get('links')[:3]:  # Show first 3 links
                source = link.get('source', 'unknown')
                target = link.get('target', 'unknown')
                rel_type = link.get('type', 'unknown')
                print(f"    - {source} --[{rel_type}]--> {target}")

    # Check specifically for main_graph
    main_graph = kg_collection.find_one({"name": "main_graph"})
    if main_graph:
        print("\n==== Main Graph Details ====")
        print(f"Nodes: {len(main_graph.get('nodes', []))}")
        print(f"Links: {len(main_graph.get('links', []))}")
        print(f"Updated At: {main_graph.get('updated_at', 'unknown')}")
    else:
        print("\nMain Graph not found!")

# Original function for mock database testing
def check_mock_knowledge_graphs():
    """
    This script uses the mock MongoDB implementation to test
    knowledge graph extraction without requiring a real database connection.
    """
    # Import the create_mock_mongodb function from extract_kg
    from extract_kg import create_mock_mongodb, process_papers
    import asyncio
    
    # Create a mock MongoDB client
    print("Creating mock MongoDB client...")
    mock_client = create_mock_mongodb()
    
    # Run the extraction with our mock client
    print("Running knowledge graph extraction...")
    asyncio.run(process_papers(local_mode=True, limit=5, reprocess=False))
    
    # Access the mock database to check results
    print("\nChecking knowledge graph results:")
    mongodb = mock_client['papers']
    kg_collection = mongodb['knowledge_graphs']
    
    # Get all documents
    kg_data = kg_collection.data if hasattr(kg_collection, 'data') else []
    
    print(f"Found {len(kg_data)} knowledge graphs")
    
    # Print summary of each knowledge graph
    for i, kg in enumerate(kg_data):
        print(f"\nKnowledge Graph {i+1}: {kg.get('title', 'Untitled')}")
        print(f"  URL: {kg.get('abs_url', 'N/A')}")
        print(f"  Nodes: {len(kg.get('nodes', []))}")
        print(f"  Relationships: {len(kg.get('relationships', []))}")
        
        # Print sample nodes
        if kg.get('nodes'):
            print("\n  Sample Nodes:")
            for node in kg.get('nodes')[:3]:  # Show first 3 nodes
                node_name = node.get('properties', {}).get('name', 'Unnamed')
                node_id = node.get('id', 'No ID')
                print(f"    - {node_name} ({node_id})")
                # Print description if available
                description = node.get('properties', {}).get('description', '')
                if description:
                    print(f"      Description: {description[:100]}...")
        
        # Print sample relationships
        if kg.get('relationships'):
            print("\n  Sample Relationships:")
            for rel in kg.get('relationships')[:3]:  # Show first 3 relationships
                source_id = rel.get('start', {}).get('id', 'unknown')
                target_id = rel.get('end', {}).get('id', 'unknown')
                rel_type = rel.get('type', 'unknown')
                print(f"    - {source_id} --[{rel_type}]--> {target_id}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == '--mock':
        check_mock_knowledge_graphs()
    else:
        check_real_knowledge_graphs()
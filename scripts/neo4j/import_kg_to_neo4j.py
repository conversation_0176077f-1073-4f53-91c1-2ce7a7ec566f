#!/usr/bin/env python3
"""
Import Knowledge Graph to Neo4j

This script imports knowledge graph data from either:
1. MongoDB knowledge_graphs collection to Neo4j, or
2. CSV files in the neo4j_import directory to Neo4j

Usage:
    python import_kg_to_neo4j.py --uri bolt://localhost:7687 --user neo4j --password yourpassword
    
Options:
    --uri       Neo4j URI (default: bolt://localhost:7687)
    --user      Neo4j username (default: neo4j)
    --password  Neo4j password
    --method    Import method: 'json' or 'csv' (default: csv)
    --limit     Limit number of knowledge graphs to import (default: no limit)
"""

import os
import sys
import json
import argparse
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

try:
    from neo4j import GraphDatabase
    NEO4J_AVAILABLE = True
except ImportError:
    NEO4J_AVAILABLE = False
    print("WARNING: Neo4j driver not installed. Install with: pip install neo4j")


def connect_to_neo4j(uri, user=None, password=None):
    """Connect to Neo4j database"""
    if not NEO4J_AVAILABLE:
        print("ERROR: Neo4j driver not installed. Install with: pip install neo4j")
        sys.exit(1)
        
    try:
        # Try with auth if provided
        if user and password is not None:
            driver = GraphDatabase.driver(uri, auth=(user, password))
        else:
            # Try without auth
            print("Attempting connection without authentication...")
            driver = GraphDatabase.driver(uri)
        
        # Test connection
        with driver.session() as session:
            result = session.run('RETURN "Connection successful!" AS message')
            print(result.single()[0])
            
        return driver
    except Exception as e:
        print(f"ERROR: Failed to connect to Neo4j: {e}")
        print("\nIf Neo4j requires authentication (which is usually the case):")
        print("1. Use Neo4j Desktop to check your credentials")
        print("2. For Neo4j Desktop, try default: username='neo4j', password='neo4j'")
        print("3. Or you may need to reset your password")
        sys.exit(1)


def create_constraints(driver):
    """Create Neo4j constraints"""
    print("Creating constraints...")
    with driver.session() as session:
        try:
            session.run('CREATE CONSTRAINT entity_id_constraint IF NOT EXISTS FOR (n:Entity) REQUIRE n.id IS UNIQUE')
            session.run('CREATE CONSTRAINT paper_id_constraint IF NOT EXISTS FOR (n:Paper) REQUIRE n.id IS UNIQUE')
            print("Constraints created successfully!")
        except Exception as e:
            print(f"WARNING: Error creating constraints: {e}")


def import_from_csv(driver, csv_dir):
    """Import knowledge graph data from CSV files"""
    print(f"Importing from CSV files in {csv_dir}...")
    
    csv_path = Path(csv_dir)
    nodes_csv = csv_path / "nodes.csv"
    rels_csv = csv_path / "relationships.csv"
    
    if not nodes_csv.exists() or not rels_csv.exists():
        print(f"ERROR: CSV files not found in {csv_dir}")
        sys.exit(1)
    
    # Get the absolute path
    nodes_csv_abs = nodes_csv.absolute()
    rels_csv_abs = rels_csv.absolute()
    
    print(f"Node file: {nodes_csv_abs}")
    print(f"Relationship file: {rels_csv_abs}")
    
    # Check if files are too large
    nodes_size_mb = nodes_csv.stat().st_size / (1024 * 1024)
    rels_size_mb = rels_csv.stat().st_size / (1024 * 1024)
    print(f"Nodes CSV size: {nodes_size_mb:.2f} MB")
    print(f"Relationships CSV size: {rels_size_mb:.2f} MB")
    
    # Create entity nodes from nodes.csv
    print("Creating Entity nodes...")
    total_entities = 0
    with open(nodes_csv, 'r') as f:
        # Skip header
        next(f)
        
        with driver.session() as session:
            batch_size = 100
            batch = []
            
            for i, line in enumerate(f):
                line = line.strip()
                if not line:
                    continue
                    
                parts = line.split(',', 2)
                if len(parts) >= 3:
                    node_id = parts[0]
                    name = parts[1]
                    papers_str = parts[2] if len(parts) > 2 else ''
                    
                    # Handle special case where papers has a quoted description with commas
                    if papers_str.startswith('"') and papers_str.count('"') > 1:
                        desc_parts = papers_str.split('"')
                        papers_str = desc_parts[-1].lstrip(',')
                        
                    batch.append((node_id, name, papers_str))
                    
                    if len(batch) >= batch_size:
                        # Execute batch
                        for node_id, name, papers_str in batch:
                            session.run(
                                'MERGE (n:Entity {id: $id}) '
                                'ON CREATE SET n.name = $name, n.papers = split($papers, ";")',
                                id=node_id, name=name, papers=papers_str
                            )
                            
                        total_entities += len(batch)
                        print(f"Processed {total_entities} entities")
                        batch = []
            
            # Process remaining batch
            if batch:
                for node_id, name, papers_str in batch:
                    session.run(
                        'MERGE (n:Entity {id: $id}) '
                        'ON CREATE SET n.name = $name, n.papers = split($papers, ";")',
                        id=node_id, name=name, papers=papers_str
                    )
                total_entities += len(batch)
    
    print(f"Created {total_entities} Entity nodes")
    
    # Create Paper nodes and MENTIONS relationships
    print("Creating Paper nodes and MENTIONS relationships...")
    with driver.session() as session:
        session.run("""
            MATCH (e:Entity)
            WHERE e.papers IS NOT NULL
            UNWIND e.papers AS paperId
            WITH e, paperId
            WHERE paperId <> ''
            MERGE (p:Paper {id: paperId})
            MERGE (p)-[:MENTIONS]->(e)
        """)
    
    # Create relationships between entities
    print("Creating relationships between entities...")
    total_rels = 0
    relationship_types = set()
    
    with open(rels_csv, 'r') as f:
        # Skip header
        next(f)
        
        with driver.session() as session:
            batch_size = 200
            batch = []
            
            for i, line in enumerate(f):
                line = line.strip()
                if not line:
                    continue
                    
                parts = line.split(',')
                if len(parts) >= 4:
                    rel_id = parts[0]
                    source_id = parts[1]
                    target_id = parts[2]
                    rel_type = parts[3]
                    
                    relationship_types.add(rel_type)
                    batch.append((source_id, target_id, rel_type))
                    
                    if len(batch) >= batch_size:
                        # Execute batch
                        for source_id, target_id, rel_type in batch:
                            try:
                                # Create dynamic relationship
                                query = f"""
                                MATCH (source:Entity {{id: $source_id}})
                                MATCH (target:Entity {{id: $target_id}})
                                MERGE (source)-[r:`{rel_type}`]->(target)
                                """
                                session.run(query, source_id=source_id, target_id=target_id)
                            except Exception as e:
                                print(f"Error creating relationship {source_id}-[{rel_type}]->{target_id}: {e}")
                                # Fall back to RELATES_TO relationship
                                session.run(
                                    'MATCH (source:Entity {id: $source_id}) '
                                    'MATCH (target:Entity {id: $target_id}) '
                                    'MERGE (source)-[r:RELATES_TO]->(target)',
                                    source_id=source_id, target_id=target_id
                                )
                        
                        total_rels += len(batch)
                        print(f"Processed {total_rels} relationships")
                        batch = []
            
            # Process remaining batch
            if batch:
                for source_id, target_id, rel_type in batch:
                    try:
                        # Create dynamic relationship
                        query = f"""
                        MATCH (source:Entity {{id: $source_id}})
                        MATCH (target:Entity {{id: $target_id}})
                        MERGE (source)-[r:`{rel_type}`]->(target)
                        """
                        session.run(query, source_id=source_id, target_id=target_id)
                    except Exception as e:
                        print(f"Error creating relationship {source_id}-[{rel_type}]->{target_id}: {e}")
                        # Fall back to RELATES_TO relationship
                        session.run(
                            'MATCH (source:Entity {id: $source_id}) '
                            'MATCH (target:Entity {id: $target_id}) '
                            'MERGE (source)-[r:RELATES_TO]->(target)',
                            source_id=source_id, target_id=target_id
                        )
                total_rels += len(batch)
    
    print(f"Created {total_rels} relationships with {len(relationship_types)} different types")
    print(f"Relationship types: {', '.join(sorted(relationship_types))}")


def import_from_json(driver, json_file, limit=None):
    """Import knowledge graph data from MongoDB JSON export"""
    print(f"Importing from JSON file: {json_file}")
    
    if not os.path.exists(json_file):
        print(f"ERROR: JSON file not found: {json_file}")
        sys.exit(1)
    
    # Check file size
    file_size_mb = os.path.getsize(json_file) / (1024 * 1024)
    print(f"JSON file size: {file_size_mb:.2f} MB")
    
    # Batch processing settings
    paper_batch_size = 50  # Number of papers to process in a batch
    node_batch_size = 200  # Number of nodes to process in a batch
    rel_batch_size = 500   # Number of relationships to process in a batch
    
    count = 0
    with open(json_file, 'r') as f:
        # Process in batches
        paper_batch = []
        all_nodes = {}  # Store unique nodes to avoid duplicates
        all_relationships = {}  # Store unique relationships to avoid duplicates
        
        for i, line in enumerate(f):
            if limit and count >= limit:
                print(f"Reached import limit of {limit} records")
                break
                
            try:
                kg = json.loads(line.strip())
                
                if 'abs_url' not in kg:
                    continue
                    
                abs_url = kg['abs_url']
                title = kg.get('title', 'Untitled')
                paper_id = abs_url.split('/')[-1]
                
                # Add to paper batch
                paper_batch.append({
                    'paper_id': paper_id,
                    'title': title,
                    'abs_url': abs_url
                })
                
                # Process nodes
                for node in kg.get('nodes', []):
                    node_id = node['id']
                    labels = node.get('labels', ['Entity'])
                    name = node.get('properties', {}).get('name', node_id)
                    description = node.get('properties', {}).get('description', '')
                    
                    # Add to nodes dictionary (overwrite if already exists)
                    all_nodes[node_id] = {
                        'node_id': node_id,
                        'labels': labels,
                        'name': name,
                        'description': description,
                        'papers': all_nodes.get(node_id, {}).get('papers', set()) | {paper_id}
                    }
                
                # Process relationships
                for rel in kg.get('relationships', []):
                    source_id = rel['start']['id']
                    target_id = rel['end']['id']
                    rel_type = rel.get('label', rel.get('type', '')).upper().replace(' ', '_')
                    
                    if not rel_type:
                        rel_type = 'RELATES_TO'
                    
                    # Create a unique key for this relationship
                    rel_key = f"{source_id}_{rel_type}_{target_id}"
                    all_relationships[rel_key] = {
                        'source_id': source_id,
                        'target_id': target_id,
                        'rel_type': rel_type
                    }
                
                count += 1
                
                # Process batches
                if len(paper_batch) >= paper_batch_size:
                    # Create Paper nodes
                    print(f"Creating {len(paper_batch)} Paper nodes...")
                    with driver.session() as session:
                        for paper in paper_batch:
                            session.run(
                                'MERGE (p:Paper {id: $paper_id}) '
                                'SET p.title = $title, p.url = $abs_url',
                                paper_id=paper['paper_id'], title=paper['title'], abs_url=paper['abs_url']
                            )
                    paper_batch = []
                
                # Process node batch if we have enough nodes
                if len(all_nodes) >= node_batch_size:
                    process_node_batch(driver, all_nodes)
                    all_nodes = {}
                
                # Process relationship batch if we have enough relationships
                if len(all_relationships) >= rel_batch_size:
                    process_relationship_batch(driver, all_relationships)
                    all_relationships = {}
                
                if count % 50 == 0:
                    print(f"Processed {count} knowledge graphs, collected {len(all_nodes)} entities and {len(all_relationships)} relationships")
            
            except Exception as e:
                print(f"Error processing record {i}: {e}")
                continue
        
        # Process remaining batches
        if paper_batch:
            print(f"Creating {len(paper_batch)} Paper nodes...")
            with driver.session() as session:
                for paper in paper_batch:
                    session.run(
                        'MERGE (p:Paper {id: $paper_id}) '
                        'SET p.title = $title, p.url = $abs_url',
                        paper_id=paper['paper_id'], title=paper['title'], abs_url=paper['abs_url']
                    )
        
        if all_nodes:
            process_node_batch(driver, all_nodes)
        
        if all_relationships:
            process_relationship_batch(driver, all_relationships)
    
    print(f"Successfully imported {count} knowledge graphs from JSON")


def process_node_batch(driver, nodes_dict):
    """Process a batch of nodes"""
    print(f"Creating {len(nodes_dict)} Entity nodes...")
    
    with driver.session() as session:
        # Create Entity nodes
        for node_id, node in nodes_dict.items():
            labels = node['labels']
            node_labels = ':'.join(labels) or 'Entity'
            papers_list = list(node['papers'])
            
            # Create or update the Entity node
            session.run(
                f'MERGE (n:{node_labels} {{id: $node_id}}) '
                'ON CREATE SET n.name = $name, n.description = $description, n.papers = $papers '
                'ON MATCH SET n.name = $name, n.description = $description, n.papers = $papers',
                node_id=node_id, name=node['name'], description=node['description'], papers=papers_list
            )
            
            # Create MENTIONS relationships
            for paper_id in papers_list:
                session.run(
                    'MATCH (p:Paper {id: $paper_id}) '
                    'MATCH (n {id: $node_id}) '
                    'MERGE (p)-[:MENTIONS]->(n)',
                    paper_id=paper_id, node_id=node_id
                )


def process_relationship_batch(driver, relationships_dict):
    """Process a batch of relationships"""
    print(f"Creating {len(relationships_dict)} relationships between entities...")
    
    with driver.session() as session:
        for rel_key, rel in relationships_dict.items():
            source_id = rel['source_id']
            target_id = rel['target_id']
            rel_type = rel['rel_type']
            
            try:
                # Create dynamic relationship
                query = f"""
                MATCH (source {{id: $source_id}})
                MATCH (target {{id: $target_id}})
                MERGE (source)-[r:`{rel_type}`]->(target)
                """
                session.run(query, source_id=source_id, target_id=target_id)
            except Exception as e:
                print(f"Error creating relationship {source_id}-[{rel_type}]->{target_id}: {e}")
                # Fall back to RELATES_TO relationship
                try:
                    session.run(
                        'MATCH (source {id: $source_id}) '
                        'MATCH (target {id: $target_id}) '
                        'MERGE (source)-[r:RELATES_TO]->(target)',
                        source_id=source_id, target_id=target_id
                    )
                except Exception as e2:
                    print(f"Error creating fallback relationship: {e2}")


def verify_import(driver):
    """Verify the imported data"""
    print("\nVerifying import:")
    
    with driver.session() as session:
        # Count nodes
        result = session.run("MATCH (n) RETURN labels(n) AS labels, count(n) AS count")
        for record in result:
            print(f"Node type: {record['labels']} - Count: {record['count']}")
        
        # Count relationships
        result = session.run("MATCH ()-[r]->() RETURN type(r) AS type, count(r) AS count")
        for record in result:
            print(f"Relationship type: {record['type']} - Count: {record['count']}")


def main():
    parser = argparse.ArgumentParser(description='Import Knowledge Graph to Neo4j')
    parser.add_argument('--uri', type=str, default='bolt://localhost:7687', help='Neo4j URI')
    parser.add_argument('--user', type=str, default='neo4j', help='Neo4j username')
    parser.add_argument('--password', type=str, required=False, help='Neo4j password')
    parser.add_argument('--no-auth', action='store_true', help='Try to connect without authentication')
    parser.add_argument('--method', type=str, choices=['json', 'csv'], default='csv', 
                        help='Import method: json or csv (default: csv)')
    parser.add_argument('--limit', type=int, default=None, help='Limit number of knowledge graphs to import')
    parser.add_argument('--use-env', action='store_true', help='Use credentials from .env file')
    
    args = parser.parse_args()
    
    # Connect to Neo4j
    if args.no_auth:
        driver = connect_to_neo4j(args.uri)
    elif args.use_env:
        # Get credentials from environment variables
        neo4j_user = os.getenv("NEO4J_USERNAME_LOCAL", "neo4j")
        neo4j_password = os.getenv("NEO4J_PASSWORD_LOCAL")
        if not neo4j_password:
            print("WARNING: NEO4J_PASSWORD_LOCAL not found in .env file")
            neo4j_password = args.password  # Fall back to command line password
        
        print(f"Using Neo4j credentials from .env file: user={neo4j_user}")
        driver = connect_to_neo4j(args.uri, neo4j_user, neo4j_password)
    else:
        driver = connect_to_neo4j(args.uri, args.user, args.password)
    
    # Create constraints
    create_constraints(driver)
    
    # Import data
    if args.method == 'csv':
        csv_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'neo4j_import')
        import_from_csv(driver, csv_dir)
    else:
        json_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
                               'data', 'mongodb', 'knowledge_graphs.json')
        import_from_json(driver, json_file, args.limit)
    
    # Verify import
    verify_import(driver)
    
    # Close the driver
    driver.close()
    print("\nImport completed successfully!")


if __name__ == "__main__":
    main()
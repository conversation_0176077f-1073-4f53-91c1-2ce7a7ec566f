// Neo4j Browser Import Script

// 1. Create constraints
CREATE CONSTRAINT entity_id_constraint IF NOT EXISTS FOR (n:Entity) REQUIRE n.id IS UNIQUE;
CREATE CONSTRAINT paper_id_constraint IF NOT EXISTS FOR (n:Paper) REQUIRE n.id IS UNIQUE;

// 2. Make sure the CSV files are in the Neo4j import directory
// You may need to copy the CSV files from /Users/<USER>/Development/fastmoatless/neo4j_import/
// to the Neo4j import directory (usually $NEO4J_HOME/import/)

// 3. Import Entity nodes
LOAD CSV WITH HEADERS FROM 'file:///nodes.csv' AS row
MERGE (n:Entity {id: row.id})
ON CREATE SET n.name = row.name, 
             n.papers = split(row.papers, ';');

// 4. Create Paper nodes and MENTIONS relationships
LOAD CSV WITH HEADERS FROM 'file:///nodes.csv' AS row
WITH row, split(row.papers, ';') AS paperIds
UNWIND paperIds AS paperId
WHERE paperId <> ''
MERGE (p:Paper {id: paperId})
WITH p, row
MATCH (e:Entity {id: row.id})
MERGE (p)-[:MENTIONS]->(e);

// 5. Create entity relationships 
LOAD CSV WITH HEADERS FROM 'file:///relationships.csv' AS row
MATCH (source:Entity {id: row.source})
MATCH (target:Entity {id: row.target})
CALL apoc.create.relationship(source, row.type, {}, target) YIELD rel
RETURN count(rel);

// 6. If APOC is not available, use this alternative
LOAD CSV WITH HEADERS FROM 'file:///relationships.csv' AS row
MATCH (source:Entity {id: row.source})
MATCH (target:Entity {id: row.target})
WITH source, target, row.type AS type
CALL apoc.do.when(
  type = 'RELATES_TO',
  'MERGE (source)-[:RELATES_TO]->(target) RETURN count(*) as count',
  'CALL apoc.create.relationship(source, type, {}, target) YIELD rel RETURN count(rel) as count',
  {source: source, target: target, type: type}
) YIELD value
RETURN sum(value.count);

// 7. If you have issues with APOC, here's a manual alternative (but it only supports a few relationship types)
LOAD CSV WITH HEADERS FROM 'file:///relationships.csv' AS row
MATCH (source:Entity {id: row.source})
MATCH (target:Entity {id: row.target})
WITH source, target, row.type AS type
CALL apoc.do.case([
  type = 'RELATES_TO', 'MERGE (source)-[:RELATES_TO]->(target) RETURN true as created',
  type = 'MENTIONS', 'MERGE (source)-[:MENTIONS]->(target) RETURN true as created',
  type = 'INCLUDES', 'MERGE (source)-[:INCLUDES]->(target) RETURN true as created',
  type = 'USES', 'MERGE (source)-[:USES]->(target) RETURN true as created',
  type = 'IMPLEMENTS', 'MERGE (source)-[:IMPLEMENTS]->(target) RETURN true as created'
], 
'MERGE (source)-[:RELATES_TO]->(target) RETURN true as created', 
{source: source, target: target}) 
YIELD value
RETURN count(value.created);

// 8. Verify the import
MATCH (n) RETURN labels(n) AS labels, count(n) AS count;
MATCH ()-[r]->() RETURN type(r) AS type, count(r) AS count;

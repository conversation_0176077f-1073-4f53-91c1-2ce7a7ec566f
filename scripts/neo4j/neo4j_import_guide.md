# Neo4j Import Guide

## Prerequisites

1. Make sure Neo4j is running at bolt://localhost:7687
2. Make sure you know the username/password for your Neo4j instance
3. Ensure the CSV files are accessible by Neo4j

## Steps to Import the Knowledge Graph

### 1. Copy CSV files to Neo4j import directory

First, you need to copy the CSV files to Neo4j's import directory:

```bash
# For Neo4j Desktop, the import directory is typically:
# Mac: ~/Library/Application Support/Neo4j Desktop/Application/neo4jDatabases/<database-id>/installation-<version>/import/
# Windows: C:\Users\<USER>\.Neo4jDesktop\neo4jDatabases\<database-id>\installation-<version>\import\
# Linux: ~/.config/Neo4j Desktop/Application/neo4jDatabases/<database-id>/installation-<version>/import/

# Copy the CSV files
cp /Users/<USER>/Development/fastmoatless/neo4j_import/*.csv <your-neo4j-import-directory>/
```

Alternatively, you can set Neo4j's configuration to allow import from any directory:

1. Open Neo4j Desktop
2. Select your database
3. Click on "..." -> "Settings" -> "dbms.directories.import"
4. Set it to allow import from any directory (e.g., "import")
5. Restart your database

### 2. Open Neo4j Browser

1. Open Neo4j Desktop
2. Start your database
3. Click "Open" to launch Neo4j Browser
4. Connect with your username and password (usually neo4j/your-password)

### 3. Run the Import Script

Copy the content from either:
- `neo4j_import_commands.txt` (if APOC is installed)
- `neo4j_simple_import.txt` (no APOC required)

Paste each statement one by one into the Neo4j Browser command line and execute them. You can also run them all at once, but running them separately helps with debugging any issues.

### 4. Verify the Import

After running the import scripts, verify that the data was imported correctly:

```cypher
// Count nodes by label
MATCH (n) RETURN labels(n) AS labels, count(n) AS count;

// Count relationships by type
MATCH ()-[r]->() RETURN type(r) AS type, count(r) AS count;

// View a sample of the knowledge graph
MATCH (p:Paper)-[:MENTIONS]->(e:Entity)
RETURN p, e LIMIT 25;

// View relationships between entities
MATCH (e1:Entity)-[r]->(e2:Entity)
RETURN e1, r, e2 LIMIT 25;
```

### Troubleshooting

1. If you encounter authentication issues:
   - Make sure you're using the correct username/password
   - Reset your password in Neo4j Desktop if necessary

2. If APOC procedures are not available:
   - Use the simple import script that doesn't require APOC
   - Or install APOC from Neo4j Desktop: Click "..." -> "Plugins" -> Install APOC

3. If CSV files cannot be found:
   - Double-check that the CSV files are in Neo4j's import directory
   - Make sure Neo4j has permission to read those files

4. If you encounter errors with relationship types:
   - The simple script includes many common relationship types, but if you encounter new ones, add them to the CASE statement

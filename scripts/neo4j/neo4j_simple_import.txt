// Neo4j Simple Import Script (No APOC required)

// 1. Create constraints
CREATE CONSTRAINT entity_id_constraint IF NOT EXISTS FOR (n:Entity) REQUIRE n.id IS UNIQUE;
CREATE CONSTRAINT paper_id_constraint IF NOT EXISTS FOR (n:Paper) REQUIRE n.id IS UNIQUE;

// 2. Import Entity nodes
LOAD CSV WITH HEADERS FROM 'file:///nodes.csv' AS row
MERGE (n:Entity {id: row.id})
ON CREATE SET n.name = row.name, 
             n.papers = split(row.papers, ';');

// 3. Create Paper nodes and MENTIONS relationships
LOAD CSV WITH HEADERS FROM 'file:///nodes.csv' AS row
WITH row, split(row.papers, ';') AS paperIds
UNWIND paperIds AS paperId
WHERE paperId <> ''
MERGE (p:Paper {id: paperId})
WITH p, row
MATCH (e:Entity {id: row.id})
MERGE (p)-[:MENTIONS]->(e);

// 4. Create entity relationships using dynamic relationships
// This is more verbose but doesn't require APOC
LOAD CSV WITH HEADERS FROM 'file:///relationships.csv' AS row
MATCH (source:Entity {id: row.source})
MATCH (target:Entity {id: row.target})
WITH source, target, row.type AS type
CASE type
  WHEN 'RELATES_TO' THEN MERGE (source)-[:RELATES_TO]->(target)
  WHEN 'RETRIEVES' THEN MERGE (source)-[:RETRIEVES]->(target)
  WHEN 'AUGMENTS' THEN MERGE (source)-[:AUGMENTS]->(target)
  WHEN 'ENHANCES' THEN MERGE (source)-[:ENHANCES]->(target)
  WHEN 'FINDS' THEN MERGE (source)-[:FINDS]->(target)
  WHEN 'ACCESSES' THEN MERGE (source)-[:ACCESSES]->(target)
  WHEN 'PROVIDES' THEN MERGE (source)-[:PROVIDES]->(target)
  WHEN 'GENERATES' THEN MERGE (source)-[:GENERATES]->(target)
  WHEN 'PROCESSES' THEN MERGE (source)-[:PROCESSES]->(target)
  WHEN 'UNDERSTANDS' THEN MERGE (source)-[:UNDERSTANDS]->(target)
  WHEN 'ENCODES' THEN MERGE (source)-[:ENCODES]->(target)
  WHEN 'TRANSFORMS' THEN MERGE (source)-[:TRANSFORMS]->(target)
  WHEN 'PRETRAINED_ON' THEN MERGE (source)-[:PRETRAINED_ON]->(target)
  WHEN 'FINE-TUNED_ON' THEN MERGE (source)-[:FINE-TUNED_ON]->(target)
  WHEN 'TRAINED_ON' THEN MERGE (source)-[:TRAINED_ON]->(target)
  WHEN 'FINE-TUNED' THEN MERGE (source)-[:FINE-TUNED]->(target)
  WHEN 'IMPROVES' THEN MERGE (source)-[:IMPROVES]->(target)
  WHEN 'ADAPTS' THEN MERGE (source)-[:ADAPTS]->(target)
  WHEN 'SPECIALIZES' THEN MERGE (source)-[:SPECIALIZES]->(target)
  WHEN 'USES' THEN MERGE (source)-[:USES]->(target)
  WHEN 'REQUIRES' THEN MERGE (source)-[:REQUIRES]->(target)
  WHEN 'PRODUCES' THEN MERGE (source)-[:PRODUCES]->(target)
  WHEN 'REDUCES' THEN MERGE (source)-[:REDUCES]->(target)
  WHEN 'CAUSES' THEN MERGE (source)-[:CAUSES]->(target)
  WHEN 'AFFECTS' THEN MERGE (source)-[:AFFECTS]->(target)
  WHEN 'GUIDES' THEN MERGE (source)-[:GUIDES]->(target)
  WHEN 'DIRECTS' THEN MERGE (source)-[:DIRECTS]->(target)
  WHEN 'SPECIFIES' THEN MERGE (source)-[:SPECIFIES]->(target)
  WHEN 'REINFORCES' THEN MERGE (source)-[:REINFORCES]->(target)
  WHEN 'ALIGNS' THEN MERGE (source)-[:ALIGNS]->(target)
  WHEN 'TRAINS' THEN MERGE (source)-[:TRAINS]->(target)
  WHEN 'ENSURES' THEN MERGE (source)-[:ENSURES]->(target)
  WHEN 'COMBINES' THEN MERGE (source)-[:COMBINES]->(target)
  WHEN 'INTEGRATES' THEN MERGE (source)-[:INTEGRATES]->(target)
  WHEN 'EVALUATES' THEN MERGE (source)-[:EVALUATES]->(target)
  WHEN 'MEASURES' THEN MERGE (source)-[:MEASURES]->(target)
  WHEN 'TESTS' THEN MERGE (source)-[:TESTS]->(target)
  ELSE MERGE (source)-[:RELATES_TO]->(target)
END;

// 5. Verify the import
MATCH (n) RETURN labels(n) AS labels, count(n) AS count;
MATCH ()-[r]->() RETURN type(r) AS type, count(r) AS count;

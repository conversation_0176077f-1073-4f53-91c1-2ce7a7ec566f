#!/usr/bin/env python3

import os
import argparse
from neo4j import GraphDatabase
import importlib.util
import dedupe
from dotenv import load_dotenv, find_dotenv

load_dotenv(find_dotenv())


def get_neo4j_driver(uri, user, password):
    """Create and return a Neo4j driver."""
    return GraphDatabase.driver(uri, auth=(user, password))


def fetch_entities(driver, entity_type=None):
    """
    Retrieve entities from Neo4j.
    
    Fetches entities of a specific type (or all entities if type not specified)
    and returns them as a dictionary keyed by entity ID.
    
    Args:
        driver: Neo4j driver instance
        entity_type: Optional entity type/label to filter by (e.g., 'Person', 'Organization')
        
    Returns:
        Dictionary of entities keyed by entity ID
    """
    if entity_type:
        query = f"MATCH (e:{entity_type}) RETURN e.id AS id, e.name AS name, e.description AS description"
    else:
        query = "MATCH (e:Entity) RETURN e.id AS id, e.name AS name, e.description AS description"
    
    entities = {}
    with driver.session() as session:
        for record in session.run(query):
            entity_id = record["id"]
            if entity_id:  # Skip entities with no ID
                entities[entity_id] = {
                    "name": record["name"] or "",
                    "description": record["description"] or ""
                }
    return entities


def deduplicate_entities(data):
    """
    Uses dedupe to identify duplicate entities.
    
    Compares entity names and descriptions to find potential duplicates.
    """
    # Define the fields to compare - name and description
    fields = [
        {'field': 'name', 'type': 'String'},
        {'field': 'description', 'type': 'Text'}
    ]
    deduper = dedupe.Dedupe(fields)
    
    # Sample the data for training - adjust sample size based on dataset
    deduper.sample(data, 15000)
    
    training_file = 'dedupe_training.json'
    if os.path.exists(training_file):
        print("Loading existing training data...")
        with open(training_file, 'rb') as tf:
            deduper.readTraining(tf)
    else:
        print("Starting active labeling... (enter 'y' for duplicates, 'n' for distinct pairs)")
        dedupe.consoleLabel(deduper)
        deduper.train()
        with open(training_file, 'w') as tf:
            deduper.writeTraining(tf)
    
    # Determine a score threshold - adjust recall_weight based on preference
    # Higher recall_weight means more duplicates will be identified (with potentially more false positives)
    threshold = deduper.threshold(data, recall_weight=1.5)
    clustered_dupes = deduper.match(data, threshold)
    
    return clustered_dupes


def merge_duplicate_entities(driver, clusters, entity_type=None):
    """
    For each duplicate cluster, merge all nodes into a canonical node.
    
    Uses APOC's mergeNodes procedure to combine duplicate entities while
    preserving relationships.
    
    Args:
        driver: Neo4j driver instance
        clusters: List of duplicate clusters from dedupe
        entity_type: Optional entity type/label to include in the merge query
    """
    entity_label = f":{entity_type}" if entity_type else ""
    
    with driver.session() as session:
        for i, cluster in enumerate(clusters):
            record_ids, score = cluster
            
            # Skip clusters with only one entity
            if len(record_ids) <= 1:
                continue
                
            # Choose the first record in the cluster as the canonical record
            canonical = record_ids[0]
            duplicates = record_ids[1:]
            
            for dup in duplicates:
                try:
                    # Merge duplicate entity into canonical entity
                    merge_query = f"""
                    MATCH (canonical{entity_label} {{id: $canonical_id}}), (dup{entity_label} {{id: $dup_id}})
                    CALL apoc.refactor.mergeNodes([canonical, dup], {{properties:"combine"}})
                    YIELD node
                    RETURN node
                    """
                    session.run(merge_query, canonical_id=canonical, dup_id=dup)
                    print(f"Merged entity {dup} into canonical entity {canonical}")
                except Exception as e:
                    print(f"Error merging {dup} into {canonical}: {e}")
            
            # Print progress every 10 clusters
            if (i + 1) % 10 == 0:
                print(f"Processed {i + 1}/{len(clusters)} duplicate clusters")


def create_new_graph(driver, source_db, target_db):
    """
    Create a new graph database from an existing one.
    
    Args:
        driver: Neo4j driver instance
        source_db: Source database name
        target_db: Target database name
    """
    try:
        with driver.session() as session:
            # Check if target database exists and create if not
            result = session.run("SHOW DATABASES")
            databases = [record["name"] for record in result]
            
            if target_db in databases:
                print(f"Target database '{target_db}' already exists.")
                return False
            
            # Create new database
            session.run(f"CREATE DATABASE {target_db}")
            print(f"Created new database '{target_db}'")
            
            # Copy data from source to target - using APOC plugin's export/import capability
            copy_query = f"""
            CALL apoc.periodic.iterate(
                'MATCH (n) RETURN n',
                'CREATE (m) SET m = n SET m.source_id = id(n)',
                {{batchSize:1000, parallel:true}}
            )
            """
            
            # First need to connect to target db
            with GraphDatabase.driver(driver.get_uri(), auth=driver.get_auth()) as target_driver:
                with target_driver.session(database=target_db) as target_session:
                    print(f"Copying nodes from '{source_db}' to '{target_db}'...")
                    target_session.run(copy_query)
                    
                    # Copy relationships
                    copy_rels_query = f"""
                    CALL apoc.periodic.iterate(
                        'MATCH (a)-[r]->(b) RETURN a, r, b',
                        'MATCH (a1) WHERE a1.source_id = a.source_id 
                         MATCH (b1) WHERE b1.source_id = b.source_id
                         CREATE (a1)-[r1:{r.type}]->(b1) SET r1 = r',
                        {{batchSize:1000, parallel:false}}
                    )
                    """
                    print(f"Copying relationships from '{source_db}' to '{target_db}'...")
                    target_session.run(copy_rels_query)
                    
                    # Remove temporary source_id property
                    cleanup_query = """
                    CALL apoc.periodic.iterate(
                        'MATCH (n) WHERE EXISTS(n.source_id) RETURN n',
                        'REMOVE n.source_id',
                        {batchSize:1000, parallel:true}
                    )
                    """
                    print("Cleaning up temporary properties...")
                    target_session.run(cleanup_query)
            
            print(f"Successfully created and populated database '{target_db}' from '{source_db}'")
            return True
            
    except Exception as e:
        print(f"Error creating new graph: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Deduplicate entities in Neo4j knowledge graph')
    parser.add_argument('--local', action='store_true', help='Use local Neo4j database')
    parser.add_argument('--uri', type=str, help='Neo4j URI (defaults to localhost for local, production for non-local)')
    parser.add_argument('--user', type=str, default='neo4j', help='Neo4j username')
    parser.add_argument('--password', type=str, default="neo4j", help='Neo4j password')
    parser.add_argument('--entity-type', type=str, help='Entity type to deduplicate (e.g., Person, Organization)')
    parser.add_argument('--new-graph', action='store_true', help='Create a new graph for deduplication')
    parser.add_argument('--source-db', type=str, default='neo4j', help='Source database name')
    parser.add_argument('--target-db', type=str, default='deduped', help='Target database name for new graph')
    
    args = parser.parse_args()
    
    # Set default URI based on local flag
    if not args.uri:
        args.uri = os.environ.get("NEO4J_URL_LOCAL") if args.local else os.environ.get("NEO4J_URL_PRIVATE")
    
    print(f"Connecting to Neo4j at {args.uri}...")
    driver = get_neo4j_driver(args.uri, args.user, args.password)
    
    if args.new_graph:
        print(f"Creating new graph '{args.target_db}' from '{args.source_db}'...")
        if not create_new_graph(driver, args.source_db, args.target_db):
            print("Failed to create new graph. Exiting.")
            driver.close()
            return
    
    # Fetch entities from Neo4j
    print(f"Fetching entities from Neo4j{' of type '+args.entity_type if args.entity_type else ''}...")
    data = fetch_entities(driver, args.entity_type)
    print(f"Fetched {len(data)} entities from Neo4j.")
    
    if len(data) == 0:
        print("No entities found. Check your database connection and entity type.")
        driver.close()
        return
    
    # Perform entity resolution to identify duplicate clusters
    print("Identifying duplicate entities...")
    clusters = deduplicate_entities(data)
    print(f"Identified {len(clusters)} duplicate clusters.")
    
    # If duplicates are found, merge them in the database
    if clusters:
        print("Starting merge process...")
        merge_duplicate_entities(driver, clusters, args.entity_type)
        print("Duplicate merging complete!")
    else:
        print("No duplicates found.")
    
    driver.close()


if __name__ == "__main__":
    main()
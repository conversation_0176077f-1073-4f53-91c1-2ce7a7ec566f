#!/bin/bash

# <PERSON><PERSON>t to set up cron jobs for regular test updates and runs
# This should be run once on the development machine

echo "Setting up cron jobs for regular test maintenance..."

# Get the absolute path to the project directory
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# Create a temporary file for the crontab
TEMP_CRONTAB="$(mktemp)"
trap 'rm -f "$TEMP_CRONTAB"' EXIT

# Export the current crontab
crontab -l > "$TEMP_CRONTAB" 2>/dev/null || echo "" > "$TEMP_CRONTAB"

# Add cron job to update tests weekly (Sunday at 1 AM)
if ! grep -q "update_tests.sh" "$TEMP_CRONTAB"; then
  echo "# Moatless: Update tests weekly (Sunday at 1 AM)" >> "$TEMP_CRONTAB"
  echo "0 1 * * 0 cd $PROJECT_DIR && ./scripts/update_tests.sh >> $PROJECT_DIR/logs/test_updates.log 2>&1" >> "$TEMP_CRONTAB"
  echo "Added weekly test update job"
fi

# Add cron job to run tests daily (every day at 3 AM)
if ! grep -q "run_tests.py" "$TEMP_CRONTAB"; then
  echo "# Moatless: Run tests daily (every day at 3 AM)" >> "$TEMP_CRONTAB"
  echo "0 3 * * * cd $PROJECT_DIR && python tests/run_tests.py >> $PROJECT_DIR/logs/daily_tests.log 2>&1" >> "$TEMP_CRONTAB"
  echo "Added daily test run job"
fi

# Create logs directory if it doesn't exist
mkdir -p "$PROJECT_DIR/logs"

# Install the updated crontab
crontab "$TEMP_CRONTAB"

echo "Cron jobs set up successfully!"
echo "Tests will be updated weekly and run daily."
echo "Logs will be saved to the logs/ directory."

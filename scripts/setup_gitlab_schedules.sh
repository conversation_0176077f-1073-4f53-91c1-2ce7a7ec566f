#!/bin/bash

# <PERSON>ript to set up GitLab CI/CD schedules using the GitLab API
# This requires a GitLab personal access token with API access

echo "Setting up GitLab CI/CD schedules for regular test runs..."

# Check if the required variables are set
if [ -z "$GITLAB_TOKEN" ]; then
  echo "Error: GITLAB_TOKEN environment variable is not set."
  echo "Please set it with: export GITLAB_TOKEN=your_personal_access_token"
  exit 1
fi

if [ -z "$GITLAB_PROJECT_ID" ]; then
  echo "Error: GITLAB_PROJECT_ID environment variable is not set."
  echo "Please set it with: export GITLAB_PROJECT_ID=your_project_id"
  echo "You can find your project ID in the GitLab project settings."
  exit 1
fi

# GitLab API URL
GITLAB_API_URL="https://gitlab.com/api/v4"

# Create daily test schedule (midnight UTC)
echo "Creating daily test schedule..."
curl --request POST \
  --header "PRIVATE-TOKEN: $GITLAB_TOKEN" \
  --header "Content-Type: application/json" \
  --data '{
    "description": "Daily tests",
    "ref": "main",
    "cron": "0 0 * * *",
    "cron_timezone": "UTC",
    "active": true
  }' \
  "$GITLAB_API_URL/projects/$GITLAB_PROJECT_ID/pipeline_schedules"

# Create weekly test update schedule (Sunday at 1 AM UTC)
echo "Creating weekly test update schedule..."
curl --request POST \
  --header "PRIVATE-TOKEN: $GITLAB_TOKEN" \
  --header "Content-Type: application/json" \
  --data '{
    "description": "Weekly test updates",
    "ref": "main",
    "cron": "0 1 * * 0",
    "cron_timezone": "UTC",
    "active": true,
    "variables": [
      {
        "key": "RUN_TEST_UPDATES",
        "value": "true"
      }
    ]
  }' \
  "$GITLAB_API_URL/projects/$GITLAB_PROJECT_ID/pipeline_schedules"

echo "GitLab CI/CD schedules set up successfully!"
echo "You can view and manage them in the GitLab CI/CD Schedules section of your project."
echo ""
echo "Usage instructions:"
echo "1. Go to your GitLab project"
echo "2. Navigate to CI/CD > Schedules"
echo "3. Verify that the schedules were created correctly"
echo "4. You can manually trigger a scheduled pipeline by clicking 'Play'"

#\!/usr/bin/env python3
"""
Check if in-memory MongoDB is consistently working across different script runs.
"""

import os
import sys
from dotenv import load_dotenv

# Add the current directory to the path so we can import from src
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), ".."))

from src.db.mongo import setup_mongo_client, setup_collections

def check_consistency():
    """Check if data persists within different setups of in-memory MongoDB"""
    print("\n======== CHECKING IN-MEMORY DB CONSISTENCY ========")
    
    # Set up first connection
    print("Setting up first connection...")
    mongo_client1 = setup_mongo_client(local_mode=True)
    collections1 = setup_collections(mongo_client1)
    
    # Get all collection names to see what's available
    print(f"Available collections in first connection: {mongo_client1['papers'].list_collection_names()}")
    
    # Add test document to summaries collection
    print("Adding test document to summaries collection in first connection...")
    collections1['summaries'].insert_one({"test_id": "123", "title": "Test Document", "entities": [["test", "category"]]})
    
    # Check if document exists
    doc1 = collections1['summaries'].find_one({"test_id": "123"})
    print(f"Document in first connection: {doc1}")
    
    # Set up second connection
    print("\nSetting up second connection...")
    mongo_client2 = setup_mongo_client(local_mode=True)
    collections2 = setup_collections(mongo_client2)
    
    # Get all collection names in second connection
    print(f"Available collections in second connection: {mongo_client2['papers'].list_collection_names()}")
    
    # Check if document exists in second connection
    doc2 = collections2['summaries'].find_one({"test_id": "123"})
    print(f"Document in second connection: {doc2}")
    
    # Count documents in summaries collection
    count1 = collections1['summaries'].count_documents({})
    count2 = collections2['summaries'].count_documents({})
    print(f"\nDocument count in summaries collection (first connection): {count1}")
    print(f"Document count in summaries collection (second connection): {count2}")
    
    # Compare memory addresses
    print("\nMemory addresses:")
    print(f"mongo_client1: {id(mongo_client1)}")
    print(f"mongo_client2: {id(mongo_client2)}")
    print(f"collections1['summaries']: {id(collections1['summaries'])}")
    print(f"collections2['summaries']: {id(collections2['summaries'])}")
    
    return doc1, doc2

if __name__ == "__main__":
    # Load environment variables
    load_dotenv()
    
    # Set local mode
    os.environ['LOCAL_MODE'] = 'true'
    
    # Check consistency
    check_consistency()

#\!/usr/bin/env python3
"""
Combined testing script for entity resolution that ensures consistent database access.
"""

import os
import sys
import asyncio
import argparse
from openai import OpenAI
from dotenv import load_dotenv

# Add the current directory to the path so we can import from src
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), ".."))

# Import necessary modules
from src.db.mongo import setup_mongo_client, setup_collections, set_collections, create_indexes
from src.services.entity_resolution import EntityResolutionService
from src.models.entity import EntityResolutionResponse

# Sample test data
TEST_SUMMARIES = [
    {
        "abs_url": "http://arxiv.org/abs/test1",
        "title": "Test Paper for Entity Resolution",
        "summary": "This is a test summary for entity resolution",
        "entities": [
            {"value": "deep learning", "category": "Methods & Techniques"},
            {"value": "neural networks", "category": "Models & Architectures"},
            {"value": "computer vision", "category": "Applications & Use Cases"}
        ]
    },
    {
        "abs_url": "http://arxiv.org/abs/test2",
        "title": "Another Test Paper for Entity Resolution",
        "summary": "This is another test summary for entity resolution",
        "entities": [
            {"value": "machine learning", "category": "Methods & Techniques"},
            {"value": "reinforcement learning", "category": "Methods & Techniques"},
            {"value": "robotics", "category": "Applications & Use Cases"}
        ]
    },
    {
        "abs_url": "http://arxiv.org/abs/test3",
        "title": "Third Test Paper for Entity Resolution",
        "summary": "This is a third test summary for entity resolution",
        "entities": [
            {"value": "deep learning", "category": "Methods & Techniques"},
            {"value": "convolutional neural networks", "category": "Models & Architectures"},
            {"value": "image recognition", "category": "Applications & Use Cases"}
        ]
    }
]

async def run_combined_test(force_update=True, local_mode=True):
    """
    Combined script to add test data and run entity resolution.
    Ensures consistent database access by using the same connection throughout.
    
    Args:
        force_update: Whether to force a full update of all entities
        local_mode: Whether to use in-memory MongoDB for local testing
    """
    print("\n======== COMBINED TEST ========")
    print(f"Force update: {force_update}")
    print(f"Local mode: {local_mode}")
    
    # Setup MongoDB client
    mongo_client = setup_mongo_client(local_mode)
    collections = setup_collections(mongo_client)
    
    # Set global collections to ensure consistent access
    set_collections(collections)
    
    # Create indexes
    create_indexes(collections)
    
    # Debug information
    db = mongo_client['papers']
    print(f"Available collections: {db.list_collection_names()}")
    
    # Clear existing test data
    print("Clearing existing test data...")
    collections['summaries'].delete_many({
        "abs_url": {"$in": [summary["abs_url"] for summary in TEST_SUMMARIES]}
    })
    
    # Insert test summaries
    print("Adding test summaries...")
    for summary in TEST_SUMMARIES:
        collections['summaries'].insert_one(summary)
    
    # Check if we have summaries
    summaries_count = collections['summaries'].count_documents({})
    print(f"Total summaries in collection: {summaries_count}")
    
    # Print a sample summary
    if summaries_count > 0:
        sample = collections['summaries'].find_one({})
        print(f"Sample summary: {sample.get('title')}")
        if 'entities' in sample:
            print(f"  Entities: {len(sample.get('entities', []))}")
            print(f"  First entity: {sample.get('entities')[0]}")
        else:
            print("  No entities found in the sample")
    
    # Setup OpenAI client with Gemini API
    openai_client = OpenAI(
        api_key=os.getenv("GEMINI_API_KEY", "mock_key"),
        base_url=os.getenv("GEMINI_BASE_URL", "https://fake.api.url")
    )
    
    # Add mock functionality for testing
    openai_client.beta = type('obj', (), {})
    openai_client.beta.chat = type('obj', (), {})
    openai_client.beta.chat.completions = type('obj', (), {
        'parse': lambda **kwargs: type('obj', (), {
            'choices': [
                type('obj', (), {
                    'message': type('obj', (), {
                        'content': '{"resolved_entities": [{"canonical_id": "deep_learning", "canonical_name": "Deep Learning", "variant_ids": ["deep learning"], "entity_type": "Methods & Techniques", "description": "A machine learning technique"}]}'
                    })
                })
            ]
        })
    })
    
    # Also mock regular chat completions
    openai_client.chat = type('obj', (), {})
    openai_client.chat.completions = type('obj', (), {
        'create': lambda **kwargs: type('obj', (), {
            'choices': [
                type('obj', (), {
                    'message': type('obj', (), {
                        'content': '{"resolved_entities": [{"canonical_id": "deep_learning", "canonical_name": "Deep Learning", "variant_ids": ["deep learning"], "entity_type": "Methods & Techniques", "description": "A machine learning technique"}]}'
                    })
                })
            ]
        })
    })
    
    # Initialize entity resolution service using the same collections
    entity_service = EntityResolutionService(collections, openai_client)
    
    # Run the entity resolution process
    print("Starting entity resolution process...")
    result = await entity_service.resolve_entities(force_update)
    
    print("\n======== ENTITY RESOLUTION COMPLETE ========")
    print(f"Status: {result.status}")
    print(f"Message: {result.message}")
    
    if hasattr(result, 'updated') and result.updated is not None:
        print(f"Updated entities: {result.updated}")
    if hasattr(result, 'new') and result.new is not None:
        print(f"New entities: {result.new}")
    if hasattr(result, 'total') and result.total is not None:
        print(f"Total entities processed: {result.total}")
    if hasattr(result, 'entities') and result.entities:
        print(f"Entities returned: {len(result.entities)}")
        for entity in result.entities:
            print(f"  - {entity.canonical_name} ({entity.entity_type})")
    
    return result

if __name__ == "__main__":
    # Load environment variables
    load_dotenv()
    
    # Set local mode environment variable
    os.environ['LOCAL_MODE'] = 'true'
    # Ensure we don't connect to real MongoDB for testing
    os.environ['MONGO_PRIVATE_URL'] = ''
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run combined test for entity resolution')
    parser.add_argument('--force', action='store_true', help='Force update of all entities')
    args = parser.parse_args()
    
    # Run the combined test
    asyncio.run(run_combined_test(args.force))

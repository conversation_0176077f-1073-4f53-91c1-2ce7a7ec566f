#\!/usr/bin/env python3
"""
FastMoatless - Test version with dummy data for local testing
Runs entirely in-memory with test data to verify entity resolution
"""
import asyncio
import os
import sys
import argparse
import json
import uuid
import time
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Import dependencies
from openai import OpenAI

# First, set up local mode to ensure all DB operations use in-memory implementations
os.environ['LOCAL_MODE'] = 'true'
os.environ['MONGO_PRIVATE_URL'] = ''
os.environ['NEO4J_URL_PRIVATE'] = ''
os.environ['GEMINI_API_KEY'] = 'fake_key'
os.environ['GEMINI_BASE_URL'] = 'https://fake.api.url'
os.environ['GEMINI_MODEL'] = 'gemini-1.5-pro'

# Import our modules
from src.db.mongo import setup_mongo_client, setup_collections, create_indexes, set_collections
from src.services.entity_resolution import EntityResolutionService

# Sample test data
TEST_SUMMARIES = [
    {
        "abs_url": "http://arxiv.org/abs/test1",
        "title": "Test Paper: Deep Learning for Computer Vision",
        "authors": ["Test Author 1", "Test Author 2"],
        "updated": "2023-03-01",
        "published": "2023-03-01",
        "abstract": "This is a test abstract about deep learning and computer vision.",
        "summary": "This is a test summary for entity resolution",
        "entities": [
            ["deep learning", "Algorithms & Learning Techniques"],
            ["neural networks", "Models & Architectures"],
            ["computer vision", "Applications & Use Cases"]
        ],
        "tags": ["deep learning", "computer vision"]
    },
    {
        "abs_url": "http://arxiv.org/abs/test2",
        "title": "Test Paper: Reinforcement Learning for Robotics",
        "authors": ["Test Author 3", "Test Author 4"],
        "updated": "2023-03-02",
        "published": "2023-03-02",
        "abstract": "This is a test abstract about reinforcement learning and robotics.",
        "summary": "This is another test summary for entity resolution",
        "entities": [
            ["machine learning", "Algorithms & Learning Techniques"],
            ["reinforcement learning", "Algorithms & Learning Techniques"],
            ["robotics", "Applications & Use Cases"]
        ],
        "tags": ["reinforcement learning", "robotics"]
    },
    {
        "abs_url": "http://arxiv.org/abs/test3",
        "title": "Test Paper: Transformer Models for NLP",
        "authors": ["Test Author 5", "Test Author 6"],
        "updated": "2023-03-03", 
        "published": "2023-03-03",
        "abstract": "This is a test abstract about transformer models and natural language processing.",
        "summary": "This is a third test summary for entity resolution",
        "entities": [
            ["transformer models", "Models & Architectures"],
            ["NLP", "Applications & Use Cases"],
            ["BERT", "Models & Architectures"],
            ["attention mechanism", "Algorithms & Learning Techniques"]
        ],
        "tags": ["transformers", "NLP"]
    }
]

SAMPLE_KNOWLEDGE_GRAPH = {
    "abs_url": "http://arxiv.org/abs/test_kg",
    "nodes": [
        {"id": "deep_learning", "label": "Deep Learning", "properties": {"category": "Algorithms & Learning Techniques"}},
        {"id": "computer_vision", "label": "Computer Vision", "properties": {"category": "Applications & Use Cases"}},
        {"id": "transformer", "label": "Transformer Models", "properties": {"category": "Models & Architectures"}},
        {"id": "nlp", "label": "Natural Language Processing", "properties": {"category": "Applications & Use Cases"}}
    ],
    "relationships": [
        {"start": {"id": "deep_learning"}, "end": {"id": "computer_vision"}, "label": "applied_to"},
        {"start": {"id": "transformer"}, "end": {"id": "nlp"}, "label": "used_for"}
    ]
}

class MockOpenAIResponse:
    """Mock OpenAI response for entity resolution"""
    def __init__(self, content):
        self.choices = [type('obj', (), {
            'message': type('obj', (), {
                'content': content
            })
        })]

class MockOpenAI:
    """Mock OpenAI client for entity resolution"""
    def __init__(self):
        # Create the nested structure needed for the OpenAI client
        self.beta = type('obj', (), {})
        self.beta.chat = type('obj', (), {})
        self.beta.chat.completions = type('obj', (), {
            'parse': self.mock_parse
        })
        
        self.chat = type('obj', (), {})
        self.chat.completions = type('obj', (), {
            'create': self.mock_create
        })
    
    def mock_parse(self, **kwargs):
        """Mock parse method that returns resolved entities"""
        # Extract entity values from the prompt
        prompt = kwargs.get('messages', [{}])[-1].get('content', '')
        entity_type = 'unknown'
        
        # Try to extract category from the prompt
        if "category '" in prompt:
            category_part = prompt.split("category '")[1]
            entity_type = category_part.split("'")[0]
        
        # Create resolved entities based on sample data
        if "deep learning" in prompt.lower():
            return MockOpenAIResponse(json.dumps({
                "resolved_entities": [
                    {
                        "canonical_id": "deep_learning",
                        "canonical_name": "Deep Learning",
                        "variant_ids": ["deep learning", "deep neural networks"],
                        "entity_type": entity_type,
                        "description": "A subset of machine learning using neural networks with multiple layers",
                        "properties": {}
                    }
                ]
            }))
        elif "transformer" in prompt.lower() or "bert" in prompt.lower():
            return MockOpenAIResponse(json.dumps({
                "resolved_entities": [
                    {
                        "canonical_id": "transformer_models",
                        "canonical_name": "Transformer Models",
                        "variant_ids": ["transformer models", "transformer", "BERT"],
                        "entity_type": entity_type,
                        "description": "Neural network architecture using self-attention mechanisms",
                        "properties": {}
                    }
                ]
            }))
        else:
            # Generic response for any other entities
            return MockOpenAIResponse(json.dumps({
                "resolved_entities": [
                    {
                        "canonical_id": "generic_entity",
                        "canonical_name": "Generic Entity",
                        "variant_ids": ["generic"],
                        "entity_type": entity_type,
                        "description": "A generic entity for testing",
                        "properties": {}
                    }
                ]
            }))
    
    def mock_create(self, **kwargs):
        """Same as parse but for the create method"""
        return self.mock_parse(**kwargs)

async def setup_test_environment():
    """Set up test environment with in-memory databases and test data"""
    print("\n======== SETTING UP TEST ENVIRONMENT ========")
    
    # Setup MongoDB client with in-memory implementation
    print("Setting up in-memory MongoDB...")
    mongo_client = setup_mongo_client(local_mode=True)
    collections = setup_collections(mongo_client)
    
    # Make collections globally accessible
    set_collections(collections)
    
    # Create indexes
    create_indexes(collections)
    
    # Clear existing data and add test data
    print("Adding test data to MongoDB...")
    
    # Clear summaries and knowledge graphs collections
    collections['summaries'].delete_many({})
    collections['knowledge_graphs'].delete_many({})
    
    # Create entity resolution and mappings collections if needed
    db = mongo_client['papers']
    if 'entity_resolution' not in collections:
        collections['entity_resolution'] = db['entity_resolution']
    if 'entity_mappings' not in collections:
        collections['entity_mappings'] = db['entity_mappings']
    
    # Clear entity collections if they exist
    collections['entity_resolution'].delete_many({})
    collections['entity_mappings'].delete_many({})
    
    # Add test summaries
    for summary in TEST_SUMMARIES:
        collections['summaries'].insert_one(summary)
    
    # Add sample knowledge graph
    collections['knowledge_graphs'].insert_one(SAMPLE_KNOWLEDGE_GRAPH)
    
    # Setup mock OpenAI client
    openai_client = MockOpenAI()
    
    # Print test environment status
    print(f"Test setup complete!")
    print(f"Added {len(TEST_SUMMARIES)} test summaries")
    print(f"Added 1 sample knowledge graph")
    
    return collections, openai_client

async def run_entity_resolution(collections, openai_client):
    """Run entity resolution on test data"""
    print("\n======== RUNNING ENTITY RESOLUTION ========")
    
    # Initialize entity resolution service
    entity_service = EntityResolutionService(collections, openai_client)
    
    # First run entity resolution without force update
    print("Running entity resolution (incremental)...")
    result = await entity_service.resolve_entities(force_update=False)
    
    print("\n======== ENTITY RESOLUTION RESULTS ========")
    print(f"Status: {result.status}")
    print(f"Message: {result.message}")
    
    if hasattr(result, 'updated') and result.updated is not None:
        print(f"Updated entities: {result.updated}")
    if hasattr(result, 'new') and result.new is not None:
        print(f"New entities: {result.new}")
    if hasattr(result, 'total') and result.total is not None:
        print(f"Total entities processed: {result.total}")
    if hasattr(result, 'entities') and result.entities:
        print(f"Entities returned: {len(result.entities)}")
        for entity in result.entities:
            print(f"  - {entity.canonical_name} ({entity.entity_type})")
    
    # Verify entity resolution collection
    entity_count = collections['entity_resolution'].count_documents({"document_type": "entity"})
    print(f"\nResolved entities in collection: {entity_count}")
    
    # Get and display a few resolved entities
    resolved_entities = list(collections['entity_resolution'].find({"document_type": "entity"}))
    for entity in resolved_entities[:3]:  # Show first three
        print(f"  - {entity.get('canonical_name')} ({entity.get('entity_type')})")
        if 'variant_ids' in entity:
            print(f"    Variants: {entity['variant_ids']}")
    
    return result

async def test_entity_mappings(collections):
    """Test entity mappings for NER tags"""
    print("\n======== TESTING ENTITY MAPPINGS ========")
    
    # Add a test paper with entities and then retrieve it to see the normalized/resolved entities
    test_paper = {
        "abs_url": "http://arxiv.org/abs/test_mapping",
        "title": "Test Paper for Entity Mapping",
        "authors": ["Test Author 7"],
        "updated": "2023-03-04",
        "published": "2023-03-04",
        "abstract": "This is a test abstract about deep learning.",
        "summary": "This is a test summary for entity mapping",
        "entities": [
            ["deep learning", "Algorithms & Learning Techniques"],
            ["BERT", "Models & Architectures"]
        ],
        "tags": ["deep learning", "BERT"]
    }
    
    # Add the test paper
    collections['summaries'].insert_one(test_paper)
    
    # Get entity mappings for normalization
    entity_mappings = list(collections['entity_mappings'].find())
    print(f"Entity mappings in collection: {len(entity_mappings)}")
    
    for mapping in entity_mappings[:3]:  # Show first three
        original = mapping.get('original_entity', {})
        resolved = mapping.get('resolved_entity', {})
        print(f"  {original.get('value')} ({original.get('category')}) → {resolved.get('canonical_name')} ({resolved.get('entity_type')})")
    
    # Check if we can perform entity normalization
    normalized_entities = {}
    test_entities = test_paper['entities']
    
    print("\nEntity normalization test:")
    for entity_value, entity_category in test_entities:
        # Look up in entity mappings
        mapping = collections['entity_mappings'].find_one({
            "original_entity.value": entity_value,
            "original_entity.category": entity_category
        })
        
        if mapping and 'resolved_entity' in mapping:
            canonical_name = mapping['resolved_entity'].get('canonical_name')
            normalized_entities[entity_value] = canonical_name
            print(f"  {entity_value} → {canonical_name}")
        else:
            normalized_entities[entity_value] = entity_value
            print(f"  {entity_value} → (not resolved)")
    
    return normalized_entities

async def main():
    """Main function to run the test suite"""
    print("\n======== FASTMOATLESS LOCAL TEST SUITE ========")
    print("Running with in-memory databases and test data\n")
    
    # Setup test environment
    collections, openai_client = await setup_test_environment()
    
    # Run entity resolution
    result = await run_entity_resolution(collections, openai_client)
    
    # Test entity mappings
    mappings = await test_entity_mappings(collections)
    
    print("\n======== TEST COMPLETED SUCCESSFULLY ========")
    
    return result, mappings

if __name__ == "__main__":
    # Load environment variables
    load_dotenv()
    
    # Run the main function
    asyncio.run(main())

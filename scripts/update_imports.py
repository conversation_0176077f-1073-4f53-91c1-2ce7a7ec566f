#!/usr/bin/env python3
"""
<PERSON><PERSON>t to update import paths in the reorganized scripts.
This ensures that scripts can still find the necessary modules after being moved.
"""

import os
import re
import glob

def update_import_path(file_path):
    """
    Update the import path in a script to ensure it can find the necessary modules.
    
    Args:
        file_path: Path to the script file
    """
    # Read the file content
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Calculate the relative path to the root directory
    rel_path = os.path.relpath(os.path.dirname(os.path.abspath(__file__)), os.path.dirname(os.path.abspath(file_path)))
    if rel_path == '.':
        rel_path = '..'
    
    # Update the import path
    # Look for the line that adds the current directory to the path
    path_pattern = r'sys\.path\.append\(os\.path\.dirname\(os\.path\.abspath\(__file__\)\)\)'
    
    # Replace with a path that points to the root directory
    replacement = f'sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "{rel_path}"))'
    
    # Check if the pattern exists
    if re.search(path_pattern, content):
        # Replace the pattern
        updated_content = re.sub(path_pattern, replacement, content)
        
        # Write the updated content back to the file
        with open(file_path, 'w') as f:
            f.write(updated_content)
        
        print(f"Updated import path in {file_path}")
        return True
    
    return False

def main():
    """
    Main function to update import paths in all scripts.
    """
    # Get all Python files in the scripts directory and its subdirectories
    script_files = glob.glob('scripts/**/*.py', recursive=True)
    
    # Update import paths in all scripts
    updated_count = 0
    for file_path in script_files:
        if update_import_path(file_path):
            updated_count += 1
    
    print(f"Updated import paths in {updated_count} scripts")

if __name__ == "__main__":
    main()

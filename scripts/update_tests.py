#!/usr/bin/env python3
"""
Script to update tests based on the current application state.
This Python version is designed to work in GitLab CI environments.
"""

import os
import sys
import tempfile
import subprocess
import re
import shutil
from pathlib import Path

def check_application_running(url="http://localhost:8000"):
    """Check if the application is running at the specified URL"""
    try:
        import requests
        response = requests.get(url)
        return response.status_code == 200
    except Exception as e:
        print(f"Error checking application: {e}")
        # In CI environment, we'll mock the application
        return False

def start_mock_server():
    """Start a mock server for testing in CI environment"""
    try:
        # Import the mock server module
        sys.path.insert(0, os.path.abspath(os.path.dirname(os.path.dirname(__file__))))
        from tests.mock_server import run_server
        import threading
        
        # Start the server in a separate thread
        server_thread = threading.Thread(target=run_server)
        server_thread.daemon = True
        server_thread.start()
        print("Mock server started for test updates")
        return True
    except Exception as e:
        print(f"Error starting mock server: {e}")
        return False

def capture_application_state(temp_dir, base_url="http://localhost:8000"):
    """Capture the current state of the application"""
    try:
        import requests
        
        # Capture main pages
        pages = {
            "homepage": "/",
            "search": "/search",
            "graph": "/graph"
        }
        
        for name, path in pages.items():
            response = requests.get(f"{base_url}{path}")
            if response.status_code == 200:
                with open(os.path.join(temp_dir, f"{name}.html"), "w") as f:
                    f.write(response.text)
                print(f"Captured {name} page")
            else:
                print(f"Failed to capture {name} page: {response.status_code}")
        
        return True
    except Exception as e:
        print(f"Error capturing application state: {e}")
        return False

def extract_selectors(temp_dir):
    """Extract selectors from the captured HTML files"""
    selectors = {}
    
    try:
        # Read the homepage HTML
        with open(os.path.join(temp_dir, "homepage.html"), "r") as f:
            html = f.read()
            
        # Extract header selector
        header_match = re.search(r'<header[^>]*class="([^"]*)"', html)
        if header_match:
            selectors["header"] = header_match.group(1)
            
        # Extract footer selector
        footer_match = re.search(r'<footer[^>]*class="([^"]*)"', html)
        if footer_match:
            selectors["footer"] = footer_match.group(1)
            
        # Extract search input selector
        search_match = re.search(r'<input[^>]*type="search"[^>]*class="([^"]*)"', html)
        if search_match:
            selectors["search"] = search_match.group(1)
            
        return selectors
    except Exception as e:
        print(f"Error extracting selectors: {e}")
        return {}

def update_test_files(selectors):
    """Update test files with the extracted selectors"""
    if not selectors:
        print("No selectors to update")
        return False
    
    try:
        # Print the selectors we found
        print("Found selectors to update:")
        for name, selector in selectors.items():
            print(f"  - {name}: .{selector}")
        
        # Get all test files
        test_files = []
        for root, _, files in os.walk("tests"):
            for file in files:
                if file.endswith(".py") or file.endswith(".ts"):
                    test_files.append(os.path.join(root, file))
        
        # Update each test file
        for file_path in test_files:
            with open(file_path, "r") as f:
                content = f.read()
                
            # Replace selectors in the file
            updated_content = content
            for name, selector in selectors.items():
                # Look for patterns like: page.locator('header') or page.locator(".old-header")
                pattern = rf"page\.locator\(['\"]\.?(?:[a-zA-Z0-9-_]+)?{name}['\"]"
                replacement = f"page.locator('.{selector}'"
                updated_content = re.sub(pattern, replacement, updated_content)
            
            # Only write the file if changes were made
            if updated_content != content:
                with open(file_path, "w") as f:
                    f.write(updated_content)
                print(f"Updated selectors in {file_path}")
        
        return True
    except Exception as e:
        print(f"Error updating test files: {e}")
        return False

def run_tests():
    """Run tests to verify the updates"""
    try:
        result = subprocess.run(["python", "-m", "pytest", "tests/e2e", "-v"], 
                               capture_output=True, text=True)
        
        print("Test results:")
        print(result.stdout)
        
        if result.returncode != 0:
            print("Tests failed after updates")
            print(result.stderr)
            return False
        
        print("Tests passed after updates")
        return True
    except Exception as e:
        print(f"Error running tests: {e}")
        return False

def main():
    """Main function"""
    print("Updating tests to match current application state...")
    
    # Create temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        # Check if the application is running
        if not check_application_running():
            print("Application is not running, starting mock server")
            if not start_mock_server():
                print("Failed to start mock server, exiting")
                return 1
        
        # Capture application state
        if not capture_application_state(temp_dir):
            print("Failed to capture application state, exiting")
            return 1
        
        # Extract selectors
        selectors = extract_selectors(temp_dir)
        
        # Update test files
        if not update_test_files(selectors):
            print("Failed to update test files")
            # Continue anyway
        
    # Run tests to verify updates
    if not run_tests():
        print("Tests failed after updates, manual intervention required")
        return 1
    
    print("Test update complete!")
    return 0

if __name__ == "__main__":
    sys.exit(main())

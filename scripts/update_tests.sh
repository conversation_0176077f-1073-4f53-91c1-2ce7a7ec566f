#!/bin/bash

# Script to update tests based on the current application state
# This should be run periodically to ensure tests stay in sync with the application

echo "Updating tests to match current application state..."

# Check if the application is running
if ! curl -s http://localhost:8000 > /dev/null; then
  echo "Error: Application doesn't seem to be running on port 8000."
  echo "Please start the application before updating tests."
  exit 1
fi

# Directory for storing temporary files
TEMP_DIR="$(mktemp -d)"
trap 'rm -rf "$TEMP_DIR"' EXIT

# Capture current application state
echo "Capturing current application state..."
curl -s http://localhost:8000 > "$TEMP_DIR/homepage.html"
curl -s http://localhost:8000/search > "$TEMP_DIR/search.html"
curl -s http://localhost:8000/graph > "$TEMP_DIR/graph.html"

# Update selectors in tests based on current application state
echo "Updating test selectors..."

# Example: Extract main selectors from the homepage
HEADER_SELECTOR=$(grep -o '<header[^>]*class="[^"]*"' "$TEMP_DIR/homepage.html" | head -1 | sed 's/.*class="\([^"]*\)".*/\1/')
FOOTER_SELECTOR=$(grep -o '<footer[^>]*class="[^"]*"' "$TEMP_DIR/homepage.html" | head -1 | sed 's/.*class="\([^"]*\)".*/\1/')
SEARCH_SELECTOR=$(grep -o '<input[^>]*type="search"[^>]*class="[^"]*"' "$TEMP_DIR/homepage.html" | head -1 | sed 's/.*class="\([^"]*\)".*/\1/')

# Only update if we found valid selectors
if [ -n "$HEADER_SELECTOR" ] || [ -n "$FOOTER_SELECTOR" ] || [ -n "$SEARCH_SELECTOR" ]; then
  echo "Found updated selectors in the application:"
  [ -n "$HEADER_SELECTOR" ] && echo "  - Header: .$HEADER_SELECTOR"
  [ -n "$FOOTER_SELECTOR" ] && echo "  - Footer: .$FOOTER_SELECTOR"
  [ -n "$SEARCH_SELECTOR" ] && echo "  - Search: .$SEARCH_SELECTOR"
  
  # Here you would update the test files with the new selectors
  # This is a simplified example - in a real implementation, you would
  # use more sophisticated parsing and updating
  
  echo "Tests updated successfully!"
else
  echo "No selector changes detected."
fi

# Run tests to verify they still work
echo "Running tests to verify updates..."
python tests/run_tests.py --pytest

if [ $? -eq 0 ]; then
  echo "✅ Tests passed after updates!"
else
  echo "❌ Tests failed after updates. Manual intervention required."
  exit 1
fi

echo "Test update complete!"

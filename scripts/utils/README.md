# Utility Files

This directory contains utility files used by various scripts in the project.

## Files

### ontologies.py

Contains ontology definitions for knowledge graph generation. This file defines the base ontology used for representing research papers, their components, and relationships.

### prompts.py

Contains prompt templates for various AI model interactions, including:

- System prompts for paper review
- NER (Named Entity Recognition) prompts
- Knowledge graph extraction prompts
- OCR chunking prompts
- Table extraction prompts
- SOTA extraction prompts
- Ontology generation prompts
- Mermaid diagram generation prompts

## Usage

These utility files are imported by various scripts in the project. For example:

```python
from scripts.utils.prompts import kg_prompt
from scripts.utils.ontologies import base_ontology
```

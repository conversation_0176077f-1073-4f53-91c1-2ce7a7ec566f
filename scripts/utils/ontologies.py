base_ontology = """@prefix : <http://www.example.org/test#> .
    @prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
    @prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
    @prefix xsd: <http://www.w3.org/2001/XMLSchema#> .
    @prefix owl: <http://www.w3.org/2002/07/owl#>.

    # Paper Structure Classes
    :Cl_ResearchPaper rdf:type owl:Class ;
        rdfs:comment "Represents a research paper on large language models" .

    :Cl_PaperSection rdf:type owl:Class ;
        rdfs:comment "Represents a section within a research paper" .

    :Cl_Abstract rdf:type owl:Class ;
        rdfs:subClassOf :Cl_PaperSection ;
        rdfs:comment "Represents the abstract section of a paper" .

    :Cl_Introduction rdf:type owl:Class ;
        rdfs:subClassOf :Cl_PaperSection ;
        rdfs:comment "Represents the introduction section of a paper" .

    :Cl_Methodology rdf:type owl:Class ;
        rdfs:subClassOf :Cl_PaperSection ;
        rdfs:comment "Represents the methodology section of a paper" .

    :Cl_Results rdf:type owl:Class ;
        rdfs:subClassOf :Cl_PaperSection ;
        rdfs:comment "Represents the results section of a paper" .

    :Cl_Discussion rdf:type owl:Class ;
        rdfs:subClassOf :Cl_PaperSection ;
        rdfs:comment "Represents the discussion section of a paper" .

    :Cl_Conclusion rdf:type owl:Class ;
        rdfs:subClassOf :Cl_PaperSection ;
        rdfs:comment "Represents the conclusion section of a paper" .

    # Research Components Classes
    :Cl_ResearchQuestion rdf:type owl:Class ;
        rdfs:comment "Represents a research question addressed in a paper" .

    :Cl_Hypothesis rdf:type owl:Class ;
        rdfs:comment "Represents a hypothesis proposed in a paper" .

    :Cl_Contribution rdf:type owl:Class ;
        rdfs:comment "Represents a scientific contribution made by a paper" .

    :Cl_Limitation rdf:type owl:Class ;
        rdfs:comment "Represents a limitation or constraint identified in a paper" .

    :Cl_FutureWork rdf:type owl:Class ;
        rdfs:comment "Represents future work proposed in a paper" .

    # Methods and Techniques Classes
    :Cl_Method rdf:type owl:Class ;
        rdfs:comment "Represents a method or technique described in a paper" .

    :Cl_Algorithm rdf:type owl:Class ;
        rdfs:subClassOf :Cl_Method ;
        rdfs:comment "Represents an algorithm described in a paper" .

    :Cl_ModelArchitecture rdf:type owl:Class ;
        rdfs:comment "Represents the architecture of an LLM described in a paper" .

    :Cl_ArchitectureComponent rdf:type owl:Class ;
        rdfs:comment "Represents a component of an LLM architecture" .

    :Cl_TrainingStrategy rdf:type owl:Class ;
        rdfs:comment "Represents a training strategy used for an LLM" .

    # Data and Evaluation Classes
    :Cl_Dataset rdf:type owl:Class ;
        rdfs:comment "Represents a dataset used in a paper" .

    :Cl_Benchmark rdf:type owl:Class ;
        rdfs:comment "Represents a benchmark used for evaluation in a paper" .

    :Cl_EvaluationMetric rdf:type owl:Class ;
        rdfs:comment "Represents a metric used for evaluation in a paper" .

    :Cl_ExperimentalSetup rdf:type owl:Class ;
        rdfs:comment "Represents an experimental setup described in a paper" .

    :Cl_Result rdf:type owl:Class ;
        rdfs:comment "Represents a result or finding reported in a paper" .

    :Cl_PerformanceComparison rdf:type owl:Class ;
        rdfs:comment "Represents a comparison of performance between models" .

    # Applications and Domains Classes
    :Cl_Application rdf:type owl:Class ;
        rdfs:comment "Represents an application area for LLMs discussed in a paper" .

    :Cl_UseCase rdf:type owl:Class ;
        rdfs:comment "Represents a specific use case for LLMs described in a paper" .

    :Cl_Domain rdf:type owl:Class ;
        rdfs:comment "Represents a domain where LLMs are applied" .

    # Ethical Considerations Classes
    :Cl_EthicalConsideration rdf:type owl:Class ;
        rdfs:comment "Represents ethical aspects discussed in a paper" .

    :Cl_SocialImpact rdf:type owl:Class ;
        rdfs:comment "Represents social impacts discussed in a paper" .

    # Claims and Evidence Classes
    :Cl_Claim rdf:type owl:Class ;
        rdfs:comment "Represents a claim made in a paper" .

    :Cl_Evidence rdf:type owl:Class ;
        rdfs:comment "Represents evidence provided to support a claim" .

    :Cl_Citation rdf:type owl:Class ;
        rdfs:comment "Represents a citation to another paper" .

    # Author and Publication Classes
    :Cl_Author rdf:type owl:Class ;
        rdfs:comment "Represents an author of a research paper" .

    :Cl_PublicationVenue rdf:type owl:Class ;
        rdfs:comment "Represents the venue where a paper was published" .

    :Cl_Institution rdf:type owl:Class ;
        rdfs:comment "Represents an institution affiliated with authors" .

    # Reification Classes
    :Cl_PaperSection rdf:type owl:Class ;
        rdfs:comment "Reification class connecting papers to their sections and content" .

    :Cl_ModelComparison rdf:type owl:Class ;
        rdfs:comment "Reification class representing comparison between two or more models" .

    :Cl_MethodApplication rdf:type owl:Class ;
        rdfs:comment "Reification class representing application of a method to a specific problem" .

    :Cl_TopicRelationship rdf:type owl:Class ;
        rdfs:comment "Reification class representing relationship between research topics" .

    :Cl_ClaimEvidence rdf:type owl:Class ;
        rdfs:comment "Reification class connecting claims to their supporting evidence" .

    # Object Properties - Paper Structure
    :hasSection rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ResearchPaper ;
        rdfs:range :Cl_PaperSection ;
        rdfs:comment "Links a paper to its sections" .

    :containsText rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_PaperSection ;
        rdfs:range xsd:string ;
        rdfs:comment "Links a paper section to its textual content" .

    :sectionOrder rdf:type owl:DatatypeProperty ;
        rdfs:domain :Cl_PaperSection ;
        rdfs:range xsd:integer ;
        rdfs:comment "The order of a section within a paper" .

    # Object Properties - Research Components
    :addressesQuestion rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ResearchPaper ;
        rdfs:range :Cl_ResearchQuestion ;
        rdfs:comment "Links a paper to the research questions it addresses" .

    :proposesHypothesis rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ResearchPaper ;
        rdfs:range :Cl_Hypothesis ;
        rdfs:comment "Links a paper to the hypotheses it proposes" .

    :makesContribution rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ResearchPaper ;
        rdfs:range :Cl_Contribution ;
        rdfs:comment "Links a paper to contributions it makes" .

    :identifiesLimitation rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ResearchPaper ;
        rdfs:range :Cl_Limitation ;
        rdfs:comment "Links a paper to limitations it identifies" .

    :proposesFutureWork rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ResearchPaper ;
        rdfs:range :Cl_FutureWork ;
        rdfs:comment "Links a paper to future work it proposes" .

    # Object Properties - Claims and Evidence
    :makesClaim rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ResearchPaper ;
        rdfs:range :Cl_Claim ;
        rdfs:comment "Links a paper to claims it makes" .

    :hasSupportingEvidence rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ClaimEvidence ;
        rdfs:range :Cl_Evidence ;
        rdfs:comment "Links a claim-evidence relationship to its evidence" .

    :relatedToClaim rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ClaimEvidence ;
        rdfs:range :Cl_Claim ;
        rdfs:comment "Links a claim-evidence relationship to its claim" .

    :citesSource rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_Evidence ;
        rdfs:range :Cl_Citation ;
        rdfs:comment "Links evidence to its citation sources" .

    :citedPaper rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_Citation ;
        rdfs:range :Cl_ResearchPaper ;
        rdfs:comment "Links a citation to the cited paper" .

    # Object Properties - Methods and Models
    :describesMethod rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ResearchPaper ;
        rdfs:range :Cl_Method ;
        rdfs:comment "Links a paper to methods it describes" .

    :proposesArchitecture rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ResearchPaper ;
        rdfs:range :Cl_ModelArchitecture ;
        rdfs:comment "Links a paper to model architectures it proposes" .

    :hasComponent rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ModelArchitecture ;
        rdfs:range :Cl_ArchitectureComponent ;
        rdfs:comment "Links a model architecture to its components" .

    :usesTrainingStrategy rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_Method ;
        rdfs:range :Cl_TrainingStrategy ;
        rdfs:comment "Links a method to its training strategy" .

    # Object Properties - Model Comparison (Reification)
    :comparesModel rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ModelComparison ;
        rdfs:range :Cl_ModelArchitecture ;
        rdfs:comment "Links a model comparison to models being compared" .

    :usesMetricForComparison rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ModelComparison ;
        rdfs:range :Cl_EvaluationMetric ;
        rdfs:comment "Links a model comparison to metrics used for comparison" .

    :hasBenchmarkForComparison rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ModelComparison ;
        rdfs:range :Cl_Benchmark ;
        rdfs:comment "Links a model comparison to benchmarks used" .

    :relatedToPaper rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ModelComparison ;
        rdfs:range :Cl_ResearchPaper ;
        rdfs:comment "Links a model comparison to the paper making the comparison" .

    # Object Properties - Method Application (Reification)
    :appliesMethod rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_MethodApplication ;
        rdfs:range :Cl_Method ;
        rdfs:comment "Links a method application to the method being applied" .

    :appliesInDomain rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_MethodApplication ;
        rdfs:range :Cl_Domain ;
        rdfs:comment "Links a method application to the domain of application" .

    :hasApplication rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_MethodApplication ;
        rdfs:range :Cl_Application ;
        rdfs:comment "Links a method application to its application" .

    :describedInPaper rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_MethodApplication ;
        rdfs:range :Cl_ResearchPaper ;
        rdfs:comment "Links a method application to the paper describing it" .

    # Object Properties - Data and Evaluation
    :usesDataset rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ResearchPaper ;
        rdfs:range :Cl_Dataset ;
        rdfs:comment "Links a paper to datasets it uses" .

    :evaluatesOn rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ResearchPaper ;
        rdfs:range :Cl_Benchmark ;
        rdfs:comment "Links a paper to benchmarks it evaluates on" .

    :usesMetric rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ResearchPaper ;
        rdfs:range :Cl_EvaluationMetric ;
        rdfs:comment "Links a paper to metrics it uses" .

    :reportsResult rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ResearchPaper ;
        rdfs:range :Cl_Result ;
        rdfs:comment "Links a paper to results it reports" .

    :comparesPerformance rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ResearchPaper ;
        rdfs:range :Cl_PerformanceComparison ;
        rdfs:comment "Links a paper to performance comparisons it makes" .

    # Object Properties - Topic Relationships (Reification)
    :relatesTopic rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_TopicRelationship ;
        rdfs:range :Cl_Topic ;
        rdfs:comment "Links a topic relationship to the topics it relates" .

    :relationshipType rdf:type owl:DatatypeProperty ;
        rdfs:domain :Cl_TopicRelationship ;
        rdfs:range xsd:string ;
        rdfs:comment "Specifies the type of relationship between topics (e.g., 'builds-upon', 'contradicts')" .

    :relationshipStrength rdf:type owl:DatatypeProperty ;
        rdfs:domain :Cl_TopicRelationship ;
        rdfs:range xsd:decimal ;
        rdfs:comment "Specifies the strength of relationship between topics (0-1)" .

    # Object Properties - Applications and Domains
    :describesApplication rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ResearchPaper ;
        rdfs:range :Cl_Application ;
        rdfs:comment "Links a paper to applications it describes" .

    :discussesUseCase rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ResearchPaper ;
        rdfs:range :Cl_UseCase ;
        rdfs:comment "Links a paper to use cases it discusses" .

    :targetsDomain rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_Application ;
        rdfs:range :Cl_Domain ;
        rdfs:comment "Links an application to its target domain" .

    # Object Properties - Ethical Considerations
    :addressesEthicalConsideration rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ResearchPaper ;
        rdfs:range :Cl_EthicalConsideration ;
        rdfs:comment "Links a paper to ethical considerations it addresses" .

    :discussesSocialImpact rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ResearchPaper ;
        rdfs:range :Cl_SocialImpact ;
        rdfs:comment "Links a paper to social impacts it discusses" .

    # Object Properties - Authors and Publication
    :writtenBy rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ResearchPaper ;
        rdfs:range :Cl_Author ;
        rdfs:comment "Links a paper to its authors" .

    :affiliatedWith rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_Author ;
        rdfs:range :Cl_Institution ;
        rdfs:comment "Links an author to their institution" .

    :publishedIn rdf:type owl:ObjectProperty ;
        rdfs:domain :Cl_ResearchPaper ;
        rdfs:range :Cl_PublicationVenue ;
        rdfs:comment "Links a paper to its publication venue" .

    # Data Properties - Paper Metadata
    :title rdf:type owl:DatatypeProperty ;
        rdfs:domain :Cl_ResearchPaper ;
        rdfs:range xsd:string ;
        rdfs:comment "The title of the paper" .

    :abstract rdf:type owl:DatatypeProperty ;
        rdfs:domain :Cl_Abstract ;
        rdfs:range xsd:string ;
        rdfs:comment "The abstract text of the paper" .

    :publicationYear rdf:type owl:DatatypeProperty ;
        rdfs:domain :Cl_ResearchPaper ;
        rdfs:range xsd:gYear ;
        rdfs:comment "The year the paper was published" .

    :doi rdf:type owl:DatatypeProperty ;
        rdfs:domain :Cl_ResearchPaper ;
        rdfs:range xsd:string ;
        rdfs:comment "Digital Object Identifier of the paper" .

    :citationCount rdf:type owl:DatatypeProperty ;
        rdfs:domain :Cl_ResearchPaper ;
        rdfs:range xsd:integer ;
        rdfs:comment "The number of citations the paper has received" .

    # Data Properties - Methods and Results
    :methodName rdf:type owl:DatatypeProperty ;
        rdfs:domain :Cl_Method ;
        rdfs:range xsd:string ;
        rdfs:comment "The name of a method" .

    :algorithmComplexity rdf:type owl:DatatypeProperty ;
        rdfs:domain :Cl_Algorithm ;
        rdfs:range xsd:string ;
        rdfs:comment "The complexity class of an algorithm" .

    :datasetSize rdf:type owl:DatatypeProperty ;
        rdfs:domain :Cl_Dataset ;
        rdfs:range xsd:long ;
        rdfs:comment "The size of a dataset in number of examples" .

    :metricScore rdf:type owl:DatatypeProperty ;
        rdfs:domain :Cl_Result ;
        rdfs:range xsd:decimal ;
        rdfs:comment "A numerical score for an evaluation metric" .

    :statisticalSignificance rdf:type owl:DatatypeProperty ;
        rdfs:domain :Cl_Result ;
        rdfs:range xsd:decimal ;
        rdfs:comment "P-value or other significance measure for a result" .

    :modelParameters rdf:type owl:DatatypeProperty ;
        rdfs:domain :Cl_ModelArchitecture ;
        rdfs:range xsd:long ;
        rdfs:comment "Number of parameters in a model" .

    # Data Properties - Author and Publication
    :authorName rdf:type owl:DatatypeProperty ;
        rdfs:domain :Cl_Author ;
        rdfs:range xsd:string ;
        rdfs:comment "The name of an author" .

    :authorEmail rdf:type owl:DatatypeProperty ;
        rdfs:domain :Cl_Author ;
        rdfs:range xsd:string ;
        rdfs:comment "The email address of an author" .

    :venueName rdf:type owl:DatatypeProperty ;
        rdfs:domain :Cl_PublicationVenue ;
        rdfs:range xsd:string ;
        rdfs:comment "The name of a publication venue" .

    :venueType rdf:type owl:DatatypeProperty ;
        rdfs:domain :Cl_PublicationVenue ;
        rdfs:range xsd:string ;
        rdfs:comment "The type of venue (e.g., 'conference', 'journal')" .

    :institutionName rdf:type owl:DatatypeProperty ;
        rdfs:domain :Cl_Institution ;
        rdfs:range xsd:string ;
        rdfs:comment "The name of an institution" .

    # Restrictions
    :Cl_ResearchPaper rdfs:subClassOf [
        rdf:type owl:Restriction ;
        owl:onProperty :hasSection ;
        owl:someValuesFrom :Cl_Abstract
    ] .

    :Cl_ResearchPaper rdfs:subClassOf [
        rdf:type owl:Restriction ;
        owl:onProperty :writtenBy ;
        owl:minCardinality "1"^^xsd:nonNegativeInteger
    ] .

    :Cl_Claim rdfs:subClassOf [
        rdf:type owl:Restriction ;
        owl:onProperty :hasSupportingEvidence ;
        owl:someValuesFrom :Cl_Evidence
    ] .

    :Cl_ClaimEvidence rdfs:subClassOf [
        rdf:type owl:Restriction ;
        owl:onProperty :relatedToClaim ;
        owl:someValuesFrom :Cl_Claim
    ] .

    :Cl_ClaimEvidence rdfs:subClassOf [
        rdf:type owl:Restriction ;
        owl:onProperty :hasSupportingEvidence ;
        owl:someValuesFrom :Cl_Evidence
    ] .

    :Cl_ModelComparison rdfs:subClassOf [
        rdf:type owl:Restriction ;
        owl:onProperty :comparesModel ;
        owl:minCardinality "2"^^xsd:nonNegativeInteger
    ] .

    :Cl_MethodApplication rdfs:subClassOf [
        rdf:type owl:Restriction ;
        owl:onProperty :appliesMethod ;
        owl:someValuesFrom :Cl_Method
    ] .

    :Cl_TopicRelationship rdfs:subClassOf [
        rdf:type owl:Restriction ;
        owl:onProperty :relatesTopic ;
        owl:minCardinality "2"^^xsd:nonNegativeInteger
    ] .

    # General Class Axioms
    [ rdf:type owl:Restriction ;
    owl:onProperty :reportsResult ;
    owl:someValuesFrom :Cl_Result ;
    rdfs:subClassOf [ rdf:type owl:Restriction ;
                    owl:onProperty :usesMetric ;
                    owl:someValuesFrom :Cl_EvaluationMetric ]
    ] .

    [ rdf:type owl:Restriction ;
    owl:onProperty :comparesPerformance ;
    owl:someValuesFrom :Cl_PerformanceComparison ;
    rdfs:subClassOf [ rdf:type owl:Restriction ;
                    owl:onProperty :usesMetric ;
                    owl:minCardinality "1"^^xsd:nonNegativeInteger ]
    ] .

    [ rdf:type owl:Restriction ;
    owl:onProperty :makesClaim ;
    owl:someValuesFrom :Cl_Claim ;
    rdfs:subClassOf [ rdf:type owl:Restriction ;
                    owl:onProperty :addressesQuestion ;
                    owl:someValuesFrom :Cl_ResearchQuestion ]
    ] .
    """

current_ontology = """@prefix : http://www.example.org/test# . @prefix rdf: http://www.w3.org/1999/02/22-rdf-syntax-ns# . @prefix rdfs: http://www.w3.org/2000/01/rdf-schema# . @prefix xsd: http://www.w3.org/2001/XMLSchema# . @prefix owl: http://www.w3.org/2002/07/owl#.
Paper Structure Classes
:Cl_ResearchPaper rdf:type owl:Class ; rdfs:comment "Represents a research paper on large language models" .
:Cl_PaperSection rdf:type owl:Class ; rdfs:comment "Represents a section within a research paper" .
:Cl_Abstract rdf:type owl:Class ; rdfs:subClassOf :Cl_PaperSection ; rdfs:comment "Represents the abstract section of a paper" .
:Cl_Introduction rdf:type owl:Class ; rdfs:subClassOf :Cl_PaperSection ; rdfs:comment "Represents the introduction section of a paper" .
:Cl_Methodology rdf:type owl:Class ; rdfs:subClassOf :Cl_PaperSection ; rdfs:comment "Represents the methodology section of a paper" .
:Cl_Results rdf:type owl:Class ; rdfs:subClassOf :Cl_PaperSection ; rdfs:comment "Represents the results section of a paper" .
:Cl_Discussion rdf:type owl:Class ; rdfs:subClassOf :Cl_PaperSection ; rdfs:comment "Represents the discussion section of a paper" .
:Cl_Conclusion rdf:type owl:Class ; rdfs:subClassOf :Cl_PaperSection ; rdfs:comment "Represents the conclusion section of a paper" .
Research Components Classes
:Cl_ResearchQuestion rdf:type owl:Class ; rdfs:comment "Represents a research question addressed in a paper" .
:Cl_Hypothesis rdf:type owl:Class ; rdfs:comment "Represents a hypothesis proposed in a paper" .
:Cl_Contribution rdf:type owl:Class ; rdfs:comment "Represents a scientific contribution made by a paper" .
:Cl_Limitation rdf:type owl:Class ; rdfs:comment "Represents a limitation or constraint identified in a paper" .
:Cl_FutureWork rdf:type owl:Class ; rdfs:comment "Represents future work proposed in a paper" .
Methods and Techniques Classes
:Cl_Method rdf:type owl:Class ; rdfs:comment "Represents a method or technique described in a paper" .
:Cl_Algorithm rdf:type owl:Class ; rdfs:subClassOf :Cl_Method ; rdfs:comment "Represents an algorithm described in a paper" .
:Cl_ModelArchitecture rdf:type owl:Class ; rdfs:comment "Represents the architecture of an LLM described in a paper" .
:Cl_ArchitectureComponent rdf:type owl:Class ; rdfs:comment "Represents a component of an LLM architecture" .
:Cl_TrainingStrategy rdf:type owl:Class ; rdfs:comment "Represents a training strategy used for an LLM" .
Data and Evaluation Classes
:Cl_Dataset rdf:type owl:Class ; rdfs:comment "Represents a dataset used in a paper" .
:Cl_Benchmark rdf:type owl:Class ; rdfs:comment "Represents a benchmark used for evaluation in a paper" .
:Cl_EvaluationMetric rdf:type owl:Class ; rdfs:comment "Represents a metric used for evaluation in a paper" .
:Cl_ExperimentalSetup rdf:type owl:Class ; rdfs:comment "Represents an experimental setup described in a paper" .
:Cl_Result rdf:type owl:Class ; rdfs:comment "Represents a result or finding reported in a paper" .
:Cl_PerformanceComparison rdf:type owl:Class ; rdfs:comment "Represents a comparison of performance between models" .
Applications and Domains Classes
:Cl_Application rdf:type owl:Class ; rdfs:comment "Represents an application area for LLMs discussed in a paper" .
:Cl_UseCase rdf:type owl:Class ; rdfs:comment "Represents a specific use case for LLMs described in a paper" .
:Cl_Domain rdf:type owl:Class ; rdfs:comment "Represents a domain where LLMs are applied" .
Ethical Considerations Classes
:Cl_EthicalConsideration rdf:type owl:Class ; rdfs:comment "Represents ethical aspects discussed in a paper" .
:Cl_SocialImpact rdf:type owl:Class ; rdfs:comment "Represents social impacts discussed in a paper" .
Claims and Evidence Classes
:Cl_Claim rdf:type owl:Class ; rdfs:comment "Represents a claim made in a paper" .
:Cl_Evidence rdf:type owl:Class ; rdfs:comment "Represents evidence provided to support a claim" .
:Cl_Citation rdf:type owl:Class ; rdfs:comment "Represents a citation to another paper" .
Author and Publication Classes
:Cl_Author rdf:type owl:Class ; rdfs:comment "Represents an author of a research paper" .
:Cl_PublicationVenue rdf:type owl:Class ; rdfs:comment "Represents the venue where a paper was published" .
:Cl_Institution rdf:type owl:Class ; rdfs:comment "Represents an institution affiliated with authors" .
Reification Classes
:Cl_PaperSection rdf:type owl:Class ; rdfs:comment "Reification class connecting papers to their sections and content" .
:Cl_ModelComparison rdf:type owl:Class ; rdfs:comment "Reification class representing comparison between two or more models" .
:Cl_MethodApplication rdf:type owl:Class ; rdfs:comment "Reification class representing application of a method to a specific problem" .
:Cl_TopicRelationship rdf:type owl:Class ; rdfs:comment "Reification class representing relationship between research topics" .
:Cl_ClaimEvidence rdf:type owl:Class ; rdfs:comment "Reification class connecting claims to their supporting evidence" .
New Classes for Entity Representation, Extraction, and Analysis
:Cl_Entity rdf:type owl:Class ; rdfs:comment "Represents a key entity identified within LLM research papers" .
:Cl_TextualMention rdf:type owl:Class ; rdfs:comment "Represents a specific mention of an entity in text" .
:Cl_EntityExtraction rdf:type owl:Class ; rdfs:comment "Reification class representing the extraction of an entity from text" .
:Cl_TextEntityMapping rdf:type owl:Class ; rdfs:comment "Reification class linking extracted entities to their textual mentions" .
:Cl_EntityRelationship rdf:type owl:Class ; rdfs:comment "Reification class modeling relationships between extracted entities" .
:Cl_EntityAnalysis rdf:type owl:Class ; rdfs:comment "Reification class for analyzing extracted entities and their properties" .
:Cl_EntityNetwork rdf:type owl:Class ; rdfs:comment "Represents a network of interconnected entities across papers" .
:Cl_EntityCluster rdf:type owl:Class ; rdfs:comment "Represents a cluster of related entities" .
:Cl_Topic rdf:type owl:Class ; rdfs:comment "Represents a research topic discussed in papers" .
Object Properties - Paper Structure
:hasSection rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ResearchPaper ; rdfs:range :Cl_PaperSection ; rdfs:comment "Links a paper to its sections" .
:containsText rdf:type owl:ObjectProperty ; rdfs:domain :Cl_PaperSection ; rdfs:range xsd:string ; rdfs:comment "Links a paper section to its textual content" .
:sectionOrder rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_PaperSection ; rdfs:range xsd:integer ; rdfs:comment "The order of a section within a paper" .
Object Properties - Research Components
:addressesQuestion rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ResearchPaper ; rdfs:range :Cl_ResearchQuestion ; rdfs:comment "Links a paper to the research questions it addresses" .
:proposesHypothesis rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ResearchPaper ; rdfs:range :Cl_Hypothesis ; rdfs:comment "Links a paper to the hypotheses it proposes" .
:makesContribution rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ResearchPaper ; rdfs:range :Cl_Contribution ; rdfs:comment "Links a paper to contributions it makes" .
:identifiesLimitation rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ResearchPaper ; rdfs:range :Cl_Limitation ; rdfs:comment "Links a paper to limitations it identifies" .
:proposesFutureWork rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ResearchPaper ; rdfs:range :Cl_FutureWork ; rdfs:comment "Links a paper to future work it proposes" .
Object Properties - Claims and Evidence
:makesClaim rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ResearchPaper ; rdfs:range :Cl_Claim ; rdfs:comment "Links a paper to claims it makes" .
:hasSupportingEvidence rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ClaimEvidence ; rdfs:range :Cl_Evidence ; rdfs:comment "Links a claim-evidence relationship to its evidence" .
:relatedToClaim rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ClaimEvidence ; rdfs:range :Cl_Claim ; rdfs:comment "Links a claim-evidence relationship to its claim" .
:citesSource rdf:type owl:ObjectProperty ; rdfs:domain :Cl_Evidence ; rdfs:range :Cl_Citation ; rdfs:comment "Links evidence to its citation sources" .
:citedPaper rdf:type owl:ObjectProperty ; rdfs:domain :Cl_Citation ; rdfs:range :Cl_ResearchPaper ; rdfs:comment "Links a citation to the cited paper" .
Object Properties - Methods and Models
:describesMethod rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ResearchPaper ; rdfs:range :Cl_Method ; rdfs:comment "Links a paper to methods it describes" .
:proposesArchitecture rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ResearchPaper ; rdfs:range :Cl_ModelArchitecture ; rdfs:comment "Links a paper to model architectures it proposes" .
:hasComponent rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ModelArchitecture ; rdfs:range :Cl_ArchitectureComponent ; rdfs:comment "Links a model architecture to its components" .
:usesTrainingStrategy rdf:type owl:ObjectProperty ; rdfs:domain :Cl_Method ; rdfs:range :Cl_TrainingStrategy ; rdfs:comment "Links a method to its training strategy" .
Object Properties - Model Comparison (Reification)
:comparesModel rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ModelComparison ; rdfs:range :Cl_ModelArchitecture ; rdfs:comment "Links a model comparison to models being compared" .
:usesMetricForComparison rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ModelComparison ; rdfs:range :Cl_EvaluationMetric ; rdfs:comment "Links a model comparison to metrics used for comparison" .
:hasBenchmarkForComparison rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ModelComparison ; rdfs:range :Cl_Benchmark ; rdfs:comment "Links a model comparison to benchmarks used" .
:relatedToPaper rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ModelComparison ; rdfs:range :Cl_ResearchPaper ; rdfs:comment "Links a model comparison to the paper making the comparison" .
Object Properties - Method Application (Reification)
:appliesMethod rdf:type owl:ObjectProperty ; rdfs:domain :Cl_MethodApplication ; rdfs:range :Cl_Method ; rdfs:comment "Links a method application to the method being applied" .
:appliesInDomain rdf:type owl:ObjectProperty ; rdfs:domain :Cl_MethodApplication ; rdfs:range :Cl_Domain ; rdfs:comment "Links a method application to the domain of application" .
:hasApplication rdf:type owl:ObjectProperty ; rdfs:domain :Cl_MethodApplication ; rdfs:range :Cl_Application ; rdfs:comment "Links a method application to its application" .
:describedInPaper rdf:type owl:ObjectProperty ; rdfs:domain :Cl_MethodApplication ; rdfs:range :Cl_ResearchPaper ; rdfs:comment "Links a method application to the paper describing it" .
Object Properties - Data and Evaluation
:usesDataset rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ResearchPaper ; rdfs:range :Cl_Dataset ; rdfs:comment "Links a paper to datasets it uses" .
:evaluatesOn rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ResearchPaper ; rdfs:range :Cl_Benchmark ; rdfs:comment "Links a paper to benchmarks it evaluates on" .
:usesMetric rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ResearchPaper ; rdfs:range :Cl_EvaluationMetric ; rdfs:comment "Links a paper to metrics it uses" .
:reportsResult rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ResearchPaper ; rdfs:range :Cl_Result ; rdfs:comment "Links a paper to results it reports" .
:comparesPerformance rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ResearchPaper ; rdfs:range :Cl_PerformanceComparison ; rdfs:comment "Links a paper to performance comparisons it makes" .
Object Properties - Topic Relationships (Reification)
:relatesTopic rdf:type owl:ObjectProperty ; rdfs:domain :Cl_TopicRelationship ; rdfs:range :Cl_Topic ; rdfs:comment "Links a topic relationship to the topics it relates" .
:relationshipType rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_TopicRelationship ; rdfs:range xsd:string ; rdfs:comment "Specifies the type of relationship between topics (e.g., 'builds-upon', 'contradicts')" .
:relationshipStrength rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_TopicRelationship ; rdfs:range xsd:decimal ; rdfs:comment "Specifies the strength of relationship between topics (0-1)" .
Object Properties - Applications and Domains
:describesApplication rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ResearchPaper ; rdfs:range :Cl_Application ; rdfs:comment "Links a paper to applications it describes" .
:discussesUseCase rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ResearchPaper ; rdfs:range :Cl_UseCase ; rdfs:comment "Links a paper to use cases it discusses" .
:targetsDomain rdf:type owl:ObjectProperty ; rdfs:domain :Cl_Application ; rdfs:range :Cl_Domain ; rdfs:comment "Links an application to its target domain" .
Object Properties - Ethical Considerations
:addressesEthicalConsideration rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ResearchPaper ; rdfs:range :Cl_EthicalConsideration ; rdfs:comment "Links a paper to ethical considerations it addresses" .
:discussesSocialImpact rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ResearchPaper ; rdfs:range :Cl_SocialImpact ; rdfs:comment "Links a paper to social impacts it discusses" .
Object Properties - Authors and Publication
:writtenBy rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ResearchPaper ; rdfs:range :Cl_Author ; rdfs:comment "Links a paper to its authors" .
:affiliatedWith rdf:type owl:ObjectProperty ; rdfs:domain :Cl_Author ; rdfs:range :Cl_Institution ; rdfs:comment "Links an author to their institution" .
:publishedIn rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ResearchPaper ; rdfs:range :Cl_PublicationVenue ; rdfs:comment "Links a paper to its publication venue" .
New Object Properties for Entity Extraction and Analysis
:containsEntity rdf:type owl:ObjectProperty ; rdfs:domain :Cl_ResearchPaper ; rdfs:range :Cl_Entity ; rdfs:comment "Links a paper to entities it contains" .
:extractedFrom rdf:type owl:ObjectProperty ; rdfs:domain :Cl_EntityExtraction ; rdfs:range :Cl_PaperSection ; rdfs:comment "Links an entity extraction to the paper section it was extracted from" .
:extractedEntity rdf:type owl:ObjectProperty ; rdfs:domain :Cl_EntityExtraction ; rdfs:range :Cl_Entity ; rdfs:comment "Links an entity extraction to the entity extracted" .
:hasTextualMention rdf:type owl:ObjectProperty ; rdfs:domain :Cl_TextEntityMapping ; rdfs:range :Cl_TextualMention ; rdfs:comment "Links a text-entity mapping to its textual mention" .
:refersToEntity rdf:type owl:ObjectProperty ; rdfs:domain :Cl_TextEntityMapping ; rdfs:range :Cl_Entity ; rdfs:comment "Links a text-entity mapping to the entity it refers to" .
:relatesEntity rdf:type owl:ObjectProperty ; rdfs:domain :Cl_EntityRelationship ; rdfs:range :Cl_Entity ; rdfs:comment "Links an entity relationship to the entities involved" .
:analyzesEntity rdf:type owl:ObjectProperty ; rdfs:domain :Cl_EntityAnalysis ; rdfs:range :Cl_Entity ; rdfs:comment "Links an entity analysis to the entity being analyzed" .
:usesAnalysisMethod rdf:type owl:ObjectProperty ; rdfs:domain :Cl_EntityAnalysis ; rdfs:range :Cl_Method ; rdfs:comment "Links an entity analysis to the method used for analysis" .
:belongsToNetwork rdf:type owl:ObjectProperty ; rdfs:domain :Cl_Entity ; rdfs:range :Cl_EntityNetwork ; rdfs:comment "Links an entity to the network it belongs to" .
:belongsToCluster rdf:type owl:ObjectProperty ; rdfs:domain :Cl_Entity ; rdfs:range :Cl_EntityCluster ; rdfs:comment "Links an entity to the cluster it belongs to" .
:relatedToMethod rdf:type owl:ObjectProperty ; rdfs:domain :Cl_Entity ; rdfs:range :Cl_Method ; rdfs:comment "Links an entity to related methods" .
:relatedToResult rdf:type owl:ObjectProperty ; rdfs:domain :Cl_Entity ; rdfs:range :Cl_Result ; rdfs:comment "Links an entity to related results" .
:relatedToClaim rdf:type owl:ObjectProperty ; rdfs:domain :Cl_Entity ; rdfs:range :Cl_Claim ; rdfs:comment "Links an entity to related claims" .
Data Properties - Paper Metadata
:title rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_ResearchPaper ; rdfs:range xsd:string ; rdfs:comment "The title of the paper" .
:abstract rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_Abstract ; rdfs:range xsd:string ; rdfs:comment "The abstract text of the paper" .
:publicationYear rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_ResearchPaper ; rdfs:range xsd:gYear ; rdfs:comment "The year the paper was published" .
:doi rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_ResearchPaper ; rdfs:range xsd:string ; rdfs:comment "Digital Object Identifier of the paper" .
:citationCount rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_ResearchPaper ; rdfs:range xsd:integer ; rdfs:comment "The number of citations the paper has received" .
Data Properties - Methods and Results
:methodName rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_Method ; rdfs:range xsd:string ; rdfs:comment "The name of a method" .
:algorithmComplexity rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_Algorithm ; rdfs:range xsd:string ; rdfs:comment "The complexity class of an algorithm" .
:datasetSize rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_Dataset ; rdfs:range xsd:long ; rdfs:comment "The size of a dataset in number of examples" .
:metricScore rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_Result ; rdfs:range xsd:decimal ; rdfs:comment "A numerical score for an evaluation metric" .
:statisticalSignificance rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_Result ; rdfs:range xsd:decimal ; rdfs:comment "P-value or other significance measure for a result" .
:modelParameters rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_ModelArchitecture ; rdfs:range xsd:long ; rdfs:comment "Number of parameters in a model" .
Data Properties - Author and Publication
:authorName rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_Author ; rdfs:range xsd:string ; rdfs:comment "The name of an author" .
:authorEmail rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_Author ; rdfs:range xsd:string ; rdfs:comment "The email address of an author" .
:venueName rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_PublicationVenue ; rdfs:range xsd:string ; rdfs:comment "The name of a publication venue" .
:venueType rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_PublicationVenue ; rdfs:range xsd:string ; rdfs:comment "The type of venue (e.g., 'conference', 'journal')" .
:institutionName rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_Institution ; rdfs:range xsd:string ; rdfs:comment "The name of an institution" .
New Data Properties for Entity Extraction and Analysis
:entityName rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_Entity ; rdfs:range xsd:string ; rdfs:comment "The name of an entity" .
:entityType rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_Entity ; rdfs:range xsd:string ; rdfs:comment "The type of entity (e.g., 'method', 'metric', 'finding')" .
:mentionText rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_TextualMention ; rdfs:range xsd:string ; rdfs:comment "The actual text of a mention" .
:mentionPosition rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_TextualMention ; rdfs:range xsd:integer ; rdfs:comment "The character position of a mention in text" .
:mentionLength rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_TextualMention ; rdfs:range xsd:integer ; rdfs:comment "The length of a mention in characters" .
:extractionMethod rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_EntityExtraction ; rdfs:range xsd:string ; rdfs:comment "The method used to extract an entity" .
:extractionConfidence rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_EntityExtraction ; rdfs:range xsd:decimal ; rdfs:comment "The confidence score of an entity extraction" .
:relationshipType rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_EntityRelationship ; rdfs:range xsd:string ; rdfs:comment "The type of relationship between entities" .
:relationshipStrength rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_EntityRelationship ; rdfs:range xsd:decimal ; rdfs:comment "The strength of relationship between entities (0-1)" .
:analysisMethod rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_EntityAnalysis ; rdfs:range xsd:string ; rdfs:comment "The method used for entity analysis" .
:analysisResult rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_EntityAnalysis ; rdfs:range xsd:string ; rdfs:comment "The result of an entity analysis" .
:networkSize rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_EntityNetwork ; rdfs:range xsd:integer ; rdfs:comment "The number of entities in a network" .
:clusterSize rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_EntityCluster ; rdfs:range xsd:integer ; rdfs:comment "The number of entities in a cluster" .
:clusterCohesion rdf:type owl:DatatypeProperty ; rdfs:domain :Cl_EntityCluster ; rdfs:range xsd:decimal ; rdfs:comment "The cohesion score of an entity cluster" .
Restrictions
:Cl_ResearchPaper rdfs:subClassOf [ rdf:type owl:Restriction ; owl:onProperty :hasSection ; owl:someValuesFrom :Cl_Abstract ] .
:Cl_ResearchPaper rdfs:subClassOf [ rdf:type owl:Restriction ; owl:onProperty :writtenBy ; owl:minCardinality "1"^^xsd:nonNegativeInteger ] .
:Cl_Claim rdfs:subClassOf [ rdf:type owl:Restriction ; owl:onProperty :hasSupportingEvidence ; owl:someValuesFrom :Cl_Evidence ] .
:Cl_ClaimEvidence rdfs:subClassOf [ rdf:type owl:Restriction ; owl:onProperty :relatedToClaim ; owl:someValuesFrom :Cl_Claim ] .
:Cl_ClaimEvidence rdfs:subClassOf [ rdf:type owl:Restriction ; owl:onProperty :hasSupportingEvidence ; owl:someValuesFrom :Cl_Evidence ] .
:Cl_ModelComparison rdfs:subClassOf [ rdf:type owl:Restriction ; owl:onProperty :comparesModel ; owl:minCardinality "2"^^xsd:nonNegativeInteger ] .
:Cl_MethodApplication rdfs:subClassOf [ rdf:type owl:Restriction ; owl:onProperty :appliesMethod ; owl:someValuesFrom :Cl_Method ] .
:Cl_TopicRelationship rdfs:subClassOf [ rdf:type owl:Restriction ; owl:onProperty :relatesTopic ; owl:minCardinality "2"^^xsd:nonNegativeInteger ] .
New Restrictions for Entity Extraction and Analysis
:Cl_EntityExtraction rdfs:subClassOf [ rdf:type owl:Restriction ; owl:onProperty :extractedEntity ; owl:someValuesFrom :Cl_Entity ] .
:Cl_EntityExtraction rdfs:subClassOf [ rdf:type owl:Restriction ; owl:onProperty :extractedFrom ; owl:someValuesFrom :Cl_PaperSection ] .
:Cl_TextEntityMapping rdfs:subClassOf [ rdf:type owl:Restriction ; owl:onProperty :hasTextualMention ; owl:someValuesFrom :Cl_TextualMention ] .
:Cl_TextEntityMapping rdfs:subClassOf [ rdf:type owl:Restriction ; owl:onProperty :refersToEntity ; owl:someValuesFrom :Cl_Entity ] .
:Cl_EntityRelationship rdfs:subClassOf [ rdf:type owl:Restriction ; owl:onProperty :relatesEntity ; owl:minCardinality "2"^^xsd:nonNegativeInteger ] .
:Cl_EntityAnalysis rdfs:subClassOf [ rdf:type owl:Restriction ; owl:onProperty :analyzesEntity ; owl:someValuesFrom :Cl_Entity ] .
:Cl_EntityNetwork rdfs:subClassOf [ rdf:type owl:Restriction ; owl:onProperty :belongsToNetwork ; owl:minCardinality "2"^^xsd:nonNegativeInteger ] .
General Class Axioms
[ rdf:type owl:Restriction ; owl:onProperty :reportsResult ; owl:someValuesFrom :Cl_Result ; rdfs:subClassOf [ rdf:type owl:Restriction ; owl:onProperty :usesMetric ; owl:someValuesFrom :Cl_EvaluationMetric ] ] .
[ rdf:type owl:Restriction ; owl:onProperty :comparesPerformance ; owl:someValuesFrom :Cl_PerformanceComparison ; rdfs:subClassOf [ rdf:type owl:Restriction ; owl:onProperty :usesMetric ; owl:minCardinality "1"^^xsd:nonNegativeInteger ] ] .
[ rdf:type owl:Restriction ; owl:onProperty :makesClaim ; owl:someValuesFrom :Cl_Claim ; rdfs:subClassOf [ rdf:type owl:Restriction ; owl:onProperty :addressesQuestion ; owl:someValuesFrom :Cl_ResearchQuestion ] ] .
New General Class Axioms for Entity Extraction and Analysis
[ rdf:type owl:Restriction ; owl:onProperty :containsEntity ; owl:someValuesFrom :Cl_Entity ; rdfs:subClassOf [ rdf:type owl:Restriction ; owl:onProperty :hasSection ; owl:someValuesFrom :Cl_PaperSection ] ] .
[ rdf:type owl:Restriction ; owl:onProperty :analyzesEntity ; owl:someValuesFrom :Cl_Entity ; rdfs:subClassOf [ rdf:type owl:Restriction ; owl:onProperty :usesAnalysisMethod ; owl:someValuesFrom :Cl_Method ] ] .
"""
def system_prompt():
    system_prompt = """You are an expert reviewer for AI conferences. You follow best practices and review papers according to the reviewer guidelines.
            Reviewer guidelines:
            1. Read the paper: It’s important to carefully read through the entire paper, and to look up any related work and citations that will help you comprehensively evaluate it. Be sure to give yourself sufficient time for this step.
            2. While reading, consider the following:
                - Objective of the work: What is the goal of the paper? Is it to better address a known application or problem, draw attention to a new application or problem, or to introduce and/or explain a new theoretical finding? A combination of these? Different objectives will require different considerations as to potential value and impact.
                - Strong points: is the submission clear, technically correct, experimentally rigorous, reproducible, does it present novel findings (e.g. theoretically, algorithmically, etc.)?
                - Weak points: is it weak in any of the aspects listed in b.?
                - Be mindful of potential biases and try to be open-minded about the value and interest a paper can hold for the community, even if it may not be very interesting for you.
            3. Answer four key questions for yourself, to make a recommendation to Accept or Reject:
                - What is the specific question and/or problem tackled by the paper?
                - Is the approach well motivated, including being well-placed in the literature?
                - Does the paper support the claims? This includes determining if results, whether theoretical or empirical, are correct and if they are scientifically rigorous.
                - What is the significance of the work? Does it contribute new knowledge and sufficient value to the community? Note, this does not necessarily require state-of-the-art results. Submissions bring value to the community when they convincingly demonstrate new, relevant, impactful knowledge (incl., empirical, theoretical, for practitioners, etc).
            4. Write your review including the following information:
                - Summarize what the paper claims to contribute. Be positive and constructive.
                - List strong and weak points of the paper. Be as comprehensive as possible.
                - Clearly state your initial recommendation (accept or reject) with one or two key reasons for this choice.
                - Provide supporting arguments for your recommendation.
                - Ask questions you would like answered by the authors to help you clarify your understanding of the paper and provide the additional evidence you need to be confident in your assessment.
                - Provide additional feedback with the aim to improve the paper. Make it clear that these points are here to help, and not necessarily part of your decision assessment.
            Your review must be formatted in markdown as follows:
            # Review
            ## Summary
            Briefly summarize the paper and its contributions. This is not the place to critique the paper; the authors should generally agree with a well-written summary.
            ## Soundness
            Please assign the paper a numerical rating on the following scale to indicate the soundness of the technical claims, experimental and research methodology and on whether the central claims of the paper are adequately supported with evidence. Choose from the following:
            4: excellent
            3: good
            2: fair
            1: poor
            ## Presentation
            Please assign the paper a numerical rating on the following scale to indicate the quality of the presentation. This should take into account the writing style and clarity, as well as contextualization relative to prior work. Choose from the following:
            4: excellent
            3: good
            2: fair
            1: poor
            ## Contribution
            Please assign the paper a numerical rating on the following scale to indicate the quality of the overall contribution this paper makes to the research area being studied. Are the questions being asked important? Does the paper bring a significant originality of ideas and/or execution? Are the results valuable to share with the broader ICLR community? Choose from the following:
            4: excellent
            3: good
            2: fair
            1: poor
            ## Strengths
            A substantive assessment of the strengths of the paper, touching on each of the following dimensions: originality, quality, clarity, and significance. We encourage reviewers to be broad in their definitions of originality and significance. For example, originality may arise from a new definition or problem formulation, creative combinations of existing ideas, application to a new domain, or removing limitations from prior results.
            ## Weaknesses
            A substantive assessment of the weaknesses of the paper. Focus on constructive and actionable insights on how the work could improve towards its stated goals. Be specific, avoid generic remarks. For example, if you believe the contribution lacks novelty, provide references and an explanation as evidence; if you believe experiments are insufficient, explain why and exactly what is missing, etc.
            ## Questions
            Please list up and carefully describe any questions and suggestions for the authors. Think of the things where a response from the author can change your opinion, clarify a confusion or address a limitation. This is important for a productive rebuttal and discussion phase with the authors.
            ## Flag For Ethics Review
            If there are ethical issues with this paper, please flag the paper for an ethics review and select area of expertise that would be most useful for the ethics reviewer to have. Please select all that apply. Choose from the following:
            No ethics review needed.
            Yes, Discrimination / bias / fairness concerns
            Yes, Privacy, security and safety
            Yes, Legal compliance (e.g., GDPR, copyright, terms of use)
            Yes, Potentially harmful insights, methodologies and applications
            Yes, Responsible research practice (e.g., human subjects, data release)
            Yes, Research integrity issues (e.g., plagiarism, dual submission)
            Yes, Unprofessional behaviors (e.g., unprofessional exchange between authors and reviewers)
            Yes, Other reasons (please specify below)
            ## Details Of Ethics Concerns
            Please provide details of your concerns.
            ## Rating
            Please provide an "overall score" for this submission. Choose from the following:
            1: strong reject
            3: reject, not good enough
            5: marginally below the acceptance threshold
            6: marginally above the acceptance threshold
            8: accept, good paper
            10: strong accept, should be highlighted at the conference
            Your response must only contain the review in markdown format with sections as defined above."""
    return system_prompt

def review_prompt(paper_abstracts):
    review_prompt = f"""Rank the following papers: {paper_abstracts}"""
    return review_prompt

def structured_rating_prompt(review):
    review_prompt = f"""Extract the ratings from the following review: {review} as JSON"""
    return review_prompt

def ner_prompt(text_input):
    ner_prompt = f"""Instruction for NER:
            You are tasked with detecting and labeling entities within text according to the following nine categories. For each segment of input, identify and tag related entities based on the description and examples provided below.
            Foundational Concepts & Theories
            Purpose: Capture the underlying ideas and theoretical frameworks that many papers build upon.
            Examples: Neural network fundamentals, overfitting vs. generalization, regularization, optimization theory, bias–variance tradeoff, interpretability, explainability.
            Models & Architectures
            Purpose: Identify and differentiate between the types of models and network structures that form the backbone of AI research.
            Examples: Convolutional Neural Networks (CNNs), Recurrent Neural Networks (RNNs), Transformers, Generative Adversarial Networks (GANs), diffusion models, specific large language models (LLMs).
            Algorithms & Learning Techniques
            Purpose: Recognize the methods and procedures used to train, optimize, and innovate within AI systems.
            Examples: Optimization algorithms (e.g., SGD, Adam, RMSProp); attention mechanisms; residual connections; learning paradigms (supervised, semi-supervised, self-supervised, reinforcement learning); transfer learning, meta-learning, few-shot learning.
            Datasets & Benchmarks
            Purpose: Highlight the empirical backbone of research by cataloging standardized datasets and benchmark tasks.
            Examples: Popular datasets (e.g., ImageNet, COCO, Common Crawl) and benchmark challenges (e.g., GLUE, SQuAD).
            Evaluation Metrics
            Purpose: Assess and measure the performance and effectiveness of AI models using standardized metrics.
            Examples: Accuracy, F1 score, perplexity, BLEU, ROUGE.
            Applications & Use Cases
            Purpose: Extract information about where and how AI is applied, thereby reflecting the practical impact of the research.
            Examples: Language generation, translation, sentiment analysis, image classification, autonomous driving, and domain-specific applications.
            Tools & Frameworks
            Purpose: Account for the software ecosystem that facilitates efficient research and development.
            Examples: Software libraries and frameworks (e.g., TensorFlow, PyTorch, Keras), dataset repositories (e.g., Hugging Face), version management tools.
            Infrastructure
            Purpose: Capture the computational and hardware resources that support AI research, development, and deployment.
            Examples: GPUs, TPUs, distributed computing systems, cloud platforms, data centers, high-performance computing setups.
            Challenges
            Purpose: Document the ongoing difficulties and limitations within AI research, reflecting both technical and ethical considerations.
            Examples: Scalability issues, data privacy concerns, interoperability challenges, ethical dilemmas, regulatory hurdles, biases, robustness or fairness problems.
            Your Task:
            Analyze incoming text and label any entity that fits one of the above categories.
            Assign clear tags that correspond to the category (for example, tag an entity as "Foundational Concepts" if it aligns with that definition).
            Ensure precision by using the provided descriptions and examples to guide your labeling.
            Maintain consistency with the revised structure to effectively distinguish between Evaluation Metrics, Infrastructure, and Challenges as separate categories.

            Here is the text for you to analyze: {text_input}"""
    return ner_prompt

def kg_prompt(text_input):
    kg_prompt = """Create a knowledge graph for all entities and their relations in a given text.

            Please output TWO parts:

            1. First, create a DOT format graph like this:

            digraph KnowledgeGraph {
            rankdir=LR;
            node [shape=box, style=filled, color=lightblue, fontname="Arial"];

            // Nodes
            LLM [label="Large Language Models (LLMs)"];
            Hallucinations [label="Hallucinations"];
            ComplexReasoning [label="Complex Reasoning Problems"];
            ...

            // Edges
            LLM -> Hallucinations [label="prone_to"];
            LLM -> ComplexReasoning [label="attempts_to_solve"];
            ...
            }

            2. After the DOT graph, ALSO output a structured JSON representation with this format:
            ```json
            {
              "raw_dot": "The complete DOT graph from above",
              "nodes": [
                {
                  "id": "LLM",
                  "label": "Large Language Models (LLMs)",
                  "properties": {}
                },
                {
                  "id": "Hallucinations",
                  "label": "Hallucinations",
                  "properties": {}
                },
                ...
              ],
              "relationships": [
                {
                  "start": {"id": "LLM"},
                  "end": {"id": "Hallucinations"},
                  "label": "prone_to",
                  "properties": {}
                },
                ...
              ]
            }
            ```

            Now create a knowledge graph for the following text:"""
    kg_prompt += f"{text_input}"
    return kg_prompt

def ocr_chunking():
    gemini_ocr_chunking = f"""\
    OCR the following page into Markdown. Tables should be formatted as HTML.
    Do not sorround your output with triple backticks.

    Chunk the document into sections of roughly 250 - 1000 words. Our goal is
    to identify parts of the page with same semantic theme. These chunks will
    be embedded and used in a RAG pipeline.

    Surround the chunks with <chunk> </chunk> html tags.
    """
    return gemini_ocr_chunking

def table_extraction():
    table_extraction = f"""\
    Extract all tables from this page and OCR them into Markdown. Before each table, mark them with a <table> tag.
    """
    return table_extraction

def table_extraction_html():
    table_extraction = f"""\
    Extract all tables from this HTML page as Markdown and create a JSON object with the following format:
    {{
        "tables": [
            {{
                "table_number": 1,
                "table_title": "Table Title Here",
                "table_description": "Table description here",
                "table_content": "Markdown table content here"
            }},
            ...
        ]
    }}
    """
    return table_extraction

def sota_extraction(text_input):
    sota_extraction = f"""\
    How much does their proposed method or framework improve upon the State-of-the-Art (SOTA)?

    Decide what the most important benchmark is and output the relative + absolute gain on that benchmark.
    Give your reasoning for why this benchmark is the most important.
    Provide the previous state of the art when giving absolute and relative gains.

    Output this in JSON format, including a score from 0-10 about how much they improved upon the SOTA.

    {text_input}
    """
    return sota_extraction

from scripts.utils.ontologies import base_ontology

def ontology_generation(rdf=base_ontology):
    CQ = """How can we effectively represent, extract, analyze, and link the key entities within LLM research papers to better understand
        their methods, findings, relationships, and contributions to the field?
        """

    story = """The domain is research papers on Large Language Models (LLMs), with a focus on their key entities and relationships.
            This ontology will model the comprehensive landscape of LLM research papers by capturing:
            - Methods, techniques, and algorithms described in papers
            - Architectures and model components
            - Datasets, benchmarks, and evaluation metrics
            - Results, findings, and performance comparisons
            - Research problems, questions, and hypotheses
            - Claims, evidence, and limitations
            - Applications and use cases
            - Ethical considerations and implications
            - Relationships between different papers and entities

            The ontology should enable comprehensive understanding of LLM research papers by:
            - Capturing fine-grained entities and their relationships
            - Supporting semantic search and knowledge discovery
            - Enabling comparative analysis across papers
            - Facilitating identification of trends, gaps, and future directions
            - Supporting extraction of key elements from papers
            """

    prompt = f"""Your task is to contribute to creating a piece of well-structured ontology by reading information that appeared in the given story, requirements, and restrictions (if there are any).
            The way you approach this is first you pick this competency question "{CQ}" and read the given turtle RDF (we append the code at the end of the previous one) to know what is the current ontology till this stage (it can be empty at the beginning). Then you add or change the RDF so it can answer this competency question. Your output at each stage is an append to the previous ones, just do not repeat. You only need to solve the question number so do not touch the next questions since they belong to the next stages of development. you can read these definisions to understand the concepts:
            lasses are the keywords/classes that are going to be node types in the knowledge graph ontology. try to extract all classes, in addition, classes are also can be defined for reification. We use Turtle Syntax for representation. Hierarchies are rdfs:subClassOf in the turtle syntax. They can be used to classify similar classes in one superclass. To do this you can find similar nodes and create/use a class as their parent class, for example, adding the node "Cl_employee" is a good middleware and superclass for "Cl_Professors" and "Cl_Administrator" if the story is about modeling ontology of a university. Mostly the lengthier the hierarchy the better. One way can be categorizing classes into several classes and creating superclasses for them. Important: Class names have Cl_ as the prefix for example Cl_Professors. Also keep in mind you can add Equivalent To, General class axioms, Disjoint with, and Disjoint Union of, for each class.
            In your ontology modeling, for each competency question, when faced with complex scenarios that involve more than two entities or a combination of entities and datatypes, apply reification. Specifically, create a pivot class to act as an intermediary for these entities, ensuring the nuanced relationships are accurately captured. For instance, when representing "a user accessed a resource at a given time", establish a pivot class like Cl_UserResourceUsage, linked from the user, resource, and the specific time of access to Cl_UserResourceInteraction, rather than directly connecting the user to both the resource and time.
            Then you need to create properties (owl:Property). In this step, you use classes from the previous stage and create object and data properties to connect them and establish the ontology. Always output a turtle syntax, if you need more classes to model a competency question between more than 2 concepts, feel free to add more pivot (reification) classes here. try to find as much relation as possible by reading competency questions, restrictions, and stories. At this stage, you can create both data and object properties. Data properties are between classes or hierarchy classes and data types such as xsd:string, xsd:integer, xsd:decimal, xsd:dateTime, xsd:date, xsd:time, xsd:boolean, xsd:byte, xsd:double, xsd:float and etc. For example, in the university domain, we have: employee_id a owl:Property ; rdfs:domain :cl_teacher ; rdfs:range xsd:integer. Object properties are between classes. try to find as much relation as possible by reading competency questions and the story. Feel free to use rdfs:subPropertyOf for creating hierarchies for relations. For modeling properties (object or data properties) if it is necessary, use these relations characteristics: Functional, Inverse functional, Transitive, Symmetric, Asymmetric, Reflexive, and Irreflexive. Also, you are flexible in domain and range so you can use Cl_class1 or Cl_class2 in domain and range or disjoint with, the inverse of between relations.
            It is common to forget to add relations that are related to reification: In RDF reification, achieving precise modeling is pivotal, especially when handling multifaceted scenarios where mere binary associations fall short. Take for instance the statement, "a user used a resource at a time". While it might initially seem to involve a direct link between a 'user' and a 'resource', it inherently embodies three entities: a 'user', a 'resource', and a 'time'. Directly connecting 'user' to both 'resource' and 'time' fails to capture the essence of the statement, as it obscures which resource was utilized by the user at a specific time. To address this, a more sophisticated modeling approach is needed, invoking a pivot class, Cl_usingResource. This pivot class acts as an intermediary, linking both Cl_user and Cl_resource. Furthermore, it integrates a time property to denote the exact instance of usage. By employing this method, we can coherently model the statement, ensuring that the user's interaction with a specific resource at a distinct time is unambiguously represented. This approach highlights the imperative of ontology design patterns and the necessity of intermediary nodes when modeling complex relationships involving multiple entities or a mix of entities and datatypes.
            Upon implementation of restrictions, feel free to use owl:equivalentClass [ rdf:type owl:Restriction ;  owl:onProperty :{{relation}} ;  owl:allValuesFrom :{{Class}} ] ; in this way, you can put restrictions for classes such as class Cl_C1 is the only class that uses the relation R. or you can put soft restrictions by using owl:someValuesFrom. Also, you can use general class axioms: [ rdf:type owl:Restriction ; owl:onProperty :R1 ; owl:someValuesFrom :Cl_1 ; rdfs:subClassOf :Cl_2 ] when you want to put restrictions on the definition of a class based on its relation and the definition is necessary but not enough (if it is enough it would be equivalent to owl:equivalentClass).

            these are the prifixes:
            @prefix : <http://www.example.org/test#> .
            @prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
            @prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
            @prefix xsd: <http://www.w3.org/2001/XMLSchema#> .
            @prefix owl: <http://www.w3.org/2002/07/owl#>.

            Important: before writing the owl code, write this text: is this competency question answerable by the previous version of the RDF (given down) or not? (most likely (~90%) is it not answerable). If no, write a reification class for this question if needed. then solve it. if it was answerable, simply rewrite the given rdf in the output without changing it.
            here is one story:

            {story}

            End of story
            here are some possible mistakes that you might make:
            1- forgetting to add prefixes at the beginning of the code.
            2- forgetting to write pivot classes at the beginning before starting to code.
            3- your output would be concatenated to the previous output rdf, so don't write repetitive words, classes, or ...
            4- in your output put all of the previous RDF classes, relations, and restrictions and add yours. your output will be passed to the next stage so don't remove previous code (it is going to replace the previous rdf)
            5- you usually forget to write the name of the reification (pivot) that you want to create at the beginning of the output
            6- In reification, the reification node (pivot class) is connected to all related classes by object properties, not by the subclassof. it can be a subclass of something, but for reification, it needs object properties.
            common mistakes in extracting classes:
            1- mistake: not extracting all classes and missing many of them. classes can be found in the story, or in the competency question number and restrictions.
            2- Returning empty answer
            3- Providing comments or explanations
            4- Extracint classes like 'Date', and 'integer' are wrong since they are data properties.
            5- not using RDF reification: not extracting pivot classes for modeling relation between classes (more than one class and one data property, or more than two classes)
            6- extracting individuals in the text as a class
            7- The pivot class is not a sublcass of its components.
            common mistakes in the hierarchy extraction:
            1- creating an ontology for non-existing classes: creating a new leaf and expanding it into the root
            2- returning empty answer or very short
            3- Providing comments or explanations
            4- Extracting attributes such as date, time, and string names that are related to data properties
            5- Forget to add "" around the strings in the tuples
            Common mistakes in the object_properties:
            1- returning new variables with anything except object_properties
            2- returning empty answer or very short
            3- providing comments or explanations
            4- when the pivot class is created, all of the related classes should point to it (direction of relation is from the classes (domains) 'to'  pivot class (range))
            Common mistakes in the data_properties:
            1- returning new variables with anything except data_properties
            2- returning empty answer or very short
            3- providing comments or explanations
            Here is the last RDF:
            {rdf}
            """
    return prompt

def mermaid_prompt(concise_summary):
    prompt = f"""{concise_summary}
    Extract the most important statement from this summary and turn it and ONLY it into a knowledge graph (node -edge-> node) mermaid diagram

    graph TD
        A["Speculative Decoding"] -- "boosts" --> B["Functional Accuracy on RTLLM"]
        B -- "by" --> C["17.19%"]
        C -- "bridges" --> D["Quality Gap for Specialized Languages"]

        %% Node styling for Dark Mode
        classDef nodeStyle fill:#1e293b,stroke:#38bdf8,stroke-width:2px,rx:10,ry:10,font-size:14px,font-weight:bold,color:#e2e8f0;
        class A,B,C,D nodeStyle;

        %% Connection styling
        linkStyle 0,1,2 stroke:#38bdf8,stroke-width:2px;

    Use this format and styling"""
    return prompt
"""
API route handlers for the FastMoatless application.
"""

def register_all_routes(app, rt, collections):
    """Register all API routes with the application"""
    from . import stats
    from . import saved_papers
    from . import search
    from . import knowledge_graph
    from . import entity_resolution
    from . import mermaid
    from . import recommendations
    from . import tables

    # Register routes from each module
    stats.register_routes(app, rt, collections)
    saved_papers.register_routes(app, rt, collections)
    search.register_routes(app, rt, collections)
    knowledge_graph.register_routes(app, rt, collections)
    entity_resolution.register_routes(app, rt, collections)
    mermaid.register_routes(app, rt, collections)
    recommendations.register_routes(app, rt, collections)
    tables.register_routes(app, rt, collections)

    return app
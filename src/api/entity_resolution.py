"""
Routes for entity resolution API endpoints.
"""
from typing import Optional, Dict, Any, List
import json
from datetime import datetime
from starlette.requests import Request
from starlette.responses import JSONResponse, RedirectResponse
from pydantic import BaseModel
from fasthtml.common import *
from monsterui.all import *

from src.ui import Navigation
from src.services.entity_resolution import EntityResolutionService
from src.models.entity import ResolvedEntity, EntityResolutionResponse, EntityListResponse

def register_routes(app, rt, collections):
    """Register routes related to entity resolution"""
    
    # Initialize the entity resolution service
    entity_service = EntityResolutionService(collections)
    
    @rt('/entities/resolve')
    async def resolve_entities(request: Request):
        """
        Trigger the entity resolution process
        Returns status of the operation
        """
        force_update = request.query_params.get('force', '').lower() == 'true'
        
        # Run the entity resolution process
        result = await entity_service.resolve_entities(force_update=force_update)
        
        # Return the structured response
        return JSONResponse(result.dict())
    
    @rt('/entities/resolved')
    async def get_resolved_entities(request: Request):
        """
        Get resolved entities with optional filtering
        """
        entity_type = request.query_params.get('type')
        limit = int(request.query_params.get('limit', '100'))
        skip = int(request.query_params.get('skip', '0'))
        
        # Get entities from the service
        response = await entity_service.get_resolved_entities(
            entity_type=entity_type,
            limit=limit,
            skip=skip
        )
        
        # Return the structured response
        return JSONResponse(response.dict())
    
    @rt('/entities/entity/{entity_id}')
    async def get_entity(request: Request):
        """
        Get a single entity by ID
        """
        entity_id = request.path_params.get('entity_id')
        
        if not entity_id:
            return JSONResponse({"error": "Entity ID is required"}, status_code=400)
        
        # Get the entity from the service
        entity = await entity_service.get_entity_by_id(entity_id)
        
        if not entity:
            return JSONResponse({"error": "Entity not found"}, status_code=404)
        
        # Return the structured entity model
        return JSONResponse(entity.dict())
    
    @app.post('/entities/update/{entity_id}')
    async def update_entity(request: Request):
        """
        Update an entity manually
        """
        entity_id = request.path_params.get('entity_id')
        
        if not entity_id:
            return JSONResponse({"error": "Entity ID is required"}, status_code=400)
        
        try:
            # Parse the request body
            body = await request.json()
            
            # Update the entity
            result = await entity_service.update_entity_manually(entity_id, body)
            
            # Return the structured response
            return JSONResponse(result.dict())
        except Exception as e:
            error_response = EntityResolutionResponse(
                status="error",
                message=f"Error updating entity: {str(e)}",
                updated=0,
                new=0,
                total=0
            )
            return JSONResponse(error_response.dict(), status_code=500)
    
    @rt('/entities/dashboard')
    async def entity_dashboard(request: Request):
        """Display entity resolution dashboard"""
        # Get statistics about resolved entities
        entity_types = {}
        
        # Count entities by type
        for entity_type in ["method", "dataset", "concept", "entity"]:
            response = await entity_service.get_resolved_entities(entity_type=entity_type, limit=1000)
            if response.count > 0:
                entity_types[entity_type] = response.count
        
        # Get recently resolved entities
        recent_response = await entity_service.get_resolved_entities(limit=10)
        recent_entities = recent_response.entities
        
        # Generate the dashboard HTML
        return Navigation(request), Title("moatless | Entity Resolution"), Body(
            Div(
                Div(
                    Div(
                        H1("Entity Resolution Dashboard", cls="text-3xl sm:text-4xl font-bold text-white mb-6 text-center"),
                        P("View and manage resolved entities across papers", cls="text-xl text-center text-gray-400 font-light mb-8"),
                        Div(
                            A("Resolve Entities", href="/entities/resolve?force=false", cls="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded text-white font-medium mx-2"),
                            A("Force Full Update", href="/entities/resolve?force=true", cls="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 rounded text-white font-medium mx-2"),
                            cls="flex justify-center items-center"
                        ),
                        cls="flex flex-col items-center w-full max-w-5xl mx-auto px-6"
                    ),
                    cls="py-8 sm:py-12 w-full bg-[#1e2532] border-b border-gray-800"
                ),
                Div(
                    Div(
                        H2("Entity Statistics", cls="text-2xl font-bold text-white mb-6"),
                        Div(
                            *(Div(
                                H3(f"{entity_type.title()} Entities", cls="text-lg font-semibold text-white mb-2"),
                                P(f"{count} total entities", cls="text-gray-400 text-sm mb-2"),
                                A(f"View All", href=f"/entities/resolved?type={entity_type}", cls="text-blue-400 hover:text-blue-300 text-sm"),
                                cls="bg-gray-800 rounded-lg p-4 flex flex-col"
                            ) for entity_type, count in entity_types.items()),
                            cls="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-10"
                        ),
                        H2("Recently Resolved Entities", cls="text-2xl font-bold text-white mb-6"),
                        Div(
                            Table(
                                Thead(
                                    Tr(
                                        Th("Canonical Name", cls="px-4 py-2 text-left"),
                                        Th("Type", cls="px-4 py-2 text-left"),
                                        Th("Variants", cls="px-4 py-2 text-left"),
                                        Th("Actions", cls="px-4 py-2 text-left")
                                    ),
                                    cls="bg-gray-700"
                                ),
                                Tbody(
                                    *(Tr(
                                        Td(entity.canonical_name or "Unknown", cls="px-4 py-2 border-t border-gray-700"),
                                        Td(entity.entity_type or "Unknown", cls="px-4 py-2 border-t border-gray-700"),
                                        Td(f"{len(entity.variant_ids)} variants", cls="px-4 py-2 border-t border-gray-700"),
                                        Td(
                                            A("View", href=f"/entities/entity/{entity.canonical_id}", cls="text-blue-400 hover:text-blue-300 mr-3"),
                                            cls="px-4 py-2 border-t border-gray-700"
                                        )
                                    ) for entity in recent_entities),
                                    cls="bg-gray-800"
                                ),
                                cls="w-full text-white border-collapse"
                            ) if recent_entities else P("No entities have been resolved yet.", cls="text-gray-400 text-center py-8"),
                            cls="bg-gray-800 rounded-lg overflow-hidden shadow-lg"
                        )
                    ),
                    cls="w-full max-w-6xl mx-auto px-4 sm:px-6 py-10"
                ),
                cls="min-h-screen bg-[#1e2532] flex flex-col items-center w-full"
            )
        )
"""
Routes for handling knowledge graph visualization and entity descriptions.
"""
from typing import Op<PERSON>, Dict, Any
import json
import sys
from datetime import datetime
from starlette.requests import Request
from starlette.responses import JSONResponse, RedirectResponse
from fasthtml.common import *
from monsterui.all import *

from src.ui import Navigation
from src.models.kg import KnowledgeGraphData, EntityNode, RelationEdge

def register_routes(app, rt, collections):
    """Register routes related to knowledge graph visualization"""
    
    knowledge_graphs_collection = collections['knowledge_graphs']
    summaries_collection = collections['summaries']
    # The entities are stored in the summaries collection, not as a separate collection
    
    # Create or update the main_graph by aggregating all individual knowledge graphs
    def update_main_knowledge_graph():
        """
        Aggregate all individual paper knowledge graphs into a single main graph
        for visualization purposes
        """
        print("\n======== UPDATING MAIN KNOWLEDGE GRAPH ========")
        
        # Check if we already have a main graph
        existing_main_graph = knowledge_graphs_collection.find_one({"name": "main_graph"})
        if existing_main_graph:
            print("Main graph already exists")
            return existing_main_graph
            
        # Get all knowledge graphs from the collection
        all_graphs = list(knowledge_graphs_collection.find(
            {"abs_url": {"$exists": True}},  # Only get actual paper KGs, not the main graph
        ))
        
        if not all_graphs:
            print("No knowledge graphs found to aggregate")
            
            # For testing only - create a sample graph
            import os
            if '--local' in sys.argv or os.environ.get('LOCAL_MODE') == 'true':
                print("Creating sample knowledge graph for testing...")
                # Create sample graph
                main_nodes = []
                main_links = []
                
                # Create some key concepts as nodes
                concepts = [
                    {'id': 'LLM', 'name': 'Large Language Models', 'type': 'concept', 'count': 15},
                    {'id': 'RAG', 'name': 'Retrieval Augmented Generation', 'type': 'concept', 'count': 10},
                    {'id': 'CV', 'name': 'Computer Vision', 'type': 'concept', 'count': 12},
                    {'id': 'NLP', 'name': 'Natural Language Processing', 'type': 'concept', 'count': 20},
                    {'id': 'RL', 'name': 'Reinforcement Learning', 'type': 'concept', 'count': 8},
                    {'id': 'Transformer', 'name': 'Transformer Architecture', 'type': 'method', 'count': 18},
                    {'id': 'BERT', 'name': 'BERT', 'type': 'method', 'count': 7},
                    {'id': 'GPT', 'name': 'GPT', 'type': 'method', 'count': 14},
                    {'id': 'ResNet', 'name': 'ResNet', 'type': 'method', 'count': 5},
                    {'id': 'CLIP', 'name': 'CLIP', 'type': 'method', 'count': 6},
                    {'id': 'Robustness', 'name': 'Robustness', 'type': 'concept', 'count': 9},
                    {'id': 'Efficiency', 'name': 'Efficiency', 'type': 'concept', 'count': 11},
                    {'id': 'Ethics', 'name': 'AI Ethics', 'type': 'concept', 'count': 7},
                    {'id': 'ImageNet', 'name': 'ImageNet', 'type': 'dataset', 'count': 4},
                    {'id': 'GLUE', 'name': 'GLUE Benchmark', 'type': 'dataset', 'count': 3},
                    {'id': 'MLLMs', 'name': 'Multimodal Large Language Models (MLLMs)', 'type': 'entity', 'count': 56},
                    {'id': 'ImageSequences', 'name': 'Image Sequences', 'type': 'entity', 'count': 30},
                ]
                main_nodes.extend(concepts)
                
                # Create relationships between concepts
                relationships = [
                    {'source': 'LLM', 'target': 'NLP', 'type': 'RELATED_TO', 'value': 1},
                    {'source': 'LLM', 'target': 'Transformer', 'type': 'USES', 'value': 1},
                    {'source': 'LLM', 'target': 'BERT', 'type': 'INCLUDES', 'value': 1},
                    {'source': 'LLM', 'target': 'GPT', 'type': 'INCLUDES', 'value': 1},
                    {'source': 'RAG', 'target': 'LLM', 'type': 'USES', 'value': 1},
                    {'source': 'BERT', 'target': 'Transformer', 'type': 'BASED_ON', 'value': 1},
                    {'source': 'GPT', 'target': 'Transformer', 'type': 'BASED_ON', 'value': 1},
                    {'source': 'CV', 'target': 'ResNet', 'type': 'USES', 'value': 1},
                    {'source': 'CV', 'target': 'CLIP', 'type': 'USES', 'value': 1},
                    {'source': 'CV', 'target': 'ImageNet', 'type': 'USES_DATASET', 'value': 1},
                    {'source': 'NLP', 'target': 'GLUE', 'type': 'USES_BENCHMARK', 'value': 1},
                    {'source': 'CLIP', 'target': 'CV', 'type': 'USED_IN', 'value': 1},
                    {'source': 'CLIP', 'target': 'NLP', 'type': 'USED_IN', 'value': 1},
                    {'source': 'RL', 'target': 'Robustness', 'type': 'IMPROVES', 'value': 1},
                    {'source': 'Efficiency', 'target': 'LLM', 'type': 'CHALLENGE_FOR', 'value': 1},
                    {'source': 'Ethics', 'target': 'LLM', 'type': 'CONCERN_FOR', 'value': 1},
                    {'source': 'MLLMs', 'target': 'ImageSequences', 'type': 'struggle_with_reasoning', 'value': 1},
                ]
                main_links.extend(relationships)
                
                # Create the main_graph document
                main_graph = {
                    "name": "main_graph",
                    "nodes": main_nodes,
                    "links": main_links,
                    "updated_at": datetime.now().isoformat()
                }
                
                # Update or insert the main_graph in the database
                knowledge_graphs_collection.update_one(
                    {"name": "main_graph"},
                    {"$set": main_graph},
                    upsert=True
                )
                
                print(f"Created sample knowledge graph with {len(main_nodes)} nodes and {len(main_links)} links")
                return main_graph
            
            return None
            
        print(f"Found {len(all_graphs)} knowledge graphs to aggregate")
        
        # Debug: show some info about each knowledge graph
        for i, graph in enumerate(all_graphs[:3]):  # Show first 3 for brevity
            print(f"\nGraph {i+1}:")
            print(f"  Title: {graph.get('title', 'No title')}")
            print(f"  URL: {graph.get('abs_url', 'No URL')}")
            print(f"  Nodes: {len(graph.get('nodes', []))}")
            print(f"  Relationships: {len(graph.get('relationships', []))}")
            print(f"  Links: {len(graph.get('links', []))}")
            
            # Show a sample node to debug format
            if graph.get('nodes') and len(graph.get('nodes')) > 0:
                print(f"\n  Sample node: {graph['nodes'][0]}")
        
        print(f"Aggregating {len(all_graphs)} knowledge graphs into main_graph")
        
        # Create node and link mappings to avoid duplicates
        all_nodes = {}
        all_links = []
        
        # Process each knowledge graph
        for graph in all_graphs:
            # Process nodes - convert from Neo4j format to D3.js format
            for node in graph.get('nodes', []):
                # Handle different node formats
                node_id = None
                node_name = None
                node_type = "concept"  # Default type
                
                # Extract node ID
                if isinstance(node, dict) and 'id' in node:
                    node_id = node.get('id')
                else:
                    # Skip nodes without an ID
                    continue
                    
                # Get node name and type based on format
                if 'properties' in node:
                    # Neo4j format
                    properties = node.get('properties', {})
                    node_name = properties.get('name', node_id)
                    
                    # Extract labels if available
                    labels = node.get('labels', [])
                    if labels and len(labels) > 0:
                        if "Method" in labels:
                            node_type = "method"
                        elif "Dataset" in labels:
                            node_type = "dataset"
                        elif "Entity" in labels:
                            node_type = "entity"
                else:
                    # D3.js format
                    node_name = node.get('name', node_id)
                    node_type = node.get('type', "concept")
                
                # Make sure we have a name (use ID if no name was found)
                if not node_name:
                    node_name = node_id
                
                # Create or update node
                if node_id in all_nodes:
                    # Increment count for existing node
                    all_nodes[node_id]["count"] += 1
                else:
                    # Add new node
                    all_nodes[node_id] = {
                        "id": node_id,
                        "name": node_name,
                        "type": node_type,
                        "count": 1
                    }
            
            # Process relationships/edges - convert from Neo4j format to D3.js format
            # Check if the graph has 'relationships' (Neo4j format), 'edges' (DOT format) or 'links' (D3.js format)
            relationships = graph.get('relationships', graph.get('edges', graph.get('links', [])))
            
            for rel in relationships:
                if not isinstance(rel, dict):
                    continue
                    
                source_id = None
                target_id = None
                rel_type = 'RELATES_TO'  # Default relationship type
                
                # Handle all possible relationship formats
                if 'start' in rel and 'end' in rel:
                    # Neo4j format
                    if isinstance(rel['start'], dict) and 'id' in rel['start']:
                        source_id = rel['start']['id']
                    else:
                        source_id = rel.get('start')
                        
                    if isinstance(rel['end'], dict) and 'id' in rel['end']:
                        target_id = rel['end']['id']
                    else:
                        target_id = rel.get('end')
                        
                    rel_type = rel.get('type', 'RELATES_TO')
                elif 'source' in rel and 'target' in rel:
                    # DOT graph format or D3.js format
                    source_id = rel.get('source')
                    target_id = rel.get('target')
                    rel_type = rel.get('relationship', rel.get('type', 'RELATES_TO'))
                
                # Skip relationships without source or target
                if not source_id or not target_id:
                    continue
                
                # Add link
                link = {
                    "source": source_id,
                    "target": target_id,
                    "value": 1,
                    "type": rel_type
                }
                
                all_links.append(link)
        
        # Convert node dictionary to list
        nodes_list = list(all_nodes.values())
        
        # Create the main_graph document
        main_graph = {
            "name": "main_graph",
            "nodes": nodes_list,
            "links": all_links,
            "updated_at": datetime.now().isoformat()
        }
        
        # Update or insert the main_graph in the database
        knowledge_graphs_collection.update_one(
            {"name": "main_graph"},
            {"$set": main_graph},
            upsert=True
        )
        
        print(f"Updated main_graph with {len(nodes_list)} nodes and {len(all_links)} links")
        return main_graph
    
    # Update the main_graph when routes are registered
    # First, print the contents of the knowledge_graphs_collection for debugging
    all_docs = list(knowledge_graphs_collection.find({}))
    print(f"Knowledge graphs collection has {len(all_docs)} documents")
    
    # Check if there are any KGs from extract_kg.py
    kg_documents_count = knowledge_graphs_collection.count_documents({})
    print(f"Found {kg_documents_count} knowledge graph documents in the database")
    
    # Now update the main graph
    update_main_knowledge_graph()
    
    @rt('/graph')
    async def graph(request: Request):
        """Redirect to interactive knowledge graph visualization"""
        return RedirectResponse(url='/interactive_graph', status_code=303)
    
    @app.get('/interactive_graph')
    async def interactive_graph(request):
        """Generate an interactive graph visualization"""
        print("\n======== INTERACTIVE GRAPH VISUALIZATION ========")
        
        # Check if we're in local mode and if Neo4j is available
        try:
            # Import the helper function to check Neo4j availability
            from main import is_neo4j_available, local_mode
            
            # In local mode without Neo4j, use sample data
            if local_mode and not is_neo4j_available():
                print("Local mode without Neo4j - using sample graph data")
                # Force the use of the sample graph in update_main_knowledge_graph
                knowledge_graph = update_main_knowledge_graph()
                if not knowledge_graph:
                    print("Failed to create sample graph")
            else:
                # Normal operation - check if main_graph exists
                knowledge_graph = knowledge_graphs_collection.find_one({"name": "main_graph"})
                print(f"Retrieved main_graph: {knowledge_graph is not None}")
                
                # If no main_graph, try to update it now
                if not knowledge_graph:
                    print("Main graph not found, attempting to create it now.")
                    knowledge_graph = update_main_knowledge_graph()
        except ImportError:
            # If we can't import the function, just proceed normally
            knowledge_graph = knowledge_graphs_collection.find_one({"name": "main_graph"})
            print(f"Retrieved main_graph: {knowledge_graph is not None}")
            
            # If no main_graph, try to update it now
            if not knowledge_graph:
                print("Main graph not found, attempting to create it now.")
                knowledge_graph = update_main_knowledge_graph()
        
        # Debug the graph structure
        if knowledge_graph:
            print(f"Main graph has nodes: {'nodes' in knowledge_graph}")
            print(f"Main graph has links: {'links' in knowledge_graph}")
            print(f"Main graph has relationships: {'relationships' in knowledge_graph}")
            
            if 'nodes' in knowledge_graph:
                print(f"Number of nodes: {len(knowledge_graph['nodes'])}")
                # Show sample node format for debugging
                if knowledge_graph['nodes']:
                    print(f"Sample node format: {knowledge_graph['nodes'][0]}")
            
            if 'links' in knowledge_graph:
                print(f"Number of links: {len(knowledge_graph['links'])}")
                # Show sample link format for debugging
                if knowledge_graph['links']:
                    print(f"Sample link format: {knowledge_graph['links'][0]}")
            
            if 'relationships' in knowledge_graph:
                print(f"Number of relationships: {len(knowledge_graph['relationships'])}")
                # Show sample relationship format for debugging
                if knowledge_graph['relationships']:
                    print(f"Sample relationship format: {knowledge_graph['relationships'][0]}")
        
        # Check if there's valid data to display
        has_valid_data = (knowledge_graph and 
                         'nodes' in knowledge_graph and len(knowledge_graph['nodes']) > 0 and
                         ('links' in knowledge_graph and len(knowledge_graph['links']) > 0 or
                          'relationships' in knowledge_graph and len(knowledge_graph['relationships']) > 0))
        
        print(f"Has valid data for display: {has_valid_data}")
        
        if not has_valid_data:
            return Navigation(request), Title("moatless | Knowledge Graph"), Body(
                Div(
                    Div(
                        H1("Knowledge Graph", cls="text-3xl sm:text-4xl font-bold text-white mb-6 text-center"),
                        P("No knowledge graph available yet.", cls="text-xl text-center text-gray-400 font-light my-16"),
                        cls="py-8 sm:py-12 w-full bg-[#1e2532] border-b border-gray-800"
                    ),
                    cls="min-h-screen bg-[#1e2532] flex flex-col items-center w-full"
                )
            )
        
        # Format data for visualization
        graph_data = format_graph_data(knowledge_graph)
        
        # Debug check if the data was properly formatted
        if graph_data:
            print(f"Formatted graph data successfully - nodes: {len(graph_data.get('nodes', []))}, links: {len(graph_data.get('links', []))}")
            print("Generated graph data structure is ready for D3.js visualization")
            # Debug: inspect a sample of the JSON to ensure it's valid
            debug_json = json.dumps(graph_data)
            print(f"JSON sample (first 200 chars): {debug_json[:200]}")
        else:
            print("ERROR: Failed to format graph data")
            
        # Generate the visualization HTML
        return Navigation(request), Title("moatless | Knowledge Graph"), Body(
            Div(
                Div(
                    Div(
                        H1("Knowledge Graph", cls="text-3xl sm:text-4xl font-bold text-white mb-6 text-center"),
                        P("Explore the relationships between entities extracted from papers", 
                          cls="text-xl text-center text-gray-400 font-light mb-8"),
                        cls="flex flex-col items-center w-full max-w-5xl mx-auto px-6"
                    ),
                    cls="py-8 sm:py-12 w-full bg-[#1e2532] border-b border-gray-800"
                ),
                Script(src="https://d3js.org/d3.v7.min.js"),
                Div(
                    Div(
                        Div(
                            Div(id="graph-container", cls="w-full h-[800px] bg-gray-900 rounded-lg"),
                            Div(id="entity-details", cls="w-full mt-6 p-6 bg-gray-800 rounded-lg hidden"),
                            cls="w-full"
                        ),
                        Script("""
                            // Knowledge graph data
                            const fullGraphData = """ + json.dumps(graph_data) + """;
                            console.log("Graph data loaded:", fullGraphData);
                            
                            // Initialize force-directed graph
                            document.addEventListener('DOMContentLoaded', function() {
                                console.log('DOM loaded, D3 version:', d3.version);
                                
                                // Create UI elements
                                const controlsContainer = document.createElement('div');
                                controlsContainer.className = 'p-3 bg-gray-800 text-white rounded-lg mb-3 flex flex-wrap items-center justify-between';
                                
                                const statusElement = document.createElement('div');
                                statusElement.className = 'mb-2 sm:mb-0';
                                statusElement.textContent = 'Total dataset: ' + fullGraphData.nodes.length + ' nodes and ' + fullGraphData.links.length + ' links. Showing nodes with 1+ connections.';
                                
                                const buttonContainer = document.createElement('div');
                                buttonContainer.className = 'flex space-x-3';
                                
                                // No buttons needed for simplified view
                                buttonContainer.style.display = 'none';
                                
                                controlsContainer.appendChild(statusElement);
                                controlsContainer.appendChild(buttonContainer);
                                
                                document.getElementById('graph-container').appendChild(controlsContainer);
                                
                                // Create simplified legend
                                const legendContainer = document.createElement('div');
                                legendContainer.className = 'absolute top-16 right-6 p-4 bg-gray-800 bg-opacity-90 text-white rounded-lg shadow-lg';
                                legendContainer.style.zIndex = '1000';
                                legendContainer.innerHTML = `
                                    <div class="text-md font-bold mb-3">Entity Graph</div>
                                    <div class="flex items-center mb-2">
                                        <span class="inline-block w-4 h-4 rounded-full mr-3" style="background-color: #3b82f6;"></span>
                                        <span class="text-sm">Entities</span>
                                    </div>
                                    <div class="text-sm mt-2 text-gray-300">
                                        Showing entities with 1+ connections
                                    </div>
                                `;
                                document.getElementById('graph-container').appendChild(legendContainer);
                                
                                // Instructions
                                const instructionsElement = document.createElement('div');
                                instructionsElement.className = 'absolute top-6 left-6 p-4 bg-gray-800 bg-opacity-90 text-white rounded-lg shadow-lg';
                                instructionsElement.style.zIndex = '1000';
                                instructionsElement.innerHTML = `
                                    <div class="text-md font-bold mb-2">Instructions</div>
                                    <ul class="text-sm space-y-1 text-gray-300">
                                        <li>• Click on a node to see entity details</li>
                                        <li>• Drag to pan the view</li>
                                        <li>• Scroll to zoom in/out</li>
                                    </ul>
                                `;
                                document.getElementById('graph-container').appendChild(instructionsElement);
                                
                                // Count the number of connections for each node
                                const nodeConnections = {};
                                
                                // Initialize connection counter for each node
                                fullGraphData.nodes.forEach(node => {{
                                    nodeConnections[node.id] = 0;
                                }});
                                
                                // Count connections for each node based on links
                                fullGraphData.links.forEach(link => {{
                                    const sourceId = typeof link.source === 'object' ? link.source.id : link.source;
                                    const targetId = typeof link.target === 'object' ? link.target.id : link.target;
                                    
                                    if (sourceId in nodeConnections) nodeConnections[sourceId]++;
                                    if (targetId in nodeConnections) nodeConnections[targetId]++;
                                }});
                                
                                // Filter nodes with at least 1 connection (reduced from 2 to ensure more nodes are displayed)
                                const connectedNodes = fullGraphData.nodes
                                    .filter(node => nodeConnections[node.id] >= 1)
                                    .sort((a, b) => nodeConnections[b.id] - nodeConnections[a.id]);
                                
                                // Create a set of node IDs we're rendering
                                const nodeIds = new Set(connectedNodes.map(n => n.id));
                                
                                // Filter links to only include those connecting our rendered nodes
                                const relevantLinks = fullGraphData.links
                                    .filter(l => {{
                                        const sourceId = typeof l.source === 'object' ? l.source.id : l.source;
                                        const targetId = typeof l.target === 'object' ? l.target.id : l.target;
                                        return nodeIds.has(sourceId) && nodeIds.has(targetId);
                                    }});
                                
                                // Our filtered graph data
                                const graphData = {{
                                    nodes: connectedNodes,
                                    links: relevantLinks
                                }};
                                
                                console.log('Displaying ' + graphData.nodes.length + ' nodes and ' + graphData.links.length + ' links');
                                
                                // Check if we have anything to display
                                if (graphData.nodes.length === 0) {{
                                    console.error("ERROR: No nodes to display in the graph!");
                                    console.log("Original data nodes:", fullGraphData.nodes.length);
                                    console.log("Original data links:", fullGraphData.links.length);
                                }}
                                
                                // Set up the D3 visualization
                                const width = document.getElementById('graph-container').offsetWidth;
                                const height = 700;
                                
                                const svg = d3.select('#graph-container')
                                    .append('svg')
                                    .attr('width', width)
                                    .attr('height', height);
                                
                                // Create main group for all graph elements
                                const g = svg.append('g');
                                
                                // Add zoom behavior
                                const zoom = d3.zoom().on('zoom', (event) => {{
                                    g.attr('transform', event.transform);
                                }});
                                
                                svg.call(zoom);
                                
                                // Create force simulation with simpler forces
                                const simulation = d3.forceSimulation(graphData.nodes)
                                    .force('link', d3.forceLink(graphData.links).id(d => d.id).distance(120)) // Increased distance for better edge label visibility
                                    .force('charge', d3.forceManyBody().strength(-200))
                                    .force('center', d3.forceCenter(width / 2, height / 2))
                                    .force('collision', d3.forceCollide().radius(30))
                                    .force('x', d3.forceX(width / 2).strength(0.05))
                                    .force('y', d3.forceY(height / 2).strength(0.05));
                                
                                // Add forces for clustering by node type
                                const forcesByType = {{
                                    'concept': d3.forceX(width * 0.25).strength(0.3),
                                    'method': d3.forceX(width * 0.75).strength(0.3),
                                    'dataset': d3.forceX(width * 0.5).strength(0.3),
                                    'entity': d3.forceX(width * 0.6).strength(0.3)
                                }};
                                
                                const forceYByType = {{
                                    'concept': d3.forceY(height * 0.25).strength(0.3),
                                    'method': d3.forceY(height * 0.75).strength(0.3),
                                    'dataset': d3.forceY(height * 0.5).strength(0.3),
                                    'entity': d3.forceY(height * 0.6).strength(0.3)
                                }};
                                
                                // Apply initial cluster forces
                                Object.keys(forcesByType).forEach(type => {{
                                    simulation.force(`x-${{type}}`, forcesByType[type])
                                           .force(`y-${{type}}`, forceYByType[type]);
                                }});
                                
                                // Draw links with improved styling and labels
                                const linkGroup = g.append('g')
                                    .selectAll('g')
                                    .data(graphData.links)
                                    .enter().append('g');
                                    
                                // Add line elements
                                const link = linkGroup.append('line')
                                    .attr('stroke', '#aaa')
                                    .attr('stroke-opacity', 0.7)
                                    .attr('stroke-width', d => Math.max(1.2, Math.min(3.5, Math.sqrt(d.value) * 1.2)));
                                    
                                // Add edge labels
                                linkGroup.append('text')
                                    .attr('dy', -5)
                                    .attr('text-anchor', 'middle')
                                    .attr('fill', '#fff')
                                    .attr('font-size', '10px')
                                    .text(d => d.type);
                                
                                // Create node groups
                                const node = g.append('g')
                                    .selectAll('.node')
                                    .data(graphData.nodes)
                                    .enter().append('g')
                                    .attr('class', 'node')
                                    .call(d3.drag()
                                        .on('start', dragstarted)
                                        .on('drag', dragged)
                                        .on('end', dragended))
                                    .on('click', showEntityDetails);
                                
                                // Add circles to nodes with blue styling
                                node.append('circle')
                                    .attr('r', d => Math.max(6, Math.min(18, 8 + Math.sqrt(d.count) * 2)))
                                    .attr('fill', '#3b82f6') /* Blue for all nodes */
                                    .attr('stroke', '#000')
                                    .attr('stroke-opacity', 0.7)
                                    .attr('stroke-width', 1.5);
                                
                                // Add text labels to all nodes (we're only showing 100 total)
                                node.append('text')
                                    .text(d => d.name)
                                    .attr('x', 12)
                                    .attr('y', 4)
                                    .attr('font-size', '12px')
                                    .attr('font-weight', 'bold')
                                    .attr('fill', '#fff')
                                    .attr('stroke', '#000')
                                    .attr('stroke-width', 0.4);
                                
                                // Add title for tooltip
                                node.append('title')
                                    .text(d => d.name);
                                
                                // Update positions on simulation tick
                                simulation.on('tick', () => {{
                                    // Update link positions
                                    link
                                        .attr('x1', d => d.source.x)
                                        .attr('y1', d => d.source.y)
                                        .attr('x2', d => d.target.x)
                                        .attr('y2', d => d.target.y);
                                    
                                    // Update link label positions
                                    linkGroup.selectAll('text')
                                        .attr('x', d => (d.source.x + d.target.x) / 2)
                                        .attr('y', d => (d.source.y + d.target.y) / 2);
                                    
                                    // Update node positions
                                    node.attr('transform', d => `translate(${d.x},${d.y})`);
                                }});
                                
                                // Log once the graph has initialized
                                console.log("Graph rendering initialized with D3.js");
                                
                                // Function to switch between view modes
                                function setViewMode(mode) {{
                                    currentViewMode = mode;
                                    
                                    // Reset selection
                                    resetSelection();
                                    
                                    if (mode === 'cluster') {{
                                        // Enable clustering by topic
                                        clusterButton.className = 'px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-white text-sm active';
                                        forceButton.className = 'px-3 py-1 bg-gray-600 hover:bg-gray-700 rounded text-white text-sm';
                                        
                                        // Apply cluster forces
                                        Object.keys(forcesByType).forEach(type => {{
                                            simulation.force(`x-${{type}}`, forcesByType[type])
                                                   .force(`y-${{type}}`, forceYByType[type]);
                                        }});
                                        
                                        // Remove general forces
                                        simulation.force('x', null)
                                                 .force('y', null);
                                    }} else {{
                                        // Enable force directed layout
                                        clusterButton.className = 'px-3 py-1 bg-gray-600 hover:bg-gray-700 rounded text-white text-sm';
                                        forceButton.className = 'px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-white text-sm active';
                                        
                                        // Remove cluster forces
                                        Object.keys(forcesByType).forEach(type => {{
                                            simulation.force(`x-${{type}}`, null)
                                                   .force(`y-${{type}}`, null);
                                        }});
                                        
                                        // Apply general forces
                                        simulation.force('x', d3.forceX(width / 2).strength(0.04))
                                                 .force('y', d3.forceY(height / 2).strength(0.04));
                                    }}
                                    
                                    // Restart simulation with heating
                                    simulation.alpha(1).restart();
                                }}
                                
                                // Button event listeners (commented out since buttons are hidden in this version)
                                // clusterButton.addEventListener('click', () => setViewMode('cluster'));
                                // forceButton.addEventListener('click', () => setViewMode('force'));
                                // resetSelectionButton.addEventListener('click', resetSelection);
                                
                                // Handle brush selection
                                function brushended(event) {{
                                    if (!event.selection) return;
                                    
                                    // Store original data before filtering
                                    if (!originalNodeData) {{
                                        originalNodeData = {{
                                            nodes: graphData.nodes.slice(),
                                            links: graphData.links.slice()
                                        }};
                                    }}
                                    
                                    // Get the selection coordinates
                                    const [[x0, y0], [x1, y1]] = event.selection;
                                    
                                    // Filter nodes within the selected area (accounting for zoom transform)
                                    const transform = d3.zoomTransform(svg.node());
                                    const selectedNodeIds = new Set();
                                    
                                    node.each(function(d) {{
                                        // Convert node position considering zoom transform
                                        const nodeX = transform.invertX(d.x);
                                        const nodeY = transform.invertY(d.y);
                                        
                                        // Check if node is within selection
                                        if (nodeX >= x0 && nodeX <= x1 && nodeY >= y0 && nodeY <= y1) {{
                                            selectedNodeIds.add(d.id);
                                        }}
                                    }});
                                    
                                    // If no nodes selected, do nothing
                                    if (selectedNodeIds.size === 0) {{
                                        brushGroup.call(brush.move, null);
                                        return;
                                    }}
                                    
                                    // Show reset button
                                    resetSelectionButton.classList.remove('hidden');
                                    
                                    // Filter to only show selected nodes and their links
                                    const filteredNodes = graphData.nodes.filter(d => selectedNodeIds.has(d.id));
                                    const filteredLinks = graphData.links.filter(d => 
                                        selectedNodeIds.has(d.source.id || d.source) && 
                                        selectedNodeIds.has(d.target.id || d.target)
                                    );
                                    
                                    // Store selected nodes
                                    selectedNodes = {{
                                        nodes: filteredNodes,
                                        links: filteredLinks
                                    }};
                                    
                                    // Update the simulation data
                                    simulation.nodes(filteredNodes);
                                    simulation.force('link').links(filteredLinks);
                                    
                                    // Update the visualization
                                    updateVisualization(filteredNodes, filteredLinks);
                                    
                                    // Update status text
                                    statusElement.textContent = `Showing selected area: ${{filteredNodes.length}} nodes and ${{filteredLinks.length}} links`;
                                    
                                    // Clear the brush selection
                                    brushGroup.call(brush.move, null);
                                    
                                    // Restart simulation
                                    simulation.alpha(1).restart();
                                }}
                                
                                // Function to reset selection
                                function resetSelection() {{
                                    if (!originalNodeData) return;
                                    
                                    // Restore original data
                                    graphData.nodes = originalNodeData.nodes.slice();
                                    graphData.links = originalNodeData.links.slice();
                                    
                                    // Update simulation
                                    simulation.nodes(graphData.nodes);
                                    simulation.force('link').links(graphData.links);
                                    
                                    // Update visualization
                                    updateVisualization(graphData.nodes, graphData.links);
                                    
                                    // Hide reset button
                                    resetSelectionButton.classList.add('hidden');
                                    
                                    // Update status
                                    statusElement.textContent = `Total dataset: ${{graphData.nodes.length}} nodes and ${{graphData.links.length}} links.`;
                                    
                                    // Clear selection state
                                    selectedNodes = null;
                                    originalNodeData = null;
                                    
                                    // Restart simulation
                                    simulation.alpha(1).restart();
                                }}
                                
                                // Function to update visualization
                                function updateVisualization(nodes, links) {{
                                    // Update links
                                    const linkSelection = g.selectAll('line')
                                        .data(links, d => `${{d.source.id || d.source}}-${{d.target.id || d.target}}`);
                                    
                                    linkSelection.exit().remove();
                                    
                                    const linkEnter = linkSelection.enter()
                                        .append('line')
                                        .attr('stroke', '#aaa')
                                        .attr('stroke-opacity', 0.7)
                                        .attr('stroke-width', d => Math.max(1.2, Math.min(3.5, Math.sqrt(d.value) * 1.2)));
                                    
                                    // Update nodes
                                    const nodeSelection = g.selectAll('.node')
                                        .data(nodes, d => d.id);
                                    
                                    nodeSelection.exit().remove();
                                    
                                    const nodeEnter = nodeSelection.enter()
                                        .append('g')
                                        .attr('class', 'node')
                                        .call(d3.drag()
                                            .on('start', dragstarted)
                                            .on('drag', dragged)
                                            .on('end', dragended))
                                        .on('click', showEntityDetails);
                                    
                                    nodeEnter.append('circle')
                                        .attr('r', d => Math.max(6, Math.min(18, 8 + Math.sqrt(d.count) * 2)))
                                        .attr('fill', d => {{
                                            return '#3b82f6'; // Blue for all nodes
                                        }})
                                        .attr('stroke', '#000')
                                        .attr('stroke-opacity', 0.7)
                                        .attr('stroke-width', 1.5);
                                    
                                    nodeEnter.append('text')
                                        .text(d => d.name)
                                        .attr('x', 12)
                                        .attr('y', 4)
                                        .attr('font-size', '12px')
                                        .attr('font-weight', 'bold')
                                        .attr('fill', '#fff')
                                        .attr('stroke', '#000')
                                        .attr('stroke-width', 0.4);
                                    
                                    nodeEnter.append('title')
                                        .text(d => d.name);
                                }}
                                
                                // Optimize performance by stopping simulation after a short while
                                setTimeout(() => simulation.stop(), 5000);
                                
                                // Drag functions
                                function dragstarted(event, d) {{
                                    if (!event.active) simulation.alphaTarget(0.3).restart();
                                    d.fx = d.x;
                                    d.fy = d.y;
                                }}
                                
                                function dragged(event, d) {{
                                    d.fx = event.x;
                                    d.fy = event.y;
                                }}
                                
                                function dragended(event, d) {{
                                    if (!event.active) simulation.alphaTarget(0);
                                    d.fx = null;
                                    d.fy = null;
                                }}
                                
                                // Show entity details on click
                                function showEntityDetails(event, d) {{
                                    // Prevent brush selection when clicking node
                                    if (event.defaultPrevented) return;
                                    
                                    const detailsContainer = document.getElementById('entity-details');
                                    detailsContainer.classList.remove('hidden');
                                    
                                    // Update container with loading message
                                    detailsContainer.innerHTML = 
                                        '<h2 class="text-xl font-semibold text-white mb-3">' + d.name + '</h2>' +
                                        '<p class="text-gray-300 mb-4">Loading details...</p>';
                                    
                                    // Fetch entity description
                                    fetch('/get_entity_description?entity=' + encodeURIComponent(d.name))
                                        .then(response => response.json())
                                        .then(data => {{
                                            detailsContainer.innerHTML = 
                                                '<h2 class="text-xl font-semibold text-white mb-3">' + d.name + '</h2>' +
                                                '<p class="text-gray-300 mb-4">' + (data.description || 'No description available.') + '</p>' +
                                                '<div class="mb-4">' +
                                                    '<h3 class="text-lg font-medium text-white mb-2">Type</h3>' +
                                                    '<span class="px-3 py-1 bg-gray-700 rounded-full text-sm text-white">' + d.type + '</span>' +
                                                '</div>' +
                                                '<div class="mb-6">' +
                                                    '<h3 class="text-lg font-medium text-white mb-2">Mentioned in</h3>' +
                                                    '<p class="text-gray-300">' + d.count + ' papers</p>' +
                                                '</div>' +
                                                '<a href="/search?entity=' + encodeURIComponent(d.name) + '" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded text-white">' +
                                                    'View Related Papers' +
                                                '</a>';
                                        }})
                                        .catch(error => {{
                                            detailsContainer.innerHTML = 
                                                '<h2 class="text-xl font-semibold text-white mb-3">' + d.name + '</h2>' +
                                                '<p class="text-red-400">Error loading details: ' + error.message + '</p>';
                                        }});
                                }}
                            }});
                        """),
                        cls="w-full max-w-6xl mx-auto px-4 sm:px-6 py-10"
                    ),
                    cls="w-full"
                ),
                Script("""
                    console.log('Checking if D3 is loaded properly:', typeof d3);
                """),
                cls="min-h-screen bg-[#1e2532] flex flex-col items-center w-full"
            )
        )
    
    @app.get('/get_entity_description')
    async def get_entity_description(request):
        """Get description for an entity"""
        entity = request.query_params.get('entity')
        abs_url = request.query_params.get('abs_url')
        
        if not entity:
            return JSONResponse({"description": "Entity not found"})
        
        # Get entity dictionary manager for entity information
        try:
            from src.utils.entity_dictionary import get_entity_dictionary_manager
            entity_dict_manager = get_entity_dictionary_manager()
            entity_info = entity_dict_manager.lookup_entity(entity)
            
            if entity_info and 'description' in entity_info:
                # Use entity dictionary information if available
                return JSONResponse({
                    "entity": entity_info.get('canonical_name', entity),
                    "description": entity_info.get('description'),
                    "type": entity_info.get('entity_type', 'entity')
                })
        except Exception as e:
            print(f"Error looking up entity in dictionary: {e}")
        
        # If specific paper context is provided
        if abs_url:
            paper = summaries_collection.find_one({"abs_url": abs_url})
            if paper and 'entities_dict' in paper and entity in paper['entities_dict']:
                entity_data = paper['entities_dict'][entity]
                return JSONResponse({
                    "entity": entity,
                    "description": entity_data.get('description', f"Entity extracted from paper: {paper.get('title', 'Unknown paper')}"),
                    "type": entity_data.get('type', 'entity')
                })
        
        # Otherwise find first paper with this entity
        query = {"entities": {"$elemMatch": {"$eq": entity}}}
        paper = summaries_collection.find_one(query)
        
        if not paper or 'entities' not in paper:
            return JSONResponse({"description": "No description available"})
        
        # Return basic entity description
        return JSONResponse({
            "entity": entity,
            "description": f"Entity extracted from paper: {paper.get('title', 'Unknown paper')}",
            "type": "entity",
            "count": 1
        })
    
    @rt('/entities')
    async def entities(request: Request):
        """Display entity analytics page"""
        # Get papers with entities from database
        papers_with_entities = list(summaries_collection.find(
            {"entities": {"$exists": True, "$ne": []}},
            {"entities": 1, "title": 1}
        ).limit(1000))
        
        # Try to get entity dictionary manager for entity resolution
        try:
            from src.utils.entity_dictionary import get_entity_dictionary_manager
            entity_dict_manager = get_entity_dictionary_manager()
            use_entity_resolution = True
            print(f"Using entity dictionary with {len(entity_dict_manager.entity_dict)} entries for resolution")
        except Exception as e:
            print(f"Could not load entity dictionary: {e}")
            use_entity_resolution = False
        
        # Extract and count entities from papers
        entity_counts = {}
        for paper in papers_with_entities:
            for raw_entity in paper.get('entities', []):
                # Handle different entity formats
                if isinstance(raw_entity, (list, tuple)) and len(raw_entity) >= 1:
                    # If entity is a tuple or list like (name, type), use the name
                    entity_name = str(raw_entity[0])
                    entity_type = str(raw_entity[1]) if len(raw_entity) > 1 else "entity"
                elif isinstance(raw_entity, dict) and 'value' in raw_entity:
                    # If entity is a dict with 'value' key, use that
                    entity_name = str(raw_entity['value'])
                    if 'category' in raw_entity and isinstance(raw_entity['category'], dict):
                        entity_type = str(raw_entity['category'].get('name', 'entity'))
                    else:
                        entity_type = "entity"
                else:
                    # Otherwise treat as a string
                    entity_name = str(raw_entity)
                    entity_type = "entity"
                
                # Apply entity resolution if available
                resolved_entity = None
                if use_entity_resolution:
                    resolved_entity = entity_dict_manager.lookup_entity(entity_name)
                
                # Use resolved entity if available, otherwise use original
                if resolved_entity:
                    canonical_id = resolved_entity.get('canonical_id')
                    canonical_name = resolved_entity.get('canonical_name')
                    resolved_type = resolved_entity.get('entity_type', entity_type)
                    
                    # Use canonical name as key for aggregation
                    entity_key = canonical_name
                    
                    # Create or update entity entry
                    if entity_key not in entity_counts:
                        entity_counts[entity_key] = {
                            "name": canonical_name,
                            "original_name": entity_name,
                            "canonical_id": canonical_id,
                            "type": resolved_type,
                            "count": 0,
                            "papers": [],
                            "variants": set([entity_name])
                        }
                    else:
                        # Add this variant to the set if not already there
                        entity_counts[entity_key]["variants"].add(entity_name)
                else:
                    # No resolution - use original entity as key
                    entity_key = entity_name
                    
                    # Create or update entity entry
                    if entity_key not in entity_counts:
                        entity_counts[entity_key] = {
                            "name": entity_name,
                            "type": entity_type,
                            "count": 0,
                            "papers": [],
                            "variants": set([entity_name])
                        }
                    else:
                        entity_counts[entity_key]["variants"].add(entity_name)
                
                # Update count and papers for this entity
                entity_counts[entity_key]["count"] += 1
                if paper.get("title") and paper["title"] not in entity_counts[entity_key]["papers"]:
                    entity_counts[entity_key]["papers"].append(paper["title"])
        
        # Convert variant sets to lists for JSON serialization
        for entity_key in entity_counts:
            if "variants" in entity_counts[entity_key]:
                entity_counts[entity_key]["variants"] = list(entity_counts[entity_key]["variants"])
        
        # Sort entities by count and take top 50
        top_entities = sorted(list(entity_counts.values()), key=lambda x: x["count"], reverse=True)[:50]
        
        if not top_entities:
            return Navigation(request), Title("moatless | Entities"), Body(
                Div(
                    Div(
                        H1("Entity Analytics", cls="text-3xl sm:text-4xl font-bold text-white mb-6 text-center"),
                        P("No entities available yet.", cls="text-xl text-center text-gray-400 font-light my-16"),
                        cls="py-8 sm:py-12 w-full bg-[#1e2532] border-b border-gray-800"
                    ),
                    cls="min-h-screen bg-[#1e2532] flex flex-col items-center w-full"
                )
            )
        
        # Group entities by type
        entity_types = {}
        for entity in top_entities:
            entity_type = entity.get("type", "entity")
            if entity_type not in entity_types:
                entity_types[entity_type] = []
            
            # Add a description based on the papers the entity appears in
            paper_count = len(entity.get("papers", []))
            if paper_count > 0:
                entity["description"] = f"This entity appears in {paper_count} papers."
            else:
                entity["description"] = "No additional information available."
                
            entity_types[entity_type].append(entity)
        
        return Navigation(request), Title("moatless | Entities"), Body(
            Div(
                Div(
                    Div(
                        H1("Entity Analytics", cls="text-3xl sm:text-4xl font-bold text-white mb-6 text-center"),
                        P("Explore entities extracted from papers", cls="text-xl text-center text-gray-400 font-light mb-8"),
                        A("View Knowledge Graph", href="/interactive_graph", cls="inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg"),
                        cls="flex flex-col items-center w-full max-w-5xl mx-auto px-6"
                    ),
                    cls="py-8 sm:py-12 w-full bg-[#1e2532] border-b border-gray-800"
                ),
                Div(
                    *(Div(
                        H2(f"{entity_type.title()} Entities", cls="text-2xl font-bold text-white mb-6"),
                        Div(
                            *(Div(
                                Div(
                                    H3(entity['name'], cls="text-xl font-semibold text-white mb-2"),
                                    P(f"Mentioned in {entity['count']} papers", cls="text-gray-400 text-sm mb-3"),
                                    Div(
                                        *([
                                            P(f"Variants: {', '.join(entity['variants'][:5])}{' and more...' if len(entity['variants']) > 5 else ''}", 
                                              cls="text-gray-400 text-xs mb-2 italic")
                                        ] if 'variants' in entity and len(entity['variants']) > 1 else []),
                                        P(entity.get('description', 'No description available')[:150] + "..." if entity.get('description', '') and len(entity.get('description', '')) > 150 else entity.get('description', 'No description available'), 
                                          cls="text-gray-300 text-sm mb-4"),
                                    ),
                                    A("View Related Papers", href=f"/search?entity={entity['name']}", cls="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-white text-sm"),
                                    cls="p-5"
                                ),
                                cls="bg-gray-800 rounded-lg overflow-hidden shadow-lg"
                            ) for entity in entity_types[entity_type]),
                            cls="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-10"
                        )
                    ) for entity_type, entities in entity_types.items()),
                    cls="w-full max-w-6xl mx-auto px-4 sm:px-6 py-10"
                ),
                cls="min-h-screen bg-[#1e2532] flex flex-col items-center w-full"
            )
        )

# Helper functions
def format_graph_data(knowledge_graph):
    """Format knowledge graph data for D3.js visualization"""
    if not knowledge_graph:
        print("Error: No knowledge graph data provided")
        return {"nodes": [], "links": []}
        
    # Extract nodes and links/relationships
    nodes = knowledge_graph.get('nodes', [])
    
    # Support both 'links' format (D3.js) and 'relationships' format (Neo4j)
    links = knowledge_graph.get('links', [])
    relationships = knowledge_graph.get('relationships', [])
    
    print(f"Formatting graph data with {len(nodes)} nodes")
    print(f"Links: {len(links)}, Relationships: {len(relationships)}")
    
    # Try to get entity dictionary manager for entity resolution
    try:
        from src.utils.entity_dictionary import get_entity_dictionary_manager
        entity_dict_manager = get_entity_dictionary_manager()
        use_entity_resolution = True
        print(f"Using entity dictionary with {len(entity_dict_manager.entity_dict)} entries for graph visualization")
    except Exception as e:
        print(f"Could not load entity dictionary for graph visualization: {e}")
        use_entity_resolution = False
    
    # Create a map to track nodes that have been merged due to resolution
    resolved_nodes = {}
    
    # Format nodes for D3
    d3_nodes = []
    for node in nodes:
        # Handle both main_graph format and individual KG format
        if isinstance(node, dict):
            node_id = None
            node_name = None
            node_type = "concept"  # Default type
            node_count = 1  # Default count
            
            # Extract node ID
            if 'id' in node:
                node_id = node.get('id')
            
            # Extract node name and type based on format
            if 'properties' in node:
                # Neo4j format
                properties = node.get('properties', {})
                node_name = properties.get('name', node_id)
                
                # Determine node type from labels
                labels = node.get('labels', [])
                if labels:
                    if "Method" in labels:
                        node_type = "method"
                    elif "Dataset" in labels:
                        node_type = "dataset"
                    elif "Entity" in labels:
                        node_type = "entity"
            else:
                # D3.js format or main graph format
                node_name = node.get('name', node_id)
                node_type = node.get('type', node_type)
                node_count = node.get('count', node_count)
            
            # Skip nodes without an ID or without text
            if not node_id or not node_name:
                continue
                
            # Skip "Large Language Models" node explicitly
            if node_name == "Large Language Models (LLMs)":
                continue
            
            # Apply entity resolution if available
            if use_entity_resolution and node_type == "entity" and node_name:
                resolved_entity = entity_dict_manager.lookup_entity(node_name)
                
                if resolved_entity:
                    canonical_id = resolved_entity.get('canonical_id')
                    canonical_name = resolved_entity.get('canonical_name')
                    resolved_type = resolved_entity.get('entity_type', node_type)
                    
                    # Check if we've already added this canonical entity
                    if canonical_id in resolved_nodes:
                        # Update the existing node's count
                        resolved_nodes[canonical_id]["count"] += node_count
                        # Map this node ID to the canonical ID for link replacement
                        resolved_nodes[node_id] = canonical_id
                        # Skip adding a new node
                        continue
                    
                    # Add the resolved node
                    d3_node = {
                        "id": canonical_id,
                        "name": canonical_name,
                        "original_name": node_name,
                        "type": resolved_type,
                        "count": node_count,
                        "variants": [node_name]
                    }
                    
                    # Add to d3_nodes and track in resolved_nodes
                    d3_nodes.append(d3_node)
                    resolved_nodes[canonical_id] = d3_node
                    resolved_nodes[node_id] = canonical_id
                    continue
            
            # If no resolution or resolution failed, add the original node
            d3_node = {
                "id": node_id,
                "name": node_name,
                "type": node_type,
                "count": node_count
            }
            
            d3_nodes.append(d3_node)
    
    # Format links for D3
    d3_links = []
    
    # Process D3.js format links
    for link in links:
        if isinstance(link, dict) and 'source' in link and 'target' in link:
            # Get source and target - they could be strings or objects
            source = link.get('source')
            target = link.get('target')
            
            # Extract IDs if they're objects
            if isinstance(source, dict) and 'id' in source:
                source = source['id']
            if isinstance(target, dict) and 'id' in target:
                target = target['id']
            
            # Handle resolved nodes - map source and target IDs to canonical IDs if they exist
            if use_entity_resolution:
                if source in resolved_nodes and isinstance(resolved_nodes[source], str):
                    source = resolved_nodes[source]
                if target in resolved_nodes and isinstance(resolved_nodes[target], str):
                    target = resolved_nodes[target]
            
            # Make sure both source and target are valid
            if source and target:
                d3_links.append({
                    "source": source,
                    "target": target,
                    "value": link.get('value', 1),
                    "type": link.get('type', 'RELATES_TO')
                })
    
    # Process Neo4j format relationships
    for rel in relationships:
        if isinstance(rel, dict):
            source = None
            target = None
            
            # Extract source and target based on format
            if 'start' in rel and 'end' in rel:
                # Neo4j format
                start_node = rel['start']
                end_node = rel['end']
                
                if isinstance(start_node, dict) and 'id' in start_node:
                    source = start_node['id']
                elif isinstance(start_node, str):
                    source = start_node
                    
                if isinstance(end_node, dict) and 'id' in end_node:
                    target = end_node['id']
                elif isinstance(end_node, str):
                    target = end_node
            elif 'source' in rel and 'target' in rel:
                # Alternative format
                source = rel.get('source')
                target = rel.get('target')
                
                # Extract IDs if they're objects
                if isinstance(source, dict) and 'id' in source:
                    source = source['id']
                if isinstance(target, dict) and 'id' in target:
                    target = target['id']
            
            # Handle resolved nodes - map source and target IDs to canonical IDs if they exist
            if use_entity_resolution:
                if source in resolved_nodes and isinstance(resolved_nodes[source], str):
                    source = resolved_nodes[source]
                if target in resolved_nodes and isinstance(resolved_nodes[target], str):
                    target = resolved_nodes[target]
            
            # Only add valid links
            if source and target:
                d3_links.append({
                    "source": source,
                    "target": target,
                    "value": rel.get('value', 1),
                    "type": rel.get('type', 'RELATES_TO')
                })
    
    # Debug: Check if any nodes or links are missing their required attributes
    for i, node in enumerate(d3_nodes):
        if 'id' not in node:
            print(f"Warning: Node at index {i} is missing id: {node}")
        if 'name' not in node:
            print(f"Warning: Node at index {i} is missing name: {node}")
            
    for i, link in enumerate(d3_links):
        if 'source' not in link:
            print(f"Warning: Link at index {i} is missing source: {link}")
        if 'target' not in link:
            print(f"Warning: Link at index {i} is missing target: {link}")
    
    result = {"nodes": d3_nodes, "links": d3_links}
    print(f"Formatted graph data has {len(d3_nodes)} nodes and {len(d3_links)} links")
    return result
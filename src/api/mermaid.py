"""
API routes for mermaid diagrams
"""
from starlette.responses import JSONResponse

def register_routes(app, rt, collections):
    """Register routes related to mermaid diagrams"""
    
    mermaid_diagrams_collection = collections['mermaid_diagrams']
    summaries_collection = collections['summaries']
    
    @app.get('/api/mermaid/{paper_id}')
    async def get_mermaid_diagram(request):
        """
        API endpoint to get the mermaid diagram for a paper
        
        Args:
            request: FastAPI request object
            
        Returns:
            JSON response with the mermaid diagram
        """
        paper_id = request.path_params.get('paper_id')
        if not paper_id:
            print("API ERROR: Paper ID is missing")
            return JSONResponse({'status': 'error', 'message': 'Paper ID is required'})
        
        # Construct abs_url from paper_id
        abs_url = f"https://arxiv.org/abs/{paper_id}"
        
        # Get all collections for debugging
        try:
            all_collections = mermaid_diagrams_collection.database.list_collection_names()
            
            # Count documents in the mermaid_diagrams collection
            count = mermaid_diagrams_collection.count_documents({})
            
            # Try both http and https URL formats
            https_url = f"https://arxiv.org/abs/{paper_id}"
            http_url = f"http://arxiv.org/abs/{paper_id}"
            
            # Try to find with https first
            mermaid_doc = mermaid_diagrams_collection.find_one({'abs_url': https_url})
            
            # If not found, try with http
            if not mermaid_doc:
                mermaid_doc = mermaid_diagrams_collection.find_one({'abs_url': http_url})
            
            if not mermaid_doc:
                print(f"API: No mermaid document found for {abs_url} (tried both https and http)")
                # For testing in local mode, generate a sample diagram
                if 'mongomock' in str(type(mermaid_diagrams_collection.database)) or count == 0:
                    print("API: Using mock data for local testing")
                    sample_diagram = """
                    graph TD
                        A[Paper Title] --> B[Main Concept]
                        B --> C[Method 1]
                        B --> D[Method 2]
                        C --> E[Results]
                        D --> E
                        E --> F[Conclusion]
                        
                        %% Node styling for Dark Mode
                        classDef nodeStyle fill:#1e293b,stroke:#38bdf8,stroke-width:2px,rx:10,ry:10,font-size:14px,font-weight:bold,color:#e2e8f0;
                        class A,B,C,D,E,F nodeStyle;

                        %% Connection styling
                        linkStyle 0,1,2,3,4,5 stroke:#38bdf8,stroke-width:2px;
                    """
                    return JSONResponse({
                        'status': 'success', 
                        'diagram': sample_diagram
                    })
                return JSONResponse({
                    'status': 'error', 
                    'message': 'Mermaid diagram not found for this paper'
                })
            
            if 'mermaid_diagram' not in mermaid_doc:
                print(f"API: Found document but no mermaid_diagram field for {abs_url}")
                print(f"API: Available fields: {list(mermaid_doc.keys())}")
                return JSONResponse({
                    'status': 'error', 
                    'message': 'Mermaid diagram field missing for this paper'
                })
            
            # Log the mermaid diagram for debugging
            #print(f"API: Found mermaid diagram for {abs_url}")
            #print(f"API: Diagram content (first 100 chars): {mermaid_doc['mermaid_diagram'][:100]}...")
            
            return JSONResponse({
                'status': 'success', 
                'diagram': mermaid_doc['mermaid_diagram']
            })
        except Exception as e:
            print(f"API ERROR: Exception in mermaid API: {str(e)}")
            import traceback
            traceback.print_exc()
            return JSONResponse({
                'status': 'error', 
                'message': f'Error retrieving mermaid diagram: {str(e)}'
            })
"""
Routes for handling paper recommendations.
"""
from typing import Optional
from starlette.requests import Request
from starlette.responses import JSONResponse, RedirectResponse
import logging
from fasthtml.common import *
from monsterui.all import *

from src.ui import Navigation, Paper
from src.services.recommendations import recommend_papers

def register_routes(app, rt, collections):
    """Register routes related to paper recommendations"""
    
    @app.get('/recommendations')
    async def get_recommendations(request):
        """Display personalized paper recommendations for the user"""
        # Get user ID from session (default if not authenticated)
        user_id = request.session.get('user_id', "default")
        
        # Get recommendations count parameter (defaults to 10)
        count_param = request.query_params.get('count', '10')
        try:
            count = int(count_param)
            # Limit to reasonable range
            count = max(1, min(count, 30))
        except ValueError:
            count = 10

        # Get recommended paper URLs using the recommendation service
        recommendations = recommend_papers(user_id, collections, top_n=count)
        
        # If no recommendations available, display message
        if not recommendations:
            return Navigation(request), Title("moatless | Recommendations"), Body(
                Div(
                    Div(
                        H1("Paper Recommendations", cls="text-3xl sm:text-4xl font-bold text-white mb-6 text-center"),
                        P("No recommendations available. Save some papers to get personalized recommendations.", 
                          cls="text-xl text-center text-gray-400 font-light my-16"),
                        cls="py-8 sm:py-12 w-full bg-[#1e2532] border-b border-gray-800"
                    ),
                    cls="min-h-screen bg-[#1e2532] flex flex-col items-center w-full"
                )
            )
        
        # Convert recommendation results to paper objects
        recommended_urls = [paper_url for paper_url, _, _ in recommendations]
        paper_components = []
        
        # Fetch full paper details for each recommendation
        for paper_url, similarity_score, details in recommendations:
            summary = collections['summaries'].find_one({"abs_url": paper_url})
            if summary:
                # Add recommendation metadata to the summary
                summary['recommendation_score'] = similarity_score
                summary['recommendation_details'] = details
                
                # Create a component with recommendation score display
                paper_div = Div(
                    # Add recommendation badge
                    Div(
                        Span(f"Match: {similarity_score}%", 
                             cls="inline-block px-3 py-1 text-sm font-medium rounded-md bg-blue-600 text-white"),
                        cls="absolute top-4 right-4 z-10"
                    ),
                    # Standard Paper component
                    Paper(summary, request.session),
                    cls="relative paper-wrapper my-6"
                )
                paper_components.append(paper_div)
                
        # Display recommendations
        return Navigation(request), Title("moatless | Recommendations"), Body(
            Div(
                Div(
                    Div(
                        H1("Paper Recommendations", cls="text-3xl sm:text-4xl font-bold text-white mb-6 text-center"),
                        P(f"Personalized recommendations based on your saved papers.", 
                          cls="text-xl text-center text-gray-400 font-light mb-8"),
                        cls="flex flex-col items-center w-full max-w-5xl mx-auto px-6"
                    ),
                    cls="py-8 sm:py-12 w-full bg-[#1e2532] border-b border-gray-800"
                ),
                Div(
                    *paper_components,
                    id="recommendations-container",
                    cls="w-full max-w-4xl mx-auto px-4 sm:px-6 py-5 sm:py-7 2xl:py-5"
                ),
                cls="min-h-screen bg-[#1e2532] flex flex-col items-center w-full"
            )
        )

    @app.get('/api/recommendations')
    async def api_recommendations(request):
        """API endpoint to get personalized paper recommendations"""
        # Get user ID from session (default if not authenticated)
        user_id = request.session.get('user_id', "default")
        
        # Get count parameter (defaults to 10)
        count_param = request.query_params.get('count', '10')
        try:
            count = int(count_param)
            # Limit to reasonable range
            count = max(1, min(count, 30))
        except ValueError:
            count = 10
            
        # Get recommendations
        recommendations = recommend_papers(user_id, collections, top_n=count)
        
        # Format response data
        result = []
        for paper_url, similarity_score, details in recommendations:
            # Get full paper details
            paper = collections['summaries'].find_one({"abs_url": paper_url})
            
            if paper:
                # Convert MongoDB _id to string if present
                if '_id' in paper:
                    paper['_id'] = str(paper['_id'])
                
                # Add recommendation metadata
                paper['recommendation_score'] = similarity_score
                paper['recommendation_details'] = details
                result.append(paper)
        
        return JSONResponse({
            "status": "success",
            "count": len(result),
            "recommendations": result
        })
"""
Routes for handling saved papers functionality.
"""
from typing import Optional
from starlette.requests import Request
from starlette.responses import JSONResponse, RedirectResponse
import os
from notion_client import Client
from fasthtml.common import *
from monsterui.all import *

from src.ui import Navigation

def register_routes(app, rt, collections):
    """Register routes related to saved papers"""
    
    @app.get('/save_paper')
    async def save_paper(request):
        """Save a paper to user's saved papers"""
        # Get parameters from query parameters
        title = request.query_params.get('title')
        abs_url = request.query_params.get('abs_url')
        paper_id = request.query_params.get('paper_id')
        
        # Get user ID from session (default if not authenticated)
        user_id = request.session.get('user_id', "default")
        
        # Check for needed parameters
        if not abs_url and not paper_id:
            return JSONResponse({"status": "error", "message": "No paper URL or ID provided"})
        
        # If we have paper_id but no abs_url, try to get the paper details
        if paper_id and not abs_url:
            # Get paper from database
            paper = collections['papers'].find_one({"_id": paper_id})
            if not paper:
                return JSONResponse({"status": "error", "message": "Paper not found"})
            
            # Get the abs_url and title from the paper
            abs_url = paper.get('abs_url')
            title = paper.get('title')
            
            # If we still don't have an abs_url, we can't save it with the new method
            if not abs_url:
                return JSONResponse({"status": "error", "message": "Paper doesn't have a URL"})
        
        # Use the standardized method with abs_url and title
        if abs_url:
            if not title:
                # Try to get the title from summaries if not provided
                summary = collections['summaries'].find_one({"abs_url": abs_url})
                if summary:
                    title = summary.get('title')
                
                if not title:
                    return JSONResponse({"status": "error", "message": "Title is required when saving by URL"})
            
            # Use the service function
            from src.services.saved_papers import save_paper as save_paper_service
            
            # Standardize on the new method - we'll only use abs_url and title
            result = save_paper_service(
                collections,
                user_id,
                {"abs_url": abs_url, "title": title}
            )
            
            return JSONResponse(result)
        
        return JSONResponse({"status": "error", "message": "Invalid parameters"})
        
    @app.get('/unsave_paper')
    async def unsave_paper(request):
        """Remove a paper from user's saved papers"""
        # Get paper URL from query parameters
        abs_url = request.query_params.get('abs_url')
        
        # Get user ID from session (default if not authenticated)
        user_id = request.session.get('user_id', "default")
        
        # Check for needed parameters
        if not abs_url:
            return JSONResponse({"status": "error", "message": "No paper URL provided"})
        
        # Use the service function
        from src.services.saved_papers import unsave_paper as unsave_paper_service
        
        # Remove the paper from saved papers
        result = unsave_paper_service(
            collections,
            user_id,
            abs_url
        )
        
        return JSONResponse(result)
    
    @app.get('/saved')
    async def get_saved_papers(request):
        """Display the user's saved papers"""
        # Get user ID from session (default if not authenticated)
        user_id = request.session.get('user_id', "default")
        
        # Use the service function to get saved papers
        from src.services.saved_papers import get_saved_papers as get_saved_papers_service
        
        # Get papers from the new method
        saved_papers_new = get_saved_papers_service(collections, user_id)
        
        # Also try the old method for backward compatibility and migrate to new method
        saved_papers_doc = collections['saved_papers'].find_one({"user_id": user_id})
        if saved_papers_doc and 'papers' in saved_papers_doc:
            paper_ids = saved_papers_doc.get('papers', [])
            for paper_id in paper_ids:
                paper = collections['papers'].find_one({"_id": paper_id})
                if paper:
                    # Convert to the new format if needed
                    if paper.get('abs_url') and not any(p.get('abs_url') == paper.get('abs_url') for p in saved_papers_new):
                        # Create a paper object with the new structure
                        new_paper = {
                            'title': paper.get('title', 'No title'),
                            'abs_url': paper.get('abs_url', ''),
                            'authors_string': paper.get('authors_string', 'Unknown authors'),
                            'date': paper.get('date', 'Unknown date'),
                            'abstract': paper.get('abstract', 'No abstract available'),
                            'pdf_url': paper.get('pdf_url', '#'),
                            '_id': paper.get('_id', '')
                        }
                        
                        # Add to the saved_papers_new list
                        saved_papers_new.append(new_paper)
                        
                        # Also save it using the new method
                        from src.services.saved_papers import save_paper as save_paper_service
                        save_paper_service(
                            collections,
                            user_id,
                            {"abs_url": paper.get('abs_url', ''), "title": paper.get('title', 'No title')}
                        )
        
        # If no papers are found, display empty message
        if not saved_papers_new:
            return Navigation(request), Title("moatless | Saved Papers"), Body(
                Div(
                    Div(
                        H1("Your Saved Papers", cls="text-3xl sm:text-4xl font-bold text-white mb-6 text-center"),
                        P("No saved papers yet.", cls="text-xl text-center text-gray-400 font-light my-16"),
                        cls="py-8 sm:py-12 w-full bg-[#1e2532] border-b border-gray-800"
                    ),
                    cls="min-h-screen bg-[#1e2532] flex flex-col items-center w-full"
                )
            )
        
        # For each saved paper, try to fetch additional details from summaries collection
        # if they are not already in the saved paper data
        for paper in saved_papers_new:
            if paper.get('abs_url') and (not paper.get('abstract') or not paper.get('authors_string')):
                summary = collections['summaries'].find_one({"abs_url": paper.get('abs_url')})
                if summary:
                    # Update with additional info from summary if available
                    paper['abstract'] = paper.get('abstract') or summary.get('abstract', '')
                    paper['authors_string'] = paper.get('authors_string') or summary.get('authors_string', '')
                    paper['pdf_url'] = paper.get('pdf_url') or summary.get('pdf_url', '#')
                    paper['date'] = paper.get('date') or summary.get('published', 'Unknown date')
                    
                    # Make sure we also have tags and entities for the Paper component
                    if 'tags' not in paper or not paper['tags']:
                        paper['tags'] = summary.get('tags', [])
                    if 'entities' not in paper or not paper['entities']:
                        paper['entities'] = summary.get('entities', [])
                    # Always use the summary from the summaries collection if available
                    paper['summary'] = summary.get('summary', paper.get('abstract', ''))
        
        # Get the Paper component to use the same UI as the main feed
        from src.ui import Paper
        
        # Create a custom wrapper for the Paper component
        
        # Display saved papers with custom paper components that include a remove button
        paper_components = []
        
        for paper in saved_papers_new:
            title = paper.get('title', '')
            abs_url = paper.get('abs_url', '')
            
            # Create each paper wrapper div with explicit remove button
            paper_div = Div(
                # Standard Paper component with now-functioning toggle bookmark button
                Paper(paper, request.session),
                
                # The red remove button has been removed as it's redundant with the bookmark toggle
                
                # Add script to handle paper removal animation for the saved papers page
                Script(f"""
                document.addEventListener('DOMContentLoaded', function() {{
                    // Find this paper's save button
                    const paper = document.currentScript.parentElement;
                    const saveButton = paper.querySelector('[id^="save-button"]');
                    
                    // Function to handle paper removal animation
                    const handleRemoveAnimation = () => {{
                        // Get the paper element
                        const paperElement = paper.closest('.paper');
                        if (paperElement) {{
                            // Animate removal
                            paperElement.style.transition = 'all 0.5s ease';
                            paperElement.style.opacity = '0';
                            paperElement.style.maxHeight = '0';
                            paperElement.style.overflow = 'hidden';
                            paperElement.style.marginBottom = '0';
                            paperElement.style.paddingTop = '0';
                            paperElement.style.paddingBottom = '0';
                            
                            // Remove paper after animation
                            setTimeout(() => {{
                                paperElement.remove();
                                // Update the count in the header
                                const countElement = document.querySelector('#saved-papers-count');
                                if (countElement) {{
                                    const currentCount = parseInt(countElement.textContent);
                                    if (!isNaN(currentCount)) {{
                                        countElement.textContent = (currentCount - 1).toString();
                                    }}
                                }}
                                
                                // Show empty message if no papers left
                                // Use a timeout to allow DOM to update
                                setTimeout(() => {{
                                    const papers = document.querySelectorAll('.paper');
                                    // If there are no papers or only one paper left (which is being removed)
                                    if (papers.length === 0 || (papers.length === 1 && papers[0].style.opacity === '0')) {{
                                        const container = document.querySelector('#papers-container');
                                        if (container) {{
                                            container.innerHTML = '<p class="text-xl text-center text-gray-400 font-light my-16">No saved papers left.</p>';
                                            
                                            // Also update the count to zero if we're showing the empty message
                                            const countElement = document.querySelector('#saved-papers-count');
                                            if (countElement) {{
                                                countElement.textContent = '0';
                                            }}
                                        }}
                                    }}
                                }}, 600); // Wait slightly longer than the removal animation
                            }}, 500);
                        }}
                    }};
                    
                    // Handle bookmark button click for immediate UI update
                    if (saveButton) {{
                        // Listen for both click and custom unsave event
                        saveButton.addEventListener('click', function() {{
                            const saveIcon = saveButton.querySelector('[id^="saveIcon"]');
                            if (saveIcon && saveIcon.classList.contains('fill-blue-500')) {{
                                // Paper is currently saved, will be unsaved - immediately trigger animation
                                handleRemoveAnimation();
                            }}
                        }});
                        
                        // Also listen for the custom event from main.js
                        saveButton.addEventListener('paperUnsaved', function() {{
                            // Paper is being unsaved by our custom event - trigger animation
                            handleRemoveAnimation();
                        }});
                    }}
                }});
                """),
                cls="relative paper-wrapper"
            )
            
            paper_components.append(paper_div)
        
        # Display saved papers with our custom paper components that include a remove button
        return Navigation(request), Title("moatless | Saved Papers"), Body(
            Div(
                Div(
                    Div(
                        H1("Your Saved Papers", cls="text-3xl sm:text-4xl font-bold text-white mb-6 text-center"),
                        P(f"You have ", Span(str(len(saved_papers_new)), id="saved-papers-count"), " saved papers.", 
                          cls="text-xl text-center text-gray-400 font-light mb-8"),
                        cls="flex flex-col items-center w-full max-w-5xl mx-auto px-6"
                    ),
                    cls="py-8 sm:py-12 w-full bg-[#1e2532] border-b border-gray-800"
                ),
                Div(
                    *paper_components,
                    id="papers-container",
                    cls="w-full max-w-4xl mx-auto px-4 sm:px-6 py-5 sm:py-7 2xl:py-5"
                ),
                cls="min-h-screen bg-[#1e2532] flex flex-col items-center w-full"
            )
        )
    
    @app.get('/save_to_notion')
    async def save_to_notion(request, title: str, abs_url: str):
        """Save a paper to Notion database"""
        try:
            # Check if Notion API key is set
            notion_api_key = os.getenv("NOTION_API_KEY")
            if not notion_api_key:
                return JSONResponse({"status": "error", "message": "Notion API key not set"})
                
            # Check if Notion database ID is set
            notion_database_id = os.getenv("NOTION_DATABASE_ID")
            if not notion_database_id:
                return JSONResponse({"status": "error", "message": "Notion database ID not set"})
                
            # Initialize Notion client
            notion = Client(auth=notion_api_key)
            
            # Create new page in the database
            new_page = {
                "Name": {
                    "title": [
                        {
                            "text": {
                                "content": title
                            }
                        }
                    ]
                },
                "URL": {
                    "url": abs_url
                }
            }
            
            # Add the page to the database
            notion.pages.create(
                parent={"database_id": notion_database_id},
                properties=new_page
            )
            
            return JSONResponse({"status": "success", "message": "Paper saved to Notion"})
        except Exception as e:
            return JSONResponse({"status": "error", "message": str(e)})
"""
Routes for handling search functionality.
"""
from typing import Optional, List, Dict, Any
import json
from datetime import datetime
from starlette.requests import Request
from starlette.responses import JSONResponse, RedirectResponse
from fasthtml.common import *
from monsterui.all import *

from src.ui import Navigation
from src.services import nlp

def register_routes(app, rt, collections):
    """Register routes related to search functionality"""

    @app.get('/semantic-search-form')
    async def semantic_search_form(request):
        """Display semantic search form"""
        return Navigation(request), Title("moatless | Semantic Search"), Body(
            Div(
                Div(
                    H1("Semantic Search", cls="text-3xl sm:text-4xl font-bold text-white mb-6 text-center"),
                    P("Search for papers using natural language", cls="text-xl text-center text-gray-400 font-light mb-8"),
                    Form(
                        Div(
                            Label("Enter your search query", for_="query", cls="block mb-2 text-white"),
                            TextArea(id="query", name="query", placeholder="E.g., papers about attention mechanisms in transformers",
                                   cls="w-full p-3 bg-gray-800 border border-gray-700 rounded-lg text-white", rows="4"),
                            cls="mb-6"
                        ),
                        Button("Search", type="submit", cls="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg"),
                        action="/semantic-search",
                        method="post",
                        cls="w-full max-w-2xl mx-auto"
                    ),
                    cls="py-16 w-full max-w-4xl mx-auto px-4"
                ),
                cls="min-h-screen bg-[#1e2532] flex flex-col items-center"
            )
        )

    @app.post('/semantic-search')
    async def semantic_search(request):
        """Handle semantic search requests"""
        form_data = await request.form()
        query = form_data.get('query')

        if not query or len(query) < 3:
            return Navigation(request), Title("moatless | Search Results"), Body(
                Div(
                    H1("Search Results", cls="text-3xl font-bold text-white mb-4 text-center"),
                    P("Your search query is too short. Please try again with a longer query.", cls="text-xl text-center text-gray-400 font-light my-8"),
                    A("Back to Search", href="/semantic-search-form", cls="inline-block mx-auto bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg"),
                    cls="min-h-screen bg-[#1e2532] flex flex-col items-center py-16 px-4"
                )
            )

        # Perform semantic search
        try:
            results = perform_semantic_search(query, collections['summaries'], limit=10)

            if not results:
                return Navigation(request), Title("moatless | Search Results"), Body(
                    Div(
                        H1("Search Results", cls="text-3xl font-bold text-white mb-4 text-center"),
                        P("No results found. Please try a different search query.", cls="text-xl text-center text-gray-400 font-light my-8"),
                        A("Back to Search", href="/semantic-search-form", cls="inline-block mx-auto bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg"),
                        cls="min-h-screen bg-[#1e2532] flex flex-col items-center py-16 px-4"
                    )
                )

            # Display results
            # Import Paper component from src.ui
            from src.ui import Paper

            # Prepare papers for display with similarity scores
            papers_with_scores = []
            for result in results:
                paper = result['paper'].copy()  # Create a copy to avoid modifying the original
                paper['similarity'] = round(result['score'] * 100, 2)  # Convert score to percentage

                # Check if this paper has a mermaid diagram
                mermaid_doc = collections['mermaid_diagrams'].find_one({'abs_url': paper['abs_url']})
                paper['has_mermaid'] = mermaid_doc is not None

                papers_with_scores.append(paper)

            return Navigation(request), Title(f"moatless | Search Results for '{query}'"), Body(
                Div(
                    Div(
                        H1("Semantic Search Results", cls="text-3xl font-bold text-white mb-4 text-center"),
                        P(f"Showing results for: {query}", cls="text-xl text-center text-gray-400 font-light mb-8"),
                        cls="py-8 w-full bg-[#1e2532] border-b border-gray-800"
                    ),
                    Div(
                        *(Paper(paper, request.session, highlight_text=query, show_similarity=True) for paper in papers_with_scores),
                        A("Back to Search", href="/semantic-search-form", cls="inline-block mx-auto mt-5 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg"),
                        cls="w-full max-w-4xl 2xl:max-w-[1200px] mx-auto px-4 sm:px-6 py-5 sm:py-7 2xl:py-5"
                    ),
                    cls="min-h-screen bg-[#1e2532] flex flex-col items-center w-full"
                )
            )
        except Exception as e:
            return Navigation(request), Title("moatless | Search Error"), Body(
                Div(
                    H1("Search Error", cls="text-3xl font-bold text-white mb-4 text-center"),
                    P(f"An error occurred: {str(e)}", cls="text-xl text-center text-gray-400 font-light my-8"),
                    A("Back to Search", href="/semantic-search-form", cls="inline-block mx-auto bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg"),
                    cls="min-h-screen bg-[#1e2532] flex flex-col items-center py-16 px-4"
                )
            )

    @rt('/search')
    async def search(request: Request):
        """Display search form and results"""
        # Get search query from query parameters
        query = request.query_params.get('q', '')
        entity = request.query_params.get('entity', '')
        feed = request.query_params.get('feed', '')

        # If entity is provided, perform entity search
        if entity:
            # Create search query for the entity
            # Entities could be stored in different formats: strings, tuples, or dicts with 'value' field
            # We need to handle all possible formats
            results = collections['summaries'].find(
                {"$or": [
                    {"entities": {"$elemMatch": {"$eq": entity}}},  # String format
                    {"entities": {"$elemMatch": {"value": entity}}},  # Dict with value field
                    {"entities": {"$elemMatch": {"$elemMatch": {"$eq": entity}}}}  # Tuple format with entity as first element
                ]},
                sort=[("date", -1)]
            ).limit(50)

            papers = list(results)

            # Add has_mermaid flag to each paper
            for paper in papers:
                mermaid_doc = collections['mermaid_diagrams'].find_one({'abs_url': paper['abs_url']})
                paper['has_mermaid'] = mermaid_doc is not None

            # Import Paper component from src.ui
            from src.ui import Paper

            # Create a back link that preserves the feed parameter
            back_link = "/"
            if feed:
                back_link = f"/?feed={feed}"

            return Navigation(request), Title(f"moatless | {entity} Papers"), Body(
                Div(
                    Div(
                        H1(f"Papers about '{entity}'", cls="text-3xl sm:text-4xl font-bold text-white mb-6 text-center"),
                        P(f"Found {len(papers)} papers related to {entity}", cls="text-xl text-center text-gray-400 font-light mb-8"),
                        cls="py-8 w-full bg-[#1e2532] border-b border-gray-800"
                    ),
                    Div(
                        *(Paper(paper, request.session, selected_entity=entity) for paper in papers),
                        A("Back to Feed", href=back_link, cls="inline-block mx-auto mt-8 bg-slate-700 hover:bg-slate-600 text-white font-medium py-2 px-4 rounded-lg"),
                        cls="w-full max-w-4xl 2xl:max-w-[1200px] mx-auto px-4 sm:px-6 py-5 sm:py-7 2xl:py-5"
                    ) if papers else Div(
                        P("No papers found for this entity.", cls="text-xl text-center text-gray-400 font-light my-16"),
                        A("Back to Feed", href=back_link, cls="inline-block mx-auto bg-slate-700 hover:bg-slate-600 text-white font-medium py-2 px-4 rounded-lg"),
                        cls="w-full py-12"
                    ),
                    cls="min-h-screen bg-[#1e2532] flex flex-col items-center w-full"
                )
            )

        # If query is provided, perform text search
        elif query:
            try:
                # Create search query
                search_query = {"$text": {"$search": query}}

                # Execute search
                results = collections['summaries'].find(
                    search_query,
                    {"score": {"$meta": "textScore"}}
                ).sort([("score", {"$meta": "textScore"}), ("date", -1)])

                papers = list(results)
            except Exception as e:
                print(f"Text search error: {e}")
                # Fallback to basic search if text search fails
                # This will happen if the text index is not available
                fallback_query = {
                    "$or": [
                        {"title": {"$regex": query, "$options": "i"}},
                        {"abstract": {"$regex": query, "$options": "i"}},
                        {"authors_string": {"$regex": query, "$options": "i"}}
                    ]
                }
                try:
                    results = collections['summaries'].find(fallback_query).sort([("date", -1)])
                    papers = list(results)
                    print(f"Using fallback search. Found {len(papers)} results.")
                except Exception as fallback_error:
                    print(f"Fallback search error: {fallback_error}")
                    papers = []

            # Add has_mermaid flag to each paper
            for paper in papers:
                try:
                    mermaid_doc = collections['mermaid_diagrams'].find_one({'abs_url': paper['abs_url']})
                    paper['has_mermaid'] = mermaid_doc is not None
                except Exception as e:
                    print(f"Error checking mermaid diagram: {e}")
                    paper['has_mermaid'] = False

            # Import Paper component from src.ui
            from src.ui import Paper

            # Create a back link that preserves the feed parameter
            back_link = "/"
            if feed:
                back_link = f"/?feed={feed}"

            return Navigation(request), Title(f"moatless | Search Results for '{query}'"), Body(
                Div(
                    Div(
                        H1("Search Results", cls="text-3xl sm:text-4xl font-bold text-white mb-6 text-center"),
                        P(f"Found {len(papers)} results for '{query}'", cls="text-xl text-center text-gray-400 font-light mb-8"),
                        cls="py-8 w-full bg-[#1e2532] border-b border-gray-800"
                    ),
                    Div(
                        *(Paper(paper, request.session, highlight_text=query) for paper in papers),
                        A("Back to Feed", href=back_link, cls="inline-block mx-auto mt-8 bg-slate-700 hover:bg-slate-600 text-white font-medium py-2 px-4 rounded-lg"),
                        cls="w-full max-w-4xl 2xl:max-w-[1200px] mx-auto px-4 sm:px-6 py-5 sm:py-7 2xl:py-5"
                    ) if papers else Div(
                        P("No results found. Try a different search term.", cls="text-xl text-center text-gray-400 font-light my-16"),
                        A("Back to Feed", href=back_link, cls="inline-block mx-auto bg-slate-700 hover:bg-slate-600 text-white font-medium py-2 px-4 rounded-lg"),
                        cls="w-full py-12"
                    ),
                    cls="min-h-screen bg-[#1e2532] flex flex-col items-center w-full"
                )
            )

        # If no query or entity, redirect to home page with feed parameter if it exists
        redirect_url = '/'
        if feed:
            redirect_url = f'/?feed={feed}'
        return RedirectResponse(url=redirect_url, status_code=303)

    # Remove the POST handler since we now use GET directly
    # @rt('/search')
    # async def search_post(request: Request):
    #     """Process search form submission"""
    #     form_data = await request.form()
    #     query = form_data.get('q', '')
    #
    #     if not query:
    #         return RedirectResponse(url='/search', status_code=303)
    #
    #     return RedirectResponse(url=f'/search?q={query}', status_code=303)

    @rt('/search/entity/{entity_name}')
    async def search_entity(request: Request, entity_name: str):
        """Redirect to entity search"""
        # Get the feed parameter if it exists
        feed = request.query_params.get('feed', '')
        feed_param = f"&feed={feed}" if feed else ""
        return RedirectResponse(url=f'/search?entity={entity_name}{feed_param}', status_code=303)

    @rt('/entity/{entity_name}')
    async def get_entity(request: Request, entity_name: str):
        """Redirect to entity search"""
        # Get the feed parameter if it exists
        feed = request.query_params.get('feed', '')
        feed_param = f"&feed={feed}" if feed else ""
        return RedirectResponse(url=f'/search?entity={entity_name}{feed_param}', status_code=303)

    @rt('/search/tag/{tag_name}')
    async def search_tag(request: Request, tag_name: str):
        """Redirect to tag search"""
        # Get the feed parameter if it exists
        feed = request.query_params.get('feed', '')
        feed_param = f"&feed={feed}" if feed else ""
        return RedirectResponse(url=f'/?tag_name={tag_name}{feed_param}', status_code=303)

# Helper functions
def perform_semantic_search(query: str, collection, limit: int = 10) -> List[Dict[str, Any]]:
    """Perform semantic search on paper collection"""
    # Generate embedding for query using NLP service
    query_embedding = nlp.get_embedding(query)

    if not query_embedding:
        return []

    # Search for papers with embeddings and find the most similar ones
    search_results = []

    # Find papers with embeddings
    papers_with_embeddings = collection.find({"embedding": {"$exists": True}})

    for paper in papers_with_embeddings:
        if "embedding" in paper:
            # Calculate similarity score
            score = nlp.calculate_similarity(query_embedding, paper["embedding"])
            search_results.append({"paper": paper, "score": score})

    # Sort by similarity score in descending order
    search_results.sort(key=lambda x: x["score"], reverse=True)

    # Return top results
    return search_results[:limit]

def render_search_results(papers):
    """Render search results as HTML"""
    return Div(
        Div(
            *(Div(
                Div(
                    H2(paper.get('title', 'No title'), cls="text-xl font-semibold text-white mb-2"),
                    P(paper.get('authors_string', 'Unknown authors'), cls="text-gray-400 text-sm mb-2"),
                    P(f"Published: {paper.get('date', 'Unknown date')}", cls="text-gray-500 text-xs mb-3"),
                    P(paper.get('abstract', 'No abstract available')[:300] + "..." if paper.get('abstract', '') else 'No abstract available',
                      cls="text-gray-300 text-sm mb-4"),
                    Div(
                        A("View Paper", href=f"/paper/{paper.get('_id')}", cls="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded text-white text-sm"),
                        A("Original Source", href=paper.get('pdf_url', '#'), target="_blank", cls="px-4 py-2 ml-3 bg-gray-700 hover:bg-gray-800 rounded text-white text-sm"),
                        cls="flex"
                    ),
                    cls="p-6"
                ),
                cls="bg-gray-800 rounded-lg overflow-hidden shadow-lg mb-6"
            ) for paper in papers),
            cls="w-full max-w-4xl mx-auto px-4 sm:px-6 py-10"
        ),
        cls="w-full"
    )
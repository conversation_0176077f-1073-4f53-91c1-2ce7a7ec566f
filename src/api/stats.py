"""
Statistics route handler for displaying paper and content statistics.
"""
from typing import Optional
from datetime import datetime
from starlette.requests import Request
from fasthtml.common import *
from monsterui.all import *

from src.ui import Navigation, StatsCard
from src.utils.date import format_date

def register_routes(app, rt, collections):
    """Register routes related to daily statistics"""
    
    @rt('/stats')
    async def get_stats(request: Request):
        """Display daily paper statistics and metadata"""
        
        daily_stats_collection = collections['daily_stats']
        
        # Get all stats entries, sorted by date in descending order
        stats_entries = list(daily_stats_collection.find({}, sort=[('date', -1)], limit=10))
        
        # If there are no stats yet, create placeholders
        if not stats_entries:
            return Navigation(request), Title("moatless | Stats"), Body(
                Div(
                    Div(
                        H1("Paper Statistics", cls="text-3xl sm:text-4xl font-bold text-white mb-6 text-center"),
                        P("No statistics available yet. Check back later.", cls="text-xl text-center text-gray-400 font-light my-16"),
                        cls="py-8 sm:py-12 w-full bg-[#1e2532] border-b border-gray-800"
                    ),
                    cls="min-h-screen bg-[#1e2532] flex flex-col items-center w-full"
                )
            )
        
        # Get previous day's stats and the day before for trend calculation
        previous_day_stats = stats_entries[0] if stats_entries else None
        day_before_previous = stats_entries[1] if len(stats_entries) > 1 else None
        
        # Calculate trends if we have data for the day before previous
        paper_count_trend = None
        token_length_trend = None
        pdf_html_ratio_trend = None
        
        if previous_day_stats and day_before_previous:
            # Calculate % change for paper count
            if day_before_previous['paper_count'] > 0:
                paper_count_trend = round(((previous_day_stats['paper_count'] - day_before_previous['paper_count']) / day_before_previous['paper_count']) * 100)
            
            # Calculate % change for average token length
            if day_before_previous['avg_token_length'] > 0:
                token_length_trend = round(((previous_day_stats['avg_token_length'] - day_before_previous['avg_token_length']) / day_before_previous['avg_token_length']) * 100)
            
            # Calculate % change for PDF/HTML ratio
            if day_before_previous['pdf_html_ratio'] > 0:
                pdf_html_ratio_trend = round(((previous_day_stats['pdf_html_ratio'] - day_before_previous['pdf_html_ratio']) / day_before_previous['pdf_html_ratio']) * 100)
        
        # Format the PDF/HTML ratio for display
        pdf_html_ratio_display = str(round(previous_day_stats['pdf_html_ratio'], 2)) if previous_day_stats and previous_day_stats['pdf_html_ratio'] != float('inf') else "∞"
        
        return Navigation(request), Title("moatless | Stats"), Body(
            Div(  # Outer container
                Div(  # Header section
                    Div(  # Content wrapper with max width
                        H1("Paper Statistics", cls="text-3xl sm:text-4xl 2xl:text-3xl font-bold text-white mb-6 2xl:mb-4 text-center"),
                        P("Daily metadata about papers and full texts from the previous day", cls="text-xl 2xl:text-lg text-center text-gray-400 font-light mb-8 2xl:mb-5"),
                        cls="flex flex-col items-center w-full max-w-5xl mx-auto px-6"
                    ),
                    cls="py-8 sm:py-12 2xl:py-8 w-full bg-[#1e2532] border-b border-gray-800"
                ),
                Div(  # Content container
                    # Previous day's statistics cards
                    H2("Previous Day's Statistics", cls="text-2xl 2xl:text-xl font-bold text-white mb-6 2xl:mb-4 mt-8 2xl:mt-6"),
                    Div(  # Stats cards grid
                        StatsCard("Papers Processed", str(previous_day_stats['paper_count']), paper_count_trend),
                        StatsCard("Papers with Full Text", str(previous_day_stats['papers_with_full_text'])),
                        StatsCard("Avg Token Length", f"{round(previous_day_stats['avg_token_length']):,}", token_length_trend),
                        StatsCard("Total Token Count", f"{round(previous_day_stats['total_token_count']):,}"),
                        cls="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 2xl:gap-4 mb-10 2xl:mb-6"
                    ),
                    # Source breakdown
                    H2("Source Breakdown", cls="text-2xl 2xl:text-xl font-bold text-white mb-6 2xl:mb-4"),
                    Div(  # Source cards grid
                        StatsCard("HTML Sources", str(previous_day_stats['html_count'])),
                        StatsCard("PDF Sources", str(previous_day_stats['pdf_count'])),
                        StatsCard("Abstract Sources", str(previous_day_stats['abstract_count'])),
                        StatsCard("PDF/HTML Ratio", pdf_html_ratio_display, pdf_html_ratio_trend),
                        cls="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 2xl:gap-4 mb-10 2xl:mb-6"
                    ),
                    # Historical data table
                    H2("Historical Data", cls="text-2xl 2xl:text-xl font-bold text-white mb-6 2xl:mb-4"),
                    Div(  # Table container
                        Div(  # Table header
                            Div("Date", cls="py-3 2xl:py-2 px-4 text-left font-medium text-white"),
                            Div("Papers", cls="py-3 2xl:py-2 px-4 text-left font-medium text-white"),
                            Div("Full Texts", cls="py-3 2xl:py-2 px-4 text-left font-medium text-white"), 
                            Div("Avg Tokens", cls="py-3 2xl:py-2 px-4 text-left font-medium text-white"),
                            Div("Total Tokens", cls="py-3 2xl:py-2 px-4 text-left font-medium text-white"),
                            Div("PDF/HTML", cls="py-3 2xl:py-2 px-4 text-left font-medium text-white"),
                            cls="grid grid-cols-6 bg-[#1e2532] rounded-t-lg border-b border-gray-800"
                        ),
                        # Table rows
                        *(Div(
                            Div(format_date(stats['date']), cls="py-3 2xl:py-2 px-4 text-left text-gray-400 2xl:text-sm"),
                            Div(str(stats['paper_count']), cls="py-3 2xl:py-2 px-4 text-left text-gray-400 2xl:text-sm"),
                            Div(str(stats['papers_with_full_text']), cls="py-3 2xl:py-2 px-4 text-left text-gray-400 2xl:text-sm"),
                            Div(f"{round(stats['avg_token_length']):,}", cls="py-3 2xl:py-2 px-4 text-left text-gray-400 2xl:text-sm"),
                            Div(f"{round(stats['total_token_count']):,}", cls="py-3 2xl:py-2 px-4 text-left text-gray-400 2xl:text-sm"),
                            Div(
                                str(round(stats['pdf_html_ratio'], 2)) if stats['pdf_html_ratio'] != float('inf') else "∞", 
                                cls="py-3 2xl:py-2 px-4 text-left text-gray-400 2xl:text-sm"
                            ),
                            cls=f"grid grid-cols-6 {'bg-[#273142]' if i % 2 == 0 else 'bg-[#1e2532]'} " + 
                                (f"rounded-b-lg" if i == len(stats_entries) - 1 else "")
                        ) for i, stats in enumerate(stats_entries)),
                        cls="overflow-x-auto rounded-lg shadow-md"
                    ),
                    cls="w-full max-w-6xl mx-auto px-4 sm:px-6 py-6 sm:py-10 2xl:py-6"
                ),
                cls="min-h-screen bg-[#1e2532] flex flex-col items-center w-full"
            )
        )
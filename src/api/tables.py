"""
API routes for tables extracted from papers
"""
from starlette.responses import JSONResponse
from typing import List, Dict, Any

def register_routes(app, rt, collections):
    """Register routes related to tables"""
    
    tables_collection = collections['tables']
    summaries_collection = collections['summaries']
    
    @app.get('/api/tables/{paper_id}')
    async def get_tables(request):
        """
        API endpoint to get tables for a paper
        
        Args:
            request: FastAPI request object
            
        Returns:
            JSON response with the tables data
        """
        paper_id = request.path_params.get('paper_id')
        if not paper_id:
            return JSONResponse({'status': 'error', 'message': 'Paper ID is required'})
        
        try:
            # Find tables for this paper
            tables = list(tables_collection.find(
                {'paper_id': paper_id},
                sort=[('table_index', 1)]  # Sort by table index
            ))
            
            if not tables:
                # Check if the paper exists but has no tables
                abs_url = f"https://arxiv.org/abs/{paper_id}"
                paper = summaries_collection.find_one({'abs_url': abs_url})
                
                if paper:
                    # Paper exists but has no tables
                    if paper.get('has_tables') is False:
                        return JSONResponse({
                            'status': 'success',
                            'message': 'No tables found for this paper',
                            'has_tables': False,
                            'tables': []
                        })
                    else:
                        # Paper exists but tables haven't been extracted yet
                        return JSONResponse({
                            'status': 'pending',
                            'message': 'Tables are being processed for this paper',
                            'has_tables': None,
                            'tables': []
                        })
                else:
                    # Paper doesn't exist
                    return JSONResponse({
                        'status': 'error',
                        'message': 'Paper not found',
                        'has_tables': None,
                        'tables': []
                    })
            
            # Process tables for the response
            processed_tables = []
            for table in tables:
                processed_table = {
                    'table_index': table.get('table_index', 0),
                    'data': table.get('data', []),
                    'columns': table.get('columns', []),
                    'created_at': table.get('created_at', None)
                }
                processed_tables.append(processed_table)
            
            return JSONResponse({
                'status': 'success',
                'message': f'Found {len(processed_tables)} tables',
                'has_tables': True,
                'tables': processed_tables
            })
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            return JSONResponse({
                'status': 'error',
                'message': f'Error retrieving tables: {str(e)}',
                'has_tables': None,
                'tables': []
            })

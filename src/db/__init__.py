from .mongo import (
    setup_mongo_client,
    setup_collections,
    create_indexes,
    MockMongoClient,
)

# Global variable to store collections
_collections = None

def get_collections():
    """
    Get the MongoDB collections
    
    Returns:
        dict: Dictionary of MongoDB collections
    """
    global _collections
    return _collections

def set_collections(collections):
    """
    Set the global collections object
    
    Args:
        collections: Dictionary of MongoDB collections
    """
    global _collections
    _collections = collections

# Import Neo4j-related functions if they exist
try:
    from .neo4j import setup_neo4j_driver, InMemoryNeo4jDriver
    __all__ = [
        'setup_mongo_client',
        'setup_collections',
        'create_indexes',
        'MockMongoClient',
        'setup_neo4j_driver',
        'InMemoryNeo4jDriver',
        'get_collections',
        'set_collections',
    ]
except ImportError:
    # Neo4j module not created yet
    __all__ = [
        'setup_mongo_client',
        'setup_collections',
        'create_indexes',
        'MockMongoClient',
        'get_collections',
        'set_collections',
    ]
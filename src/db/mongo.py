import os
import sys
import json
from typing import Dict, List, Optional, Union, Any, Tuple
from pathlib import Path

# MongoDB imports
from pymongo import MongoClient, ASCENDING, DESCENDING, TEXT
from pymongo.errors import ServerSelectionTimeoutError

# Global collections variable that can be accessed across modules
_collections = None


def setup_mongo_client(local_mode: bool = False) -> MongoClient:
    """
    Setup MongoDB client based on local mode or remote connection.

    Args:
        local_mode (bool): Whether to use in-memory MongoDB for local testing

    Returns:
        MongoClient: The MongoDB client
    """
    if local_mode:
        print("Using in-memory MongoDB for local testing...")

        # Choose the appropriate in-memory MongoDB implementation
        mongo_client = None

        # First try mongomock
        try:
            import mongomock
            mongo_client = mongomock.MongoClient()
            print("Using mongomock for local testing.")
        except ImportError:
            print("mongomock not available, trying other options.")

        # If mongomock failed, try pymongo-inmemory
        if not mongo_client:
            try:
                from pymongo_inmemory import MongoClient as InMemoryMongoClient
                mongo_client = InMemoryMongoClient()
                print("Using pymongo-inmemory for local testing.")
            except (ImportError, Exception) as e:
                print(f"pymongo-inmemory not available or failed: {str(e)}")

        # If both failed, use the dictionary-based mock
        if not mongo_client:
            print("No MongoDB mock library found. Using a simple dictionary-based mock.")
            mongo_client = MockMongoClient()

        print("In-memory MongoDB started successfully.")
    else:
        print("Connecting to remote MongoDB...")
        try:
            # Connect to remote MongoDB
            mongo_client = MongoClient(os.getenv("MONGO_PRIVATE_URL"),
                                connectTimeoutMS=30000,  # 30 seconds
                                serverSelectionTimeoutMS=30000,
                                socketTimeoutMS=60000)  # 60 seconds)
            # Test connection
            mongo_client.admin.command('ping')
            print("Connected to remote MongoDB successfully.")
        except ServerSelectionTimeoutError:
            print("Error: Could not connect to remote MongoDB.")
            print("If you want to use in-memory MongoDB for local testing, run with the --local flag.")
            sys.exit(1)

    return mongo_client


def setup_collections(mongo_client: MongoClient) -> Dict[str, Any]:
    """
    Setup MongoDB collections

    Args:
        mongo_client (MongoClient): The MongoDB client

    Returns:
        Dict[str, Any]: A dictionary with all collections
    """
    mongodb = mongo_client['papers']
    collections = {
        'summaries': mongodb['summaries'],
        'users': mongodb['users'],
        'full_texts': mongodb['full_texts'],
        'daily_stats': mongodb['daily_stats'],
        'sota_results': mongodb['sota_results'],
        'reviews': mongodb['reviews'],
        'knowledge_graphs': mongodb['knowledge_graphs'],
        'saved_papers': mongodb['saved_papers'],
        'entity_resolution': mongodb['entity_resolution'],
        'mermaid_diagrams': mongodb['mermaid_diagrams'],
        'entities': mongodb['entities'],
        'entity_mappings': mongodb['entity_mappings'],
        'tables': mongodb['tables']
    }

    return collections


def create_indexes(collections: Dict[str, Any]) -> None:
    """
    Create indexes for faster querying

    Args:
        collections (Dict[str, Any]): Dictionary with all collections
    """
    try:
        # Basic indexes
        collections['summaries'].create_index('abs_url', unique=True)
        collections['users'].create_index('user_id', unique=True)
        collections['full_texts'].create_index('paper_id', unique=True)  # Index on arXiv paper ID
        collections['daily_stats'].create_index('date', unique=True)  # Index on date for daily stats
        collections['sota_results'].create_index('abs_url', unique=True)  # Index on arXiv URL for SOTA results
        collections['reviews'].create_index([('abs_url', 1), ('user_id', 1)], unique=True)  # Compound index for unique reviews per user per paper
        collections['knowledge_graphs'].create_index('abs_url', unique=True)  # Index on arXiv URL for knowledge graphs
        collections['saved_papers'].create_index([('abs_url', 1), ('user_id', 1)], unique=True)  # Compound index for unique saved papers per user
        collections['mermaid_diagrams'].create_index('abs_url', unique=True)  # Index on arXiv URL for mermaid diagrams
        collections['tables'].create_index([('paper_id', 1), ('table_index', 1)], unique=True)  # Compound index for unique tables per paper

        # Add text search index for summaries collection
        try:
            # Create a text index on title, abstract, and authors_string for full-text search
            collections['summaries'].create_index(
                [
                    ('title', TEXT),
                    ('abstract', TEXT),
                    ('authors_string', TEXT)
                ],
                name='text_search_index',
                default_language='english'
            )
            print("Created text search index for summaries collection")
        except Exception as e:
            print(f"Error creating text search index: {e}")

        # Add indexes for entity_resolution collection
        if 'entity_resolution' in collections:
            collections['entity_resolution'].create_index([("canonical_id", 1)], unique=True)
            collections['entity_resolution'].create_index([("variant_ids", 1)])
            collections['entity_resolution'].create_index([("entity_type", 1)])
    except Exception as e:
        print(f"Error creating indexes: {e}")


def get_collections() -> Dict[str, Any]:
    """
    Get the global MongoDB collections

    Returns:
        Dict[str, Any]: A dictionary with all collections
    """
    global _collections

    if _collections is None:
        # If collections are not initialized, try to initialize with default parameters
        # Check if we're in local mode
        local_mode = os.environ.get('LOCAL_MODE', '').lower() == 'true'
        client = setup_mongo_client(local_mode=local_mode)
        _collections = setup_collections(client)

    return _collections


def set_collections(collections: Dict[str, Any]) -> None:
    """
    Set the global MongoDB collections

    Args:
        collections (Dict[str, Any]): Dictionary with all collections
    """
    global _collections
    _collections = collections


def load_json_data_to_mock(mongo_client: MongoClient, data_dir: str = None) -> None:
    """
    Load JSON data files from a directory into the mock MongoDB

    Args:
        mongo_client (MongoClient): The MongoDB client
        data_dir (str): Directory containing JSON data files (defaults to data/mongodb/papers)
    """
    if data_dir is None:
        # Default to the project's data directory
        data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                               "data", "mongodb", "papers")

    data_path = Path(data_dir)
    if not data_path.exists():
        print(f"Error: Data directory {data_dir} does not exist.")
        return

    print(f"Loading JSON data from {data_dir}")

    # Get database
    db = mongo_client['papers']

    # Find all JSON files in the directory (excluding Zone.Identifier files)
    json_files = [f for f in data_path.glob("*.json") if f.is_file() and not f.name.endswith('.json:Zone.Identifier')]

    for json_file in json_files:
        collection_name = json_file.stem
        print(f"Loading {collection_name} collection from {json_file}")

        try:
            # Get or create collection
            collection = db[collection_name]

            # Clear existing data in the collection
            collection.delete_many({})

            # Read JSON data line by line (NDJSON format)
            doc_count = 0
            with open(json_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:  # Skip empty lines
                        continue

                    try:
                        doc = json.loads(line)
                        if doc:  # Only insert if there's data
                            collection.insert_one(doc)
                            doc_count += 1
                    except json.JSONDecodeError as e:
                        print(f"  Error parsing JSON line: {e}")
                        continue

            print(f"Inserted {doc_count} documents into {collection_name}")

        except Exception as e:
            print(f"Error loading data from {json_file}: {str(e)}")

    print("Finished loading JSON data")


# Mock MongoDB implementation for local testing
class MockCollection:
    def __init__(self, name):
        self.name = name
        self.data = []
        self.indexes = []

    def insert_one(self, document):
        # Generate a unique ID if not provided
        if '_id' not in document:
            document['_id'] = len(self.data) + 1
        self.data.append(document)
        return type('obj', (object,), {'inserted_id': document['_id']})

    def insert_many(self, documents):
        inserted_ids = []
        for doc in documents:
            result = self.insert_one(doc)
            inserted_ids.append(result.inserted_id)
        return type('obj', (object,), {'inserted_ids': inserted_ids})

    def find_one(self, query):
        for doc in self.data:
            match = True
            for key, value in query.items():
                if key not in doc or doc[key] != value:
                    match = False
                    break
            if match:
                return doc
        return None

    def find(self, query=None, sort=None, limit=None, **kwargs):
        query = query or {}
        results = []

        # Basic filtering
        for doc in self.data:
            match = True
            for key, value in query.items():
                # Handle special MongoDB operators
                if isinstance(value, dict):
                    if '$regex' in value:
                        # Very basic regex support (case-insensitive substring match)
                        if key in doc and isinstance(doc[key], str):
                            search_term = value['$regex']
                            if value.get('$options', '') == 'i':
                                if search_term.lower() not in doc[key].lower():
                                    match = False
                                    break
                            else:
                                if search_term not in doc[key]:
                                    match = False
                                    break
                        else:
                            match = False
                            break
                    elif '$exists' in value:
                        # Check if the field exists
                        exists = value['$exists']
                        if (key in doc) != exists:
                            match = False
                            break
                    elif '$nin' in value:  # Not in operator
                        # Check if value is not in the list
                        if key in doc and doc[key] in value['$nin']:
                            match = False
                            break
                # Handle $or operator
                elif key == '$or':
                    # value should be a list of condition dicts
                    or_match = False
                    for condition in value:
                        condition_match = True
                        for cond_key, cond_val in condition.items():
                            if cond_key in doc:
                                if isinstance(cond_val, dict) and '$exists' in cond_val:
                                    exists = cond_val['$exists']
                                    if (cond_key in doc) != exists:
                                        condition_match = False
                                        break
                                # Special handling for lists like tags
                                elif isinstance(doc[cond_key], list) and cond_val in doc[cond_key]:
                                    continue  # It's a match
                                elif doc[cond_key] != cond_val:
                                    condition_match = False
                                    break
                            else:
                                condition_match = False
                                break

                        if condition_match:
                            or_match = True
                            break

                    if not or_match:
                        match = False
                        break
                elif key in doc:
                    # Special handling for lists like tags
                    if isinstance(doc[key], list) and value in doc[key]:
                        continue  # It's a match if the value is in the list
                    elif doc[key] != value:
                        match = False
                        break
                else:
                    match = False
                    break
            if match:
                results.append(doc)

        # Apply sorting if provided
        if sort:
            # Simple sort implementation - handles only single field
            if isinstance(sort, list) and len(sort) > 0:
                sort_key, sort_dir = sort[0]
                reverse = sort_dir == -1 or sort_dir == 'desc'
                results.sort(key=lambda x: x.get(sort_key, ''), reverse=reverse)

        # Apply limit
        if limit and len(results) > limit:
            results = results[:limit]

        return results

    def count_documents(self, query=None):
        query = query or {}
        count = 0
        for doc in self.data:
            match = True
            for key, value in query.items():
                # Handle special MongoDB operators like $exists
                if isinstance(value, dict) and '$exists' in value:
                    exists = value['$exists']
                    if (key in doc) != exists:
                        match = False
                        break
                # Handle $or operator
                elif key == '$or':
                    # value should be a list of condition dicts
                    or_match = False
                    for condition in value:
                        condition_match = True
                        for cond_key, cond_val in condition.items():
                            if cond_key in doc:
                                if isinstance(cond_val, dict) and '$exists' in cond_val:
                                    exists = cond_val['$exists']
                                    if (cond_key in doc) != exists:
                                        condition_match = False
                                        break
                                # Special handling for lists like tags
                                elif isinstance(doc[cond_key], list) and cond_val in doc[cond_key]:
                                    continue  # It's a match
                                elif doc[cond_key] != cond_val:
                                    condition_match = False
                                    break
                            else:
                                condition_match = False
                                break

                        if condition_match:
                            or_match = True
                            break

                    if not or_match:
                        match = False
                        break
                elif key in doc:
                    # Special handling for lists like tags
                    if isinstance(doc[key], list) and value in doc[key]:
                        continue  # It's a match if the value is in the list
                    elif doc[key] != value:
                        match = False
                        break
                else:
                    match = False
                    break
            if match:
                count += 1
        return count

    def create_index(self, key, **kwargs):
        self.indexes.append((key, kwargs))
        return key

    def update_one(self, query, update, upsert=False):
        doc = self.find_one(query)

        if doc:
            # Apply updates
            if '$set' in update:
                for key, value in update['$set'].items():
                    doc[key] = value
            return type('obj', (object,), {
                'modified_count': 1,
                'matched_count': 1,
                'upserted_id': None
            })
        elif upsert:
            # Create new document with query fields + update fields
            new_doc = query.copy()
            if '$set' in update:
                for key, value in update['$set'].items():
                    new_doc[key] = value
            self.insert_one(new_doc)
            return type('obj', (object,), {
                'modified_count': 0,
                'matched_count': 0,
                'upserted_id': new_doc['_id']
            })
        else:
            return type('obj', (object,), {
                'modified_count': 0,
                'matched_count': 0,
                'upserted_id': None
            })

    def delete_many(self, query):
        """Delete many documents that match the filter"""
        if not query:  # Empty query means delete all
            count = len(self.data)
            self.data = []
            return type('obj', (object,), {'deleted_count': count})

        # Count matching documents before deletion
        matching_docs = []
        non_matching_docs = []

        for doc in self.data:
            match = True
            for key, value in query.items():
                if key not in doc or doc[key] != value:
                    match = False
                    break
            if match:
                matching_docs.append(doc)
            else:
                non_matching_docs.append(doc)

        # Update the collection with only non-matching documents
        self.data = non_matching_docs

        return type('obj', (object,), {'deleted_count': len(matching_docs)})


class MockDatabase:
    def __init__(self, name):
        self.name = name
        self.collections = {}

    def __getitem__(self, collection_name):
        if collection_name not in self.collections:
            self.collections[collection_name] = MockCollection(collection_name)
        return self.collections[collection_name]

    def list_collection_names(self):
        """Return a list of collection names in this database"""
        return list(self.collections.keys())

    def create_collection(self, collection_name):
        """Create a new collection"""
        if collection_name not in self.collections:
            self.collections[collection_name] = MockCollection(collection_name)
        return self.collections[collection_name]


class MockMongoClient:
    def __init__(self):
        self.databases = {}
        # Create admin database with ping command
        self.admin = type('obj', (object,), {
            'command': lambda cmd: {'ok': 1} if cmd == 'ping' else None
        })

    def __getitem__(self, db_name):
        if db_name not in self.databases:
            self.databases[db_name] = MockDatabase(db_name)
        return self.databases[db_name]
"""
Neo4j database utilities, including in-memory mock for local testing.
"""
import os
import networkx as nx
from typing import Dict, List, Any, Optional, Tuple, Union

# Use neo4j import conditionally
try:
    from neo4j import GraphDatabase
    REAL_NEO4J_AVAILABLE = True
except ImportError:
    REAL_NEO4J_AVAILABLE = False
    # Create a dummy GraphDatabase for type hinting
    class GraphDatabase:
        @staticmethod
        def driver(*args, **kwargs):
            return None


class Neo4jSession:
    """Mock Neo4j session for in-memory testing"""

    def __init__(self, driver):
        self.driver = driver
        self.graph = driver.graph
        self.closed = False

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

    def close(self):
        self.closed = True

    def run(self, query, **params):
        """Execute a Cypher query against the in-memory graph"""
        # Parse and execute Cypher queries (simplified implementation)
        query = query.strip()
        result_processor = None

        try:
            # Very basic Cypher parsing
            if query.upper().startswith("CREATE CONSTRAINT"):
                # Just mock success for constraints
                result_processor = lambda: MockStatementResult([{"name": "Constraint created"}])

            elif query.upper().startswith("MATCH (n) RETURN count(n)"):
                # Count nodes
                count = len(self.graph.nodes)

                # Handle different formats based on the AS alias
                if "AS node_count" in query.upper():
                    result_processor = lambda: MockStatementResult([{"node_count": count}])
                elif "AS count" in query.upper():
                    result_processor = lambda: MockStatementResult([{"count": count}])
                else:
                    result_processor = lambda: MockStatementResult([{"count": count}])

            elif query.upper().startswith("MATCH (n)") and "COUNT" in query.upper():
                # Count nodes with optional filtering
                count = len(self.graph.nodes)
                result_processor = lambda: MockStatementResult([{"count": count}])

            elif query.upper().startswith("MATCH (n)") and "degree" in query.lower():
                # Node query with degree
                nodes = []
                for node_id in self.graph.nodes:
                    node = self.graph.nodes[node_id]
                    degree = len(list(self.graph.neighbors(node_id)))
                    nodes.append({
                        "id": node.get("id", node_id),
                        "name": node.get("name", str(node_id)),
                        "labels": node.get("labels", ["Entity"]),
                        "degree": degree
                    })
                # Sort by degree (descending)
                nodes.sort(key=lambda n: n["degree"], reverse=True)

                # Apply limit if specified
                limit = params.get("limit", len(nodes))
                if limit and len(nodes) > limit:
                    nodes = nodes[:limit]

                result_processor = lambda: MockStatementResult([n for n in nodes])

            elif query.upper().startswith("MATCH (a)-[r]->(b)"):
                # Relationship query between nodes
                # Get node IDs to filter relationships
                node_ids = params.get("node_ids", [])
                links = []

                # Iterate through all edges
                for source, target, data in self.graph.edges(data=True):
                    # Check if both source and target are in the node IDs list
                    if not node_ids or (source in node_ids and target in node_ids):
                        source_id = self.graph.nodes[source].get("id", source)
                        target_id = self.graph.nodes[target].get("id", target)
                        rel_type = data.get("type", "RELATES_TO")

                        links.append({
                            "source": source_id,
                            "target": target_id,
                            "type": rel_type
                        })

                result_processor = lambda: MockStatementResult([l for l in links])

            elif query.upper().startswith("MATCH (p:Paper)"):
                # Get paper nodes
                paper_ids = []
                for node_id, attrs in self.graph.nodes(data=True):
                    if "labels" in attrs and "Paper" in attrs["labels"]:
                        paper_ids.append({"paper_id": attrs.get("id", node_id)})

                result_processor = lambda: MockStatementResult(paper_ids)

            elif query.upper().startswith("MERGE (p:Paper"):
                # Create or update a Paper node
                paper_id = params.get("paper_id")
                title = params.get("title", "")
                abs_url = params.get("abs_url", "")

                # Check if node exists
                paper_node = None
                for node_id, attrs in self.graph.nodes(data=True):
                    if "id" in attrs and attrs["id"] == paper_id:
                        paper_node = node_id
                        break

                if paper_node is None:
                    # Create new node
                    paper_node = len(self.graph.nodes)
                    self.graph.add_node(paper_node, id=paper_id, name=title, url=abs_url, labels=["Paper"])
                else:
                    # Update existing node
                    self.graph.nodes[paper_node]["name"] = title
                    self.graph.nodes[paper_node]["url"] = abs_url

                result_processor = lambda: MockStatementResult([{"p": {"id": paper_id}}])

            elif "MERGE (n:" in query.upper():
                # Create or update an entity node
                node_id = params.get("node_id")
                name = params.get("name", "")
                description = params.get("description", "")

                # Extract label from query (very basic parsing)
                label = "Entity"  # Default
                if "MERGE (n:" in query:
                    label_part = query.split("MERGE (n:")[1].split("{")[0].strip()
                    if label_part:
                        label = label_part

                # Check if node exists
                entity_node = None
                for n_id, attrs in self.graph.nodes(data=True):
                    if "id" in attrs and attrs["id"] == node_id:
                        entity_node = n_id
                        break

                if entity_node is None:
                    # Create new node
                    entity_node = len(self.graph.nodes)
                    self.graph.add_node(entity_node, id=node_id, name=name,
                                      description=description, labels=[label])
                else:
                    # Update existing node
                    self.graph.nodes[entity_node]["name"] = name
                    self.graph.nodes[entity_node]["description"] = description

                result_processor = lambda: MockStatementResult([{"n": {"id": node_id}}])

            elif "MERGE (source)-[r:" in query.upper() or "MERGE (source)-[r:`" in query:
                # Create a relationship between nodes
                source_id = params.get("source_id")
                target_id = params.get("target_id")

                # Extract relationship type (very basic parsing)
                rel_type = "RELATES_TO"  # Default
                if "MERGE (source)-[r:" in query:
                    rel_part = query.split("MERGE (source)-[r:")[1].split("]->(target)")[0].strip()
                    if rel_part:
                        rel_type = rel_part
                elif "MERGE (source)-[r:`" in query:
                    rel_part = query.split("MERGE (source)-[r:`")[1].split("`]->(target)")[0].strip()
                    if rel_part:
                        rel_type = rel_part

                # Find source and target nodes
                source_node = None
                target_node = None

                for node_id, attrs in self.graph.nodes(data=True):
                    if "id" in attrs:
                        if attrs["id"] == source_id:
                            source_node = node_id
                        elif attrs["id"] == target_id:
                            target_node = node_id

                # Add nodes if they don't exist (which shouldn't happen in normal Neo4j usage)
                if source_node is None:
                    source_node = len(self.graph.nodes)
                    self.graph.add_node(source_node, id=source_id, name=source_id, labels=["Entity"])

                if target_node is None:
                    target_node = len(self.graph.nodes)
                    self.graph.add_node(target_node, id=target_id, name=target_id, labels=["Entity"])

                # Add or update the relationship
                self.graph.add_edge(source_node, target_node, type=rel_type)

                result_processor = lambda: MockStatementResult([{"r": {"type": rel_type}}])

            elif query.upper() == "RETURN 'CONNECTION SUCCESSFUL' AS MESSAGE":
                # Connection test
                result_processor = lambda: MockStatementResult([{"message": "Connection successful"}])

            else:
                # Unknown query, return empty result
                print(f"Warning: Unsupported Cypher query in InMemoryNeo4jDriver: {query}")
                result_processor = lambda: MockStatementResult([])

            return result_processor()

        except Exception as e:
            print(f"Error executing Cypher query: {e}")
            return MockStatementResult([])


class MockStatementResult:
    """Mock Neo4j statement result"""

    def __init__(self, records):
        self.records = records
        self._current = 0

    def __iter__(self):
        self._current = 0
        return self

    def __next__(self):
        if self._current < len(self.records):
            record = MockRecord(self.records[self._current])
            self._current += 1
            return record
        raise StopIteration

    def single(self):
        """Return the first record or None"""
        if not self.records:
            return MockRecord({})  # Return empty record instead of None
        return MockRecord(self.records[0])


class MockRecord(dict):
    """Mock Neo4j record that behaves like a dict and allows attribute access"""

    def __init__(self, data):
        super().__init__(data)

    def __getitem__(self, key):
        # Safely handle missing keys to avoid the 'NoneType' object is not subscriptable error
        try:
            return super().__getitem__(key)
        except KeyError:
            # Return None instead of raising KeyError
            return None

    def get(self, key, default=None):
        return super().get(key, default)


def safe_neo4j_operation(func):
    """
    Decorator for safely executing Neo4j operations.
    If Neo4j is unavailable, it will log the operation and return None.

    Args:
        func: The function to wrap

    Returns:
        The wrapped function
    """
    def wrapper(*args, **kwargs):
        if not NEO4J_AVAILABLE:
            # Extract function name for better logging
            func_name = func.__name__
            # Log that we're skipping this operation
            print(f"Skipping Neo4j operation '{func_name}' because Neo4j is unavailable")
            return None
        try:
            return func(*args, **kwargs)
        except Exception as e:
            print(f"Error in Neo4j operation '{func.__name__}': {e}")
            return None
    return wrapper


class InMemoryNeo4jDriver:
    """
    In-memory Neo4j driver implementation using NetworkX
    """

    def __init__(self):
        """Initialize the in-memory graph"""
        self.graph = nx.DiGraph()  # Directed graph
        self.sessions = []

    def session(self):
        """Create a new session"""
        session = Neo4jSession(self)
        self.sessions.append(session)
        return session

    def close(self):
        """Close the driver and all sessions"""
        for session in self.sessions:
            if not session.closed:
                session.close()
        self.sessions = []

    # Add methods for safely adding nodes and relationships
    def add_node(self, node_id, properties=None):
        """Safely add a node to the graph"""
        try:
            self.graph.add_node(node_id, **properties if properties else {})
            return True
        except Exception as e:
            print(f"Error creating node {node_id}: {e}")
            return False

    def add_relationship(self, source_id, target_id, rel_type):
        """Safely add a relationship to the graph"""
        try:
            self.graph.add_edge(source_id, target_id, type=rel_type)
            return True
        except Exception as e:
            print(f"Error creating relationship {source_id}-[{rel_type}]->{target_id}: {e}")
            return False

    def get_graph_data(self):
        """
        Export the graph data in a format compatible with visualization

        Returns:
            dict: Graph data with nodes and links
        """
        nodes = []
        links = []

        # Process nodes
        for node_id, attrs in self.graph.nodes(data=True):
            node_data = {
                "id": attrs.get("id", str(node_id)),
                "name": attrs.get("name", str(node_id)),
                "label": attrs.get("labels", ["Entity"])[0],
                "color": "#4b7bec"  # Default color
            }

            # Set color based on node label
            label = node_data["label"]
            if label == "Paper":
                node_data["color"] = "#ff6b6b"  # Red for papers
            elif label in ["Model", "Algorithm", "Method"]:
                node_data["color"] = "#1dd1a1"  # Green for models/algorithms
            elif label in ["Dataset", "Benchmark"]:
                node_data["color"] = "#feca57"  # Yellow for datasets

            # Calculate degree
            node_data["degree"] = len(list(self.graph.neighbors(node_id)))

            nodes.append(node_data)

        # Process links
        for source, target, attrs in self.graph.edges(data=True):
            source_id = self.graph.nodes[source].get("id", str(source))
            target_id = self.graph.nodes[target].get("id", str(target))
            rel_type = attrs.get("type", "RELATES_TO")

            links.append({
                "source": source_id,
                "target": target_id,
                "type": rel_type
            })

        return {
            "nodes": nodes,
            "links": links
        }


# Global flag to track Neo4j availability
NEO4J_AVAILABLE = False

def setup_neo4j_driver(local_mode: bool = False) -> Union[GraphDatabase.driver, InMemoryNeo4jDriver]:
    """
    Setup Neo4j driver based on local mode or remote connection.

    Args:
        local_mode (bool): Whether to use in-memory Neo4j for local testing

    Returns:
        Union[GraphDatabase.driver, InMemoryNeo4jDriver]: The Neo4j driver
    """
    global NEO4J_AVAILABLE
    NEO4J_AVAILABLE = False  # Reset the flag

    if local_mode:
        # Check if a local Neo4j URL is provided
        neo4j_url = os.environ.get("NEO4J_URL_LOCAL")

        if neo4j_url and REAL_NEO4J_AVAILABLE:
            print(f"Connecting to local Neo4j at {neo4j_url}...")
            try:
                # Try to connect to local Neo4j
                # Check if credentials are already in the URL (e.g., bolt://user:pass@host)
                if "@" in neo4j_url:
                    # Credentials are already in the URL
                    driver = GraphDatabase.driver(neo4j_url)
                else:
                    # Get credentials from environment variables
                    neo4j_user = os.environ.get("NEO4J_USERNAME", "neo4j")
                    neo4j_password = os.environ.get("NEO4J_PASSWORD", "neo4j")
                    driver = GraphDatabase.driver(neo4j_url, auth=(neo4j_user, neo4j_password))

                # Test connection quickly
                with driver.session() as session:
                    result = session.run("RETURN 'Connection successful' AS message")
                    message = result.single()["message"]
                    print(f"Local Neo4j connection test: {message}")
                    NEO4J_AVAILABLE = True

                return driver
            except Exception as e:
                print(f"Error connecting to local Neo4j: {e}")
                print("Falling back to in-memory Neo4j...")
        else:
            print("No local Neo4j URL provided or Neo4j driver not available")
            print("Using in-memory Neo4j implementation...")

        # Use in-memory implementation
        return InMemoryNeo4jDriver()
    else:
        # Connect to remote Neo4j
        if not REAL_NEO4J_AVAILABLE:
            print("Warning: Neo4j driver not installed. Install with 'pip install neo4j'")
            return InMemoryNeo4jDriver()

        # Get the URL and ensure it has the correct protocol prefix
        neo4j_url = os.environ.get("NEO4J_URL_PRIVATE", "bolt://shuttle.proxy.rlwy.net:43630")

        # Make sure the URL has a protocol prefix
        if not neo4j_url.startswith(("bolt://", "neo4j://", "neo4j+s://", "neo4j+ssc://")):
            # Add bolt:// protocol if missing
            neo4j_url = f"bolt://{neo4j_url}"

        print(f"Connecting to remote Neo4j at {neo4j_url}...")

        try:
            # Check if credentials are already in the URL (e.g., bolt://user:pass@host)
            if "@" in neo4j_url:
                # Credentials are already in the URL
                driver = GraphDatabase.driver(neo4j_url, connection_timeout=5)  # Add timeout
            else:
                # Get credentials from environment variables
                neo4j_user = os.environ.get("NEO4J_USERNAME", "neo4j")
                neo4j_password = os.environ.get("NEO4J_PASSWORD", "neo4j")
                driver = GraphDatabase.driver(neo4j_url, auth=(neo4j_user, neo4j_password), connection_timeout=5)  # Add timeout

            # Test connection
            with driver.session() as session:
                result = session.run("RETURN 'Connection successful' AS message")
                message = result.single()["message"]
                print(f"Remote Neo4j connection test: {message}")
                NEO4J_AVAILABLE = True

            return driver
        except Exception as e:
            print(f"Error connecting to remote Neo4j: {e}")
            print("Neo4j is unavailable. Neo4j-dependent features will be disabled.")
            print("Falling back to in-memory Neo4j implementation...")
            return InMemoryNeo4jDriver()
"""
Pydantic models for entity resolution data structures.
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field

class EntityProperty(BaseModel):
    """Additional properties for an entity"""
    name: str
    value: Any

class ResolvedEntity(BaseModel):
    """A single resolved entity with canonical representation"""
    canonical_id: str
    canonical_name: str
    variant_ids: List[str] = Field(default_factory=list)
    entity_type: str
    description: str = ""
    properties: Dict[str, Any] = Field(default_factory=dict)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

class EntityResolutionResponse(BaseModel):
    """Response model for entity resolution process"""
    status: str
    message: str
    updated: Optional[int] = None
    new: Optional[int] = None
    total: Optional[int] = None
    entities: List[ResolvedEntity] = Field(default_factory=list)

class EntityResolutionBatch(BaseModel):
    """A batch of resolved entities used for API responses"""
    resolved_entities: List[ResolvedEntity] = Field(default_factory=list)

class EntityListResponse(BaseModel):
    """Response model for entity listing"""
    entities: List[ResolvedEntity] = Field(default_factory=list)
    count: int
    skip: int = 0
    limit: int = 100
    entity_type: Optional[str] = None
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field

class EntityNode(BaseModel):
    id: str
    label: str = Field(default="")  # Using a single label to directly reflect the dot "label" property
    properties: Dict[str, Any] = Field(default_factory=dict)

class NodeReference(BaseModel):
    id: str

class RelationEdge(BaseModel):
    start: NodeReference
    end: NodeReference
    label: str  # This represents the relationship label (e.g., "prone_to", "integrates", etc.)
    properties: Dict[str, Any] = Field(default_factory=dict)

class KnowledgeGraphData(BaseModel):
    raw_dot: str
    nodes: List[EntityNode]
    relationships: List[RelationEdge]
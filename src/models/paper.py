from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field


class EntityItem(BaseModel):
    """Entity representation with consistent format"""
    value: str
    category: str = "Uncategorized"

class SummaryModel(BaseModel):
    abs_url: str
    pdf_url: str
    title: str
    authors: List[str]           # Assuming authors are stored as comma-separated strings
    updated: str
    published: str
    abstract: str
    summary: str
    tags: List[str]              # Assuming tags are stored as comma-separated strings
    entities: List[dict] = Field(default_factory=list)  # List of entity dictionaries with 'value' and 'category' keys
    
    
class RatingModel(BaseModel):
    soundness: int
    presentation: int
    contribution: int
    final: int


class Category(BaseModel):
    name: str


class NEREntity(BaseModel):
    value: str      # The actual text of the entity (e.g., "Alice", "IBM", "New York")
    category: Category  # The category of the entity (e.g., Person, Organization, Location)


class NERResult(BaseModel):
    entities: List[NEREntity]


class Classification(BaseModel):
    tags: List[Category]


class SavedPaper(BaseModel):
    """
    Model for a saved paper reference
    """
    user_id: str
    abs_url: str
    title: str
    saved_at: datetime = Field(default_factory=datetime.now)

class SOTA(BaseModel):
    benchmark: str
    table_nr: int
    previous_method: str
    previous_metric: str
    previous_value: float
    proposed_method: str
    proposed_metric: str
    proposed_value: float
    absolute_gain: float
    relative_gain_percent: float

class SOTAResult(BaseModel):
    sota: SOTA
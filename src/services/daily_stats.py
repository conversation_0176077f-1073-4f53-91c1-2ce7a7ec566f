from datetime import datetime, timedelta

async def update_daily_stats(daily_stats_collection, summaries_collection, full_texts_collection):
    """Calculate and update daily statistics about papers and full texts for the previous day"""
    try:
        # Get previous day's date in YYYY-MM-DD format
        previous_day = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        
        # Check if we already have stats for the previous day
        existing_stats = daily_stats_collection.find_one({'date': previous_day})
        if existing_stats:
            # Always recalculate if important values are 0 (indicator of a problem)
            if (existing_stats.get('paper_count', 0) == 0 or 
                existing_stats.get('papers_with_full_text', 0) == 0 or 
                existing_stats.get('html_count', 0) == 0 and
                existing_stats.get('pdf_count', 0) == 0 and
                existing_stats.get('abstract_count', 0) == 0):
                print(f"Daily stats for {previous_day} exist but contain zero values - recalculating")
            else:
                print(f"Daily stats for {previous_day} already exist - skipping recalculation")
                return existing_stats
            
        print(f"Calculating daily paper statistics for {previous_day}...")
        
        # Count papers published on the previous day
        papers_previous_day = summaries_collection.count_documents({
            'published': previous_day
        })
        
        # Get papers published on the previous day
        all_papers = list(summaries_collection.find(
            {'published': previous_day},
            {'abs_url': 1}
        ))
        
        # Get papers with full text available by checking the full_texts_collection
        papers_with_full_text = []
        for paper in all_papers:
            paper_id = paper['abs_url'].split('/')[-1]
            full_text_doc = full_texts_collection.find_one({'paper_id': paper_id})
            if full_text_doc and 'text' in full_text_doc and full_text_doc.get('text', ''):
                papers_with_full_text.append(paper)
        
        # Initialize counters
        total_token_count = 0
        html_count = 0
        pdf_count = 0
        abstract_count = 0
        token_lengths = []
        
        # Process each paper to get token counts and source info
        for paper in papers_with_full_text:
            paper_id = paper['abs_url'].split('/')[-1]
            
            # Get full text document
            full_text_doc = full_texts_collection.find_one({'paper_id': paper_id})
            if not full_text_doc or 'text' not in full_text_doc:
                continue
                
            # Get the source and text
            source = full_text_doc.get('source', 'unknown')
            text = full_text_doc.get('text', '')
            
            # Count tokens (approximation: words * 1.3)
            token_count = len(text.split()) * 1.3
            
            # Update counters
            total_token_count += token_count
            token_lengths.append(token_count)
            
            # Update source counters
            if source == 'html':
                html_count += 1
            elif source == 'pdf':
                pdf_count += 1
            elif source == 'abstract':
                abstract_count += 1
        
        # Calculate average token length
        avg_token_length = 0
        if token_lengths:
            avg_token_length = sum(token_lengths) / len(token_lengths)
        
        # Calculate PDF/HTML ratio
        pdf_html_ratio = 0
        if html_count > 0:
            pdf_html_ratio = pdf_count / html_count
        elif pdf_count > 0:
            # If there are PDFs but no HTML, set ratio to a high number
            pdf_html_ratio = pdf_count
        
        # Create stats document
        stats = {
            'date': previous_day,
            'paper_count': papers_previous_day,
            'papers_with_full_text': len(papers_with_full_text),
            'avg_token_length': round(avg_token_length, 2),
            'total_token_count': round(total_token_count, 2),
            'html_count': html_count,
            'pdf_count': pdf_count,
            'abstract_count': abstract_count,
            'pdf_html_ratio': round(pdf_html_ratio, 3),
            'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # Save to database
        daily_stats_collection.update_one(
            {'date': previous_day},
            {'$set': stats},
            upsert=True
        )
        
        print(f"Daily stats updated for {previous_day}: {papers_previous_day} papers, {len(papers_with_full_text)} with full text")
        return stats

    except Exception as e:
        print(f"Error updating daily stats: {e}")
        import traceback
        traceback.print_exc()
        return None
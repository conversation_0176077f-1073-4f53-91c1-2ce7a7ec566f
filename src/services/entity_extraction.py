import json
import os
from typing import List, <PERSON><PERSON>
from openai import OpenAI

from ..models.paper import NERResult
from scripts.utils.prompts import ner_prompt

async def perform_ner(text: str, client: OpenAI) -> List[Tuple[str, str]]:
    """
    Extract named entities from text using NER

    Args:
        text (str): Text to analyze
        client (OpenAI): OpenAI client

    Returns:
        List[Tuple[str, str]]: List of entity tuples (name, category)
    """
    chat_completion = client.beta.chat.completions.parse(
    model= os.getenv("GEMINI_MODEL"),
    messages=[{
            "role": "system",
            "content": """You are a helpful assistant with world-class expertise in AI research and application.
Your role is to extract and recognize entities from research papers better than anyone else in the world."""
        }, {
            "role": "user",
            "content": ner_prompt(text)
        }],
    response_format=NERResult,
)

    ner_result = NERResult(**json.loads(chat_completion.choices[0].message.content))
    return [(entity.value, entity.category.name) for entity in ner_result.entities]
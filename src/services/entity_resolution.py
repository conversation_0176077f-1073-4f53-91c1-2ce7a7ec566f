"""
Service for entity resolution using Google Gemini API with OpenAI compatibility.
Resolves, deduplicates, and normalizes entities from the summaries collection
and creates a synchronized entity resolution table.
"""

import os
import re
import json
import logging
import asyncio
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timed<PERSON>ta
from openai import OpenAI

from ..models.entity import ResolvedEntity, EntityResolutionResponse, EntityResolutionBatch, EntityListResponse
import traceback  # For better debugging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def normalize_text(text):
    """
    Normalize text by converting to lower case, removing punctuation,
    and reducing any sequence of whitespace to a single space.
    """
    # Lowercase the text
    text = text.lower()
    # Replace any sequence of whitespace with a single space
    text = re.sub(r'\s+', ' ', text)
    # Remove punctuation (keep only word characters and spaces)
    text = re.sub(r'[^\w\s]', '', text)
    return text.strip()

class EntityResolutionService:
    """
    Service for entity resolution using Google Gemini with OpenAI-compatible interface.
    Extracts entities from summaries, handles normalization, deduplication, and resolution.
    """
    
    def __init__(self, collections: Dict[str, Any], client: Optional[OpenAI] = None):
        """
        Initialize the EntityResolutionService with DB collections and API client.
        
        Args:
            collections: Dictionary of MongoDB collections
            client: OpenAI client configured for Gemini API
        """
        self.summaries_collection = collections['summaries']
        self.entity_resolution_collection = collections.get('entity_resolution')
        self.entity_mappings_collection = collections.get('entity_mappings')
        
        # Create entity_resolution collection if it doesn't exist in the collections dict
        if self.entity_resolution_collection is None and 'papers' in collections:
            db = collections['papers'].database
            
            # Check if the entity_resolution collection exists and create it if not
            collection_names = db.list_collection_names()
            if 'entity_resolution' not in collection_names:
                logger.info("Creating entity_resolution collection")
                db.create_collection('entity_resolution')
                
            self.entity_resolution_collection = db['entity_resolution']
            collections['entity_resolution'] = self.entity_resolution_collection
        
        # Create entity_mappings collection if it doesn't exist
        if self.entity_mappings_collection is None and 'papers' in collections:
            db = collections['papers'].database
            
            # Check if the entity_mappings collection exists and create it if not
            collection_names = db.list_collection_names()
            if 'entity_mappings' not in collection_names:
                logger.info("Creating entity_mappings collection")
                db.create_collection('entity_mappings')
                
            self.entity_mappings_collection = db['entity_mappings']
            collections['entity_mappings'] = self.entity_mappings_collection
        
        # Create a client if one wasn't provided
        if client is None:
            self.client = OpenAI(
                api_key=os.getenv("GEMINI_API_KEY"),
                base_url=os.getenv("GEMINI_BASE_URL"),
            )
        else:
            self.client = client
            
        # Create indexes on entity_resolution collection if needed
        try:
            if self.entity_resolution_collection is not None:
                self.entity_resolution_collection.create_index([("canonical_id", 1)], unique=True)
                self.entity_resolution_collection.create_index([("variant_ids", 1)])
                self.entity_resolution_collection.create_index([("entity_type", 1)])
        except Exception as e:
            logger.error(f"Error creating indexes on entity_resolution collection: {e}")
        
        # Create indexes on entity_mappings collection if needed
        try:
            if self.entity_mappings_collection is not None:
                # Drop any existing indexes to avoid conflicts
                try:
                    # Try to drop the auto-generated index
                    self.entity_mappings_collection.drop_index("original_entity.value_1_original_entity.category_1")
                except Exception:
                    # Index might not exist, which is fine
                    pass
                
                try:
                    # Try to drop any custom index we might have created previously
                    self.entity_mappings_collection.drop_index("entity_mapping_unique_index")
                except Exception:
                    # Index might not exist, which is fine
                    pass
                
                # Create a new index with a custom name for easier management
                self.entity_mappings_collection.create_index(
                    [("original_entity.value", 1), ("original_entity.category", 1)], 
                    unique=True,
                    name="entity_mapping_unique_index"
                )
                self.entity_mappings_collection.create_index([("updated_at", 1)])
        except Exception as e:
            logger.error(f"Error creating indexes on entity_mappings collection: {e}")
    
    async def resolve_entities(self, force_update: bool = False) -> EntityResolutionResponse:
        """
        Main method to resolve entities from the summaries collection.
        Creates or updates the entity_resolution collection.
        
        Args:
            force_update: Whether to force a full update of all entities
            
        Returns:
            EntityResolutionResponse with status information about the resolution process
        """
        # Get last update time to perform incremental updates
        last_update = None
        if self.entity_resolution_collection is not None:
            last_update = self.entity_resolution_collection.find_one(
                {"document_type": "metadata"},
                {"last_update": 1}
            )
        
        last_update_time = last_update.get("last_update") if last_update is not None else None
        
        # Query to get summaries that need processing
        query = {}
        if not force_update and last_update_time:
            query["updated_at"] = {"$gt": last_update_time}
        
        # Get all summaries to process
        try:
            summaries = list(self.summaries_collection.find(query))
            logger.info(f"Found {len(summaries)} summaries to process")
            logger.info(f"Collection names: {self.summaries_collection.database.list_collection_names()}")
            logger.info(f"First summary example: {summaries[0] if summaries else 'None'}")
        except Exception as e:
            logger.error(f"Error fetching summaries: {e}")
            traceback.print_exc()
            summaries = []
        
        if not summaries:
            return EntityResolutionResponse(
                status="success", 
                message="No new entities to resolve", 
                total=0
            )
        
        # Extract all unique entities from the summaries
        unique_entities = await self._extract_unique_entities(summaries)
        logger.info(f"Extracted {len(unique_entities)} unique entities from summaries")
        
        # Debug log for entity investigation
        if unique_entities:
            logger.info(f"Sample entity: {unique_entities[0]}")
            entity_categories = set(e['category'] for e in unique_entities)
            logger.info(f"Entity categories found: {entity_categories}")
        else:
            logger.info("No entities extracted. Sample summaries format:")
            if summaries and len(summaries) > 0:
                sample = summaries[0]
                logger.info(f"Sample summary: {sample.get('title')}")
                if 'entities' in sample:
                    logger.info(f"  Entities field type: {type(sample['entities'])}")
                    logger.info(f"  Entities content: {sample['entities'][:2] if len(sample['entities']) > 0 else 'empty list'}")
                else:
                    logger.info("  No 'entities' field in summary")
        
        if not unique_entities:
            return EntityResolutionResponse(
                status="success", 
                message="No entities found in summaries", 
                total=0
            )
        
        # Get existing entity mappings to avoid reprocessing
        existing_mappings = self._get_existing_entity_mappings(unique_entities)
        logger.info(f"Found {len(existing_mappings)} existing entity mappings")
        
        # Filter out entities that already have mappings
        entities_to_process = []
        for entity in unique_entities:
            entity_key = (entity['value'], entity['category'])
            if entity_key not in existing_mappings:
                entities_to_process.append(entity)
        
        logger.info(f"Need to process {len(entities_to_process)} new entities")
        
        # Process new entities in batches
        new_mappings = []
        if entities_to_process:
            new_mappings = await self._create_entity_mappings(entities_to_process)
            
            # Store new mappings
            if new_mappings:
                self._store_entity_mappings(new_mappings)
                logger.info(f"Stored {len(new_mappings)} new entity mappings")
        
        # Combine existing and new mappings
        all_mappings = existing_mappings.copy()
        for mapping in new_mappings:
            entity_key = (mapping['original_entity']['value'], mapping['original_entity']['category'])
            all_mappings[entity_key] = mapping['resolved_entity']
        
        # Update the entity_resolution collection with all mappings
        updated_count, new_count, processed_entities = await self._update_entity_resolution_collection(all_mappings)
        
        # Update metadata
        if self.entity_resolution_collection is not None:
            self.entity_resolution_collection.update_one(
                {"document_type": "metadata"},
                {"$set": {"last_update": datetime.now().isoformat()}},
                upsert=True
            )
        
        return EntityResolutionResponse(
            status="success", 
            message="Entity resolution completed",
            updated=updated_count,
            new=new_count,
            total=updated_count + new_count,
            entities=processed_entities
        )
    
    async def _extract_unique_entities(self, summaries: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Extract all unique entities from summaries
        
        Args:
            summaries: List of summary documents
            
        Returns:
            List of unique entities with value and category
        """
        unique_entities = {}
        need_extraction_count = 0
        direct_entities_count = 0
        
        for summary in summaries:
            entities = []
            
            # First try to get entities from the 'entities' field
            if "entities" in summary and isinstance(summary["entities"], list):
                direct_entities_count += 1
                entities = summary["entities"]
            # If no entities field or it's empty, extract from abstract
            elif "abstract" in summary and summary.get("abstract"):
                need_extraction_count += 1
                # Try to extract entities from the abstract
                extracted_entities = await self._extract_entities_from_abstract(summary)
                entities = extracted_entities
                
                # Save the extracted entities back to the summary
                if extracted_entities:
                    try:
                        # Handle the ObjectId in dict format
                        summary_id = summary["_id"]
                        if isinstance(summary_id, dict) and "$oid" in summary_id:
                            from bson import ObjectId
                            summary_id = ObjectId(summary_id["$oid"])
                            
                        self.summaries_collection.update_one(
                            {"_id": summary_id},
                            {"$set": {"entities": extracted_entities}}
                        )
                        logger.info(f"Updated summary with {len(extracted_entities)} extracted entities")
                    except Exception as e:
                        logger.error(f"Error updating summary with extracted entities: {e}")
            else:
                # Skip if no entities and no abstract
                continue
            
            # Process each entity
            for entity in entities:
                entity_value = None
                entity_category = None
                
                # Handle different entity formats and standardize
                if isinstance(entity, dict) and "value" in entity:
                    # Standard dictionary format
                    entity_value = entity["value"]
                    entity_category = entity.get("category", "Uncategorized")
                elif isinstance(entity, (list, tuple)) and len(entity) >= 2:
                    # List/tuple format [value, category]
                    entity_value = entity[0]
                    entity_category = entity[1]
                elif isinstance(entity, str):
                    # Simple string format - treat as value and use default category
                    entity_value = entity
                    entity_category = "Uncategorized"
                else:
                    # Log invalid format and skip
                    logger.warning(f"Skipping entity with invalid format: {type(entity)}")
                    continue
                
                # Debug logging
                logger.debug(f"Processing entity: {entity_value} ({entity_category})")
                
                # Create a unique key for this entity
                entity_key = (entity_value, entity_category)
                
                # Get proper summary ID
                summary_id = summary.get("_id")
                if isinstance(summary_id, dict) and "$oid" in summary_id:
                    from bson import ObjectId
                    summary_id = ObjectId(summary_id["$oid"])
                
                if entity_key not in unique_entities:
                    unique_entities[entity_key] = {
                        "value": entity_value,
                        "category": entity_category,
                        "count": 1,
                        "sources": [summary_id]
                    }
                else:
                    # Update existing entity
                    unique_entities[entity_key]["count"] += 1
                    if summary_id and summary_id not in unique_entities[entity_key]["sources"]:
                        unique_entities[entity_key]["sources"].append(summary_id)
        
        # Log entity extraction statistics
        logger.info(f"Processed {len(summaries)} summaries: {direct_entities_count} with entities field, {need_extraction_count} needed extraction")
        
        return list(unique_entities.values())
    
    async def _extract_entities_from_abstract(self, summary: Dict[str, Any]) -> List[Dict[str, str]]:
        """
        Extract entities from abstract using LLM when entities field is missing
        
        Args:
            summary: The summary document
            
        Returns:
            List of entities in standardized dict format with 'value' and 'category' keys
        """
        # If no abstract, return empty list
        if not summary.get("abstract"):
            return []
            
        abstract = summary.get("abstract", "")
        title = summary.get("title", "")
        
        try:
            # Try using structured parsing with the beta API
            prompt = f"""
            Extract key entities from this scientific paper. Focus on significant concepts, methods, technologies, and research areas.
            
            Title: {title}
            
            Abstract: 
            {abstract}
            
            Extract entities in the following format, categorizing each into one of these types:
            - Models & Architectures (e.g., LLMs, CNNs, Transformers, specific model names)
            - Algorithms & Learning Techniques (e.g., reinforcement learning, transfer learning)
            - Applications & Use Cases (e.g., image classification, NLP tasks)
            - Datasets & Benchmarks (e.g., ImageNet, GLUE, specific datasets)
            - Evaluation Metrics (e.g., accuracy, BLEU, F1 score)
            - Challenges (e.g., limitations, problems addressed)
            - Foundational Concepts & Theories (e.g., mathematical concepts, theoretical frameworks)
            - Tools & Frameworks (e.g., programming frameworks, software)
            - Infrastructure (e.g., hardware, computing systems)
            
            Return in JSON format a list of objects with 'value' and 'category' fields. The value should be a specific term or phrase (not a general description), and the category must be one from the list above.
            For example: [{"value": "transformer model", "category": "Models & Architectures"}, {"value": "BERT", "category": "Models & Architectures"}]
            
            Do not include general or vague concepts. Focus on the most important 5-15 entities.
            """
            
            response = self.client.chat.completions.create(
                model=os.getenv("GEMINI_MODEL"),
                messages=[{
                    "role": "user",
                    "content": prompt
                }],
                temperature=0.1,
                response_format={"type": "json_object"}
            )
            
            content = response.choices[0].message.content.strip()
            
            # Try to parse the response as JSON
            try:
                # Handle various response formats
                if "```json" in content:
                    json_str = content.split("```json")[1].split("```")[0].strip()
                elif "```" in content:
                    json_str = content.split("```")[1].strip()
                else:
                    json_str = content
                
                # Check if the content is a JSON object with an "entities" key
                try:
                    parsed = json.loads(json_str)
                    if isinstance(parsed, dict) and "entities" in parsed:
                        entities = parsed["entities"]
                    else:
                        entities = parsed  # Assume the content is directly the entities list
                except Exception:
                    # If parsing fails, try wrapping with brackets to make it a valid JSON array
                    if not (json_str.startswith('[') and json_str.endswith(']')):
                        json_str = '[' + json_str + ']'
                    entities = json.loads(json_str)
                
                # Validate the structure and standardize to dict format
                standardized_entities = []
                if isinstance(entities, list):
                    for entity in entities:
                        if isinstance(entity, list) and len(entity) >= 2:
                            # Convert list format [value, category] to dict format
                            standardized_entities.append({
                                "value": entity[0],
                                "category": entity[1]
                            })
                        elif isinstance(entity, dict):
                            if "entity" in entity and "category" in entity:
                                # Convert {"entity": "...", "category": "..."} to {"value": "...", "category": "..."}
                                standardized_entities.append({
                                    "value": entity["entity"],
                                    "category": entity["category"]
                                })
                            elif "value" in entity and "category" in entity:
                                # Already in correct format
                                standardized_entities.append(entity)
                
                if standardized_entities:
                    return standardized_entities
                
                # log the raw content for debugging
                logger.warning(f"Unexpected entity format: {json_str[:200]}...")
                
                # Create a default entity dict from the title if parsing fails
                if title:
                    return [{"value": title, "category": "Models & Architectures"}]
                return []
                
            except Exception as e:
                logger.error(f"Error parsing entities from abstract: {e}")
                if title:
                    return [{"value": title, "category": "Models & Architectures"}]
                return []
                
        except Exception as e:
            logger.error(f"Error extracting entities from abstract: {e}")
            if title:
                return [{"value": title, "category": "Models & Architectures"}]
            return []
    
    def _get_existing_entity_mappings(self, entities: List[Dict[str, Any]]) -> Dict[Tuple[str, str], Dict[str, Any]]:
        """
        Get existing entity mappings for the given entities
        
        Args:
            entities: List of entities to check for existing mappings
            
        Returns:
            Dictionary mapping (value, category) tuples to resolved entities
        """
        existing_mappings = {}
        
        # Return empty mappings if collection doesn't exist or no entities to process
        if self.entity_mappings_collection is None:
            return existing_mappings
            
        # Build a query to get all entity mappings in one go
        entity_keys = [(entity["value"], entity["category"]) for entity in entities]
        
        if not entity_keys:
            return existing_mappings
        
        # Find all mappings that match any of the entity keys
        query = {
            "$or": [
                {"original_entity.value": value, "original_entity.category": category}
                for value, category in entity_keys
            ]
        }
        
        mappings = list(self.entity_mappings_collection.find(query))
        
        # Build a dictionary for quick lookup
        for mapping in mappings:
            if "original_entity" in mapping and "resolved_entity" in mapping:
                key = (mapping["original_entity"]["value"], mapping["original_entity"]["category"])
                existing_mappings[key] = mapping["resolved_entity"]
        
        return existing_mappings
    
    async def _create_entity_mappings(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Create mappings for entities using the LLM API.
        First checks the entity dictionary for matches to avoid redundant API calls.
        
        Args:
            entities: List of entities to create mappings for
            
        Returns:
            List of entity mappings
        """
        if not entities:
            return []
        
        # Try to load the entity dictionary manager
        try:
            from src.utils.entity_dictionary import get_entity_dictionary_manager
            entity_dict_manager = get_entity_dictionary_manager()
            use_dictionary = True
            logger.info(f"Using entity dictionary with {len(entity_dict_manager.entity_dict)} entries")
        except (ImportError, Exception) as e:
            logger.warning(f"Could not load entity dictionary manager, proceeding without dictionary: {e}")
            use_dictionary = False
        
        # Process entities in batches to avoid API limitations
        batch_size = 50
        all_mappings = []
        
        # First check if any entities are already in the dictionary
        dictionary_mappings = []
        entities_to_process = []
        
        if use_dictionary:
            for entity in entities:
                entity_value = entity.get("value", "")
                if entity_value:
                    # Look up in the dictionary
                    dict_entry = entity_dict_manager.lookup_entity(entity_value)
                    if dict_entry:
                        # Found in dictionary, create mapping
                        mapping = {
                            "original_entity": {
                                "value": entity["value"],
                                "category": entity["category"]
                            },
                            "resolved_entity": {
                                "canonical_id": dict_entry.get("canonical_id"),
                                "canonical_name": dict_entry.get("canonical_name"),
                                "entity_type": dict_entry.get("entity_type", entity.get("category", "Unknown")),
                                "description": dict_entry.get("description", ""),
                                "variant_ids": [entity_value],
                                "properties": dict_entry.get("properties", {})
                            },
                            "last_updated": datetime.now().isoformat()
                        }
                        dictionary_mappings.append(mapping)
                    else:
                        entities_to_process.append(entity)
                else:
                    entities_to_process.append(entity)
            
            logger.info(f"Found {len(dictionary_mappings)} entities in dictionary, {len(entities_to_process)} need LLM processing")
            all_mappings.extend(dictionary_mappings)
        else:
            entities_to_process = entities
        
        # Only proceed with LLM if we have entities that weren't in the dictionary
        if not entities_to_process:
            return all_mappings
        
        # Group remaining entities by category for more effective resolution
        entities_by_category = {}
        for entity in entities_to_process:
            category = entity.get("category", "unknown")
            if category not in entities_by_category:
                entities_by_category[category] = []
            entities_by_category[category].append(entity)
        
        # Process each category separately
        for category, category_entities in entities_by_category.items():
            logger.info(f"Processing {len(category_entities)} entities of category {category}")
            
            for i in range(0, len(category_entities), batch_size):
                batch = category_entities[i:i+batch_size]
                
                # Extract just the values for the LLM
                entity_values = [e.get("value", "") for e in batch]
                
                # Resolve this batch using the LLM
                resolved_entities = await self._resolve_entity_batch(entity_values, category)
                
                # Create mappings for each entity
                for original_entity, resolved_entity in zip(batch, resolved_entities):
                    if resolved_entity:  # Only add if we got a valid resolution
                        mapping = {
                            "original_entity": {
                                "value": original_entity["value"],
                                "category": original_entity["category"]
                            },
                            "resolved_entity": resolved_entity,
                            "last_updated": datetime.now().isoformat()
                        }
                        all_mappings.append(mapping)
        
        return all_mappings
    
    def _store_entity_mappings(self, mappings: List[Dict[str, Any]]) -> None:
        """
        Store entity mappings in the entity_mappings collection
        
        Args:
            mappings: List of entity mappings to store
        """
        if not mappings or self.entity_mappings_collection is None:
            return
        
        # Keep track of processed mappings to avoid duplicate attempt insertions
        processed_keys = set()
        success_count = 0
        
        for mapping in mappings:
            try:
                # Ensure category is a string rather than None to avoid duplicate key issues
                if mapping["original_entity"]["category"] is None:
                    mapping["original_entity"]["category"] = "unknown"
                
                # Create a unique key for this mapping to avoid processing duplicates
                mapping_key = (
                    mapping["original_entity"]["value"],
                    mapping["original_entity"]["category"]
                )
                
                # Skip if we've already processed this mapping in this batch
                if mapping_key in processed_keys:
                    continue
                    
                processed_keys.add(mapping_key)
                
                # Check if the mapping already exists to avoid duplicate key errors
                existing = self.entity_mappings_collection.find_one({
                    "original_entity.value": mapping["original_entity"]["value"],
                    "original_entity.category": mapping["original_entity"]["category"]
                })
                
                if existing:
                    # Update existing mapping
                    self.entity_mappings_collection.update_one(
                        {
                            "original_entity.value": mapping["original_entity"]["value"],
                            "original_entity.category": mapping["original_entity"]["category"]
                        },
                        {"$set": mapping}
                    )
                else:
                    # Insert new mapping
                    self.entity_mappings_collection.insert_one(mapping)
                
                success_count += 1
                
                # Log progress periodically
                if success_count % 1000 == 0:
                    logger.info(f"Stored {success_count} entity mappings so far")
            except Exception as e:
                logger.error(f"Error storing entity mapping for {mapping['original_entity'].get('value')}: {e}")
                import traceback
                logger.error(traceback.format_exc())
        
        logger.info(f"Successfully stored {success_count} out of {len(mappings)} entity mappings")
    
    async def _update_entity_resolution_collection(self, mappings: Dict[Tuple[str, str], Dict[str, Any]]) -> Tuple[int, int, List[ResolvedEntity]]:
        """
        Update the entity_resolution collection with resolved entities
        
        Args:
            mappings: Dictionary mapping (value, category) tuples to resolved entities
            
        Returns:
            Tuple of (updated_count, new_count, list of processed entities)
        """
        # Track processing stats
        updated_count = 0
        new_count = 0
        processed_entities = []
        
        # Return early if collection doesn't exist
        if self.entity_resolution_collection is None:
            return updated_count, new_count, processed_entities
            
        # Get existing entities from entity_resolution collection
        existing_entities = list(self.entity_resolution_collection.find(
            {"document_type": {"$ne": "metadata"}}
        ))
        entity_map = {entity.get("canonical_id", ""): entity for entity in existing_entities if "canonical_id" in entity}
        
        # Process each resolved entity
        processed_canonical_ids = set()  # Track canonical IDs we've processed to avoid duplicates
        
        for resolved_entity in mappings.values():
            if not resolved_entity or "canonical_id" not in resolved_entity:
                continue
                
            canonical_id = resolved_entity["canonical_id"]
            
            # Skip if we've already processed this canonical ID in this batch
            if canonical_id in processed_canonical_ids:
                continue
                
            processed_canonical_ids.add(canonical_id)
            
            if canonical_id in entity_map:
                # Update existing entity
                existing = entity_map[canonical_id]
                
                # Merge variant IDs (avoiding duplicates)
                all_variants = set(existing.get("variant_ids", []))
                all_variants.update(resolved_entity.get("variant_ids", []))
                
                # Update entity with new information
                current_time = datetime.now().isoformat()
                update_data = {
                    "$set": {
                        "description": resolved_entity.get("description", existing.get("description", "")),
                        "entity_type": resolved_entity.get("entity_type", existing.get("entity_type", "")),
                        "variant_ids": list(all_variants),
                        "updated_at": current_time
                    }
                }
                
                # Add optional fields if present
                if "properties" in resolved_entity:
                    update_data["$set"]["properties"] = resolved_entity["properties"]
                
                try:
                    self.entity_resolution_collection.update_one(
                        {"canonical_id": canonical_id},
                        update_data
                    )
                    updated_count += 1
                    
                    # Add to processed entities list
                    entity_data = existing.copy()
                    entity_data.update(update_data["$set"])
                    processed_entities.append(ResolvedEntity(**entity_data))
                except Exception as e:
                    logger.error(f"Error updating entity {canonical_id}: {e}")
            else:
                # Add new entity
                try:
                    current_time = datetime.now().isoformat()
                    entity_data = resolved_entity.copy()
                    entity_data["created_at"] = current_time
                    entity_data["updated_at"] = current_time
                    entity_data["document_type"] = "entity"
                    
                    # Use upsert to avoid duplicate key errors
                    self.entity_resolution_collection.update_one(
                        {"canonical_id": canonical_id},
                        {"$set": entity_data},
                        upsert=True
                    )
                    new_count += 1
                    
                    # Add to processed entities list
                    processed_entities.append(ResolvedEntity(**entity_data))
                except Exception as e:
                    logger.error(f"Error inserting entity {canonical_id}: {e}")
        
        return updated_count, new_count, processed_entities
    
    async def _resolve_entity_batch(self, entity_values: List[str], entity_category: str) -> List[Dict[str, Any]]:
        """
        Resolve a batch of entities using the LLM API
        
        Args:
            entity_values: List of entity values to resolve
            entity_category: Category of entities for context
            
        Returns:
            List of resolved entity dictionaries
        """
        # Create structured request for entity resolution
        try:
            # First try using structured parsing with the beta API
            response = self.client.beta.chat.completions.parse(
                model=os.getenv("GEMINI_MODEL"),
                messages=[{
                    "role": "system",
                    "content": "You are an expert at entity resolution and normalization for scientific literature."
                }, {
                    "role": "user",
                    "content": f"""
                    Resolve and normalize these entities of category '{entity_category}' from scientific papers:
                    {json.dumps(entity_values)}
                    
                    For each entity:
                    1. Find similar/duplicate entities and group them
                    2. Create a canonical form for each group
                    3. Provide a brief description of what this entity is
                    
                    Use this format:
                    {{
                        "resolved_entities": [
                            {{
                                "canonical_id": "unique_normalized_name",
                                "canonical_name": "Preferred Display Name",
                                "variant_ids": ["variant1", "variant2"],
                                "entity_type": "{entity_category}",
                                "description": "Brief description",
                                "properties": {{
                                    "additional_info": "value"
                                }}
                            }}
                        ]
                    }}
                    """
                }],
                response_format={"type": "json_object"}
            )
            
            # Parse the response into EntityResolutionBatch model
            result = json.loads(response.choices[0].message.content)
            # Convert to model but return as dict for easier MongoDB integration
            parsed_result = EntityResolutionBatch(**result)
            return [entity.dict() for entity in parsed_result.resolved_entities]
            
        except Exception as e:
            logger.error(f"Error using structured API, falling back to regular chat completion: {e}")
            
            # Fallback to regular chat completion
            response = self.client.chat.completions.create(
                model=os.getenv("GEMINI_MODEL"),
                messages=[{
                    "role": "system",
                    "content": "You are an expert at entity resolution and normalization for scientific literature."
                }, {
                    "role": "user",
                    "content": f"""
                    Resolve and normalize these entities of category '{entity_category}' from scientific papers:
                    {json.dumps(entity_values)}
                    
                    For each entity:
                    1. Find similar/duplicate entities and group them
                    2. Create a canonical form for each group
                    3. Provide a brief description of what this entity is
                    
                    Use this JSON format:
                    {{
                        "resolved_entities": [
                            {{
                                "canonical_id": "unique_normalized_name",
                                "canonical_name": "Preferred Display Name",
                                "variant_ids": ["variant1", "variant2"],
                                "entity_type": "{entity_category}",
                                "description": "Brief description",
                                "properties": {{
                                    "additional_info": "value"
                                }}
                            }}
                        ]
                    }}
                    """
                }],
                response_format={"type": "json_object"}
            )
            
            # Parse the response
            try:
                result = json.loads(response.choices[0].message.content)
                # Convert to model but return as dict for easier MongoDB integration
                parsed_result = EntityResolutionBatch(**result)
                return [entity.dict() for entity in parsed_result.resolved_entities]
            except Exception as parse_error:
                logger.error(f"Error parsing response: {parse_error}")
                return []
    
    async def get_resolved_entities(self, entity_type: Optional[str] = None, 
                                   limit: int = 100, skip: int = 0) -> Union[EntityListResponse, List[ResolvedEntity]]:
        """
        Get resolved entities from the collection, optionally filtered by type
        
        Args:
            entity_type: Optional entity type to filter by
            limit: Maximum number of entities to return
            skip: Number of entities to skip
            
        Returns:
            EntityListResponse containing resolved entities
        """
        entities_data = []
        
        if self.entity_resolution_collection is not None:
            query = {"document_type": "entity"}
            if entity_type:
                query["entity_type"] = entity_type
            
            # Get matching entities from the collection
            entities_data = list(self.entity_resolution_collection.find(
                query,
                sort=[("canonical_name", 1)],
                limit=limit,
                skip=skip
            ))
        
        # Convert to Pydantic models
        entities = [ResolvedEntity(**entity) for entity in entities_data]
        
        # Return as EntityListResponse
        return EntityListResponse(
            entities=entities,
            count=len(entities),
            skip=skip,
            limit=limit,
            entity_type=entity_type
        )
    
    async def get_entity_by_id(self, entity_id: str) -> Optional[ResolvedEntity]:
        """
        Get a single entity by its canonical ID or variant ID
        
        Args:
            entity_id: The canonical ID or variant ID
            
        Returns:
            ResolvedEntity object or None if not found
        """
        entity = None
        
        if self.entity_resolution_collection is not None:
            # Try finding by canonical ID first
            entity = self.entity_resolution_collection.find_one({"canonical_id": entity_id})
            
            # If not found, try finding by variant ID
            if entity is None:
                entity = self.entity_resolution_collection.find_one({
                    "variant_ids": entity_id
                })
        
        # Convert to Pydantic model if found
        if entity is not None:
            return ResolvedEntity(**entity)
        
        return None
    
    async def update_entity_manually(self, canonical_id: str, updates: Dict[str, Any]) -> EntityResolutionResponse:
        """
        Manually update an entity's information
        
        Args:
            canonical_id: The canonical ID of the entity
            updates: Dictionary of field updates
            
        Returns:
            EntityResolutionResponse with update status
        """
        # Check if collection exists
        if self.entity_resolution_collection is None:
            return EntityResolutionResponse(
                status="error",
                message="Entity resolution collection not available",
                updated=0,
                new=0,
                total=0
            )
            
        # Don't allow changing document_type
        if "document_type" in updates:
            del updates["document_type"]
        
        # Add updated_at timestamp
        updates["updated_at"] = datetime.now().isoformat()
        
        try:
            result = self.entity_resolution_collection.update_one(
                {"canonical_id": canonical_id, "document_type": "entity"},
                {"$set": updates}
            )
            
            updated_entity = None
            if result.matched_count > 0:
                # Get the updated entity
                entity_doc = self.entity_resolution_collection.find_one({"canonical_id": canonical_id})
                if entity_doc:
                    updated_entity = ResolvedEntity(**entity_doc)
                
                return EntityResolutionResponse(
                    status="success",
                    message=f"Updated entity {canonical_id}",
                    updated=result.modified_count,
                    new=0,
                    total=result.modified_count,
                    entities=[updated_entity] if updated_entity else []
                )
            else:
                return EntityResolutionResponse(
                    status="error",
                    message="Entity not found",
                    updated=0,
                    new=0,
                    total=0
                )
        except Exception as e:
            logger.error(f"Error updating entity {canonical_id}: {e}")
            return EntityResolutionResponse(
                status="error",
                message=f"Failed to update entity: {str(e)}",
                updated=0,
                new=0,
                total=0
            )
            
# Periodic entity resolution task
async def run_periodic_entity_resolution():
    """
    Runs entity resolution periodically to keep entity mappings up to date.
    This function is designed to be run as a background task.
    """
    try:
        # Import necessary modules here to avoid circular imports
        from ..db import get_collections
        
        # Initial delay to allow other startup tasks to complete
        await asyncio.sleep(60)
        
        logger.info("Starting periodic entity resolution task")
        
        while True:
            try:
                # Get the collections
                collections = get_collections()
                
                # Create entity resolution service
                entity_service = EntityResolutionService(collections)
                
                # Run entity resolution
                logger.info("Running scheduled entity resolution...")
                result = await entity_service.resolve_entities(force_update=False)
                
                logger.info(f"Entity resolution completed: {result.message}")
                logger.info(f"Updated: {result.updated}, New: {result.new}, Total: {result.total}")
                
                # Wait for 12 hours before next run
                await asyncio.sleep(12 * 60 * 60)  # 12 hours in seconds
                
            except Exception as e:
                logger.error(f"Error in periodic entity resolution: {e}")
                import traceback
                traceback.print_exc()
                
                # Wait for 1 hour before retry after error
                await asyncio.sleep(60 * 60)  # 1 hour in seconds
    
    except Exception as e:
        logger.error(f"Fatal error in run_periodic_entity_resolution: {e}")
        import traceback
        traceback.print_exc()
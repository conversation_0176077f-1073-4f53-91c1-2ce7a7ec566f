import json
import re
from typing import Dict, Any, Optional

def extract_json_from_markdown(markdown_text: str) -> Optional[str]:
    """
    Extract JSON content from markdown text that might be enclosed in triple backticks
    
    Args:
        markdown_text (str): Markdown text that might contain JSON
        
    Returns:
        Optional[str]: Extracted JSON string or None if not found
    """
    # Use regex to find content between triple backticks
    match = re.search(r'```json\s*([\s\S]*?)\s*```', markdown_text)
    if match:
        return match.group(1)
    return None

def process_input(input_text: str) -> Optional[Any]:
    """
    Process input text that may contain JSON in markdown format
    
    Args:
        input_text (str): Text that may contain JSON in markdown format
        
    Returns:
        Optional[Any]: Parsed JSON object or None if parsing failed
    """
    # Extract JSON from markdown-style input
    json_str = extract_json_from_markdown(input_text)
    
    if json_str is None:
        print("No JSON found in the input.")
        return None

    try:
        # Parse the JSON string into a Python data structure
        data = json.loads(json_str)
        return data
    except json.JSONDecodeError as e:
        print(f"Error decoding JSON: {e}")
        return None
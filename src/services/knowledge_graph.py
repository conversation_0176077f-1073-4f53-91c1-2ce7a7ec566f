import json
import os
from typing import Dict, Any
from openai import OpenAI

from ..models.kg import KnowledgeGraphData, EntityNode, NodeReference, RelationEdge
from scripts.utils.prompts import kg_prompt
from .json_utils import extract_json_from_markdown

# Function to extract the knowledge graph using the API and adapt to client capabilities
async def extract_knowledge_graph(text: str, client: OpenAI) -> KnowledgeGraphData:
    """
    Extract a knowledge graph from the provided text

    Args:
        text (str): Text to analyze
        client (OpenAI): OpenAI client

    Returns:
        KnowledgeGraphData: Extracted knowledge graph data
    """
    # Use normal chat completions if the parse method isn't supported
    try:
        chat_completion = client.beta.chat.completions.parse(
            messages=[{
                "role": "user",
                "content": kg_prompt(text)
            }],
            model=os.getenv("GEMINI_MODEL"),
            # Here we use the structured outputs API by providing the schema
            response_format=KnowledgeGraphData
        )

        response_content = chat_completion.choices[0].message.content
        print(f"KG response: {response_content}")
        kg_data = json.loads(response_content)
        result = KnowledgeGraphData(
            raw_dot=kg_data.get('raw_dot', ''),
            nodes=[EntityNode(**node) for node in kg_data.get('nodes', [])],
            relationships=[RelationEdge(**rel) for rel in kg_data.get('relationships', [])]
        )
        return result

    except Exception as api_error:
        print(f"Error using parse API, falling back to regular chat: {api_error}")
        # Fall back to regular chat completions
        chat_completion = client.chat.completions.create(
            messages=[{
                "role": "system",
                "content": "You are a helpful assistant that creates knowledge graphs from text."
            }, {
                "role": "user",
                "content": kg_prompt(text)
            }],
            model=os.getenv("GEMINI_MODEL")
        )

        try:
            # Try to parse directly from the response
            content = chat_completion.choices[0].message.content

            # Extract JSON from markdown if needed
            json_str = extract_json_from_markdown(content)
            if json_str:
                data = json.loads(json_str)
            elif isinstance(content, dict):
                # If content is already a dict, use it directly
                data = content
            else:
                # Otherwise try to parse the entire content as JSON
                data = json.loads(content)

            # Create the KnowledgeGraphData instance
            result = KnowledgeGraphData(
                raw_dot=data.get('raw_dot', ''),
                nodes=[EntityNode(**node) for node in data.get('nodes', [])],
                relationships=[RelationEdge(**rel) for rel in data.get('relationships', [])]
            )
            return result

        except Exception as e:
            # Log the error
            print(f"Error processing knowledge graph response: {e}")
            print(f"Response content type: {type(chat_completion.choices[0].message.content)}")
            # Return an empty knowledge graph
            return KnowledgeGraphData(raw_dot="", nodes=[], relationships=[])
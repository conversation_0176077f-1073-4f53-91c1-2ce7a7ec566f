# This file now just re-exports functionality from the separate modules
# It's kept for backward compatibility

from .summarization import summarize_text
from .entity_extraction import perform_ner
from .tagging import tagging_papers, TAGS
from .sota_extraction import extract_sota
from .knowledge_graph import extract_knowledge_graph
from .json_utils import extract_json_from_markdown, process_input

__all__ = [
    'summarize_text',
    'perform_ner',
    'tagging_papers',
    'extract_sota',
    'extract_knowledge_graph',
    'extract_json_from_markdown',
    'process_input',
    'TAGS'
]
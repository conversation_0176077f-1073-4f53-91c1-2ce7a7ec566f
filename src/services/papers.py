import asyncio
import aiohttp
from bs4 import BeautifulSoup
from datetime import datetime
from pymongo.collection import Collection
from typing import Dict, List, Any, Optional, Union
from openai import OpenAI

from .daily_stats import update_daily_stats
from .reviews import generate_review
from ..utils import process_input
from .nlp import summarize_text, perform_ner, tagging_papers, extract_knowledge_graph
from .sota_extraction import extract_sota
from .table_extraction import extract_and_save_tables, update_tables_for_papers
from scripts.utils.prompts import mermaid_prompt

async def generate_mermaid_diagram(summary: str, client: OpenAI) -> str:
    """
    Generate a mermaid diagram from a paper summary

    Args:
        summary (str): Paper summary text
        client (OpenAI): OpenAI client for API calls

    Returns:
        str: Generated mermaid diagram
    """
    try:
        import os

        # Get model from environment variables - use the same Gemini model as other operations
        model = os.environ.get("GEMINI_MODEL", "gemini-pro")

        prompt = mermaid_prompt(summary)
        completion = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": "You are a helpful assistant that transforms text into mermaid diagrams."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.2  # Lower temperature for more consistent output
        )

        # Extract just the mermaid code from the response
        response_content = completion.choices[0].message.content

        # Try to extract the mermaid diagram code
        diagram = response_content

        # If the response contains markdown code blocks, extract just the diagram
        if "```mermaid" in response_content:
            start_idx = response_content.find("```mermaid")
            end_idx = response_content.find("```", start_idx + 10)
            if end_idx > start_idx:
                diagram = response_content[start_idx + 10:end_idx].strip()

        # If the response has the mermaid prefix without code block tags
        elif response_content.strip().startswith("graph "):
            diagram = response_content.strip()

        return diagram
    except Exception as e:
        print(f"Error generating mermaid diagram: {e}")
        return ""

async def update_mermaid_diagrams(summaries_collection, mermaid_diagrams_collection, client: OpenAI) -> None:
    """
    Update mermaid diagrams for paper summaries

    Args:
        summaries_collection: MongoDB collection for summaries
        mermaid_diagrams_collection: MongoDB collection for mermaid diagrams
        client (OpenAI): OpenAI client for API calls
    """
    try:
        print("Checking for papers needing mermaid diagrams...")

        # Get URLs of papers that already have mermaid diagrams
        mermaid_urls = set(doc.get('abs_url') for doc in mermaid_diagrams_collection.find({}, {'abs_url': 1}))

        # Find papers without mermaid diagrams
        papers_needing_diagrams = list(summaries_collection.find(
            {'abs_url': {'$nin': list(mermaid_urls)}},
            sort=[('published', -1)],
            limit=20  # Limit to 20 papers per cycle
        ))

        if papers_needing_diagrams:
            print(f"Found {len(papers_needing_diagrams)} papers needing mermaid diagrams")
            for paper in papers_needing_diagrams:
                abs_url = paper.get('abs_url')
                summary = paper.get('summary', '')
                if summary:
                    print(f"Generating mermaid diagram for {abs_url}")
                    mermaid_diagram = await generate_mermaid_diagram(summary, client)

                    if mermaid_diagram:
                        # Store in mermaid_diagrams collection
                        mermaid_diagrams_collection.update_one(
                            {'abs_url': abs_url},
                            {
                                '$set': {
                                    'abs_url': abs_url,
                                    'title': paper.get('title', ''),
                                    'mermaid_diagram': mermaid_diagram,
                                    'created_at': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                                }
                            },
                            upsert=True
                        )

                        # Update the has_mermaid flag in the summaries collection
                        summaries_collection.update_one(
                            {'abs_url': abs_url},
                            {'$set': {'has_mermaid': True}}
                        )

                        print(f"Mermaid diagram saved for {abs_url} and has_mermaid flag updated")

                # Add a delay to avoid rate limiting
                await asyncio.sleep(2)
        else:
            print("No papers needing mermaid diagrams found")

        # Update existing papers to set has_mermaid property
        # First get all papers with mermaid diagrams but without has_mermaid flag
        papers_to_update = list(summaries_collection.find(
            {
                'abs_url': {'$in': list(mermaid_urls)},
                '$or': [
                    {'has_mermaid': {'$exists': False}},
                    {'has_mermaid': False}
                ]
            },
            {'abs_url': 1}
        ))

        if papers_to_update:
            print(f"Found {len(papers_to_update)} papers that need has_mermaid flag updated")
            for paper in papers_to_update:
                abs_url = paper.get('abs_url')
                if abs_url:
                    summaries_collection.update_one(
                        {'abs_url': abs_url},
                        {'$set': {'has_mermaid': True}}
                    )
            print(f"Updated has_mermaid flag for {len(papers_to_update)} papers")

    except Exception as e:
        print(f"Error updating mermaid diagrams: {e}")
        import traceback
        traceback.print_exc()

async def update_summaries(url, summaries_collection, full_texts_collection, reviews_collection, sota_collection, knowledge_graphs_collection, daily_stats_collection, mermaid_diagrams_collection, tables_collection, client):
    while True:
        try:
            # Fetch new papers first
            papers = await fetch_papers(url)
            for paper in papers:
                await process_paper(paper, summaries_collection, full_texts_collection, sota_collection, knowledge_graphs_collection, client)

            # Then check for existing papers without full text
            print("Checking for existing papers that need full text updates...")

            # Find papers in summaries that don't have an associated full text entry
            # or papers with has_full_text flag set to False
            papers_needing_update = []

            # Get all papers from summaries with has_full_text = False or missing
            candidates = list(summaries_collection.find(
                {'$or': [
                    {'has_full_text': False},
                    {'has_full_text': {'$exists': False}}
                ]},
                sort=[('published', -1)],
                limit=20  # Get more than we'll process to filter
            ))

            # Check which ones don't have entries in full_texts_collection
            for paper in candidates:
                paper_id = paper['abs_url'].split('/')[-1]
                if full_texts_collection.find_one({'paper_id': paper_id}) is None:
                    papers_needing_update.append(paper)
                    # Limit to 10 papers per update cycle
                    if len(papers_needing_update) >= 10:
                        break

            if papers_needing_update:
                print(f"Found {len(papers_needing_update)} papers needing full text updates")
                for paper in papers_needing_update:
                    print(f"Updating full text for {paper['abs_url']}")
                    await process_paper(paper, summaries_collection, full_texts_collection, sota_collection, knowledge_graphs_collection, client)
                    # Add a delay to avoid rate limiting
                    await asyncio.sleep(2)
            else:
                print("No papers needing full text updates found")

            # Check for papers without auto-reviews
            print("Checking for papers needing auto-reviews...")

            # Find papers with no auto-reviews
            new_papers = list(summaries_collection.find(
                {},
                sort=[('published', -1)],
                limit=20
            ))

            papers_to_review = []
            for paper in new_papers:
                # Check if this paper already has an auto-review
                auto_review = reviews_collection.find_one({
                    'abs_url': paper['abs_url'],
                    'user_id': 'system_auto_review'
                })

                if not auto_review:
                    papers_to_review.append(paper)
                    # Limit to 5 papers per update cycle
                    if len(papers_to_review) >= 5:
                        break

            if papers_to_review:
                print(f"Found {len(papers_to_review)} papers needing auto-reviews")
                for paper in papers_to_review:
                    print(f"Generating auto-review for {paper['abs_url']}")
                    await generate_review(paper['abs_url'], summaries_collection, reviews_collection, client)
                    # Add a delay to avoid rate limiting
                    await asyncio.sleep(2)
            else:
                print("No papers needing auto-reviews found")

            # Update mermaid diagrams for papers without them
            await update_mermaid_diagrams(summaries_collection, mermaid_diagrams_collection, client)

            # Extract tables from papers that don't have them yet
            await update_tables_for_papers(summaries_collection, tables_collection)

            # Update daily statistics
            await update_daily_stats(daily_stats_collection, summaries_collection, full_texts_collection)

        except Exception as e:
            print("Failed to fetch or update papers", e)
            import traceback
            traceback.print_exc()

        # Sleep before next cycle
        print(f"Sleeping until next update cycle ({datetime.now().strftime('%Y-%m-%d %H:%M:%S')})")
        await asyncio.sleep(3600)  # Update every hour

async def extract_information(response):
    """
    Extract paper information from arXiv API response

    Args:
        response: ArXiv API XML response

    Returns:
        List[Dict]: List of paper dictionaries with metadata
    """
    soup = BeautifulSoup(response, 'xml')
    entries = soup.find_all('entry')
    papers = []
    for entry in entries:
        try:
            paper = {}
            paper['abs_url'] = entry.id.text
            paper['title'] = entry.title.text
            paper['abstract'] = entry.summary.text

            # Convert to datetime format, then to dates
            updated_datetime = datetime.strptime(entry.updated.text, "%Y-%m-%dT%H:%M:%SZ")
            paper["updated"] = updated_datetime.strftime("%Y-%m-%d")
            published_datetime = datetime.strptime(entry.published.text, "%Y-%m-%dT%H:%M:%SZ")
            paper["published"] = published_datetime.strftime("%Y-%m-%d")

            # Extract authors
            authors = entry.find_all('author')
            paper['authors'] = [author.find('name').text for author in authors]

            # Extract primary category
            primary_category = entry.find('arxiv:primary_category')
            if primary_category:
                paper['primary_category'] = primary_category['term']

            # Extract additional categories
            categories = entry.find_all('category')
            paper['categories'] = [category['term'] for category in categories]

            # Extract PDF link
            pdf_link = entry.find('link', title='pdf')
            if pdf_link:
                paper['pdf_url'] = pdf_link['href']

            # Append the paper info to the list
            papers.append(paper)
        except Exception as e:
            print("Error processing paper:", e)
    return papers

async def fetch_papers(url):
    """Fetch latest papers using aiohttp"""
    # Set a reasonable timeout (60 seconds) to prevent hanging
    timeout = aiohttp.ClientTimeout(total=60)
    try:
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.get(url) as response:
                data = await response.read()
                papers = await extract_information(data)
                return papers
    except asyncio.TimeoutError:
        print(f"Timeout error fetching papers from {url}, returning empty list")
        return []
    except aiohttp.ClientError as e:
        print(f"HTTP error fetching papers from {url}: {e}")
        return []
    except Exception as e:
        print(f"Unexpected error fetching papers from {url}: {e}")
        return []

async def process_paper(
    paper: Dict[str, Any],
    summaries_collection: Collection,
    full_texts_collection: Collection,
    sota_collection: Collection,
    knowledge_graphs_collection: Collection,
    client: OpenAI
):
    """
    Fetch and process a single paper

    Args:
        paper: Paper dictionary with metadata
        summaries_collection: MongoDB collection for summaries
        full_texts_collection: MongoDB collection for full texts
        sota_collection: MongoDB collection for SOTA results
        knowledge_graphs_collection: MongoDB collection for knowledge graphs
        client: OpenAI client

    Returns:
        Dict: Processed paper with summary, tags, entities, etc.
    """
    # Extract arXiv ID from the URL
    paper_id = paper['abs_url'].split('/')[-1]

    # Check if URL has already been summarized
    existing_summary = summaries_collection.find_one({'abs_url': paper['abs_url']})

    # Check if we already have the full text in the separate collection
    existing_full_text = full_texts_collection.find_one({'paper_id': paper_id})

    # Check if we already have a knowledge graph
    existing_kg = knowledge_graphs_collection.find_one({'abs_url': paper['abs_url']})

    # Determine if we need to update
    update_needed = not existing_full_text or existing_full_text.get('text', '') == ''

    # If we have both summary and full text, just return them
    if existing_summary and not update_needed:
        # Combine for API compatibility
        result = existing_summary.copy()
        if existing_full_text:
            result['full_text'] = existing_full_text.get('text', '')
            result['text_source'] = existing_full_text.get('source', 'unknown')
        return result

    try:
        # Import needed modules for full text extraction
        from scripts.data_processing.arxiv_html_checker import get_full_paper_text as get_html_text
        from scripts.data_processing.gemini_ocr import get_full_paper_text as get_pdf_text

        # Start by summarizing the abstract (this is always done)
        if not existing_summary:
            summary_str = await summarize_text(paper["abstract"], client)
            summary_dict = process_input(summary_str)
            summary = summary_dict[-1]["Denser_Summary"].strip()
            print(f"Summary for {paper['abs_url']}:\n", summary)

            tags = await tagging_papers(paper["abstract"], client)
            print(f"Tags for {paper['abs_url']}:\n", tags)

            entities = await perform_ner(paper["abstract"], client)
            print(f"Entities for {paper['abs_url']}:\n", entities)

            # Extract SOTA information
            sota_result = await extract_sota(paper["abstract"], client)
            print(f"SOTA for {paper['abs_url']}:\n", sota_result)

            # Extract knowledge graph only if it doesn't exist
            kg_result = None
            if not existing_kg:
                try:
                    kg_result = await extract_knowledge_graph(paper["abstract"], client)
                    print(f"Knowledge graph for {paper['abs_url']}: {len(kg_result.nodes if kg_result else [])} nodes, {len(kg_result.relationships if kg_result else [])} relationships")
                except Exception as e:
                    print(f"Knowledge graph extraction failed: {e}")
            else:
                print(f"Using existing knowledge graph for {paper['abs_url']}")
                kg_result = existing_kg
        else:
            # Use existing values if we're just updating
            summary = existing_summary.get('summary', '')
            tags = existing_summary.get('tags', [])
            entities = existing_summary.get('entities', [])
            sota_result = None  # We'll keep SOTA as None for existing papers unless explicitly updated

        # Process full text only if needed
        full_text = ''
        text_source = 'none'
        if update_needed:
            # Attempt to get full paper text, preferring HTML version when available
            # First, try to get the text using HTML version
            print(f"Attempting to fetch full text for {paper['abs_url']}")
            full_text, used_html = await get_html_text(paper['abs_url'])

            # If HTML version failed or isn't available, use PDF OCR
            if not used_html:
                print(f"HTML version not available for {paper['abs_url']}, trying PDF OCR")
                full_text, success = await get_pdf_text(client, paper['abs_url'])
                if not success:
                    print(f"Warning: Failed to extract full text from PDF for {paper['abs_url']}")
                    # Use abstract as fallback if full text extraction failed
                    full_text = paper["abstract"]
                    text_source = 'abstract'
                else:
                    text_source = 'pdf'
            else:
                text_source = 'html'

            # Store full text in the separate collection
            full_texts_collection.update_one(
                {'paper_id': paper_id},
                {'$set': {
                    'paper_id': paper_id,
                    'text': full_text,
                    'source': text_source
                }},
                upsert=True
            )

        # Store or update paper summary and metadata
        if not existing_summary:
            print(f"Creating new entry to summary table")
            # Create new document
            summary_doc = {
                'abs_url': paper['abs_url'],
                'pdf_url': paper.get('pdf_url', ''),
                'title': paper['title'],
                'authors': paper['authors'],
                'updated': paper.get('updated', ''),
                'published': paper.get('published', ''),
                'abstract': paper['abstract'],
                'summary': summary,
                'tags': tags,
                'entities': entities
            }
            summaries_collection.insert_one(summary_doc)
            print("New entry inserted!")

            # Store SOTA result if available
            if sota_result and sota_result.sota:
                try:
                    sota_data = {
                        'abs_url': paper['abs_url'],
                        'benchmark': sota_result.sota.benchmark,
                        'previous_sota_method': sota_result.sota.previous_method,
                        'previous_sota_metric': sota_result.sota.previous_metric,
                        'previous_sota_value': sota_result.sota.previous_value,
                        'proposed_method': sota_result.sota.proposed_method,
                        'proposed_metric': sota_result.sota.proposed_metric,
                        'proposed_value': sota_result.sota.proposed_value,
                        'absolute_gain': sota_result.sota.absolute_gain,
                        'relative_gain_percent': sota_result.sota.relative_gain_percent,
                        'added_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                    sota_collection.insert_one(sota_data)
                    print(f"SOTA data inserted for {paper['abs_url']}")
                except Exception as e:
                    print(f"Error inserting SOTA data: {e}")

            # Store knowledge graph if available and not already existing
            if kg_result and not existing_kg:
                kg_data = {
                    'abs_url': paper['abs_url'],
                    'raw_dot': kg_result.raw_dot,
                    'nodes': [node.model_dump() for node in kg_result.nodes],
                    'relationships': [rel.model_dump() for rel in kg_result.relationships]
                }
                knowledge_graphs_collection.insert_one(kg_data)

            # Extract tables if we have a PDF URL and full text from PDF
            if paper.get('pdf_url') and text_source == 'pdf':
                try:
                    # Extract tables from the PDF
                    print(f"Extracting tables from PDF for {paper['abs_url']}")
                    success, table_count, _ = await extract_and_save_tables(
                        abs_url=paper['abs_url'],
                        pdf_url=paper.get('pdf_url')
                    )

                    if success:
                        if table_count > 0:
                            print(f"Found {table_count} tables in {paper['abs_url']}")
                        else:
                            print(f"No tables found in {paper['abs_url']}")
                except Exception as e:
                    print(f"Error extracting tables: {e}")

        # Update the has_full_text flag in summaries_collection if we have full text
        if full_text and text_source in ['html', 'pdf']:
            summaries_collection.update_one(
                {'abs_url': paper['abs_url']},
                {'$set': {'has_full_text': True}}
            )

        # Combine the data for the response
        result = {
            'abs_url': paper['abs_url'],
            'pdf_url': paper.get('pdf_url', ''),
            'title': paper['title'],
            'authors': paper['authors'],
            'updated': paper.get('updated', ''),
            'published': paper.get('published', ''),
            'abstract': paper['abstract'],
            'summary': summary,
            'tags': tags,
            'entities': entities,
            'full_text': full_text,
            'text_source': text_source,
            'has_full_text': bool(full_text and text_source in ['html', 'pdf']),
            'has_tables': existing_summary.get('has_tables', False) if existing_summary else False,
            'table_count': existing_summary.get('table_count', 0) if existing_summary else 0
        }
        return result

    except Exception as e:
        print(f"Error processing paper {paper.get('abs_url', 'unknown')}: {e}")
        # Return available data even if an error occurred
        return {
            'abs_url': paper.get('abs_url', ''),
            'pdf_url': paper.get('pdf_url', ''),
            'title': paper.get('title', ''),
            'authors': paper.get('authors', []),
            'updated': paper.get('updated', ''),
            'published': paper.get('published', ''),
            'abstract': paper.get('abstract', ''),
            'summary': existing_summary.get('summary', '') if existing_summary else '',
            'tags': existing_summary.get('tags', []) if existing_summary else [],
            'entities': existing_summary.get('entities', []) if existing_summary else [],
            'has_tables': existing_summary.get('has_tables', False) if existing_summary else False,
            'table_count': existing_summary.get('table_count', 0) if existing_summary else 0,
            'error': str(e)
        }
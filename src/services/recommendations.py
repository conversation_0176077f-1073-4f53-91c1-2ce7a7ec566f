from typing import Dict, List, Any, Optional, <PERSON><PERSON>
import numpy as np
from scipy.spatial.distance import cdist
import datetime
import logging
import os

def recommend_papers(
    user_id: str, 
    collections: Dict[str, Any], 
    top_n: int = 10
) -> List[Tuple[str, float, Dict[str, Any]]]:
    """
    Generates paper recommendations for a user based on their saved papers' embeddings.

    Args:
        user_id: The ID of the user for whom to generate recommendations.
        collections: Dictionary containing MongoDB collections.
        top_n: The maximum number of recommendations to return.

    Returns:
        A list of tuples containing (paper_url, similarity_score, details)
    """
    # MongoDB Collection names from environment variables or defaults
    PAPERS_COLLECTION = os.environ.get("SUMMARIES_COLLECTION", "summaries")
    SAVED_PAPERS_COLLECTION = os.environ.get("SAVED_PAPERS_COLLECTION", "saved_papers")
    
    # Recommendation Configuration from environment variables or defaults
    DEFAULT_TOP_N = int(os.environ.get("RECOMMENDATIONS_TOP_N", "10"))
    RECENCY_BOOST_SAME_DAY = float(os.environ.get("RECENCY_BOOST_SAME_DAY", "3.0"))
    RECENCY_BOOST_1_DAY = float(os.environ.get("RECENCY_BOOST_1_DAY", "2.0"))
    RECENCY_BOOST_2_DAY = float(os.environ.get("RECENCY_BOOST_2_DAY", "1.5"))
    RECENCY_BOOST_3_DAY = float(os.environ.get("RECENCY_BOOST_3_DAY", "1.2"))
    RECENCY_DAYS = int(os.environ.get("RECENCY_DAYS", "3"))
    USE_REFERENCE_DATE = os.environ.get("USE_REFERENCE_DATE", "True").lower() in ["true", "1", "yes"]
    
    papers_coll = collections[PAPERS_COLLECTION]
    saved_papers_coll = collections[SAVED_PAPERS_COLLECTION]

    # 1. Identify User's Saved Papers
    try:
        saved_paper_docs = list(saved_papers_coll.find({'user_id': user_id}, {'abs_url': 1, '_id': 0}))
        saved_urls = [doc['abs_url'] for doc in saved_paper_docs]

        if not saved_urls:
            logging.info(f"User {user_id} has no saved papers. Cannot generate recommendations.")
            return []

        logging.info(f"Successfully retrieved {len(saved_urls)} saved papers for user {user_id}.")

    except Exception as e:
        logging.error(f"Error fetching saved papers for user {user_id}: {e}")
        return []

    # 2. Retrieve Embeddings for Saved Papers
    try:
        # Fetch papers ensuring the embedding field exists and is not empty/null
        saved_papers_with_embeddings = list(papers_coll.find(
            {'abs_url': {'$in': saved_urls}, 'embedding': {'$exists': True, '$ne': None, '$not': {'$size': 0}}},
            {'embedding': 1, 'abs_url': 1}
        ))

        if not saved_papers_with_embeddings:
            logging.warning(f"Could not find valid embeddings for any of the saved papers for user {user_id}.")
            return []

        saved_embeddings = [doc['embedding'] for doc in saved_papers_with_embeddings]
        logging.info(f"Retrieved embeddings for {len(saved_embeddings)} saved papers.")

    except Exception as e:
        logging.error(f"Error fetching embeddings for saved papers: {e}")
        return []

    # 3. Calculate User Profile Vector (Average Embedding)
    user_profile_vector = np.mean(np.array(saved_embeddings), axis=0)
    if user_profile_vector is None or user_profile_vector.size == 0:
         logging.error(f"Could not calculate user profile vector for user {user_id}.")
         return []
    logging.info(f"Calculated user profile vector (shape: {user_profile_vector.shape}).")

    # 4. Retrieve Embeddings for Candidate Papers (Papers NOT saved by the user)
    try:
        # Fetch candidate papers ensuring the embedding field exists and is not empty/null
        # Include published date for recency calculation
        candidate_docs_cursor = papers_coll.find(
            {'abs_url': {'$nin': saved_urls}, 'embedding': {'$exists': True, '$ne': None, '$not': {'$size': 0}}},
            {'abs_url': 1, 'embedding': 1, 'published': 1, 'updated': 1}
        )

        # Process candidates efficiently (avoid loading all into memory if very large)
        candidate_ids = []
        candidate_embeddings_list = []
        candidate_dates = []  # Store dates for recency calculation
        
        # Get today's date for recency calculation
        today = datetime.date.today()
        
        for doc in candidate_docs_cursor:
            # Basic validation of embedding structure (e.g., ensure it's a list/vector)
            if isinstance(doc.get('embedding'), list) and len(doc['embedding']) > 0:
                candidate_ids.append(doc['abs_url'])
                candidate_embeddings_list.append(doc['embedding'])
                
                # Store the date (use updated date if available, otherwise published date)
                paper_date = None
                if 'updated' in doc and doc['updated']:
                    paper_date = doc['updated']
                elif 'published' in doc and doc['published']:
                    paper_date = doc['published']
                    
                # Default to today if no date is available
                if not paper_date:
                    paper_date = today.strftime("%Y-%m-%d")
                    
                candidate_dates.append(paper_date)
                
        if not candidate_ids:
            logging.warning("No candidate papers found for recommendation.")
            return []

        candidate_embeddings = np.array(candidate_embeddings_list)
        logging.info(f"Found {len(candidate_ids)} candidate papers with valid embeddings.")

    except Exception as e:
        logging.error(f"Error fetching candidate papers: {e}")
        return []

    # 5. Calculate Similarity (Cosine Similarity)
    try:
        # Reshape user profile vector for cdist (needs 2D array)
        user_profile_2d = user_profile_vector.reshape(1, -1)

        # Calculate cosine distance (1 - similarity)
        # cdist calculates the distance between each candidate and the user profile
        distances = cdist(candidate_embeddings, user_profile_2d, 'cosine')

        # Convert distances to similarities (similarity = 1 - distance)
        similarities = 1 - distances.flatten() # flatten converts the result to a 1D array

        if similarities.size != len(candidate_ids):
             logging.error("Mismatch between number of similarities and candidate IDs. Aborting.")
             return []

        logging.info(f"Calculated similarities for {len(similarities)} candidates.")

        # 5B. Apply recency boost to similarity scores
        today = datetime.date.today()
        boosted_similarities = []
        
        # Store both the original and boosted similarities for reporting
        similarity_info = []
        
        # Parse all paper dates for finding the reference date
        parsed_dates = []
        for date_str in candidate_dates:
            try:
                paper_date = datetime.datetime.strptime(date_str, "%Y-%m-%d").date()
                parsed_dates.append(paper_date)
            except ValueError:
                # Skip unparseable dates
                pass
        
        # Find the most recent date from the papers as reference if enabled
        reference_date = today
        if USE_REFERENCE_DATE and parsed_dates:
            reference_date = max(parsed_dates)
            logging.info(f"Using reference date: {reference_date} instead of today ({today})")
        
        for i, similarity in enumerate(similarities):
            paper_date_str = candidate_dates[i]
            try:
                # Parse the date string into a datetime object
                paper_date = datetime.datetime.strptime(paper_date_str, "%Y-%m-%d").date()
                
                # Calculate days difference from reference date
                days_old = (reference_date - paper_date).days
                
                # Apply granular recency boost with specific multiplier for each day
                recency_multiplier = 1.0  # Default multiplier (no boost)
                
                if days_old <= 0:  # Published on reference date or newer
                    recency_multiplier = RECENCY_BOOST_SAME_DAY
                elif days_old == 1:  # Published 1 day before reference date
                    recency_multiplier = RECENCY_BOOST_1_DAY
                elif days_old == 2:  # Published 2 days before reference date
                    recency_multiplier = RECENCY_BOOST_2_DAY
                elif days_old == 3:  # Published 3 days before reference date
                    recency_multiplier = RECENCY_BOOST_3_DAY
                
                # Apply boost to similarity score
                boosted_similarity = similarity * recency_multiplier
                
                # Cap at 1.0 (100% similarity)
                boosted_similarity = min(boosted_similarity, 1.0)
                
                boosted_similarities.append(boosted_similarity)
                similarity_info.append({
                    'abs_url': candidate_ids[i],
                    'original_similarity': similarity,
                    'boosted_similarity': boosted_similarity,
                    'date': paper_date_str,
                    'days_old': days_old,
                    'recency_multiplier': recency_multiplier,
                    'reference_date': reference_date.strftime("%Y-%m-%d")
                })
                
            except ValueError:
                # If date parsing fails, use the original similarity
                boosted_similarities.append(similarity)
                similarity_info.append({
                    'abs_url': candidate_ids[i],
                    'original_similarity': similarity,
                    'boosted_similarity': similarity,
                    'date': paper_date_str,
                    'days_old': None,
                    'recency_multiplier': 1.0,
                    'reference_date': reference_date.strftime("%Y-%m-%d")
                })
        
        logging.info(f"Applied recency boost to similarity scores.")

    except Exception as e:
        logging.error(f"Error calculating similarities: {e}")
        import traceback
        traceback.print_exc()
        return []

    # 6. Rank and Select Recommendations
    # Combine candidate IDs with their similarity scores
    results = list(zip(candidate_ids, boosted_similarities, similarity_info))

    # Sort by boosted similarity score in descending order
    results.sort(key=lambda x: x[1], reverse=True)

    # Select the top N paper IDs with their scores and info
    top_results = results[:top_n]
    recommendations_with_scores = []
    
    for paper_url, boosted_score, info in top_results:
        # Round scores to 2 decimal places
        original_score = round(info['original_similarity'] * 100, 2)
        boosted_score = round(boosted_score * 100, 2)
        days_old = info['days_old']
        
        recommendations_with_scores.append((
            paper_url, 
            boosted_score,
            {
                'original_score': original_score,
                'date': info['date'],
                'days_old': days_old,
                'recency_multiplier': round(info['recency_multiplier'], 2)
            }
        ))

    logging.info(f"Generated {len(recommendations_with_scores)} recommendations.")

    # Return results with scores
    return recommendations_with_scores
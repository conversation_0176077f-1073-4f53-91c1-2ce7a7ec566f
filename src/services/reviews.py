import os
from datetime import datetime

def get_rating_for_paper(abs_url, user_id=None, reviews_collection=None):
    """
    Get the rating for a paper, either for a specific user or the average across all users
    
    Args:
        abs_url: The paper URL
        user_id: Optional user ID for personalized ratings
        reviews_collection: MongoDB collection for reviews
        
    Returns:
        dict: Rating data or None if no rating found
    """
    if reviews_collection is None:
        # Try to get reviews collection from global scope
        from src.db import get_collections
        try:
            collections = get_collections()
            reviews_collection = collections.get('reviews')
        except:
            return None
    
    if reviews_collection is None:
        return None
    
    if user_id:
        # Get rating for specific user
        review = reviews_collection.find_one({'abs_url': abs_url, 'user_id': user_id})
        if review:
            return review.get('rating', None)
    else:
        # Get average rating across all users
        reviews = list(reviews_collection.find({'abs_url': abs_url}))
        if reviews:
            # Calculate average for each rating dimension
            soundness_sum = sum(review.get('rating', {}).get('soundness', 0) for review in reviews)
            presentation_sum = sum(review.get('rating', {}).get('presentation', 0) for review in reviews)
            contribution_sum = sum(review.get('rating', {}).get('contribution', 0) for review in reviews)
            final_sum = sum(review.get('rating', {}).get('final', 0) for review in reviews)
            count = len(reviews)
            
            if count > 0:
                return {
                    'soundness': round(soundness_sum / count, 1),
                    'presentation': round(presentation_sum / count, 1),
                    'contribution': round(contribution_sum / count, 1),
                    'final': round(final_sum / count, 1),
                    'count': count
                }
    
    return None

def save_review(abs_url, user_id, review_text, rating, reviews_collection):
    """Save or update a review and rating for a paper"""
    try:
        # Create the review document
        review_doc = {
            'abs_url': abs_url,
            'user_id': user_id,
            'review_text': review_text,
            'rating': rating,
            'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # Upsert the review (update if exists, insert if not)
        result = reviews_collection.update_one(
            {'abs_url': abs_url, 'user_id': user_id},
            {'$set': review_doc},
            upsert=True
        )
        
        return True, f"Review {'updated' if result.matched_count else 'created'} successfully"
    except Exception as e:
        print(f"Error saving review: {e}")
        return False, str(e)
    
async def generate_review(abs_url, summaries_collection, reviews_collection, client, user_id="system_auto_review"):
    """Generate an AI review for a paper using the Gemini API"""
    try:
        # Get the paper from the database
        paper = summaries_collection.find_one({'abs_url': abs_url})
        if not paper:
            print(f"Paper not found for auto-review: {abs_url}")
            return False
        
        # Check if review already exists for this paper and user
        existing_review = reviews_collection.find_one({'abs_url': abs_url, 'user_id': user_id})
        if existing_review:
            print(f"Review already exists for {abs_url} by {user_id}, skipping generation")
            return False
        
        # Format the input for the AI review generation with explicit JSON structure
        review_input = f"""
        Title: {paper['title']}
        Abstract: {paper['abstract']}
        
        Act as an expert reviewer for this research paper. Generate a comprehensive review that:
        1. Evaluates the soundness of technical claims and methodology (1-4 scale)
        2. Assesses the presentation quality including writing style and clarity (1-4 scale)
        3. Evaluates the significance of the contribution to the field (1-4 scale)
        4. Provides an overall recommendation (1-10 scale, with 1=strong reject, 10=strong accept)
        
        Your review should include:
        - Critical assessment of strengths
        - Detailed analysis of weaknesses or limitations
        - Questions or suggestions for improvement
        
        You must return your review in the following JSON format exactly:
        
        {{
          "review_text": "Your detailed review goes here...",
          "rating": {{
            "soundness": X,
            "presentation": Y,
            "contribution": Z,
            "final": W
          }}
        }}
        
        Where X, Y, Z are integers from 1-4, and W is an integer from 1-10.
        
        Make sure the generated JSON is valid - don't include any newlines or special characters within the review_text value that could break JSON parsing.
        """
        
        try:
            # Call the API to generate the review
            chat_completion = client.chat.completions.create(
                messages=[{
                    "role": "system",
                    "content": "You are an expert AI reviewer for academic papers. You always return valid JSON."
                }, {
                    "role": "user",
                    "content": review_input
                }],
                model=os.getenv("GEMINI_MODEL"),
                temperature=0.1  # Lower temperature for more consistent JSON formatting
            )
            
            review_output = chat_completion.choices[0].message.content.strip()
            
            # Parse the JSON output - improved method
            import json
            import re
            
            # If we see a JSON block, extract it
            json_str = ""
            if "```json" in review_output:
                json_match = re.search(r'```json\s*([\s\S]*?)\s*```', review_output)
                if json_match:
                    json_str = json_match.group(1).strip()
            elif "```" in review_output:
                # Maybe it's a code block without language specification
                json_match = re.search(r'```\s*([\s\S]*?)\s*```', review_output)
                if json_match:
                    json_str = json_match.group(1).strip()
            else:
                # Try to extract JSON object with regex
                json_match = re.search(r'({[\s\S]*})', review_output)
                if json_match:
                    json_str = json_match.group(1).strip()
                else:
                    # Just use the whole output
                    json_str = review_output
            
            # Fallback to full review text if nothing matched
            if not json_str:
                json_str = review_output
            
            # First attempt: try to load as is
            try:
                review_data = json.loads(json_str)
            except json.JSONDecodeError:
                print(f"Initial JSON parsing failed for {abs_url}")
                
                # Second attempt: basic cleanup
                try:
                    # Remove whitespace, ensure property names are quoted
                    clean_json = json_str.strip()
                    
                    # If JSON starts after some text, extract it
                    if '{' in clean_json:
                        start_idx = clean_json.find('{')
                        clean_json = clean_json[start_idx:]
                    
                    # If JSON ends before some text, extract it
                    if '}' in clean_json:
                        end_idx = clean_json.rfind('}') + 1
                        clean_json = clean_json[:end_idx]
                    
                    # Replace single quotes with double quotes if needed
                    if "'" in clean_json and '"' not in clean_json:
                        clean_json = clean_json.replace("'", '"')
                    
                    # Try parsing with this cleanup
                    review_data = json.loads(clean_json)
                except json.JSONDecodeError:
                    print(f"JSON cleanup parsing failed for {abs_url}")
                    
                    # Third attempt: Use regex to extract structure and rebuild
                    try:
                        # Extract review text and rating blocks separately 
                        review_pattern = r'"review_text"\s*:\s*"((?:[^"\\]|\\"|\\\\)*)"'
                        rating_pattern = r'"rating"\s*:\s*({[^{}]*})'
                        
                        review_match = re.search(review_pattern, json_str)
                        rating_match = re.search(rating_pattern, json_str)
                        
                        if review_match and rating_match:
                            # Get the review text and rating object
                            review_text_value = review_match.group(1)
                            rating_obj = rating_match.group(1)
                            
                            # Remove problematic characters from the review text
                            review_text_value = ''.join(ch for ch in review_text_value if ord(ch) >= 32 or ch in '\t\r\n')
                            review_text_value = review_text_value.replace('"', '\\"').replace('\n', '\\n').replace('\r', '\\r')
                            
                            # Clean up the rating object
                            rating_obj = re.sub(r'([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'"\1":', rating_obj)
                            rating_obj = rating_obj.replace("'", '"')
                            
                            # Create a valid JSON string
                            rebuilt_json = f'{{"review_text": "{review_text_value}", "rating": {rating_obj}}}'
                            
                            try:
                                review_data = json.loads(rebuilt_json)
                            except json.JSONDecodeError:
                                print(f"Failed to parse rebuilt JSON for {abs_url}")
                                # Use a default review as a last resort
                                review_data = {
                                    "review_text": "Unable to generate review due to processing error. Please check the paper manually.",
                                    "rating": {
                                        "soundness": 3,
                                        "presentation": 3,
                                        "contribution": 3,
                                        "final": 6
                                    }
                                }
                        else:
                            # If we couldn't extract the structure, use a default review
                            review_data = {
                                "review_text": "Unable to generate review due to processing error. Please check the paper manually.",
                                "rating": {
                                    "soundness": 3,
                                    "presentation": 3,
                                    "contribution": 3,
                                    "final": 6
                                }
                            }
                    except Exception as e:
                        print(f"JSON extraction failed: {e}")
                        # Use a default review as a last resort
                        review_data = {
                            "review_text": "Unable to generate review due to processing error. Please check the paper manually.",
                            "rating": {
                                "soundness": 3,
                                "presentation": 3,
                                "contribution": 3,
                                "final": 6
                            }
                        }
            
            # Extract review text and ratings
            review_text = review_data.get('review_text', '')
            rating = review_data.get('rating', {})
            
            # Ensure all rating fields are present and valid
            soundness = max(1, min(4, int(rating.get('soundness', 3))))
            presentation = max(1, min(4, int(rating.get('presentation', 3))))
            contribution = max(1, min(4, int(rating.get('contribution', 3))))
            final = max(1, min(10, int(rating.get('final', 6))))
            
            # Create the final rating
            final_rating = {
                'soundness': soundness,
                'presentation': presentation,
                'contribution': contribution,
                'final': final
            }
            
            # Save the review
            success, message = save_review(abs_url, user_id, review_text, final_rating, reviews_collection)
            if success:
                print(f"Successfully generated and saved auto-review for {abs_url}")
                return True
            else:
                print(f"Failed to save auto-review for {abs_url}: {message}")
                return False
                
        except Exception as e:
            print(f"Error generating review for {abs_url}: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"Error in generate_review for {abs_url}: {e}")
        import traceback
        traceback.print_exc()
        return False
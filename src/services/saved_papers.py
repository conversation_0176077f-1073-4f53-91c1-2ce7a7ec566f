from typing import Dict, List, Optional, Union, Any
from datetime import datetime
import logging


def save_paper(collections: Dict[str, Any], user_id: str, paper_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Save a paper to the user's saved papers collection

    Args:
        collections (Dict[str, Any]): MongoDB collections dict
        user_id (str): ID of the user saving the paper
        paper_data (Dict[str, Any]): Basic paper data to save (abs_url and title)

    Returns:
        Dict[str, Any]: Result of the save operation
    """
    try:
        if not user_id:
            return {"status": "error", "message": "User not authenticated"}
        
        # Basic validation
        if "abs_url" not in paper_data or "title" not in paper_data:
            return {"status": "error", "message": "Missing required paper data (abs_url or title)"}

        # Check if the paper is already saved to avoid duplicates
        existing = collections['saved_papers'].find_one({
            "user_id": user_id, 
            "abs_url": paper_data["abs_url"]
        })
        
        if existing:
            # Paper is already saved
            return {"status": "success", "message": "Paper already saved"}
        
        # Get additional data from summaries if available
        additional_data = {}
        summary = collections['summaries'].find_one({"abs_url": paper_data["abs_url"]})
        if summary:
            # Add useful fields from the summary
            additional_data = {
                "authors_string": summary.get("authors_string", ""),
                "abstract": summary.get("abstract", ""),
                "summary": summary.get("summary", ""),  # Add summary field
                "pdf_url": summary.get("pdf_url", ""),
                "date": summary.get("published", ""),
                "tags": summary.get("tags", []),
                "entities": summary.get("entities", [])
            }
        
        # Prepare data for MongoDB with any additional fields
        save_data = {
            "user_id": user_id,
            "abs_url": paper_data["abs_url"],
            "title": paper_data["title"],
            "saved_at": datetime.now(),
            **additional_data  # Include any additional data we found
        }
        
        # Insert into MongoDB
        collections['saved_papers'].insert_one(save_data)
        
        # If using the old format too, ensure consistency
        # This will gradually be phased out as we move to the new format
        old_format = collections['saved_papers'].find_one({"user_id": user_id, "papers": {"$exists": True}})
        if old_format:
            # If there's a paper ID in the summaries, save it in the old format too
            if summary and "_id" in summary:
                paper_id = summary["_id"]
                if paper_id not in old_format.get("papers", []):
                    collections['saved_papers'].update_one(
                        {"user_id": user_id, "papers": {"$exists": True}},
                        {"$addToSet": {"papers": paper_id}}
                    )
        
        return {"status": "success", "message": "Paper saved successfully"}
    except Exception as e:
        logging.error(f"Error saving paper: {e}")
        return {"status": "error", "message": str(e)}


def get_saved_papers(collections: Dict[str, Any], user_id: str, limit: int = 50) -> List[Dict]:
    """
    Get a list of papers saved by a specific user

    Args:
        collections (Dict[str, Any]): MongoDB collections dict
        user_id (str): ID of the user 
        limit (int, optional): Maximum number of saved papers to return. Defaults to 50.

    Returns:
        List[Dict]: List of saved paper dictionaries
    """
    try:
        if not user_id:
            return []
            
        # Query MongoDB for papers saved by the user using the new format
        saved_papers = list(collections['saved_papers'].find(
            {"user_id": user_id, "abs_url": {"$exists": True}},  # Only get documents with abs_url (new format)
            sort=[("saved_at", -1)],  # Sort by save date, newest first
            limit=limit
        ))
        
        # Ensure each paper has the required fields for the Paper component
        for paper in saved_papers:
            # MongoDB _id needs special handling
            if '_id' in paper:
                paper['_id'] = str(paper['_id'])  # Convert ObjectId to string
            
            # Try to get the summary from the summaries collection if available
            abs_url = paper.get('abs_url')
            if abs_url:
                summary_doc = collections['summaries'].find_one({"abs_url": abs_url})
                if summary_doc and 'summary' in summary_doc:
                    paper['summary'] = summary_doc['summary']
                elif 'summary' not in paper or not paper['summary']:
                    paper['summary'] = paper.get('abstract', '')
            
            # Ensure we have all required fields with proper defaults
            # These fields are used by the Paper component in main.py
            required_fields = {
                'tags': [],
                'entities': [],
                'published': '',
                'authors': [],
                'abstract': '',
                'pdf_url': ''
            }
            
            for field, default in required_fields.items():
                if field not in paper or paper[field] is None:
                    paper[field] = default
                
        return saved_papers
    except Exception as e:
        logging.error(f"Error fetching saved papers: {e}")
        return []


def unsave_paper(collections: Dict[str, Any], user_id: str, abs_url: str) -> Dict[str, Any]:
    """
    Remove a paper from a user's saved papers

    Args:
        collections (Dict[str, Any]): MongoDB collections dict
        user_id (str): ID of the user 
        abs_url (str): URL of the paper to remove

    Returns:
        Dict[str, Any]: Result of the operation
    """
    try:
        if not user_id:
            return {"status": "error", "message": "User not authenticated"}
        
        # Remove the paper from saved papers
        result = collections['saved_papers'].delete_one({
            "user_id": user_id,
            "abs_url": abs_url
        })
        
        if result.deleted_count > 0:
            return {"status": "success", "message": "Paper removed from saved items"}
        else:
            return {"status": "error", "message": "Paper not found in saved items"}
    except Exception as e:
        logging.error(f"Error removing saved paper: {e}")
        return {"status": "error", "message": str(e)}


def is_paper_saved_by_user(collections: Dict[str, Any], user_id: str, abs_url: str) -> bool:
    """
    Check if a paper is saved by a specific user

    Args:
        collections (Dict[str, Any]): MongoDB collections dict
        user_id (str): ID of the user
        abs_url (str): URL of the paper to check

    Returns:
        bool: True if the paper is saved by the user, False otherwise
    """
    try:
        if not user_id:
            return False
        
        # Check if the paper exists in the user's saved papers using the new format
        count = collections['saved_papers'].count_documents({
            "user_id": user_id,
            "abs_url": abs_url
        })
        
        if count > 0:
            return True
            
        # Also check the old format for backward compatibility
        saved_papers_doc = collections['saved_papers'].find_one({"user_id": user_id})
        if saved_papers_doc and 'papers' in saved_papers_doc:
            paper_ids = saved_papers_doc.get('papers', [])
            for paper_id in paper_ids:
                paper = collections['papers'].find_one({"_id": paper_id})
                if paper and paper.get('abs_url') == abs_url:
                    return True
        
        return False
    except Exception as e:
        logging.error(f"Error checking if paper is saved: {e}")
        return False
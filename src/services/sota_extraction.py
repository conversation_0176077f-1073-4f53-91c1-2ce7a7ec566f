import os
import json
import traceback
from typing import Optional
from openai import OpenAI
import inspect

from src.models.paper import SOTAResult

async def extract_sota(text: str, client: OpenAI) -> Optional[SOTAResult]:
    """
    Extract state-of-the-art information from text using structured outputs API
    
    Args:
        text (str): Text to analyze
        client (OpenAI): OpenAI client
        
    Returns:
        Optional[SOTAResult]: Extracted SOTA information, or None if not found
    """
    print("Extracting SOTA...")
    print(f"Using model: {os.getenv('GEMINI_EXPERIMENTAL_MODEL')}")
    
    if not text or len(text.strip()) < 10:
        print("Text too short, skipping SOTA extraction")
        return None
    
    try:
        print("Attempting to call client.beta.chat.completions.parse...")
        
        # Try using the structured parse API
        try:
            chat_completion = client.beta.chat.completions.parse(
                model=os.getenv("GEMINI_EXPERIMENTAL_MODEL"),
                messages=[{
                    "role": "system",
                    "content": """You are a helpful assistant with world-class expertise in AI research and application.
Your role is to extract SOTA improvements from research papers better than anyone else in the world.
You only need to focus on the most important benchmark - the one that is most relevant to advancing the field."""
                }, {
                    "role": "user",
                    "content": f"""Extract the SOTA (state-of-the-art) information from this text. Focus on the main benchmark and performance metrics.

Text: {text}

Return a JSON object with a 'sota' field containing these properties:
- benchmark: name of benchmark dataset or task
- table_nr: the table number as an integer, use 0 if unknown
- previous_method: name of previous SOTA method
- previous_metric: metric name like accuracy, F1, etc.
- previous_value: previous method's score as a number
- proposed_method: name of the proposed method
- proposed_metric: metric name like accuracy, F1, etc.
- proposed_value: proposed method's score as a number
- absolute_gain: absolute improvement as a number
- relative_gain_percent: percentage improvement as a number

Example format:
{{
  "sota": {{
    "benchmark": "ImageNet",
    "table_nr": 2,
    "previous_method": "ResNet",
    "previous_metric": "accuracy",
    "previous_value": 76.5,
    "proposed_method": "ViT",
    "proposed_metric": "accuracy",
    "proposed_value": 80.2,
    "absolute_gain": 3.7,
    "relative_gain_percent": 4.8
  }}
}}

If the paper doesn't have SOTA claims or you can't extract this data, use "None" for strings and 0 for numbers.
"""
                }],
                response_format={"type": "json_object"}
            )
            
            # Extract the JSON response
            response_content = chat_completion.choices[0].message.content
            print(f"SOTA response: {response_content}")
            
            # Parse the JSON into our SOTAResult model
            sota_data = json.loads(response_content)
            
            # Check if the response has the expected structure
            if "sota" not in sota_data:
                # Wrap the data in a 'sota' field if it's missing
                sota_data = {"sota": sota_data}
                
            sota_result = SOTAResult(**sota_data)
            
            print(f"SOTA for {text[:50]}...: {sota_result}")
            return sota_result
            
        except Exception as parse_error:
            print(f"Error using parse API, falling back to regular chat: {parse_error}")
            
            # Fallback to regular chat completion
            chat_completion = client.chat.completions.create(
                model=os.getenv("GEMINI_EXPERIMENTAL_MODEL"),
                messages=[{
                    "role": "system",
                    "content": """You are a helpful assistant with world-class expertise in AI research and application.
Your role is to extract SOTA improvements from research papers better than anyone else in the world.
You only need to focus on the most important benchmark - the one that is most relevant to advancing the field."""
                }, {
                    "role": "user",
                    "content": f"""Extract the SOTA (state-of-the-art) information from this text. Focus on the main benchmark and performance metrics.

Text: {text}

Return a JSON object with a 'sota' field containing these properties:
- benchmark: name of benchmark dataset or task
- table_nr: the table number as an integer, use 0 if unknown
- previous_method: name of previous SOTA method
- previous_metric: metric name like accuracy, F1, etc.
- previous_value: previous method's score as a number
- proposed_method: name of the proposed method
- proposed_metric: metric name like accuracy, F1, etc.
- proposed_value: proposed method's score as a number
- absolute_gain: absolute improvement as a number
- relative_gain_percent: percentage improvement as a number

Example format:
{{
  "sota": {{
    "benchmark": "ImageNet",
    "table_nr": 2,
    "previous_method": "ResNet",
    "previous_metric": "accuracy",
    "previous_value": 76.5,
    "proposed_method": "ViT",
    "proposed_metric": "accuracy",
    "proposed_value": 80.2,
    "absolute_gain": 3.7,
    "relative_gain_percent": 4.8
  }}
}}

If the paper doesn't have SOTA claims or you can't extract this data, use "None" for strings and 0 for numbers.
"""
                }],
                response_format={"type": "json_object"}
            )
            
            # Extract the JSON response
            response_content = chat_completion.choices[0].message.content
            print(f"SOTA response (fallback): {response_content}")
            
            # Parse the JSON into our SOTAResult model
            sota_data = json.loads(response_content)
            
            # Check if the response has the expected structure
            if "sota" not in sota_data:
                # Wrap the data in a 'sota' field if it's missing
                sota_data = {"sota": sota_data}
                
            sota_result = SOTAResult(**sota_data)
            
            print(f"SOTA for {text[:50]}... (fallback): {sota_result}")
            return sota_result
        
    except Exception as e:
        print(f"Error in extract_sota: {e}")
        traceback.print_exc()
        return None

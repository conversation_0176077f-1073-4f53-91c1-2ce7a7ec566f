import os
from typing import Any, Optional
from openai import OpenAI

# System prompt for summarization
SUMMARY_SYSTEM_PROMPT = """You are a helpful assistant with world-class expertise in AI research and application. 
You are able to summarize articles better than anyone else in the world."""

async def summarize_text(text: str, client: OpenAI) -> str:
    """
    Generate a summary of the provided text
    
    Args:
        text (str): Text to summarize
        client (OpenAI): OpenAI client
        
    Returns:
        str: Generated summary
    """
    chat_completion = client.chat.completions.create(
        messages=[{
            "role": "system",
            "content": SUMMARY_SYSTEM_PROMPT
        }, {
            "role": "user",
            "content": f"""Article: {text} 
            You will generate increasingly concise, entity-dense summaries of the above article.

            Repeat the following 2 steps 5 times.

            Step 1. Identify 1-3 informative entities (";" delimited) from the article which are missing from the previously generated summary.
            Step 2. Write a new, denser summary of identical length which covers every entity and detail from the previous summary plus the missing entities.

            A missing entity is:
            - relevant to the main story,
            - specific yet concise (5 words or fewer),
            - novel (not in the previous summary),
            - faithful (present in the article),
            - anywhere (can be located anywhere in the article).

            Guidelines:

            - The first summary should be long (4-5 sentences, ~80 words) yet highly non-specific, containing little information beyond the entities marked as missing. Use overly verbose language and fillers (e.g., "this article discusses") to reach ~80 words.
            - Make every word count: rewrite the previous summary to improve flow and make space for additional entities.
            - Make space with fusion, compression, and removal of uninformative phrases like "the article discusses".
            - The summaries should become highly dense and concise yet self-contained, i.e., easily understood without the article.
            - Missing entities can appear anywhere in the new summary.
            - Never drop entities from the previous summary. If space cannot be made, add fewer new entities.

            Remember, use the exact same number of words for each summary.
            Answer in JSON. The JSON should be a list (length 5) of dictionaries whose keys are "Missing_Entities" and "Denser_Summary".
            """
        }],
        model=os.getenv("GEMINI_MODEL"),
    )

    summary = chat_completion.choices[0].message.content.strip()
    return summary
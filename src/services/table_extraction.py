from gmft.auto import AutoTableDetector, AutoTableFormatter
from gmft.pdf_bindings import PyPDFium2Document
import pandas as pd
from pathlib import Path
from typing import List, Any, Dict, Optional, Tuple
from tqdm.auto import tqdm
from datetime import datetime
import os
import aiohttp
import asyncio

# Import MongoDB utilities
from src.db.mongo import get_collections

class GMFTExtractor:
    """
    Extracts tables from PDFs using gmft, with optional saving to CSV/JSON.
    """
    def __init__(self, pdf_path: str):
        self.pdf_path = pdf_path
        self.detector = AutoTableDetector()
        self.formatter = AutoTableFormatter()
        self.tables = []
        self.formatted_tables = []

    def extract_tables(self, save: bool = False, paper_id: str = None, output_dir: str = "output_tables") -> List[Any]:
        """
        Extract tables from a PDF document.

        Args:
            save: Whether to save the tables
            paper_id: Optional paper ID to associate with tables in MongoDB
            output_dir: Directory to save tables if save=True

        Returns:
            List of formatted tables
        """
        doc = PyPDFium2Document(self.pdf_path)
        tables = []
        for page in tqdm(doc):
            tables += self.detector.extract(page)
        self.tables = tables
        self.formatted_tables = [self.formatter.extract(table) for table in tqdm(tables)]
        if save:
            self.save_tables(self.formatted_tables, paper_id=paper_id, output_dir=output_dir)
        return self.formatted_tables

    @staticmethod
    def save_tables(tables, paper_id: str = None, output_dir: str = None):
        """
        Save tables to MongoDB and optionally to local files.

        Args:
            tables: List of table objects to save
            paper_id: The ID of the paper these tables belong to
            output_dir: Optional directory to save tables as JSON files locally

        Returns:
            List of saved table IDs from MongoDB
        """
        # Save to MongoDB if paper_id is provided
        table_ids = []
        if paper_id:
            try:
                # Get MongoDB collections
                collections = get_collections()
                if not collections or 'tables' not in collections:
                    print("Warning: MongoDB collections not available, tables not saved to database")
                else:
                    tables_collection = collections['tables']

                    # Save each table to MongoDB
                    for index, table in enumerate(tables):
                        df = table.df()

                        # Handle duplicate columns by renaming them
                        df.columns = [f"{col}_{i}" if df.columns.tolist().count(col) > 1 else col
                                    for i, col in enumerate(df.columns)]

                        # Convert table to JSON-compatible format
                        table_data = {
                            'paper_id': paper_id,
                            'table_index': index,
                            'data': df.to_dict(orient='records'),
                            'columns': df.columns.tolist(),
                            'created_at': datetime.now()
                        }

                        # Insert or update the table in MongoDB
                        result = tables_collection.update_one(
                            {'paper_id': paper_id, 'table_index': index},
                            {'$set': table_data},
                            upsert=True
                        )

                        if result.upserted_id:
                            table_ids.append(str(result.upserted_id))
                            print(f"Table {index} saved to MongoDB with ID {result.upserted_id}")
                        else:
                            print(f"Table {index} updated in MongoDB")
            except Exception as e:
                print(f"Error saving tables to MongoDB: {e}")

        # Save to local files if output_dir is provided
        if output_dir:
            output_dir = Path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
            for index, table in enumerate(tables):
                try:
                    df = table.df()
                    json_path = output_dir / f"table_{index}.json"
                    df.to_json(json_path, orient='records')
                    print(f"Table {index} saved to {json_path}")
                except Exception as e:
                    print(f"Error saving table {index} to file: {e}")

        return table_ids

def extract_tables(pdf_path: str, paper_id: str = None, output_dir: Optional[str] = None) -> tuple[List[dict], List[Any], List[str]]:
    """
    Extract tables from a PDF using GMFT and optionally save them to MongoDB.

    Args:
        pdf_path: Path to the PDF file
        paper_id: Optional paper ID to associate with the tables in MongoDB
        output_dir: Optional directory to save extracted tables as JSON files

    Returns:
        Tuple containing:
        - List of processed tables as dictionaries
        - List of raw table objects
        - List of MongoDB IDs for saved tables (if paper_id was provided)
    """
    extractor = GMFTExtractor(pdf_path)
    tables = extractor.extract_tables(save=False)  # Don't save here, we'll do it explicitly

    # Handle duplicate columns by renaming them
    processed_tables = []
    for table in tables:
        df = table.df()
        # Rename duplicate columns by appending _1, _2, etc.
        df.columns = [f"{col}_{i}" if df.columns.tolist().count(col) > 1 else col
                     for i, col in enumerate(df.columns)]
        processed_tables.append(df.to_dict(orient='records'))

    # Save tables to MongoDB and/or local files
    table_ids = []
    if paper_id or output_dir:
        table_ids = GMFTExtractor.save_tables(tables, paper_id=paper_id, output_dir=output_dir)

    return processed_tables, tables, table_ids


async def download_pdf(pdf_url: str, temp_dir: str = "temp_pdfs") -> Tuple[str, bool]:
    """
    Download a PDF from a URL and save it to a temporary file.

    Args:
        pdf_url: URL of the PDF to download
        temp_dir: Directory to save the temporary PDF file

    Returns:
        Tuple containing:
        - Path to the downloaded PDF file
        - Boolean indicating success or failure
    """
    # Create temp directory if it doesn't exist
    os.makedirs(temp_dir, exist_ok=True)

    # Generate a unique filename based on the URL
    filename = pdf_url.split('/')[-1]
    if not filename.endswith('.pdf'):
        filename += '.pdf'
    temp_path = os.path.join(temp_dir, filename)

    # Download the PDF
    timeout = aiohttp.ClientTimeout(total=120)  # 2 minutes timeout
    try:
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.get(pdf_url) as response:
                if response.status != 200:
                    print(f"Error downloading PDF: HTTP status {response.status}")
                    return None, False

                # Save the PDF to a temporary file
                pdf_content = await response.read()
                with open(temp_path, 'wb') as f:
                    f.write(pdf_content)

                return temp_path, True
    except asyncio.TimeoutError:
        print(f"Timeout downloading PDF from {pdf_url}")
        return None, False
    except Exception as e:
        print(f"Error downloading PDF: {e}")
        return None, False


async def extract_and_save_tables(abs_url: str, pdf_url: str = None, output_dir: Optional[str] = None) -> Tuple[bool, int, List[str]]:
    """
    Download a PDF from arXiv, extract tables, and save them to MongoDB.

    Args:
        abs_url: The arXiv abstract URL
        pdf_url: Optional PDF URL (will be derived from abs_url if not provided)
        output_dir: Optional directory to save extracted tables as JSON files

    Returns:
        Tuple containing:
        - Boolean indicating success or failure
        - Number of tables found
        - List of MongoDB IDs for saved tables
    """
    try:
        # Extract paper_id from abs_url
        paper_id = abs_url.split('/')[-1]

        # If pdf_url is not provided, derive it from abs_url
        if not pdf_url:
            pdf_url = f"https://arxiv.org/pdf/{paper_id}.pdf"

        # Download the PDF
        pdf_path, success = await download_pdf(pdf_url)
        if not success or not pdf_path:
            print(f"Failed to download PDF for {abs_url}")
            return False, 0, []

        try:
            # Extract tables from the PDF
            processed_tables, raw_tables, table_ids = extract_tables(
                pdf_path=pdf_path,
                paper_id=paper_id,
                output_dir=output_dir
            )

            # Update the summaries collection to indicate that tables have been extracted
            if len(raw_tables) > 0:
                collections = get_collections()
                if collections and 'summaries' in collections:
                    collections['summaries'].update_one(
                        {'abs_url': abs_url},
                        {'$set': {'has_tables': True, 'table_count': len(raw_tables)}}
                    )
                    print(f"Updated summaries collection for {abs_url} with table information")

            # Clean up the temporary PDF file
            if os.path.exists(pdf_path):
                os.remove(pdf_path)

            return True, len(raw_tables), table_ids

        except Exception as e:
            print(f"Error extracting tables from PDF: {e}")
            # Clean up the temporary PDF file
            if pdf_path and os.path.exists(pdf_path):
                os.remove(pdf_path)
            return False, 0, []

    except Exception as e:
        print(f"Error in extract_and_save_tables: {e}")
        return False, 0, []


async def update_tables_for_papers(summaries_collection, tables_collection, limit: int = 10):
    """
    Find papers that don't have tables extracted yet and extract tables from them.

    Args:
        summaries_collection: MongoDB collection for paper summaries
        tables_collection: MongoDB collection for tables
        limit: Maximum number of papers to process in one batch
    """
    try:
        print("Checking for papers needing table extraction...")

        # Find papers with has_full_text=True but no has_tables flag or has_tables=False
        papers_needing_tables = list(summaries_collection.find(
            {
                'has_full_text': True,
                '$or': [
                    {'has_tables': {'$exists': False}},
                    {'has_tables': False}
                ]
            },
            sort=[('published', -1)],
            limit=limit
        ))

        if papers_needing_tables:
            print(f"Found {len(papers_needing_tables)} papers needing table extraction")

            for paper in papers_needing_tables:
                abs_url = paper.get('abs_url')
                pdf_url = paper.get('pdf_url')

                if abs_url:
                    print(f"Extracting tables for {abs_url}")
                    success, table_count, _ = await extract_and_save_tables(abs_url, pdf_url)

                    # Update the paper record even if no tables were found
                    if success:
                        if table_count > 0:
                            print(f"Found {table_count} tables in {abs_url}")
                        else:
                            # Mark papers with no tables to avoid reprocessing
                            summaries_collection.update_one(
                                {'abs_url': abs_url},
                                {'$set': {'has_tables': False, 'table_count': 0}}
                            )
                            print(f"No tables found in {abs_url}")

                    # Add a delay to avoid overloading the system
                    await asyncio.sleep(2)
        else:
            print("No papers needing table extraction found")

    except Exception as e:
        print(f"Error updating tables for papers: {e}")
        import traceback
        traceback.print_exc()
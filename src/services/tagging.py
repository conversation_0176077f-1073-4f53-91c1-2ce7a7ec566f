import json
import os
from typing import List
from openai import OpenAI

from ..models.paper import Classification

# Tags used for paper classification
TAGS = [
    "Vision", "Audio", "Tabular", "Health", "Finance", "Education", 
    "Chemistry", "Physics", "Psychology", "Law", "Biology", 
    "Agents", "Reasoning", "Tool-Use", "AI Ethics", "Coding", "RAG", 
    "Efficiency", "Security", "Alignment", "Prompt Engineering", 
    "Robotics", "Graphs", "Explainable AI"
]

async def tagging_papers(text: str, client: OpenAI) -> List[str]:
    """
    Tag a paper with relevant categories
    
    Args:
        text (str): Text to analyze
        client (OpenAI): OpenAI client
        
    Returns:
        List[str]: List of applicable tags
    """
    chat_completion = client.beta.chat.completions.parse(
    model= os.getenv("GEMINI_MODEL"),
    messages=[{
            "role": "system",
            "content": f"""You are a helpful assistant with world-class expertise in AI research and application.
Your role is to classify research papers into categories based on their abstracts better than anyone else in the world. 
Use the following topics and only the following topics: {TAGS}."""
        }, {
            "role": "user",
            "content": f"""
            Use the following topics and only the following topics: "Vision", "Audio", "Tabular", "Health", "Finance", "Education", "Chemistry", "Physics", "Psychology", "Law", "Biology", 
            "Agents", "Reasoning", "Tool-Use", "AI Ethics", "Coding", "RAG", "Efficiency", "Security", "Alignment", "Prompt Engineering", "Robotics", "Graphs", "Explainable AI".

            You are allowed to use multiple topics for a single paper. Never choose a topic that you're not asbolutely certain about or you'll catch fire and potentially die.
            If none of these topics fits a description, output an empty json.

            Description: {text}

            Topic:
            """
        }],
    response_format=Classification,
)

    classifications = Classification(**json.loads(chat_completion.choices[0].message.content))
    return [tag.name for tag in classifications.tags if tag.name in TAGS]
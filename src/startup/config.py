#!/usr/bin/env python3
"""
Application configuration functions
"""
import os
import sys
from urllib.parse import urlencode
from openai import OpenAI
from google import genai
from dotenv import load_dotenv, find_dotenv


def setup_arxiv_url(local_mode=False):
    """
    Setup arXiv API URL
    
    Args:
        local_mode: Whether to use local mode for testing
        
    Returns:
        str: arXiv API URL
    """
    # API url setup
    params = {
        "search_query": "%22large%20language%20models%22",
        "start": 0,
        "max_results": 3 if local_mode else 100,  # Only get 3 papers in local mode
        "sortBy": "submittedDate",
        "sortOrder": "descending"
    }
    url = f'http://export.arxiv.org/api/query?{urlencode(params)}'
    return url


def setup_environment(local_mode=False):
    """
    Setup environment variables and configuration
    
    Args:
        local_mode: Whether to use local mode for testing
        
    Returns:
        tuple: (openai_client, genai_client, arxiv_url)
    """
    # Load environment variables
    load_dotenv(find_dotenv())
    
    # Add the current directory to the path so we can import from src
    sys.path.append(os.path.dirname(os.path.abspath(os.path.dirname(__file__))))
    
    # Set MongoDB URL based on local mode
    if local_mode:
        print("LOCAL TESTING MODE ENABLED")
        # Use local MongoDB URL if available, otherwise fall back to in-memory MongoDB
        mongo_url = os.environ.get("MONGO_URL_LOCAL", "")
        if mongo_url:
            print(f"Using local MongoDB at {mongo_url}")
        else:
            print("No MONGO_URL_LOCAL found, using in-memory MongoDB")
        
        # Use local Neo4j URL if available
        neo4j_url = os.environ.get("NEO4J_URL_LOCAL", "")
        if neo4j_url:
            print(f"Using local Neo4j at {neo4j_url}")
        else:
            print("No NEO4J_URL_LOCAL found, Neo4j operations will be limited")
    else:
        # Use production URLs for Neo4j
        os.environ.setdefault("NEO4J_URL", "bolt://shuttle.proxy.rlwy.net:43630")
    
    # Create OpenAI and Gemini clients
    openai_client = OpenAI(
        api_key=os.getenv("GEMINI_API_KEY"), 
        base_url=os.getenv("GEMINI_BASE_URL")
    )
    
    genai_client = genai.Client(api_key=os.getenv("GEMINI_API_KEY"))
    
    # Set Railway environment variables if we detect Railway-specific MongoDB URLs
    # This is used to determine which redirect URL to use for OAuth
    mongo_url = os.environ.get("MONGO_URL", "")
    if ("railway" in mongo_url.lower() or "rlwy" in mongo_url.lower()) and not os.environ.get("RAILWAY_ENVIRONMENT"):
        os.environ["RAILWAY_ENVIRONMENT"] = "production"
        print("Detected Railway deployment - setting RAILWAY_ENVIRONMENT=production")
    
    # Setup arXiv API URL
    arxiv_url = setup_arxiv_url(local_mode)
    
    return openai_client, genai_client, arxiv_url
#!/usr/bin/env python3
"""
Database initialization and connection functions
"""
import os
from pymongo import MongoClient
from pymongo.errors import ServerSelectionTimeoutError
from neo4j import GraphDatabase

from src.db import setup_mongo_client, setup_collections, create_indexes, set_collections
from src.db.neo4j import setup_neo4j_driver


def test_local_mongodb_connection(mongo_url):
    """
    Test connection to MongoDB before proceeding in local mode
    
    Args:
        mongo_url: The MongoDB connection URL to test
        
    Returns:
        bool: True if connection is successful, False otherwise
    """
    try:
        print(f"Testing connection to MongoDB at {mongo_url}...")
        test_client = MongoClient(
            mongo_url,
            connectTimeoutMS=5000,  # Lower timeout for testing
            serverSelectionTimeoutMS=5000,
            socketTimeoutMS=10000
        )
        
        # Test connection with ping
        result = test_client.admin.command('ping')
        if result.get('ok') == 1.0:
            print("Successfully connected to MongoDB")
            os.environ["MONGO_PRIVATE_URL"] = mongo_url
            return True
        else:
            print("MongoDB ping failed, falling back to in-memory MongoDB")
            # Don't set MONGO_PRIVATE_URL so in-memory DB will be used
            return False
    except ServerSelectionTimeoutError:
        print(f"Could not connect to MongoDB at {mongo_url}, falling back to in-memory MongoDB")
        # Don't set MONGO_PRIVATE_URL so in-memory DB will be used
        return False
    except Exception as e:
        print(f"Error testing MongoDB connection: {str(e)}, falling back to in-memory MongoDB")
        # Don't set MONGO_PRIVATE_URL so in-memory DB will be used
        return False


def setup_mongodb(local_mode):
    """
    Setup MongoDB client and collections
    
    Args:
        local_mode: Whether to use local MongoDB for testing
        
    Returns:
        dict: Dictionary of MongoDB collections
    """
    # Setup MongoDB
    mongo_client = setup_mongo_client(local_mode)
    collections = setup_collections(mongo_client)
    print("MongoDB collections loaded!")
    create_indexes(collections)
    # Store collections in global state for access from UI components
    set_collections(collections)
    
    return collections


def test_neo4j_connection(driver=None, local_mode=False):
    """
    Test Neo4j connection using the driver
    
    Args:
        driver: Neo4j driver instance
        local_mode: Whether to use local Neo4j for testing
        
    Returns:
        bool: True if connection is successful or in-memory driver is used, False otherwise
    """
    try:
        print("Testing Neo4j connection...")
        
        # Test the connection
        with driver.session() as session:
            result = session.run("RETURN 'Connection successful' AS message")
            message = result.single()["message"]
            print(f"Neo4j connection test: {message}")
            
            # Check if there are any nodes
            result = session.run("MATCH (n) RETURN count(n) AS node_count")
            node_count = result.single()["node_count"]
            print(f"Node count in Neo4j database: {node_count}")
            
        return True
    except Exception as e:
        print(f"Neo4j connection error: {e}")
        # Check if we're using an in-memory driver
        if hasattr(driver, 'get_graph_data'):
            print("Using in-memory Neo4j implementation")
            return True
        elif local_mode:
            print("Neo4j operations will use in-memory implementation in local mode")
        return False


def setup_database_connections(local_mode):
    """
    Setup all database connections
    
    Args:
        local_mode: Whether to use local databases for testing
        
    Returns:
        tuple: (collections, neo4j_driver)
    """
    # Setup Neo4j driver
    neo4j_driver = setup_neo4j_driver(local_mode)
    
    # Run the Neo4j connection test
    test_neo4j_connection(neo4j_driver, local_mode)
    
    # Setup MongoDB
    collections = setup_mongodb(local_mode)
    
    return collections, neo4j_driver


def is_neo4j_available(local_mode=False):
    """
    Check if Neo4j is available in the current mode
    
    Args:
        local_mode: Whether to use local Neo4j for testing
        
    Returns:
        bool: True if Neo4j is available, False otherwise
    """
    if local_mode:
        # Check if NEO4J_URL_LOCAL is set
        neo4j_url = os.environ.get("NEO4J_URL_LOCAL")
        if not neo4j_url:
            return False
        
        # Try to connect to Neo4j to verify it's available
        try:
            driver = GraphDatabase.driver(neo4j_url, auth=("neo4j", "neo4j"))
            with driver.session() as session:
                result = session.run("RETURN 'Connection successful' AS message")
                message = result.single()["message"]
                driver.close()
                return True
        except Exception:
            return False
    return True  # Assume always available in production mode
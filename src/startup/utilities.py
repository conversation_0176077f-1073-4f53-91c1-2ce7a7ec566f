#!/usr/bin/env python3
"""
Utility functions for startup
"""
import os
from neo4j import GraphDatabase


def get_knowledge_graph_data(neo4j_driver, limit=100):
    """
    Query Neo4j for knowledge graph data to visualize.
    Returns data formatted for D3.js visualization.
    
    Args:
        neo4j_driver: Neo4j driver instance
        limit: Maximum number of nodes to retrieve. Using a large number (e.g., 5000)
               will attempt to get the full graph for pruning locally.
               
    Returns:
        dict: Formatted graph data with nodes and links
    """
    try:
        # Get the global Neo4j driver (could be real or in-memory)
        driver = neo4j_driver
        
        # Check if we have an in-memory driver with direct graph access
        if hasattr(driver, 'get_graph_data'):
            # Use the optimized method for in-memory graphs
            return driver.get_graph_data()
        
        # Otherwise, use standard Neo4j queries
        with driver.session() as session:
            # Query for nodes - for large limits, add node importance criteria
            node_query = """
            MATCH (n)
            WITH n, COUNT {(n)--()} AS degree
            ORDER BY degree DESC
            RETURN n.id AS id, n.name AS name, labels(n) AS labels, degree
            LIMIT $limit
            """
            nodes_result = session.run(node_query, limit=limit)
            nodes = []
            
            # Process node results into a format suitable for visualization
            for record in nodes_result:
                node_id = record["id"]
                if not node_id:
                    continue
                    
                # Determine color based on node label
                label = record["labels"][0] if record["labels"] else "Entity"
                color = "#4b7bec"  # Default blue color
                
                if label == "Paper":
                    color = "#ff6b6b"  # Red for papers
                elif label in ["Model", "Algorithm", "Method"]:
                    color = "#1dd1a1"  # Green for models/algorithms
                elif label in ["Dataset", "Benchmark"]:
                    color = "#feca57"  # Yellow for datasets
                
                nodes.append({
                    "id": node_id,
                    "name": record["name"] or node_id,
                    "label": label,
                    "color": color,
                    "degree": record["degree"]  # Include degree for local filtering
                })
            
            # Get node IDs to limit relationship query
            node_ids = [node["id"] for node in nodes]
            
            # Query for relationships between retrieved nodes 
            # For large limits, we get connections between important nodes
            rel_query = """
            MATCH (a)-[r]->(b)
            WHERE a.id IN $node_ids AND b.id IN $node_ids
            AND a.id IS NOT NULL AND b.id IS NOT NULL
            RETURN a.id AS source, b.id AS target, type(r) AS type
            """
            rel_result = session.run(rel_query, node_ids=node_ids)
            links = []
            
            # Process relationship results
            for record in rel_result:
                links.append({
                    "source": record["source"],
                    "target": record["target"],
                    "type": record["type"]
                })
            
            # Return formatted graph data
            return {
                "nodes": nodes,
                "links": links
            }
    except Exception as e:
        print(f"Error querying Neo4j: {e}")
        import traceback
        traceback.print_exc()
        return {"nodes": [], "links": []}
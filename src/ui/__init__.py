"""
UI Components Module
"""
# Navigation components
from .navigation import Navigation, NavButton

# Paper display components
from .paper import Paper

# Search components
from .search import SearchForm

# Stats components
from .stats import StatsCard

# Entity components
from .entities import Entities, RatingDisplay

# Common utilities
from .common import get_star_rating_html, highlight_entities_in_summary

# Button components
from .buttons import SaveButton

# Metadata components
from .metadata import DateDisplay, SimilarityScore, PaperTitle, PaperSummary

# Tag components
from .tags import Tag, TagsContainer

# Table components
from .tables import TablesSection, TableDisplay

__all__ = [
    # Navigation
    'Navigation',
    'NavButton',

    # Paper display
    'Paper',

    # Search
    'SearchForm',

    # Stats
    'StatsCard',

    # Entities
    'Entities',
    'RatingDisplay',

    # Common utilities
    'get_star_rating_html',
    'highlight_entities_in_summary',

    # Button components
    'SaveButton',

    # Metadata components
    'DateDisplay',
    'SimilarityScore',
    'PaperTitle',
    'PaperSummary',

    # Tag components
    'Tag',
    'TagsContainer',

    # Table components
    'TablesSection',
    'TableDisplay'
]
"""
Button UI components
"""
import json
from typing import Optional

try:
    from fasthtml.common import *
    from monsterui.all import *
except ImportError:
    # Create placeholders for testing
    def Div(*args, **kwargs): return None
    def A(*args, **kwargs): return None
    def Span(*args, **kwargs): return None
    def Img(*args, **kwargs): return None
    def Script(*args, **kwargs): return None


def MermaidToggleButton(abs_url: str, mermaid_data: str = None):
    """
    Create a toggle button for displaying a mermaid diagram inline as an expandable accordion section
    
    Args:
        abs_url: The URL of the paper
        mermaid_data: Optional pre-loaded mermaid diagram data
        
    Returns:
        Div: FastHTML div component
    """
    paper_id = abs_url.split('/')[-1]
    button_id = f"mermaid-toggle-{paper_id}"
    content_id = f"mermaid-content-{paper_id}"
    
    #print('Mermaid data received:', mermaid_data)

    return Div(
        Div(
            Div(
                Img(
                    src="/assets/accordion-plus-icon.svg",
                    cls="w-5 h-5 transition-opacity",
                    id=f"mermaid-plus-icon-{paper_id}",
                    style="filter: invert(75%);"
                ),
                Img(
                    src="/assets/accordion-minus-icon.svg",
                    cls="w-5 h-5 transition-opacity hidden",
                    id=f"mermaid-minus-icon-{paper_id}",
                    style="filter: invert(75%);"
                ),
                cls="flex items-center"
            ),
            cls="flex hover:text-white cursor-pointer transition-all rounded-full p-2 hover:bg-gray-700/30",
            id=button_id,
            title="Toggle Mermaid Diagram"
        ),
        Div(
            id=content_id, 
            style="display: none;",
            cls="p-4 mt-4 bg-gray-800 rounded-lg"
        ),
        Script(f"""
        console.log('Mermaid toggle script started'); 
        // Use a function that works regardless of when it's executed
        (function initMermaidToggle() {{
            const toggleButton = document.getElementById('{button_id}');
            const contentDiv = document.getElementById('{content_id}');
            const plusIcon = document.getElementById('mermaid-plus-icon-{paper_id}');
            const minusIcon = document.getElementById('mermaid-minus-icon-{paper_id}');
            
            if (!toggleButton || !contentDiv) {{
                // Elements not ready yet, try again soon
                if (document.readyState !== 'complete') {{
                    console.log('Document not ready, waiting to initialize mermaid toggle');
                    window.addEventListener('load', initMermaidToggle);
                    return;
                }}
                console.log('Elements not found for mermaid toggle, retrying in 100ms');
                setTimeout(initMermaidToggle, 100);
                return;
            }}
            
            if (toggleButton && contentDiv) {{
                toggleButton.addEventListener('click', function() {{
                    // Toggle visibility
                    if (contentDiv.style.display === 'none' || contentDiv.style.display === '') {{
                        contentDiv.style.display = 'block';
                        plusIcon.classList.add('hidden');
                        minusIcon.classList.remove('hidden');
                        toggleButton.classList.add('bg-gray-700/30');
                        
                        // Check if mermaid needs to be loaded/rendered
                        if (!contentDiv.dataset.loaded) {{
                            contentDiv.innerHTML = '<div class="p-4 text-center">Loading diagram...</div>';
                            
                            // Prepare rendering function
                            const renderMermaid = (diagramCode) => {{
                                // Create the mermaid container with the diagram code and add custom styling
                                // Convert LR to TD orientation if needed
                                const convertedCode = diagramCode.replace(/graph\s+LR/g, 'graph TD');
                                contentDiv.innerHTML = `<div class="mermaid" style="width: 100%; min-height: 300px; display: flex; justify-content: center;">${{convertedCode}}</div>`;
                                
                                const initMermaid = () => {{
                                    try {{
                                        if (window.mermaid) {{
                                            console.log("Initializing mermaid with config");
                                            // Initialize mermaid with proper configuration
                                            window.mermaid.initialize({{ 
                                                startOnLoad: false, 
                                                theme: 'dark',
                                                securityLevel: 'loose',
                                                // Add font size and scaling options
                                                fontSize: 16,
                                                flowchart: {{
                                                    htmlLabels: true,
                                                    curve: 'basis',
                                                    useMaxWidth: false,
                                                    diagramPadding: 20
                                                }}
                                            }});
                                            
                                            // Make sure DOM is completely ready
                                            const mermaidElements = contentDiv.querySelectorAll('.mermaid');
                                            console.log('Found ' + mermaidElements.length + ' mermaid elements to render');
                                            
                                            // Small delay to ensure DOM is ready
                                            setTimeout(() => {{
                                                console.log("Attempting to render mermaid diagram");
                                                try {{
                                                    window.mermaid.run();
                                                    console.log("Mermaid diagram rendered successfully");
                                                    
                                                    // Apply additional styling to SVG after rendering
                                                    setTimeout(() => {{
                                                        const svgElements = contentDiv.querySelectorAll('svg');
                                                        svgElements.forEach(svg => {{
                                                            svg.style.maxWidth = '100%';
                                                            svg.style.height = 'auto';
                                                            svg.style.minHeight = '300px';
                                                        }});
                                                    }}, 100);
                                                }} catch (runErr) {{
                                                    console.error("Error during mermaid.run():", runErr);
                                                    contentDiv.innerHTML = `<div class="p-4 text-center text-red-500">Error rendering diagram: ${{runErr.message}}</div>`;
                                                }}
                                            }}, 200);
                                        }} else {{
                                            console.error("Mermaid library not available");
                                            contentDiv.innerHTML = `<div class="p-4 text-center text-red-500">Mermaid library failed to load</div>`;
                                        }}
                                    }} catch (err) {{
                                        console.error("Error rendering mermaid diagram:", err);
                                        contentDiv.innerHTML = `<div class="p-4 text-center text-red-500">Error rendering diagram: ${{err.message}}</div>`;
                                    }}
                                }};
                                
                                // Load mermaid if needed, then initialize
                                if (!window.mermaid) {{
                                    console.log("Loading mermaid library");
                                    const script = document.createElement('script');
                                    script.src = 'https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js';
                                    script.onload = () => {{
                                        console.log("Mermaid library loaded successfully");
                                        // Short delay to ensure library is fully initialized
                                        setTimeout(initMermaid, 100);
                                    }};
                                    script.onerror = (err) => {{
                                        console.error("Failed to load mermaid library:", err);
                                        contentDiv.innerHTML = `<div class="p-4 text-center text-red-500">Failed to load mermaid library</div>`;
                                    }};
                                    document.head.appendChild(script);
                                }} else {{
                                    console.log("Mermaid library already loaded");
                                    initMermaid();
                                }}
                            }};
                            
                            // Get diagram data (preloaded or from API)
                            // Using properly stringified and quoted data
                            var mermaid_data = {json.dumps(mermaid_data) if mermaid_data is not None else 'null'};
                            console.log('mermaid_data defined as:', mermaid_data);
                            const preloadedData = mermaid_data;
                            console.log('preloadedData type:', typeof preloadedData);
                            
                            if (preloadedData) {{
                                renderMermaid(preloadedData);
                                contentDiv.dataset.loaded = 'true';
                            }} else {{
                                // Fetch from API
                                console.log(`Fetching mermaid data for paper ID: {paper_id}`);
                                fetch('/api/mermaid/{paper_id}')
                                    .then(response => {{
                                        console.log('Response status:', response.status);
                                        return response.json();
                                    }})
                                    .then(data => {{
                                        console.log(`API data received:`, data);
                                        if (data.status === 'success' && data.diagram) {{
                                            // Debug the received diagram
                                            console.log('Received diagram code:', data.diagram);
                                            renderMermaid(data.diagram);
                                            contentDiv.dataset.loaded = 'true';
                                        }} else {{
                                            console.error('Failed response:', data);
                                            contentDiv.innerHTML = `<div class="p-4 text-center text-red-500">Failed to load diagram: ${{data.message || 'Unknown error'}}</div>`;
                                        }}
                                    }})
                                    .catch(error => {{
                                        console.error('Error fetching mermaid diagram:', error);
                                        contentDiv.innerHTML = `<div class="p-4 text-center text-red-500">Error loading diagram: ${{error.message || 'Network error'}}</div>`;
                                    }});
                            }}
                        }}
                    }} else {{
                        contentDiv.style.display = 'none';
                        plusIcon.classList.remove('hidden');
                        minusIcon.classList.add('hidden');
                        toggleButton.classList.remove('bg-gray-700/30');
                    }}
                }});
            }}
        }})();
        """),
        cls="flex items-center mr-6 w-full"  # Added margin to separate buttons
    )


def MermaidButton(abs_url: str):
    """
    Create a button linking to the mermaid diagram for a paper
    
    Args:
        abs_url: The URL of the paper
        
    Returns:
        Div: FastHTML div component
    """
    return Div(
        A(
            Img(
                src="/assets/icons/stack/hugging-face.svg",
                cls="w-6 h-6 hover:opacity-80 transition-opacity",
                style="filter: invert(75%);"
            ),
            href=f"/mermaid/{abs_url.split('/')[-1]}",
            cls="flex items-center justify-center p-2 rounded-full hover:bg-blue-500/10 active:bg-blue-500/20 transition-colors",
            title="View Mermaid Diagram"
        ),
        cls="flex items-center"
    )


def SaveButton(abs_url: str, title: str, is_saved: bool = False):
    """
    Create a save/bookmark button for a paper
    
    Args:
        abs_url: The URL of the paper
        title: The title of the paper
        is_saved: Whether the paper is already saved
        
    Returns:
        Div: FastHTML div component
    """
    return Div(
        # Paper bookmark icon only
        Img(
            src="/assets/icons/bookmark_button.svg",
            cls=f'w-12 h-12 sm:w-14 sm:h-14 transition-all duration-200',
            style=f'{"filter: invert(43%) sepia(95%) saturate(1731%) hue-rotate(199deg) brightness(101%) contrast(101%);" if is_saved else "filter: invert(100%);"}',
            id=f"saveIcon-{abs_url.replace('/', '_')}",
            alt="Bookmark" if not is_saved else "Bookmarked"
        ),
        SaveButtonScript(abs_url, title, is_saved),
        cls=f'flex items-center justify-center cursor-pointer rounded-full hover:bg-blue-500/10 active:bg-blue-500/20 transition-colors p-4 sm:p-5 relative min-w-[72px] min-h-[72px] w-[72px] h-[72px] sm:min-w-[76px] sm:min-h-[76px] sm:w-[76px] sm:h-[76px] border border-blue-500/30',
        id=f"save-button-{abs_url.replace('/', '_')}",
    )


def SaveButtonScript(abs_url: str, title: str, is_saved: bool):
    """
    Create the interactivity script for the save button
    
    Args:
        abs_url: The URL of the paper
        title: The title of the paper
        is_saved: Whether the paper is already saved
        
    Returns:
        Script: FastHTML script component
    """
    return Script("""
    document.addEventListener('DOMContentLoaded', function() {
        const saveButton = document.getElementById('save-button-""" + abs_url.replace('/', '_') + """');
        const isSaved = """ + json.dumps(is_saved) + """;
        
        if (saveButton) {
            saveButton.addEventListener('click', function() {
                const saveIcon = this.querySelector('[id^="saveIcon"]');
                // Check if saved by looking at the filter style instead of class name
                // Blue filter indicates saved, white (invert(100%)) indicates not saved
                const currentlySaved = saveIcon.style.filter.includes('hue-rotate(199deg)');
                
                console.log('Current saved state:', currentlySaved, saveIcon.style.filter);
                
                // Switch to a loading indicator
                saveIcon.classList.add('animate-pulse');
                
                try {
                    // Use proper double encoding to prevent issues with special characters
                    const title = """ + json.dumps(title) + """;
                    const abs_url = """ + json.dumps(abs_url) + """;
                    
                    // Explicitly handle encoding separately to avoid issues
                    const encodedTitle = encodeURIComponent(title);
                    const encodedUrl = encodeURIComponent(abs_url);
                    
                    // Choose endpoint based on current state
                    const endpoint = currentlySaved ? '/unsave_paper?abs_url=' + encodedUrl : '/save_paper?title=' + encodedTitle + '&abs_url=' + encodedUrl;
                    
                    console.log(currentlySaved ? 'Unsaving paper' : 'Saving paper');
                    console.log('URL:', abs_url);
                    
                    // Create and dispatch a custom event before fetch to allow other handlers to respond immediately
                    if (currentlySaved) {
                        // Paper is being unsaved
                        const unsaveEvent = new CustomEvent('paperUnsaved', {
                            bubbles: true,
                            detail: { absUrl: abs_url }
                        });
                        this.dispatchEvent(unsaveEvent);
                    }
                    
                    fetch(endpoint)
                      .then(response => {
                        console.log('Response received:', response.status);
                        return response.json();
                      })
                      .then(data => {
                        console.log('Data received:', data);
                        if (data.status === 'success') {
                          // Show success state
                          saveIcon.classList.remove('animate-pulse');
                          
                          // Toggle the saved state visually
                          if (currentlySaved) {
                            // Unsaved state
                            saveIcon.style.filter = 'invert(100%)';
                            
                            // Also update the text if it exists
                            const saveText = document.getElementById('saveText-' + """ + json.dumps(abs_url.replace('/', '_')) + """);
                            if (saveText) {
                              saveText.textContent = 'Save';
                              saveText.classList.remove('text-blue-500', 'font-medium');
                              saveText.classList.add('text-gray-400');
                            }
                          } else {
                            // Saved state
                            saveIcon.style.filter = 'invert(43%) sepia(95%) saturate(1731%) hue-rotate(199deg) brightness(101%) contrast(101%)';
                            
                            // Also update the text if it exists
                            const saveText = document.getElementById('saveText-' + """ + json.dumps(abs_url.replace('/', '_')) + """);
                            if (saveText) {
                              saveText.textContent = 'Saved';
                              saveText.classList.remove('text-gray-400');
                              saveText.classList.add('text-blue-500', 'font-medium');
                            }
                          }
                          
                          // Visual feedback for action success
                          saveIcon.classList.add('scale-125');
                          if (!currentlySaved) {
                            // Only add shadow effect when saving, not unsaving
                            saveIcon.style.filter = 'invert(43%) sepia(95%) saturate(1731%) hue-rotate(199deg) brightness(101%) contrast(101%) drop-shadow(0 0 4px rgba(59, 130, 246, 0.7))';
                          }
                          setTimeout(() => { 
                            saveIcon.classList.remove('scale-125');
                            if (currentlySaved) {
                              // Make sure we maintain the unsaved state (white color)
                              saveIcon.style.filter = 'invert(100%)';
                            } else {
                              // Make sure we maintain the saved state (blue color)
                              saveIcon.style.filter = 'invert(43%) sepia(95%) saturate(1731%) hue-rotate(199deg) brightness(101%) contrast(101%)';
                            }
                          }, 500);
                        } else {
                          console.error('Failed to save/unsave:', data.message);
                          saveIcon.classList.remove('animate-pulse');
                          
                          // Show error message but don't use alert
                          const errorMsg = document.createElement('div');
                          errorMsg.className = 'absolute bottom-0 right-0 bg-red-500 text-white px-2 py-1 text-sm rounded-md';
                          errorMsg.textContent = data.message;
                          this.appendChild(errorMsg);
                          
                          // Remove error message after 3 seconds
                          setTimeout(() => {
                            errorMsg.remove();
                          }, 3000);
                        }
                      })
                      .catch(error => {
                        console.error('Error:', error);
                        saveIcon.classList.remove('animate-pulse');
                        
                        // Show error message
                        const errorMsg = document.createElement('div');
                        errorMsg.className = 'absolute bottom-0 right-0 bg-red-500 text-white px-2 py-1 text-sm rounded-md';
                        errorMsg.textContent = 'Network error';
                        this.appendChild(errorMsg);
                        
                        // Remove error message after 3 seconds
                        setTimeout(() => {
                          errorMsg.remove();
                        }, 3000);
                      });
                } catch (e) {
                    console.error('Error in save/unsave handler:', e);
                    saveIcon.classList.remove('animate-pulse');
                }
            });
        }
    });
    """)
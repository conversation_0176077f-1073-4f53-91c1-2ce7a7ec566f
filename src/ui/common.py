"""
Common UI utility functions and components
"""
from typing import Optional

try:
    from fasthtml.common import *
    from monsterui.all import *
except ImportError:
    # Create placeholders for testing
    def Div(*args, **kwargs): return None
    def A(*args, **kwargs): return None
    def Nav(*args, **kwargs): return None
    def Form(*args, **kwargs): return None
    def Input(*args, **kwargs): return None
    def Button(*args, **kwargs): return None
    def H1(*args, **kwargs): return None
    def H2(*args, **kwargs): return None
    def H3(*args, **kwargs): return None
    def P(*args, **kwargs): return None
    def Span(*args, **kwargs): return None
    def Img(*args, **kwargs): return None
    def Script(*args, **kwargs): return None

def get_star_rating_html(value: float, max_value: int = 4) -> str:
    """
    Generate HTML for star rating display
    
    Args:
        value (float): Rating value
        max_value (int): Maximum rating value
        
    Returns:
        str: HTML string with star characters
    """
    full_stars = int(value)
    half_star = value - full_stars >= 0.5
    empty_stars = max_value - full_stars - (1 if half_star else 0)
    
    stars_html = ""
    # Add full stars
    for _ in range(full_stars):
        stars_html += "★"
    
    # Add half star if needed
    if half_star:
        stars_html += "☆"
    
    # Add empty stars
    for _ in range(empty_stars):
        stars_html += "☆"
    
    return stars_html

def highlight_entities_in_summary(summary, entities_list, abs_url):
    """
    Process the summary text to highlight entity mentions.
    Wraps entity mentions with spans that show entity information on click.
    
    Args:
        summary: Text summary to process
        entities_list: List of entities to highlight (can be strings, dicts with 'value' key, or tuples)
        abs_url: Paper URL for entity lookup
        
    Returns:
        str or Component: Processed text with highlighted entities
    """
    if not summary or not entities_list:
        return summary
        
    # Extract entity names from the entities list (handle different entity formats)
    entity_names = []
    
    for entity_item in entities_list:
        # Handle different entity formats
        if isinstance(entity_item, dict) and "value" in entity_item:
            # Standard dictionary format
            entity_names.append(entity_item["value"])
        elif isinstance(entity_item, tuple) or isinstance(entity_item, list) and len(entity_item) > 0:
            # List/tuple format [value, category]
            entity_names.append(entity_item[0])
        elif isinstance(entity_item, str):
            # Simple string format
            entity_names.append(entity_item)
    
    # Sort entities by length (descending) to prevent nested replacements
    entity_names = sorted(entity_names, key=len, reverse=True)
    
    # If no valid entities to process, return the original summary
    if not entity_names:
        return summary
        
    # Create styled spans for all entities
    parts = []
    last_pos = 0
    
    # Use a regex to find all entity matches
    import re
    
    # Build a combined regex pattern for all entities
    entity_patterns = []
    for entity in entity_names:
        if len(entity) >= 3:  # Skip very short entities
            entity_patterns.append(r'\b' + re.escape(entity) + r'\b')
    
    if not entity_patterns:
        return summary
        
    combined_pattern = '|'.join(entity_patterns)
    matches = list(re.finditer(combined_pattern, summary, re.IGNORECASE))
    
    if not matches:
        return summary
    
    # Process each match and build parts list
    for match in matches:
        start, end = match.span()
        entity_text = match.group(0)
        
        # Add text before this entity
        if start > last_pos:
            parts.append(summary[last_pos:start])
        
        # Add the entity with span highlighting
        span_props = {
            "cls": "entity-highlight",
            "style": "background-color: rgba(117, 117, 240, 0.15); border-bottom: 1px dashed #7575F0; cursor: pointer; position: relative;",
            "data-entity": entity_text,
            "data-abs-url": abs_url,
        }
        parts.append(Span(entity_text, **span_props))
        
        last_pos = end
    
    # Add any remaining text after the last entity
    if last_pos < len(summary):
        parts.append(summary[last_pos:])
    
    # This avoids the string representation issues
    return P(*parts)
    
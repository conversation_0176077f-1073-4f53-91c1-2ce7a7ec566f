"""
Deprecated: This file is kept for backward compatibility.
All components have been moved to specific modules.
Please import from the appropriate module instead.
"""
from typing import Optional, <PERSON><PERSON>

try:
    from fasthtml.common import *
    from monsterui.all import *
except ImportError:
    # Create placeholders for testing
    def Div(*args, **kwargs): return None
    def Script(*args, **kwargs): return None

# Import directly from modules to avoid circular imports
from src.ui.navigation import Navigation, NavButton
from src.ui.search import SearchForm
from src.ui.stats import StatsCard
from src.ui.entities import Entities, RatingDisplay
from src.ui.common import get_star_rating_html, highlight_entities_in_summary
from src.ui.buttons import MermaidToggleButton

def MermaidSection(abs_url: str, mermaid_data: Optional[str] = None) -> Tuple:
    """
    Create a mermaid diagram section for a paper
    
    Args:
        abs_url: Paper URL
        mermaid_data: Mermaid diagram data if available
        
    Returns:
        tuple: Div and Script components for the mermaid section
    """
    mermaid_container = Div(
        # Only the toggle button with no text
        MermaidToggleButton(
            abs_url,
            # Look up the mermaid diagram data if it exists
            mermaid_data=mermaid_data
        ),
        # Mermaid content container (hidden by default)
        Div(
            # Content will be loaded via JavaScript when toggled
            "",
            id=f"mermaid-content-{abs_url.split('/')[-1]}",
            cls="w-full p-6 bg-gray-800/50 rounded-lg overflow-auto transition-all border border-gray-700",
            style="display: none;"
        ),
        cls="w-full mt-6"
    )
    
    mermaid_script = Script("""
    if (!window.mermaid) {
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js';
        script.onload = function() {
            window.mermaid.initialize({ 
                startOnLoad: true, 
                theme: 'dark',
                securityLevel: 'loose',
                logLevel: 5, // Silence logging
                fontFamily: "Geist, sans-serif"
            });
        };
        document.head.appendChild(script);
    }
    """)
    
    return mermaid_container, mermaid_script
"""
Entity UI components
"""
from typing import List, Optional, Dict, Any, Union

try:
    from fasthtml.common import *
    from monsterui.all import *
except ImportError:
    # Create placeholders for testing
    def Div(*args, **kwargs): return None
    def A(*args, **kwargs): return None

from src.utils.entity_dictionary import get_entity_dictionary_manager


def Entities(entities_list: Union[List[str], List[Dict], List[tuple]], selected_entity: Optional[str] = None, normalize: bool = False):
    """
    Create an entities display component with optional normalization
    
    Args:
        entities_list (List): List of entity strings, dicts, or tuples (name, category)
        selected_entity (Optional[str]): Currently selected entity
        normalize (bool): Whether to normalize entities using the entity dictionary
        
    Returns:
        Div: FastHTML div component
    """
    # Guard against None or empty list
    if not entities_list:
        return Div(cls='flex flex-wrap gap-2.5 mt-4')
    
    # Get the entity dictionary manager for normalization
    entity_dict_manager = get_entity_dictionary_manager() if normalize else None
    
    # Create a list to hold the normalized entity components
    entity_components = []
    seen_canonical_ids = set()  # Track seen canonical IDs to prevent duplicates
    
    # For debugging
    entity_formats = set()
    
    # Process each entity
    for entity_item in entities_list:
        # Handle different entity formats
        entity_category = "Uncategorized"
        if isinstance(entity_item, dict) and 'value' in entity_item:
            entity_name = entity_item.get('value', '')
            entity_category = entity_item.get('category', entity_category)
            entity_formats.add("dict")
        elif isinstance(entity_item, (tuple, list)) and len(entity_item) > 0:
            entity_name = entity_item[0]
            if len(entity_item) > 1:
                entity_category = entity_item[1]
            entity_formats.add("tuple/list")
        elif isinstance(entity_item, str):
            entity_name = entity_item
            entity_formats.add("string")
        else:
            # Skip invalid entity formats
            print(f"Skipping invalid entity format: {type(entity_item)}")
            continue
            
        # Skip empty entity names
        if not entity_name:
            continue
            
        # If normalization is enabled, look up the entity in the dictionary
        display_name = entity_name
        canonical_id = None
        
        if normalize and entity_dict_manager:
            try:
                entity_info = entity_dict_manager.lookup_entity(entity_name)
                if entity_info:
                    canonical_id = entity_info.get('canonical_id')
                    # Only add this entity if we haven't seen its canonical ID yet
                    if canonical_id in seen_canonical_ids:
                        continue  # Skip duplicates
                    
                    # Use canonical name for display
                    display_name = entity_info.get('canonical_name', entity_name)
                    seen_canonical_ids.add(canonical_id)
            except Exception as e:
                # Log the exception but continue with original entity name
                print(f"Entity normalization error for '{entity_name}': {str(e)}")
                # Continue with original entity name (already set)
                pass
        
        # Always use the original entity name if display_name is empty
        if not display_name:
            display_name = entity_name
            
        # Add the component with the appropriate display name
        entity_components.append(
            A(display_name,
              cls=f"tag text-sm sm:text-base {'tag-biology' if display_name == selected_entity else 'tag-biology'} cursor-pointer rounded-full px-3 sm:px-4 py-1.5 sm:py-2 hover:shadow-md transition-all duration-200 transform hover:-translate-y-0.5",
              style="color: #FFFFFF; text-decoration: none; user-select: none; background-color: #3b82f6;",
              onclick=f"window.location.href='/entity/{display_name}'"
            )
        )
    
    # Debug info for entity formats
    if entity_formats:
        print(f"Entity formats detected: {', '.join(entity_formats)}")
    
    # Return the container with the entity components
    return Div(  # Entities container
        *entity_components,
        cls='flex flex-wrap gap-2.5 mt-4'
    )


def RatingDisplay(rating):
    """
    Generate a component to display paper ratings
    
    Args:
        rating: Rating data
        
    Returns:
        Div: FastHTML div component
    """
    # Return an empty div with no content to effectively remove ratings from the feed
    return Div(cls="hidden")
"""
Metadata UI components for displaying paper information
"""
from typing import Optional, Union

try:
    from fasthtml.common import *
    from monsterui.all import *
except ImportError:
    # Create placeholders for testing
    def Div(*args, **kwargs): return None
    def P(*args, **kwargs): return None
    def H2(*args, **kwargs): return None
    def A(*args, **kwargs): return None


def DateDisplay(formatted_date: str):
    """
    Create a component for displaying the publication date
    
    Args:
        formatted_date: Formatted date string
        
    Returns:
        Div: FastHTML div component
    """
    return Div(
        P(formatted_date,
          cls="text-xl sm:text-2xl md:text-3xl 2xl:text-2xl text-gray-400 font-medium mb-2 sm:mb-3 2xl:mb-2"
        ),
    )


def SimilarityScore(similarity_value: Union[int, float, str]):
    """
    Create a component for displaying similarity score
    
    Args:
        similarity_value: Similarity score (0-100)
        
    Returns:
        Div: FastHTML div component
    """
    return Div(
        P(f"Similarity: {similarity_value}%",
          cls="text-xl sm:text-2xl md:text-3xl 2xl:text-2xl text-green-400 font-medium mb-2 sm:mb-3 2xl:mb-2"
        ),
    )


def PaperTitle(title: str, abs_url: str):
    """
    Create a component for displaying the paper title with link
    
    Args:
        title: Paper title
        abs_url: URL to the paper
        
    Returns:
        H2: FastHTML h2 component
    """
    return H2(
        A(title, 
          href=abs_url,
          cls="text-2xl sm:text-3xl md:text-4xl 2xl:text-2xl font-semibold text-white hover:text-gray-400 transition-colors duration-200 leading-tight"
        ),
        cls="mb-3 sm:mb-5 2xl:mb-4"  # Reduced spacing
    )


def PaperSummary(summary_content):
    """
    Create a component for displaying the paper summary
    
    Args:
        summary_content: Content of the summary (may be string or P component with highlighted entities)
        
    Returns:
        Div: FastHTML div component
    """
    # Add the summary to a container with proper styling
    if isinstance(summary_content, str):
        # For plain text summary, wrap in a P tag
        content = P(summary_content, raw=True)
    else:
        # For component summary (already a P tag with entities), use directly
        content = summary_content
        
    return Div(
        content,
        cls="text-2xl sm:text-3xl md:text-4xl 2xl:text-2xl text-gray-400 leading-relaxed mb-5 sm:mb-6 2xl:mb-5"  # Reduced spacing
    )
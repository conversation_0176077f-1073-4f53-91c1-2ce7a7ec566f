"""
Navigation UI components
"""
from typing import Optional

try:
    from fasthtml.common import *
    from monsterui.all import *
except ImportError:
    # Create placeholders for testing
    def Div(*args, **kwargs): return None
    def A(*args, **kwargs): return None
    def Nav(*args, **kwargs): return None


def NavButton(text: str, href: str):
    """
    Create a navigation button component
    
    Args:
        text (str): Button text
        href (str): Button link URL
        
    Returns:
        Div: FastHTML div component
    """
    return Div(
        A(text, 
          href=href,
          cls="px-4 sm:px-6 md:px-7 py-2 sm:py-4 rounded-lg hover:text-gray-400 transition-colors duration-200 text-white font-medium text-base sm:text-2xl md:text-3xl 2xl:text-xl"
        ),
        cls="cursor-pointer"
    )


def FeedToggle(active_feed):
    """
    Create a toggle switch between normal and recommended papers feed
    
    Args:
        active_feed (str): Current active feed ('normal' or 'recommended')
        
    Returns:
        Div: FastHTML div component with toggle buttons
    """
    normal_cls = "px-4 py-2 rounded-l-lg font-medium text-base transition-colors duration-200 "
    recommended_cls = "px-4 py-2 rounded-r-lg font-medium text-base transition-colors duration-200 "
    
    # Add active styling
    if active_feed == 'normal':
        normal_cls += "bg-blue-700 text-white"
        recommended_cls += "bg-slate-700 text-gray-300 hover:bg-slate-600"
    else:
        normal_cls += "bg-slate-700 text-gray-300 hover:bg-slate-600"
        recommended_cls += "bg-blue-700 text-white"
    
    return Div(
        A("Latest", href="/?feed=normal", cls=normal_cls),
        A("For You", href="/?feed=recommended", cls=recommended_cls),
        cls="flex items-center border border-gray-700 rounded-lg overflow-hidden"
    )


def Navigation(request):
    """
    Create a navigation bar component
    
    Args:
        request: HTTP request object
        
    Returns:
        Nav: FastHTML nav component
    """
    # Extract the current feed type from query params or default to 'normal'
    active_feed = request.query_params.get('feed', 'normal')
    
    return Nav(
        Div(
            # Left-aligned home button
            NavButton("Home", href="/"),
            # Add Stats, Knowledge Graph, Entities, and Saved buttons
            NavButton("Stats", href="/stats"),
            NavButton("Graph", href="/graph"),
            NavButton("Entities", href="/entities"),
            NavButton("Saved", href="/saved"),
            # Center space (empty)
            Div(cls="flex-grow"),  # Add flexible space in middle
            # Feed toggle
            FeedToggle(active_feed),
            Div(cls="w-4"),  # Small spacer
            # Right-aligned login button
            NavButton("Login" if not request.scope["auth"] else "Logout", href="/login" if not request.scope["auth"] else "/logout"),
            cls="flex flex-wrap justify-center sm:justify-between items-center w-full max-w-6xl mx-auto px-4 py-3 sm:px-7 sm:py-6 md:px-8 md:py-7 2xl:px-8 2xl:py-5 text-lg sm:text-2xl md:text-3xl 2xl:text-xl h-auto min-h-16 sm:h-24 md:h-28 2xl:h-20 gap-3 sm:gap-3"
        ),
        cls="bg-[#1e2532] sticky top-0 z-10 shadow-none border-b-0"
    )
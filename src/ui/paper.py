"""
Paper UI components
"""
from typing import Dict, List, Optional, Any

try:
    from fasthtml.common import *
    from monsterui.all import *
except ImportError:
    # Create placeholders for testing
    def Div(*args, **kwargs): return None

from src.utils.date import format_date
from src.ui.common import highlight_entities_in_summary
from src.ui.entities import RatingDisplay, Entities
from src.ui.metadata import DateDisplay, SimilarityScore, PaperTitle, PaperSummary
from src.ui.buttons import SaveButton
from src.ui.tags import TagsContainer
from src.ui.components import MermaidSection


def Paper(results, session, selected_entity=None, previously_selected_entity=None, selected_tag=None, highlight_text=None, show_similarity=False):
    """
    Render a paper component with all its details
    
    Args:
        results: Paper data
        session: User session data
        selected_entity: Currently selected entity
        previously_selected_entity: Previously selected entity
        selected_tag: Currently selected tag
        highlight_text: Text to highlight in the summary
        show_similarity: Whether to show similarity score
        
    Returns:
        Div: FastHTML div component for the paper
    """
    # Use get() with defaults for safety
    summary = results.get("summary", "")
    tags = results.get("tags", [])
    published = results.get("published", "")
    title = results.get("title", "")
    abs_url = results.get("abs_url", "")
    entities = results.get("entities", [])
    
    # Get the user ID from the session for personalized ratings
    user_id = session.get('user_id', None)
    
    # Get the rating for this paper
    from src.services.reviews import get_rating_for_paper
    rating = get_rating_for_paper(abs_url)
    
    # Check if the paper is already saved by this user
    from src.services.saved_papers import is_paper_saved_by_user
    from src.db import get_collections
    collections = get_collections()
    is_saved = is_paper_saved_by_user(collections, user_id, abs_url) if user_id else False

    # Format the published date
    formatted_date = format_date(published)

    # Process the summary to highlight entities
    highlighted_summary = highlight_entities_in_summary(summary, entities, abs_url)
    
    # Now build the component using our modular components
    return Div(
        # Metadata container with date and similarity score
        Div(
            DateDisplay(formatted_date),
            SimilarityScore(results.get('similarity', 0)) if show_similarity and 'similarity' in results else "",
            cls="flex justify-between items-start mb-3 sm:mb-4 2xl:mb-3 w-full"
        ),
        
        # Paper title
        PaperTitle(title, abs_url),
        
        # Rating display if available
        RatingDisplay(rating) if rating else "",
        
        # Paper summary with highlighted entities
        PaperSummary(highlighted_summary),
        
        # Tags and buttons container
        Div(
            # Tags container (commented out)
            # TagsContainer(tags, selected_tag),
            
            # Entity Tags container (with normalized values)
            Entities(entities, selected_entity, normalize=True),
            
            # Buttons section
            Div(
                # Save/bookmark button
                SaveButton(abs_url, title, is_saved),
                cls="flex items-center gap-2 ml-auto min-w-[68px]" # Added ml-auto to right-align the button and min-width for consistent sizing
            ),
            cls='flex flex-col sm:flex-row sm:justify-between sm:items-center mt-3 gap-3 sm:gap-4 2xl:gap-3'
        ),
        
        # Uncomment to enable mermaid section
        *MermaidSection(abs_url, results.get("mermaid_diagram", None)),
        
        cls="paper bg-[#273142] p-6 sm:p-7 md:p-9 2xl:p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 mb-6 sm:mb-8 md:mb-10 2xl:mb-6 w-full max-w-full 2xl:max-w-[1000px] mx-auto"
    )
"""
Search UI components
"""
try:
    from fasthtml.common import *
    from monsterui.all import *
except ImportError:
    # Create placeholders for testing
    def Div(*args, **kwargs): return None
    def Form(*args, **kwargs): return None
    def Input(*args, **kwargs): return None
    def Button(*args, **kwargs): return None
    def Img(*args, **kwargs): return None


def SearchForm(value="", feed=None):
    """
    Create a search form component with optional initial value

    Args:
        value: Optional initial value for the search input
        feed: Current feed type ('normal' or 'recommended')

    Returns:
        Div: FastHTML div component
    """
    # Create the form action URL, including the feed parameter if provided
    action = "/search"
    if feed:
        action = f"/search?feed={feed}"

    return Div(
        Form(
            Div(
                Input(
                    type="search",
                    name="q",
                    placeholder="Search papers by title, abstract, or author...",
                    value=value,
                    cls="w-full h-16 sm:h-18 px-6 sm:px-8 py-3 text-xl sm:text-2xl text-gray-200 bg-slate-800 border border-gray-700 rounded-full focus:outline-none focus:ring-2 focus:ring-slate-600 focus:border-transparent"
                ),
                # Add a hidden input for the feed parameter if provided
                Input(
                    type="hidden",
                    name="feed",
                    value=feed
                ) if feed else None,
                Button(
                    Img(
                        src="/assets/icons/search-icon.svg",
                        cls="w-6 h-6",
                        style="filter: invert(1);"
                    ),
                    type="submit",
                    cls="absolute right-4 top-1/2 -translate-y-1/2 p-3 bg-slate-700 hover:bg-slate-600 text-white rounded-full transition-all duration-200 flex items-center justify-center"
                ),
                cls="relative w-full max-w-xl sm:max-w-2xl mx-auto"
            ),
            method="get",
            action=action,
            cls="w-full"
        ),
        cls="w-full"
    )
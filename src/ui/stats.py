"""
Stats UI components
"""
try:
    from fasthtml.common import *
    from monsterui.all import *
except ImportError:
    # Create placeholders for testing
    def Div(*args, **kwargs): return None
    def P(*args, **kwargs): return None
    def Span(*args, **kwargs): return None


def StatsCard(title, value, trend=None, cls=""):
    """
    Renders a card for displaying a statistic with optional trend indicator
    
    Args:
        title: The title of the statistic
        value: The value to display
        trend: Optional trend percentage
        cls: Additional CSS classes
        
    Returns:
        Div: FastHTML div component
    """
    trend_element = ""
    if trend is not None:
        trend_color = "text-green-500" if trend > 0 else "text-red-500" if trend < 0 else "text-gray-400"
        trend_icon = "↑" if trend > 0 else "↓" if trend < 0 else "→"
        trend_element = Span(f"{trend_icon} {abs(trend)}%", cls=f"ml-2 {trend_color} text-sm font-medium")
    
    return Div(
        Div(
            P(title, cls="text-gray-400 text-sm font-medium mb-1"),
            Div(
                Span(value, cls="text-2xl 2xl:text-xl font-bold text-white"),
                trend_element if trend is not None else "",
                cls="flex items-center"
            ),
            cls="p-6 2xl:p-4"
        ),
        cls=f"bg-[#1e2532] rounded-lg shadow-md {cls}"
    )
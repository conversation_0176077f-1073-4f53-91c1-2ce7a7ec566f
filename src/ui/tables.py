"""
Table UI components for displaying tables extracted from papers
"""
from typing import Op<PERSON>, Tuple, List, Dict, Any

try:
    from fasthtml.common import *
    from monsterui.all import *
except ImportError:
    # Create placeholders for testing
    def Div(*args, **kwargs): return None
    def Script(*args, **kwargs): return None
    def Table(*args, **kwargs): return None
    def Tr(*args, **kwargs): return None
    def Th(*args, **kwargs): return None
    def Td(*args, **kwargs): return None
    def Button(*args, **kwargs): return None
    def P(*args, **kwargs): return None
    def H3(*args, **kwargs): return None
    def Span(*args, **kwargs): return None


def TableToggleButton(paper_id: str, has_tables: bool = False, table_count: int = 0):
    """
    Create a toggle button for displaying tables

    Args:
        paper_id: The ID of the paper
        has_tables: Whether the paper has tables
        table_count: Number of tables in the paper

    Returns:
        Button: FastHTML button component
    """
    button_text = "No tables found" if not has_tables else f"View {table_count} Table{'s' if table_count != 1 else ''}"
    button_class = "text-gray-500 cursor-not-allowed" if not has_tables else "text-blue-400 hover:text-blue-300 cursor-pointer"

    return Button(
        Span(button_text, cls="flex items-center"),
        id=f"table-toggle-{paper_id}",
        cls=f"flex items-center gap-2 px-4 py-2 rounded-md bg-gray-800 {button_class} transition-colors duration-200",
        disabled="disabled" if not has_tables else None,
        onclick=f"toggleTables('{paper_id}')" if has_tables else None
    )


def TablesSection(paper_id: str, has_tables: bool = False, table_count: int = 0) -> Tuple:
    """
    Create a tables section for a paper with pagination

    Args:
        paper_id: The ID of the paper
        has_tables: Whether the paper has tables
        table_count: Number of tables in the paper

    Returns:
        tuple: Div and Script components for the tables section
    """
    tables_container = Div(
        # Toggle button
        TableToggleButton(paper_id, has_tables, table_count),

        # Tables content container (hidden by default)
        Div(
            # Loading indicator (shown when loading)
            Div(
                Div(cls="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"),
                P("Loading tables...", cls="text-center mt-2 text-gray-400"),
                id=f"tables-loading-{paper_id}",
                cls="py-8",
                style="display: none;"
            ),

            # Tables navigation
            Div(
                # Table counter
                Div(
                    Span("Table ", cls="text-gray-400"),
                    Span("1", id=f"table-current-{paper_id}", cls="text-white"),
                    Span(" of ", cls="text-gray-400"),
                    Span("0", id=f"table-total-{paper_id}", cls="text-white"),
                    cls="text-lg"
                ),

                # Navigation buttons
                Div(
                    Button(
                        Span("←", cls="text-xl"),
                        id=f"table-prev-{paper_id}",
                        cls="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-white rounded-md mr-2 transition-colors duration-200",
                        onclick=f"prevTable('{paper_id}')"
                    ),
                    Button(
                        Span("→", cls="text-xl"),
                        id=f"table-next-{paper_id}",
                        cls="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-white rounded-md transition-colors duration-200",
                        onclick=f"nextTable('{paper_id}')"
                    ),
                    cls="flex"
                ),

                id=f"tables-nav-{paper_id}",
                cls="flex justify-between items-center mb-4",
                style="display: none;"
            ),

            # Tables content (populated via JavaScript)
            Div(
                id=f"tables-content-{paper_id}",
                cls="w-full"
            ),

            id=f"tables-container-{paper_id}",
            cls="w-full mt-4 p-6 bg-gray-800/50 rounded-lg overflow-auto transition-all border border-gray-700",
            style="display: none;"
        ),
        cls="w-full mt-6"
    )

    tables_script = Script(f"""
    // Store tables data globally
    let tablesData_{paper_id} = [];
    let currentTableIndex_{paper_id} = 0;

    // Function to toggle tables visibility
    function toggleTables(paperId) {{
        const container = document.getElementById(`tables-container-${{paperId}}`);
        const loading = document.getElementById(`tables-loading-${{paperId}}`);
        const content = document.getElementById(`tables-content-${{paperId}}`);
        const nav = document.getElementById(`tables-nav-${{paperId}}`);

        // Toggle container visibility
        if (container.style.display === 'none') {{
            container.style.display = 'block';

            // Check if tables are already loaded
            if (window[`tablesData_${{paperId}}`].length === 0) {{
                // Show loading indicator
                loading.style.display = 'block';

                // Fetch tables data
                fetch(`/api/tables/${{paperId}}`)
                    .then(response => response.json())
                    .then(data => {{
                        // Hide loading indicator
                        loading.style.display = 'none';

                        if (data.status === 'success' && data.tables && data.tables.length > 0) {{
                            // Store tables data
                            window[`tablesData_${{paperId}}`] = data.tables;
                            window[`currentTableIndex_${{paperId}}`] = 0;

                            // Update navigation
                            document.getElementById(`table-total-${{paperId}}`).textContent = data.tables.length;
                            document.getElementById(`table-current-${{paperId}}`).textContent = '1';
                            nav.style.display = 'flex';

                            // Render the first table
                            renderCurrentTable(paperId);
                        }} else {{
                            // Show message if no tables found
                            content.innerHTML = `<p class="text-center py-4 text-gray-400">${{data.message || 'No tables found'}}</p>`;
                        }}
                    }})
                    .catch(error => {{
                        // Hide loading indicator and show error
                        loading.style.display = 'none';
                        content.innerHTML = `<p class="text-center py-4 text-red-400">Error loading tables: ${{error.message}}</p>`;
                        console.error('Error fetching tables:', error);
                    }});
            }} else {{
                // Tables already loaded, just show them
                nav.style.display = 'flex';
                renderCurrentTable(paperId);
            }}
        }} else {{
            container.style.display = 'none';
        }}
    }}

    // Function to render the current table
    function renderCurrentTable(paperId) {{
        const content = document.getElementById(`tables-content-${{paperId}}`);
        const tables = window[`tablesData_${{paperId}}`];
        const currentIndex = window[`currentTableIndex_${{paperId}}`];

        if (tables.length > 0) {{
            content.innerHTML = renderTable(tables[currentIndex], currentIndex);

            // Update navigation buttons
            const prevButton = document.getElementById(`table-prev-${{paperId}}`);
            const nextButton = document.getElementById(`table-next-${{paperId}}`);

            prevButton.disabled = currentIndex === 0;
            prevButton.className = `px-3 py-1 ${{currentIndex === 0 ? 'bg-gray-800 text-gray-500 cursor-not-allowed' : 'bg-gray-700 hover:bg-gray-600 text-white cursor-pointer'}} rounded-md mr-2 transition-colors duration-200`;

            nextButton.disabled = currentIndex === tables.length - 1;
            nextButton.className = `px-3 py-1 ${{currentIndex === tables.length - 1 ? 'bg-gray-800 text-gray-500 cursor-not-allowed' : 'bg-gray-700 hover:bg-gray-600 text-white cursor-pointer'}} rounded-md transition-colors duration-200`;

            // Update counter
            document.getElementById(`table-current-${{paperId}}`).textContent = currentIndex + 1;
        }}
    }}

    // Function to navigate to the previous table
    function prevTable(paperId) {{
        if (window[`currentTableIndex_${{paperId}}`] > 0) {{
            window[`currentTableIndex_${{paperId}}`]--;
            renderCurrentTable(paperId);
        }}
    }}

    // Function to navigate to the next table
    function nextTable(paperId) {{
        if (window[`currentTableIndex_${{paperId}}`] < window[`tablesData_${{paperId}}`].length - 1) {{
            window[`currentTableIndex_${{paperId}}`]++;
            renderCurrentTable(paperId);
        }}
    }}

    // Function to render a table
    function renderTable(tableData, index) {{
        const columns = tableData.columns || [];
        const data = tableData.data || [];

        if (columns.length === 0 || data.length === 0) {{
            return `<div>
                <h3 class="text-xl font-semibold text-white mb-2">Table ${{index + 1}}</h3>
                <p class="text-gray-400">No data available for this table</p>
            </div>`;
        }}

        // Create table header
        let tableHtml = `
            <div>
                <h3 class="text-xl font-semibold text-white mb-2">Table ${{index + 1}}</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full bg-gray-900 border border-gray-700 rounded-lg">
                        <thead>
                            <tr class="bg-gray-800">`;

        // Add header columns
        columns.forEach(column => {{
            tableHtml += `<th class="px-4 py-2 text-left text-sm font-medium text-gray-300 uppercase tracking-wider border-b border-gray-700">${{column}}</th>`;
        }});

        tableHtml += `</tr>
                        </thead>
                        <tbody>`;

        // Add data rows
        data.forEach((row, rowIndex) => {{
            tableHtml += `<tr class="${{rowIndex % 2 === 0 ? 'bg-gray-900' : 'bg-gray-800'}}">`;

            columns.forEach(column => {{
                const cellValue = row[column] !== undefined ? row[column] : '';
                tableHtml += `<td class="px-4 py-2 text-sm text-gray-300 border-b border-gray-700">${{cellValue}}</td>`;
            }});

            tableHtml += `</tr>`;
        }});

        tableHtml += `</tbody>
                    </table>
                </div>
            </div>`;

        return tableHtml;
    }}
    """)

    return tables_container, tables_script


def TableDisplay(table_data: Dict[str, Any], index: int = 0, total_tables: int = 1, paper_id: str = None):
    """
    Create a component for displaying a single table with navigation

    Args:
        table_data: Table data dictionary
        index: Table index
        total_tables: Total number of tables
        paper_id: The ID of the paper

    Returns:
        Div: FastHTML div component with navigation
    """
    columns = table_data.get('columns', [])
    data = table_data.get('data', [])

    # Navigation component
    navigation = Div(
        # Table counter
        Div(
            Span("Table ", cls="text-gray-400"),
            Span(str(index + 1), cls="text-white"),
            Span(" of ", cls="text-gray-400"),
            Span(str(total_tables), cls="text-white"),
            cls="text-lg"
        ),

        # Navigation buttons
        Div(
            Button(
                Span("←", cls="text-xl"),
                cls=f"px-3 py-1 {'bg-gray-800 text-gray-500 cursor-not-allowed' if index == 0 else 'bg-gray-700 hover:bg-gray-600 text-white cursor-pointer'} rounded-md mr-2 transition-colors duration-200",
                disabled="disabled" if index == 0 else None,
                onclick=f"prevTable('{paper_id}')" if paper_id and index > 0 else None
            ),
            Button(
                Span("→", cls="text-xl"),
                cls=f"px-3 py-1 {'bg-gray-800 text-gray-500 cursor-not-allowed' if index == total_tables - 1 else 'bg-gray-700 hover:bg-gray-600 text-white cursor-pointer'} rounded-md transition-colors duration-200",
                disabled="disabled" if index == total_tables - 1 else None,
                onclick=f"nextTable('{paper_id}')" if paper_id and index < total_tables - 1 else None
            ),
            cls="flex"
        ),

        cls="flex justify-between items-center mb-4"
    )

    if not columns or not data:
        return Div(
            navigation,
            H3(f"Table {index + 1}", cls="text-xl font-semibold text-white mb-2"),
            P("No data available for this table", cls="text-gray-400")
        )

    # Create table header
    header_row = Tr(
        *[Th(col, cls="px-4 py-2 text-left text-sm font-medium text-gray-300 uppercase tracking-wider border-b border-gray-700") for col in columns],
        cls="bg-gray-800"
    )

    # Create table rows
    rows = []
    for row_index, row in enumerate(data):
        row_class = "bg-gray-900" if row_index % 2 == 0 else "bg-gray-800"
        cells = []

        for col in columns:
            cell_value = row.get(col, '')
            cells.append(Td(str(cell_value), cls="px-4 py-2 text-sm text-gray-300 border-b border-gray-700"))

        rows.append(Tr(*cells, cls=row_class))

    return Div(
        navigation,
        Div(
            Table(
                Tr(header_row),
                *rows,
                cls="min-w-full bg-gray-900 border border-gray-700 rounded-lg"
            ),
            cls="overflow-x-auto"
        )
    )

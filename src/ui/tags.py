"""
Tag UI components
"""
from typing import List, Dict, Any, Optional

try:
    from fasthtml.common import *
    from monsterui.all import *
except ImportError:
    # Create placeholders for testing
    def Div(*args, **kwargs): return None
    def A(*args, **kwargs): return None


# Tag mapping for CSS classes
DEFAULT_TAG_MAPPING = {
    "Security": "tag-security",
    "Agents": "tag-agents",
    "Finance": "tag-finance",
    "Tabular": "tag-tabular",
    "Audio": "tag-audio", 
    "Vision": "tag-vision",
    "Time Series": "tag-time-series",
    "Education": "tag-education",
    "Chemistry": "tag-chemistry",
    "Physics": "tag-physics",
    "Biology": "tag-biology",
    "Psychology": "tag-psychology",
    "Sociology": "tag-sociology", 
    "Law": "tag-law", 
    "Reinforcement Learning": "tag-reinforcement-learning", 
    "Reasoning": "tag-reasoning", 
    "Tool-Use": "tag-tool-use", 
    "AI Ethics": "tag-ai-ethics", 
    "Coding": "tag-coding", 
    "Efficiency": "tag-efficiency", 
    "Alignment": "tag-alignment", 
    "Prompt Engineering": "tag-prompt-engineering", 
    "Robotics": "tag-robotics",
    "Graphs": "tag-graphs", 
    "Explainable AI": "tag-explainable-ai",
    "RAG": "tag-rag",
    "Health": "tag-health",
    "Safety": "tag-safety",
    "Language": "tag-language",
    "Language Processing": "tag-language"
}

# Color mapping for tags
DEFAULT_COLOR_MAPPING = {
    "Security": "#ff4757", # Red for security
    "Safety": "#ff4757", # Red for safety
    "Agents": "#5352ed", # Blue for AI agents
    "Finance": "#20bf6b", # Green for finance
    "Health":"#eb3b5a", # Red for health
    "Tabular": "#3867d6", # Blue for structured data
    "Audio": "#8854d0", # Purple for audio
    "Vision": "#4b7bec", # Blue for computer vision
    "Language": "#4b7bec", # Blue for computer vision
    "Time Series": "#a5b1c2", # Gray for time series
}


def Tag(tag: str, selected_tag: Optional[str] = None, tag_mapping: Optional[Dict[str, str]] = None, 
        color_mapping: Optional[Dict[str, str]] = None):
    """
    Create a tag component for a paper
    
    Args:
        tag: The tag name
        selected_tag: Currently selected tag (for highlighting)
        tag_mapping: Dictionary mapping tags to CSS classes
        color_mapping: Dictionary mapping tags to colors
        
    Returns:
        A: FastHTML a component
    """
    if tag_mapping is None:
        tag_mapping = DEFAULT_TAG_MAPPING
    
    if color_mapping is None:
        color_mapping = DEFAULT_COLOR_MAPPING
    
    # Get the CSS class and color for this tag
    css_class = tag_mapping.get(tag, "tag-biology")
    if tag == selected_tag:
        css_class = "tag-biology"  # Always use the biology tag class for selected tags
    
    color = color_mapping.get(tag, '#4b7bec')  # Default blue color if no mapping
    
    return A(tag,
        cls=f"tag text-xl sm:text-2xl md:text-3xl 2xl:text-xl {css_class} cursor-pointer rounded-full px-4 sm:px-5 md:px-6 2xl:px-5 py-2 sm:py-2.5 md:py-3 2xl:py-2.5 hover:shadow-lg hover:-translate-y-1 transition-all duration-200",
        style=f"color: #FFFFFF; text-decoration: none; user-select: none; box-shadow: 0 2px 6px rgba(0, 0, 0, 0.25); background-color: {color};",
        onclick=f"window.location.href='/tag/{tag}'"
    )


def TagsContainer(tags: List[str], selected_tag: Optional[str] = None, tag_mapping: Optional[Dict[str, str]] = None,
                 color_mapping: Optional[Dict[str, str]] = None):
    """
    Create a container with all the tags for a paper
    
    Args:
        tags: List of tags
        selected_tag: Currently selected tag (for highlighting)
        tag_mapping: Dictionary mapping tags to CSS classes
        color_mapping: Dictionary mapping tags to colors
        
    Returns:
        Div: FastHTML div component
    """
    if tag_mapping is None:
        tag_mapping = DEFAULT_TAG_MAPPING
        
    # Filter to valid tags only
    valid_tags = [tag for tag in tags if tag in tag_mapping]
    
    # If no tags after filtering, return empty div
    if not valid_tags:
        return Div(cls="hidden")
    
    return Div(
        *(Tag(tag, selected_tag, tag_mapping, color_mapping) for tag in valid_tags),
        cls='flex flex-wrap gap-2 sm:gap-3 md:gap-4 items-center w-full'  # Reduced gaps
    )
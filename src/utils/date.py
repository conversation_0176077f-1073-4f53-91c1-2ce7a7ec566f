from datetime import datetime

def format_date(date_str):
    """
    Format a date string from various formats to "24th Feb, 2023" format
    
    Args:
        date_str (str): Date string in various formats (YYYY-MM-DD, YYYY-MM-DD HH:MM:SS, etc.)
        
    Returns:
        str: Formatted date string or empty string if invalid
    """
    if not date_str:
        return ""
        
    try:
        # First try exact format YYYY-MM-DD
        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
    except ValueError:
        try:
            # Try with time component YYYY-MM-DD HH:MM:SS
            date_obj = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            try:
                # Try with T separator and timezone YYYY-MM-DDT00:00:00Z
                date_obj = datetime.strptime(date_str, '%Y-%m-%dT%H:%M:%SZ')
            except ValueError:
                # Return empty string if format is unknown
                return ""
    
    # Get day with suffix (1st, 2nd, 3rd, 4th, etc)
    day = date_obj.day
    if 4 <= day <= 20 or 24 <= day <= 30:
        suffix = "th"
    else:
        suffix = ["st", "nd", "rd"][day % 10 - 1] if day % 10 < 4 else "th"
    
    # Format date as "24th Feb, 2023"
    return f"{day}{suffix} {date_obj.strftime('%b')}, {date_obj.strftime('%Y')}"
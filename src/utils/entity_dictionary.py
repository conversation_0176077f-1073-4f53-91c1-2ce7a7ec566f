import re
import json
import os
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def normalize_text(text):
    """
    Normalize text by converting to lower case, removing punctuation,
    and reducing any sequence of whitespace to a single space.
    """
    # Lowercase the text
    text = text.lower()
    # Replace any sequence of whitespace with a single space
    text = re.sub(r'\s+', ' ', text)
    # Remove punctuation (keep only word characters and spaces)
    text = re.sub(r'[^\w\s]', '', text)
    return text.strip()

def build_pattern(entity_value):
    """
    Given a normalized entity_value, build a regex pattern that allows for flexible matching.
    The pattern splits the entity into tokens and allows any non-word characters (e.g., spaces, punctuation)
    between them.
    """
    # Split the entity into word tokens (ignoring any punctuation)
    tokens = re.findall(r'\w+', entity_value)
    # Build a pattern that matches each token with flexible non-word characters in between.
    # The \b ensures word boundaries.
    pattern = r'\b' + r'\W+'.join(tokens) + r'\b'
    return pattern

def match_entities_in_text(text, entity_dict):
    """
    For each entity in the dictionary, build a regex pattern and search within the given text.
    Returns a dictionary of matched entity texts and their categories.
    """
    matches = {}
    # Iterate over each entity and its label
    for entity_value, category in entity_dict.items():
        pattern = build_pattern(entity_value)
        # Search the raw text (using IGNORECASE for case insensitivity)
        if re.search(pattern, text, re.IGNORECASE):
            matches[entity_value] = category
    return matches

class EntityDictionaryManager:
    """
    Manages a dictionary mapping from entity values to their canonical IDs and names.
    This dictionary is synchronized with the MongoDB entity resolution collection.
    
    In the updated implementation, we prioritize constructing the dictionary on-the-fly
    from the database rather than relying on persistent disk storage, which helps avoid
    file system issues and keeps the dictionary always in sync with the database.
    """
    
    def __init__(self, dictionary_path: str = "data/entity_dictionary.json", auto_update: bool = True, local_mode: bool = False):
        """
        Initialize the EntityDictionaryManager
        
        Args:
            dictionary_path: Path to the dictionary file (kept for backward compatibility)
            auto_update: Whether to automatically update from MongoDB on initialization
            local_mode: Whether to use in-memory MongoDB for local testing
        """
        self.dictionary_path = dictionary_path
        self.entity_dict = {}
        self.last_updated = None
        
        # Always build dictionary on-the-fly from the database
        import time
        start_time = time.time()
        
        # Check if we're in local mode and running from file initialization
        local_env = os.environ.get('LOCAL_MODE', '').lower() == 'true'
        if local_mode or local_env:
            # In local mode, just use default mappings to avoid remote connections
            logger.info("Running in local mode, using default mappings only")
            self.add_default_entity_mappings()
            return
            
        try:
            # Dynamically import here to avoid circular imports
            from ..db import setup_mongo_client, setup_collections
            client = setup_mongo_client(local_mode=local_mode)
            collections = setup_collections(client)
            
            # Add entity_mappings collection if it doesn't exist
            if collections.get('entity_mappings') is None and collections.get('entity_resolution') is not None:
                logger.info("entity_mappings collection not found, but entity_resolution exists")
                mongodb = client['papers']
                collections['entity_mappings'] = mongodb['entity_mappings']
                logger.info("Created entity_mappings collection")
            
            if collections.get('entity_resolution') is not None:
                update_count = self.update_from_mongodb(
                    collections.get('entity_mappings'),
                    collections['entity_resolution']
                )
                if update_count > 0:
                    end_time = time.time()
                    logger.info(f"Built entity dictionary with {update_count} mappings in {end_time - start_time:.3f} seconds")
                else:
                    # If no entities were loaded, add some defaults to make sure tags display
                    logger.info("No entities loaded from database, adding default mappings")
                    self.add_default_entity_mappings()
            else:
                logger.warning("MongoDB collections not available, using empty dictionary")
                # Add default mappings to ensure tags display
                self.add_default_entity_mappings()
        except Exception as e:
            logger.warning(f"Failed to build entity dictionary: {e}")
            logger.info("Using empty entity dictionary, adding default mappings")
            # Add default mappings to ensure tags display
            self.add_default_entity_mappings()
    
    def load_dictionary(self) -> bool:
        """
        Load the entity dictionary from disk
        
        Note: This method is kept for backward compatibility but is now a no-op
        as we build the dictionary on-the-fly from the database.
        
        Returns:
            bool: Always returns False to trigger on-the-fly construction
        """
        logger.info("Dictionary persistence to disk disabled, using on-the-fly construction")
        return False
    
    def save_dictionary(self) -> bool:
        """
        Save the entity dictionary to disk
        
        Note: This method is kept for backward compatibility but is now a no-op
        as we build the dictionary on-the-fly from the database.
        
        Returns:
            bool: Always returns True
        """
        logger.info("Dictionary persistence to disk disabled, using on-the-fly construction")
        return True
    
    def lookup_entity(self, entity_value: str) -> Optional[Dict[str, Any]]:
        """
        Look up an entity in the dictionary
        
        Args:
            entity_value: The entity value to look up
            
        Returns:
            Dict containing canonical_id and canonical_name if found, None otherwise
        """
        normalized_value = normalize_text(entity_value)
        return self.entity_dict.get(normalized_value)
    
    def update_from_mongodb(self, entity_mappings_collection, entity_resolution_collection) -> int:
        """
        Update the entity dictionary from MongoDB collections
        
        Args:
            entity_mappings_collection: MongoDB collection for entity mappings
            entity_resolution_collection: MongoDB collection for resolved entities
            
        Returns:
            int: Number of entities updated
        """
        if entity_resolution_collection is None:
            logger.warning("Cannot update entity dictionary: MongoDB entity_resolution collection not provided")
            return 0
        
        try:
            # Check if dictionary is empty - if so, force a full rebuild
            force_rebuild = len(self.entity_dict) == 0
            
            # Check if entity_mappings_collection exists
            mappings_exist = entity_mappings_collection is not None
            
            # If no mappings collection or force rebuild, try to build from entity_resolution
            if (not mappings_exist) or force_rebuild:
                logger.info("Entity mappings collection not available or force rebuild requested")
                logger.info("Building dictionary directly from entity_resolution collection")
                
                # Clear existing dictionary if rebuilding
                if force_rebuild:
                    self.entity_dict = {}
                    self.last_updated = None
                
                # Do a full rebuild from scratch using entity_resolution collection
                update_count = 0
                
                # Get all resolved entities from MongoDB
                resolved_entities = list(entity_resolution_collection.find({"document_type": "entity"}))
                
                if resolved_entities:
                    for entity in resolved_entities:
                        canonical_id = entity.get('canonical_id')
                        canonical_name = entity.get('canonical_name')
                        entity_type = entity.get('entity_type')
                        variant_ids = entity.get('variant_ids', [])
                        
                        # Add canonical form to dictionary
                        if canonical_id and canonical_name:
                            normalized_canonical = normalize_text(canonical_name)
                            self.entity_dict[normalized_canonical] = {
                                'canonical_id': canonical_id,
                                'canonical_name': canonical_name,
                                'entity_type': entity_type
                            }
                            update_count += 1
                            
                            # Add all variants to dictionary 
                            for variant in variant_ids:
                                if variant:
                                    normalized_variant = normalize_text(variant)
                                    self.entity_dict[normalized_variant] = {
                                        'canonical_id': canonical_id,
                                        'canonical_name': canonical_name,
                                        'entity_type': entity_type
                                    }
                                    update_count += 1
                    
                    if update_count > 0:
                        self.last_updated = datetime.now().isoformat()
                        logger.info(f"Created entity dictionary with {update_count} mappings from entity_resolution")
                    
                    # If mappings_collection exists, try to create it from our generated mappings
                    if mappings_exist and update_count > 0:
                        try:
                            self.create_mappings_collection(entity_mappings_collection)
                        except Exception as e:
                            logger.error(f"Failed to create mappings collection: {e}")
                    
                    return update_count
                else:
                    logger.warning("No resolved entities found in entity_resolution collection")
                    return 0
            
            # If we have entity_mappings collection, proceed with normal dictionary building
            if mappings_exist:
                # Compare entity count with dictionary size to detect mismatches
                entity_count = entity_mappings_collection.count_documents({})
                if not force_rebuild and entity_count > 0 and abs(entity_count - len(self.entity_dict)) / entity_count > 0.1:
                    # If more than 10% difference, force a rebuild
                    logger.info(f"Entity count mismatch: {entity_count} in DB vs {len(self.entity_dict)} in dictionary")
                    logger.info("Forcing full rebuild of entity dictionary")
                    force_rebuild = True
                    self.entity_dict = {}
                    self.last_updated = None
                
                # Different paths for incremental update vs full rebuild
                if force_rebuild:
                    # Do a full rebuild from scratch
                    logger.info("Performing full rebuild of entity dictionary from MongoDB")
                    update_count = 0
                    
                    # Get all mappings from MongoDB
                    all_mappings = list(entity_mappings_collection.find({}))
                    
                    if all_mappings:
                        for mapping in all_mappings:
                            original_entity = mapping.get('original_entity', {})
                            resolved_entity = mapping.get('resolved_entity', {})
                            
                            if original_entity and resolved_entity:
                                entity_value = original_entity.get('value')
                                if entity_value:
                                    normalized_value = normalize_text(entity_value)
                                    canonical_id = resolved_entity.get('canonical_id')
                                    canonical_name = resolved_entity.get('canonical_name')
                                    entity_type = resolved_entity.get('entity_type')
                                    
                                    if canonical_id and canonical_name:
                                        self.entity_dict[normalized_value] = {
                                            'canonical_id': canonical_id,
                                            'canonical_name': canonical_name,
                                            'entity_type': entity_type
                                        }
                                        update_count += 1
                        
                        if update_count > 0:
                            self.last_updated = datetime.now().isoformat()
                            logger.info(f"Created entity dictionary with {update_count} mappings")
                    
                    return update_count
                else:
                    # Incremental update
                    # Get the last update time for incremental updates
                    query = {}
                    if self.last_updated:
                        query["last_updated"] = {"$gt": self.last_updated}
                    
                    # Get all entity mappings
                    mappings = list(entity_mappings_collection.find(query))
                    
                    # If no new mappings, return early
                    if not mappings:
                        logger.info("No new entity mappings to update")
                        return 0
                    
                    # Update the dictionary with new mappings
                    update_count = 0
                    for mapping in mappings:
                        original_entity = mapping.get('original_entity', {})
                        resolved_entity = mapping.get('resolved_entity', {})
                        
                        if original_entity and resolved_entity:
                            entity_value = original_entity.get('value')
                            if entity_value:
                                normalized_value = normalize_text(entity_value)
                                canonical_id = resolved_entity.get('canonical_id')
                                canonical_name = resolved_entity.get('canonical_name')
                                entity_type = resolved_entity.get('entity_type')
                                
                                if canonical_id and canonical_name:
                                    self.entity_dict[normalized_value] = {
                                        'canonical_id': canonical_id,
                                        'canonical_name': canonical_name,
                                        'entity_type': entity_type
                                    }
                                    update_count += 1
                    
                    # If we updated entities, update timestamp
                    if update_count > 0:
                        self.last_updated = datetime.now().isoformat()
                        logger.info(f"Updated entity dictionary with {update_count} new mappings")
                    
                    return update_count
            
        except Exception as e:
            logger.error(f"Error updating entity dictionary from MongoDB: {e}")
            return 0
            
    def add_default_entity_mappings(self):
        """
        Add default entity mappings to ensure entity tags always display
        even when database connection fails
        """
        logger.info("Adding default entity mappings")
        
        # Add some common AI/ML entities that are likely to appear in papers
        default_entities = {
            "transformer": {"canonical_id": "entity-transformer", "canonical_name": "Transformer", "entity_type": "MODEL"},
            "llm": {"canonical_id": "entity-llm", "canonical_name": "LLM", "entity_type": "MODEL"},
            "large language model": {"canonical_id": "entity-llm", "canonical_name": "LLM", "entity_type": "MODEL"},
            "bert": {"canonical_id": "entity-bert", "canonical_name": "BERT", "entity_type": "MODEL"},
            "gpt": {"canonical_id": "entity-gpt", "canonical_name": "GPT", "entity_type": "MODEL"},
            "chatgpt": {"canonical_id": "entity-chatgpt", "canonical_name": "ChatGPT", "entity_type": "MODEL"},
            "deep learning": {"canonical_id": "entity-deep-learning", "canonical_name": "Deep Learning", "entity_type": "CONCEPT"},
            "reinforcement learning": {"canonical_id": "entity-rl", "canonical_name": "Reinforcement Learning", "entity_type": "CONCEPT"},
            "neural network": {"canonical_id": "entity-neural-network", "canonical_name": "Neural Network", "entity_type": "CONCEPT"},
            "attention": {"canonical_id": "entity-attention", "canonical_name": "Attention", "entity_type": "CONCEPT"},
            "machine learning": {"canonical_id": "entity-ml", "canonical_name": "Machine Learning", "entity_type": "CONCEPT"},
            "computer vision": {"canonical_id": "entity-cv", "canonical_name": "Computer Vision", "entity_type": "FIELD"},
            "nlp": {"canonical_id": "entity-nlp", "canonical_name": "NLP", "entity_type": "FIELD"},
            "natural language processing": {"canonical_id": "entity-nlp", "canonical_name": "NLP", "entity_type": "FIELD"}
        }
        
        # Add these entities to the dictionary
        for entity_name, entity_info in default_entities.items():
            normalized_name = normalize_text(entity_name)
            self.entity_dict[normalized_name] = entity_info
            
        logger.info(f"Added {len(default_entities)} default entity mappings")
    
    def create_mappings_collection(self, entity_mappings_collection):
        """
        Create entity_mappings collection from the entity dictionary
        
        Args:
            entity_mappings_collection: MongoDB collection for entity mappings
        """
        if entity_mappings_collection is None:
            logger.warning("Cannot create entity mappings: MongoDB collection not provided")
            return
            
        logger.info("Creating entity_mappings collection from dictionary")
        
        # Keep track of mappings we've seen to avoid duplicates
        seen_mappings = set()
        timestamp = datetime.now().isoformat()
        insert_count = 0
        
        # Process each entry in the dictionary one by one
        for normalized_value, entity_info in self.entity_dict.items():
            canonical_id = entity_info.get('canonical_id')
            canonical_name = entity_info.get('canonical_name')
            entity_type = entity_info.get('entity_type')
            
            # Skip if we don't have the required fields
            if not (canonical_id and canonical_name):
                continue
                
            # Create the mapping key to check for duplicates
            # Use a tuple of (value, category) for the key
            mapping_key = (normalized_value, entity_type)
            
            # Skip if we've already seen this mapping
            if mapping_key in seen_mappings:
                continue
                
            seen_mappings.add(mapping_key)
            
            # Create mapping document
            mapping = {
                'original_entity': {
                    'value': normalized_value,
                    'category': entity_type  # Changed 'type' to 'category' to match schema
                },
                'resolved_entity': {
                    'canonical_id': canonical_id,
                    'canonical_name': canonical_name,
                    'entity_type': entity_type
                },
                'created_at': timestamp,
                'updated_at': timestamp
            }
            
            # Check if mapping already exists to avoid duplicate key errors
            try:
                existing = entity_mappings_collection.find_one({
                    "original_entity.value": normalized_value,
                    "original_entity.category": entity_type
                })
                
                if existing:
                    # Update existing mapping instead of inserting
                    entity_mappings_collection.update_one(
                        {
                            "original_entity.value": normalized_value,
                            "original_entity.category": entity_type
                        },
                        {"$set": mapping}
                    )
                else:
                    # Insert new mapping
                    entity_mappings_collection.insert_one(mapping)
                
                insert_count += 1
                
                # Log progress periodically
                if insert_count % 1000 == 0:
                    logger.info(f"Processed {insert_count} entity mappings so far")
            except Exception as e:
                logger.error(f"Error creating entity mapping for '{normalized_value}': {e}")
        
        logger.info(f"Created {insert_count} entity mappings")

# Create a global instance
entity_dictionary_manager = None

def get_entity_dictionary_manager(dictionary_path: str = "data/entity_dictionary.json", auto_update: bool = True, local_mode: bool = False) -> EntityDictionaryManager:
    """
    Get or create the global EntityDictionaryManager instance
    
    Args:
        dictionary_path: Path to the dictionary file
        auto_update: Whether to automatically update from MongoDB on initialization
        local_mode: Whether to use in-memory MongoDB for local testing
        
    Returns:
        EntityDictionaryManager: The global instance
    """
    global entity_dictionary_manager
    
    # Check if we're in local mode from environment variable
    env_local_mode = os.environ.get('LOCAL_MODE', '').lower() == 'true'
    if env_local_mode:
        local_mode = True
    
    # If we're in local mode, use a preset entity dictionary with common AI terms for faster startup
    if local_mode and entity_dictionary_manager is None:
        # Create dictionary manager with auto_update=False to avoid MongoDB connection
        entity_dictionary_manager = EntityDictionaryManager(dictionary_path, auto_update=False, local_mode=True)
        
        # Populate with common AI research entities
        preset_entities = {
            "transformer": {"canonical_id": "transformer-1", "canonical_name": "Transformer", "entity_type": "Architecture"},
            "attention": {"canonical_id": "attention-1", "canonical_name": "Attention", "entity_type": "Mechanism"},
            "bert": {"canonical_id": "bert-1", "canonical_name": "BERT", "entity_type": "Model"},
            "gpt": {"canonical_id": "gpt-1", "canonical_name": "GPT", "entity_type": "Model"},
            "llm": {"canonical_id": "llm-1", "canonical_name": "LLM", "entity_type": "Model"},
            "deep learning": {"canonical_id": "deep-learning-1", "canonical_name": "Deep Learning", "entity_type": "Field"},
            "nlp": {"canonical_id": "nlp-1", "canonical_name": "NLP", "entity_type": "Field"},
            "fine-tuning": {"canonical_id": "fine-tuning-1", "canonical_name": "Fine-tuning", "entity_type": "Technique"},
            "transfer learning": {"canonical_id": "transfer-learning-1", "canonical_name": "Transfer Learning", "entity_type": "Technique"},
            "embeddings": {"canonical_id": "embeddings-1", "canonical_name": "Embeddings", "entity_type": "Concept"},
            "tokenization": {"canonical_id": "tokenization-1", "canonical_name": "Tokenization", "entity_type": "Technique"},
            "prompt engineering": {"canonical_id": "prompt-engineering-1", "canonical_name": "Prompt Engineering", "entity_type": "Technique"},
            "rag": {"canonical_id": "rag-1", "canonical_name": "RAG", "entity_type": "Technique"},
            "retrieval augmented generation": {"canonical_id": "rag-1", "canonical_name": "RAG", "entity_type": "Technique"},
            "vector database": {"canonical_id": "vector-db-1", "canonical_name": "Vector Database", "entity_type": "Tool"},
            "knowledge graph": {"canonical_id": "kg-1", "canonical_name": "Knowledge Graph", "entity_type": "Concept"},
            "entity resolution": {"canonical_id": "entity-resolution-1", "canonical_name": "Entity Resolution", "entity_type": "Technique"},
            "semantic search": {"canonical_id": "semantic-search-1", "canonical_name": "Semantic Search", "entity_type": "Application"}
        }
        
        # Add the preset entities to the dictionary
        entity_dictionary_manager.entity_dict.update(preset_entities)
        
        print(f"Local mode: Loaded entity dictionary with {len(entity_dictionary_manager.entity_dict)} preset entities")
        return entity_dictionary_manager
    
    # Normal initialization for non-local mode or if already initialized
    if entity_dictionary_manager is None:
        entity_dictionary_manager = EntityDictionaryManager(dictionary_path, auto_update, local_mode)
    
    return entity_dictionary_manager

if __name__ == "__main__":
    # Example of testing with manual entity dictionary
    test_dict = {
        "new york": "LOCATION",
        "united states": "LOCATION",
        "john doe": "PERSON"
    }
    
    # New text with varied spacing, special characters, etc.
    new_text = "john    doe went to new york! Later, he also traveled across united states."
    
    # Perform flexible string matching to find entities from the dictionary within the new text.
    matched_entities = match_entities_in_text(new_text, test_dict)
    print("\nMatched Entities in new text:")
    print(matched_entities)
    
    # Test entity dictionary
    manager = EntityDictionaryManager()
    print("\nEntity Dictionary Manager Test:")
    print(f"Dictionary loaded: {len(manager.entity_dict)} entries")
    manager.entity_dict["example entity"] = {
        "canonical_id": "example-id-123",
        "canonical_name": "Example Entity",
        "entity_type": "Test"
    }
    manager.save_dictionary()
    print("Dictionary updated and saved")
import re
import json

def extract_json_from_markdown(markdown_text):
    """
    Extract JSON content from markdown-style codeblocks
    
    Args:
        markdown_text (str): Markdown text containing JSON in triple backtick blocks
        
    Returns:
        str: The extracted JSON string, or None if not found
    """
    # Use regex to find content between triple backticks
    match = re.search(r'```json\s*([\s\S]*?)\s*```', markdown_text)
    if match:
        return match.group(1)
    return None


def sanitize_json_string(json_str):
    """
    Sanitize JSON string by removing or replacing invalid control characters
    and fixing invalid escape sequences that could cause parsing errors.
    
    Args:
        json_str (str): JSON string to sanitize
        
    Returns:
        str: Sanitized JSON string
    """
    if not json_str:
        return json_str
    
    # Step 1: Fix common escape sequence issues
    # Replace invalid backslash escapes with proper escapes
    sanitized = json_str
    
    # Fix invalid escape sequences like lone backslashes
    # This regex finds backslashes that aren't part of valid escape sequences
    import re
    sanitized = re.sub(r'\\(?!["\\/bfnrt]|u[0-9a-fA-F]{4})', r'\\\\', sanitized)
    
    # Step 2: Remove or replace control characters (ASCII values 0-31, except whitespace)
    # \t (9), \n (10), \r (13) are allowed in JSON
    cleaned = ''
    for char in sanitized:
        if ord(char) < 32 and char not in '\t\n\r':
            # Skip or replace with space
            cleaned += ' '
        else:
            cleaned += char
    
    return cleaned


def process_input(input_text):
    """
    Extract and process JSON from markdown-style input
    
    Args:
        input_text (str): Markdown text containing JSON
        
    Returns:
        dict: Parsed JSON data, or None if parsing failed
    """
    # Extract JSON from markdown-style input
    json_str = extract_json_from_markdown(input_text)
    
    if json_str is None:
        print("No JSON found in the input.")
        return None

    try:
        # Sanitize the JSON string before parsing
        sanitized_json = sanitize_json_string(json_str)
        
        # Parse the JSON string into a Python data structure
        data = json.loads(sanitized_json)
        return data
    except json.JSONDecodeError as e:
        print(f"Error decoding JSON: {e}")
        # Try a more aggressive approach if first attempt fails
        try:
            # Remove all control characters for a more aggressive cleanup
            clean_json = ''.join(ch for ch in json_str if ord(ch) >= 32 or ch in '\n\r\t')
            data = json.loads(clean_json)
            print("JSON parsed successfully after aggressive cleanup")
            return data
        except json.JSONDecodeError as e2:
            print(f"Failed with aggressive cleanup too: {e2}")
            return None
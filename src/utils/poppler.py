import os
import sys
import shutil
import subprocess


def check_poppler_installed():
    """
    Check if <PERSON><PERSON> is installed and available in PATH
    
    Returns:
        bool: True if <PERSON><PERSON> is installed and available, False otherwise
    """
    try:
        # Check for pdfinfo which is part of poppler-utils
        poppler_path = shutil.which('pdfinfo')
        return poppler_path is not None
    except Exception:
        return False


def install_poppler_if_needed():
    """
    Check for Poppler installation and install if missing
    
    Returns:
        bool: True if <PERSON><PERSON> is installed (either was already or was successfully installed), False otherwise
    """
    if check_poppler_installed():
        print("✅ Poppler is installed and available.")
        return True
    
    print("⚠️ Poppler is not installed or not in PATH.")
    
    try:
        # Try to detect OS type
        if sys.platform.startswith('linux'):
            # Debian/Ubuntu based
            if os.path.exists('/etc/debian_version'):
                print("Installing Poppler for Debian/Ubuntu...")
                subprocess.check_call(['apt-get', 'update'])
                subprocess.check_call(['apt-get', 'install', '-y', 'poppler-utils'])
                return check_poppler_installed()
            # RHEL/CentOS/Fedora based
            elif os.path.exists('/etc/redhat-release'):
                print("Installing Poppler for RHEL/CentOS/Fedora...")
                subprocess.check_call(['yum', 'install', '-y', 'poppler-utils'])
                return check_poppler_installed()
        elif sys.platform == 'darwin':  # macOS
            print("On macOS, please install Poppler manually with: brew install poppler")
        
        # If we get here, we couldn't install automatically
        print("Could not automatically install Poppler. PDF processing will be unavailable.")
        print("Please install Poppler manually:")
        print("- Ubuntu/Debian: apt-get install poppler-utils")
        print("- RHEL/CentOS: yum install poppler-utils")
        print("- macOS: brew install poppler")
        return False
        
    except Exception as e:
        print(f"Error installing Poppler: {e}")
        print("Please install Poppler manually:")
        print("- Ubuntu/Debian: apt-get install poppler-utils")
        print("- RHEL/CentOS: yum install poppler-utils")
        print("- macOS: brew install poppler")
        return False
def highlight_entities_in_summary(summary, entities_list, abs_url):
    """
    Process the summary text to highlight entity mentions.
    Wraps entity mentions with spans that show entity information on click.
    
    Args:
        summary (str): Summary text to process
        entities_list (list): List of entity tuples (name, category)
        abs_url (str): URL for the arxiv paper
        
    Returns:
        str: HTML string with highlighted entities
    """
    if not summary or not entities_list:
        return summary
        
    # Extract entity names from the entities list
    entity_names = [entity[0] for entity in entities_list if isinstance(entity, (list, tuple)) and len(entity) > 0]
    
    # Sort entities by length (descending) to prevent nested replacements
    entity_names = sorted(entity_names, key=len, reverse=True)
    
    highlighted_text = summary
    
    # Replace entity mentions with highlighted spans
    for entity in entity_names:
        if len(entity) < 3:  # Skip very short entities to avoid false positives
            continue
            
        # Create a styled span for the entity with interactive features using inline JS
        entity_html = f'''<span 
            class="entity-highlight" 
            style="background-color: rgba(117, 117, 240, 0.15); border-bottom: 1px dashed #7575F0; cursor: pointer; position: relative;"
            onclick="
                // Get entity info via AJAX request
                fetch('/get_entity_description?entity={entity}&abs_url={abs_url}')
                .then(response => response.json())
                .then(data => {{
                    // Remove any existing tooltips
                    document.querySelectorAll('.entity-popup').forEach(el => el.remove());
                    
                    // Create tooltip element
                    const tooltip = document.createElement('div');
                    tooltip.className = 'entity-popup';
                    tooltip.style.position = 'absolute';
                    tooltip.style.zIndex = '1000';
                    tooltip.style.background = '#F8F9FB';
                    tooltip.style.border = '1px solid #E2E8F0';
                    tooltip.style.borderRadius = '6px';
                    tooltip.style.padding = '12px';
                    tooltip.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
                    tooltip.style.maxWidth = '300px';
                    tooltip.style.top = '25px';
                    tooltip.style.left = '0';
                    
                    // Add content to tooltip
                    if (data.description) {{
                        tooltip.innerHTML = `<div style='font-weight: 600; margin-bottom: 8px;'>${{entity}}</div><div>${{data.description}}</div>`;
                    }} else {{
                        tooltip.innerHTML = `<div style='font-weight: 600; margin-bottom: 8px;'>${{entity}}</div><div>No additional information available.</div>`;
                    }}
                    
                    // Add close button
                    const closeBtn = document.createElement('button');
                    closeBtn.innerText = '×';
                    closeBtn.style.position = 'absolute';
                    closeBtn.style.top = '5px';
                    closeBtn.style.right = '8px';
                    closeBtn.style.border = 'none';
                    closeBtn.style.background = 'none';
                    closeBtn.style.cursor = 'pointer';
                    closeBtn.style.fontSize = '16px';
                    closeBtn.style.fontWeight = 'bold';
                    closeBtn.onclick = function(e) {{
                        e.stopPropagation();
                        tooltip.remove();
                    }};
                    tooltip.appendChild(closeBtn);
                    
                    // Add tooltip to the DOM
                    this.appendChild(tooltip);
                    
                    // Close on clicking elsewhere
                    document.addEventListener('click', function closeTooltip(e) {{
                        if (!tooltip.contains(e.target) && e.target !== tooltip) {{
                            tooltip.remove();
                            document.removeEventListener('click', closeTooltip);
                        }}
                    }});
                }});
            "
        >{entity}</span>'''
        
        # Use regex to replace whole words only to prevent partial matches
        import re
        highlighted_text = re.sub(r'\b' + re.escape(entity) + r'\b', entity_html, highlighted_text)
        
    return highlighted_text
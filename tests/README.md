# Moatless Tests

This directory contains tests for the Moatless application.

## Test Structure

The project uses two types of end-to-end tests:

### TypeScript Playwright Tests (Recommended)
- Located in `e2e-ts/` directory
- Uses the official Playwright test runner
- Configured in `playwright.config.js`
- These are the recommended tests for new development

### Python Playwright Tests (Legacy)
- Located in `e2e/` directory
- Uses pytest with the Playwright plugin
- These tests are maintained for backward compatibility

### Other Test Types
- `integration/`: Integration tests
- `unit/`: Unit tests

## Running Tests

### Using the Mock Server

Since the actual application runs in a VM that may not be accessible from your local machine, we've provided a mock server for testing:

```bash
# Start the mock server
python tests/mock_server.py

# Start on a specific port
python tests/mock_server.py 9000
```

The mock server provides simplified responses that mimic the behavior of the Moatless application for testing purposes.

### Handling Port Conflicts

If you encounter "Address already in use" errors, you can use the provided script to kill processes using the port:

```bash
# Kill any process using port 8000
python tests/kill_port.py 8000

# Then start your tests again
```

Alternatively, both the mock server and test fixtures are designed to automatically try alternative ports if the default port (8000) is already in use.

### Using the Test Runner Script

The easiest way to run tests is to use the provided test runner script, which handles port conflicts and server setup automatically:

```bash
# Run all tests (TypeScript, Python Playwright, and pytest)
python tests/run_tests.py

# Run only TypeScript Playwright tests (recommended)
python tests/run_tests.py --typescript

# Run only Python Playwright tests
python tests/run_tests.py --playwright

# Run only pytest tests
python tests/run_tests.py --pytest

# Run a specific test file
python tests/run_tests.py --test-file tests/e2e/test_navigation.py
python tests/run_tests.py --test-file tests/e2e-ts/navigation.spec.ts

# Use a different port
python tests/run_tests.py --port 9000
```

### TypeScript Playwright Tests (Manual)

To run the TypeScript Playwright tests manually:

```bash
# Run all TypeScript Playwright tests
npx playwright test

# Run with UI mode (interactive)
npx playwright test --ui

# Run with headed browsers
npx playwright test --headed

# Run only in Chrome
npx playwright test --project=chromium

# Run a specific test file
npx playwright test tests/e2e-ts/navigation.spec.ts
```

### Python Playwright Tests (Manual)

To run the Python Playwright tests manually:

```bash
# Start the mock server in one terminal
python tests/mock_server.py

# In another terminal, run the tests
python -m pytest tests/e2e

# Run with verbose output
python -m pytest tests/e2e -v

# Run with detailed output
python -m pytest tests/e2e -v --showlocals

# Run a specific test file
python -m pytest tests/e2e/test_navigation.py
```

## Test Configuration

- `playwright.config.js`: Configuration for Playwright tests
- `conftest.py`: Pytest fixtures and configuration
- `pytest.ini`: Pytest configuration

## VS Code Integration

This project includes VS Code integration for Playwright tests. To use it:

1. Install the recommended extensions:
   - Playwright Test for VS Code (`ms-playwright.playwright`)
   - Python (`ms-python.python`)
   - Test Explorer UI (`hbenl.vscode-test-explorer`)

2. Open the Testing panel in VS Code (flask icon in the sidebar)

3. For TypeScript tests:
   - The Playwright extension will automatically detect tests in the `e2e-ts` directory
   - Run tests directly from the Testing panel by clicking the play button
   - Debug tests by clicking the debug icon next to a test
   - Use the "Show browser" option to run tests with visible browsers

4. For Python tests:
   - The Python Test Explorer will detect tests in the `e2e` directory
   - Run tests directly from the Testing panel
   - Debug tests by clicking the debug icon

5. Use the VS Code tasks (Terminal > Run Task...):
   - `Run All Playwright Tests`: Run all TypeScript Playwright tests
   - `Run Current Playwright Test File`: Run the currently open test file
   - `Run Playwright Tests with UI`: Open the Playwright UI mode
   - `Run Playwright Tests with Report`: Run tests and show the HTML report
   - `Start Mock Server`: Start the mock server for testing

## Automated Testing

### GitLab CI/CD

This project uses GitLab CI/CD for continuous integration and testing. The configuration is defined in the `.gitlab-ci.yml` file in the root of the repository.

To set up scheduled pipelines in GitLab:

```bash
# Set up GitLab CI/CD schedules (requires GitLab API token)
export GITLAB_TOKEN=your_personal_access_token
export GITLAB_PROJECT_ID=your_project_id
./scripts/**********************.sh
```

Alternatively, you can manually set up schedules in the GitLab UI:
1. Go to your GitLab project
2. Navigate to CI/CD > Schedules
3. Click "New schedule"
4. Set up a daily schedule (e.g., every day at midnight) and a weekly schedule (e.g., every Sunday at 1 AM)

### Continuous Integration

Tests are automatically run in the following scenarios:

1. When code is pushed to the main/master/develop branches
2. When a merge request is created against main/master/develop
3. Every day at midnight (scheduled run)

Test results are available in the GitLab CI/CD Pipelines section of the repository. You can view test reports and artifacts directly in the GitLab interface.

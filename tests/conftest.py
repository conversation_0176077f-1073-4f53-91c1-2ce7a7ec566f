import pytest
from playwright.sync_api import sync_playwright
from http.server import <PERSON><PERSON><PERSON>erve<PERSON>, SimpleHTTPRequestHandler
import threading
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Enhanced mock server for testing without the real application
class <PERSON><PERSON><PERSON><PERSON>ler(SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/' or self.path == '/index.html':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            html = """
            <!DOCTYPE html>
            <html>
                <head><title>Moatless</title></head>
                <body>
                    <header>Mock Header</header>
                    <h1>Moatless Test Page</h1>
                    <div class="paper-list">
                        <div class="paper-item">Sample Paper</div>
                    </div>
                    <input type="search" placeholder="Search papers...">
                    <footer>Mock Footer</footer>
                </body>
            </html>
            """
            self.wfile.write(html.encode())
        elif self.path == '/search':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            html = """
            <!DOCTYPE html>
            <html>
                <head><title>Moatless - Search</title></head>
                <body>
                    <header>Mock Header</header>
                    <h1>Search Page</h1>
                    <div class="search-container">
                        <input type="search" placeholder="Search papers...">
                    </div>
                    <footer>Mock Footer</footer>
                </body>
            </html>
            """
            self.wfile.write(html.encode())
        elif self.path == '/graph':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            html = """
            <!DOCTYPE html>
            <html>
                <head><title>Moatless - Knowledge Graph</title></head>
                <body>
                    <header>Mock Header</header>
                    <h1>Knowledge Graph</h1>
                    <div id="graph-container">Mock Graph</div>
                    <footer>Mock Footer</footer>
                </body>
            </html>
            """
            self.wfile.write(html.encode())
        elif self.path.startswith('/paper/'):
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            html = """
            <!DOCTYPE html>
            <html>
                <head><title>Moatless - Paper Details</title></head>
                <body>
                    <header>Mock Header</header>
                    <div class="paper-title">Sample Paper Title</div>
                    <div class="paper-authors">Author 1, Author 2</div>
                    <div class="paper-abstract">This is a sample abstract for testing purposes.</div>
                    <footer>Mock Footer</footer>
                </body>
            </html>
            """
            self.wfile.write(html.encode())
        else:
            super().do_GET()

@pytest.fixture(scope="session")
def mock_server():
    # Check if we should skip starting the mock server
    if os.environ.get('NO_SERVER', '0') == '1':
        # Return a dummy server object with the expected attributes
        class DummyServer:
            def __init__(self):
                self.server_address = ('localhost', 8000)

        print("\nSkipping mock server startup (NO_SERVER=1)")
        yield DummyServer()
        return

    # Start mock server in a separate thread
    # Try ports 8000-8010 to avoid conflicts
    for port in range(8000, 8011):
        try:
            server = HTTPServer(('localhost', port), MockHandler)
            print(f"\nMock server started on port {port}")
            thread = threading.Thread(target=server.serve_forever)
            thread.daemon = True
            thread.start()
            yield server
            server.shutdown()
            server.server_close()
            return
        except OSError as e:
            if e.errno == 98 or e.errno == 48:  # Address already in use
                print(f"Port {port} is already in use, trying next port")
                continue
            else:
                raise

    pytest.skip("Could not find an available port for the mock server")

@pytest.fixture(scope="session")
def browser():
    # Check if headless mode is requested via environment variable
    headless = os.environ.get('PLAYWRIGHT_HEADLESS', '0') == '1'

    with sync_playwright() as p:
        browser = p.chromium.launch(headless=headless)
        yield browser
        browser.close()

@pytest.fixture
def page(browser, mock_server):
    # Get the port from the mock server
    port = mock_server.server_address[1]
    base_url = f"http://localhost:{port}"
    print(f"\nUsing base URL: {base_url}")

    # Create a new page with the base URL
    page = browser.new_page(base_url=base_url)
    yield page
    page.close()

@pytest.fixture
def authenticated_page(browser, mock_server):
    """Fixture for authenticated page sessions"""
    # Get the port from the mock server
    port = mock_server.server_address[1]
    base_url = f"http://localhost:{port}"

    # Create a new page with the base URL
    page = browser.new_page(base_url=base_url)

    # Add authentication logic here if needed
    # page.goto('/login')
    # page.fill('input[name="username"]', os.getenv('TEST_USER', '<EMAIL>'))
    # page.fill('input[name="password"]', os.getenv('TEST_PASSWORD', 'password'))
    # page.click('button[type="submit"]')
    yield page
    page.close()
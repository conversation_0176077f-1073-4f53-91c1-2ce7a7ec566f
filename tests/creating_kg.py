from openai import OpenAI
import json
import os, openai
from dotenv import load_dotenv, find_dotenv

load_dotenv(find_dotenv())

def kg_prompt(text_input):
    kg_prompt = """Create a knowledge graph for all entities and their relations in a given text.

            Here is an example of how the output should look like:

            digraph KnowledgeGraph {
            rankdir=LR;
            node [shape=box, style=filled, color=lightblue, fontname="Arial"];

            // Nodes
            LLM [label="Large Language Models (LLMs)"];
            Hallucinations [label="Hallucinations"];
            ComplexReasoning [label="Complex Reasoning Problems"];
            ErrResults [label="Erroneous Results"];
            KGs [label="Knowledge Graphs (KGs)"];
            KGIncompleteness [label="KG Incompleteness"];
            StaticRepo [label="Static Repository Assumption"];
            ImplicitLogic [label="Implicit Logical Reasoning Structures"];
            SymAgent [label="SymAgent Framework"];
            AgentPlanner [label="Agent-Planner"];
            AgentExecutor [label="Agent-Executor"];
            ActionTools [label="Predefined Action Tools"];
            ExternalDocs [label="External Documents"];
            SelfLearning [label="Self-Learning Framework"];
            OnlineExpl [label="Online Exploration"];
            OfflinePolicy [label="Offline Iterative Policy Updating"];
            ReasoningSynth [label="Synthesis of Reasoning Trajectories"];
            WeakLLM [label="Weak LLM Backbone (7B series)"];
            StrongBaselines [label="Strong Baselines"];
            MissingTriples [label="Missing Triples"];
            KGUpdates [label="Automatic KG Updates"];
            QuestionDecomp [label="Question Decomposition"];

            // Edges for LLMs and outcomes
            LLM -> Hallucinations [label="prone_to"];
            LLM -> ComplexReasoning [label="attempts_to_solve"];
            ComplexReasoning -> ErrResults [label="leads_to"];

            // Edges for KGs and their limitations
            KGs -> LLM [label="improves_reasoning"];
            KGs -> KGIncompleteness [label="suffers_from"];
            KGs -> StaticRepo [label="often_treated_as"];
            StaticRepo -> ImplicitLogic [label="overlooks"];

            // Edges for SymAgent Framework integration
            SymAgent -> LLM [label="integrates"];
            SymAgent -> KGs [label="integrates"];
            SymAgent -> AgentPlanner [label="comprises"];
            SymAgent -> AgentExecutor [label="comprises"];
            SymAgent -> SelfLearning [label="employs"];
            SymAgent -> WeakLLM [label="uses"];
            SymAgent -> StrongBaselines [label="outperforms/compared_to"];
            SymAgent -> MissingTriples [label="identifies"];
            
            // Edges for Agent-Planner
            AgentPlanner -> LLM [label="leverages_inductive_reasoning"];
            AgentPlanner -> KGs [label="extracts_symbolic_rules_from"];
            AgentPlanner -> QuestionDecomp [label="performs"];
            
            // Edges for Agent-Executor
            AgentExecutor -> ActionTools [label="invokes"];
            AgentExecutor -> KGs [label="integrates_info_from"];
            AgentExecutor -> ExternalDocs [label="integrates_info_from"];

            // Edge for Predefined Action Tools addressing KG incompleteness
            ActionTools -> KGIncompleteness [label="addresses"];

            // Edges for Self-Learning Framework
            SelfLearning -> OnlineExpl [label="includes"];
            SelfLearning -> OfflinePolicy [label="includes"];
            SelfLearning -> ReasoningSynth [label="enables"];

            // Edge for Missing Triples facilitating KG updates
            MissingTriples -> KGUpdates [label="facilitates"];
            }
            
            Now create a knowledge graph for the following text:"""
    kg_prompt += f"{text_input}"
    return kg_prompt

client = OpenAI(
    api_key=os.getenv("GEMINI_API_KEY"), 
    base_url=os.getenv("GEMINI_BASE_URL")
    )

def extracting_kg(text):
    chat_completion = client.chat.completions.create(
        messages=[{
            "role": "user",
            "content": kg_prompt(text)
        }],
        model=os.getenv("GEMINI_MODEL"),
        response_format={ "type":"json_object" },
        temperature=0.0
    )

    kg_result = chat_completion.choices[0].message.content.strip()
    return json.loads(kg_result)

abstract = """In the rapidly evolving landscape of Natural Language Processing (NLP) and text generation, the emergence of Retrieval Augmented Generation (RAG) presents 
a promising avenue for improving the quality and reliability of generated text by leveraging information retrieved from user specified database. Benchmarking is essential 
to evaluate and compare the performance of the different RAG configurations in terms of retriever and generator, providing insights into their effectiveness, scalability, 
and suitability for the specific domain and applications. In this paper, we present a comprehensive framework to generate a domain relevant RAG benchmark. Our framework is
 based on automatic question-answer generation with Human (domain experts)-AI Large Language Model (LLM) teaming. As a case study, we demonstrate the framework by 
 introducing PermitQA, a first-of-its-kind benchmark on the wind siting and permitting domain which comprises of multiple scientific documents/reports related to 
 environmental impact of wind energy projects. Our framework systematically evaluates RAG performance using diverse metrics and multiple question types with varying 
 complexity level. We also demonstrate the performance of different models on our benchmark."""

kg_result = extracting_kg(abstract)

print(kg_result)
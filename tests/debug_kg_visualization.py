#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import json
from dotenv import load_dotenv, find_dotenv
from datetime import datetime

# Load environment variables from .env file
load_dotenv(find_dotenv())

# Import environment variables
MONGO_URL_LOCAL = os.getenv("MONGO_URL_LOCAL")

# Add parent directory to path to import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Create a MongoDB mock
class MockCollection:
    def __init__(self, name):
        self.name = name
        self.data = []
        self.indexes = []
    
    def insert_one(self, document):
        # Generate a unique ID if not provided
        if '_id' not in document:
            document['_id'] = len(self.data) + 1
        self.data.append(document)
        return type('obj', (object,), {'inserted_id': document['_id']})
    
    def find_one(self, query):
        for doc in self.data:
            match = True
            for key, value in query.items():
                if key not in doc or doc[key] != value:
                    match = False
                    break
            if match:
                return doc
        return None
    
    def find(self, query=None, sort=None, limit=None, **kwargs):
        query = query or {}
        results = []
        
        # Basic filtering
        for doc in self.data:
            match = True
            for key, value in query.items():
                if key in doc:
                    if doc[key] != value:
                        match = False
                        break
                else:
                    match = False
                    break
            if match:
                results.append(doc)
        
        return results
    
    def count_documents(self, query=None):
        query = query or {}
        count = 0
        
        for doc in self.data:
            match = True
            for key, value in query.items():
                if key in doc:
                    if doc[key] != value:
                        match = False
                        break
                else:
                    match = False
                    break
            if match:
                count += 1
        return count
    
    def create_index(self, key, **kwargs):
        self.indexes.append((key, kwargs))
        return key
    
    def update_one(self, query, update, upsert=False):
        doc = self.find_one(query)
        
        if doc:
            # Apply updates
            if '$set' in update:
                for key, value in update['$set'].items():
                    doc[key] = value
            return type('obj', (object,), {
                'modified_count': 1,
                'matched_count': 1,
                'upserted_id': None
            })
        elif upsert:
            # Create new document with query fields + update fields
            new_doc = query.copy()
            if '$set' in update:
                for key, value in update['$set'].items():
                    new_doc[key] = value
            self.insert_one(new_doc)
            return type('obj', (object,), {
                'modified_count': 0,
                'matched_count': 0,
                'upserted_id': new_doc['_id']
            })
        else:
            return type('obj', (object,), {
                'modified_count': 0,
                'matched_count': 0,
                'upserted_id': None
            })

# Import the format_graph_data function from knowledge_graph.py
# Setup a mock user_entity_dictionary before importing format_graph_data
sys.modules['src.utils.entity_dictionary'] = type('obj', (object,), {
    'get_entity_dictionary_manager': lambda local_mode=False: type('obj', (object,), {
        'entity_dict': {},
        'lookup_entity': lambda e: None
    })
})

from src.api.knowledge_graph import format_graph_data

def debug_kg_visualization():
    """Debug the knowledge graph visualization issue"""
    print("Debugging Knowledge Graph Visualization")
    print("======================================")
    
    # Create mock collections
    knowledge_graphs_collection = MockCollection('knowledge_graphs')
    
    # Create sample knowledge graph data with more connections
    # This ensures nodes have at least 2 connections to pass the D3.js filter
    sample_kg = {
        'name': 'main_graph',
        'nodes': [
            {'id': 'llm', 'name': 'Large Language Models', 'type': 'concept', 'count': 5},
            {'id': 'rag', 'name': 'Retrieval Augmented Generation', 'type': 'concept', 'count': 3},
            {'id': 'transformer', 'name': 'Transformer Architecture', 'type': 'method', 'count': 4},
            {'id': 'bert', 'name': 'BERT', 'type': 'method', 'count': 2},
            {'id': 'gpt', 'name': 'GPT', 'type': 'method', 'count': 3}
        ],
        'links': [
            {'source': 'llm', 'target': 'transformer', 'type': 'USES', 'value': 1},
            {'source': 'rag', 'target': 'llm', 'type': 'USES', 'value': 1},
            {'source': 'bert', 'target': 'transformer', 'type': 'BASED_ON', 'value': 1},
            {'source': 'gpt', 'target': 'transformer', 'type': 'BASED_ON', 'value': 1},
            {'source': 'llm', 'target': 'bert', 'type': 'INCLUDES', 'value': 1},
            {'source': 'llm', 'target': 'gpt', 'type': 'INCLUDES', 'value': 1}
        ],
        'updated_at': datetime.now().isoformat()
    }
    
    # Add sample data to collection
    knowledge_graphs_collection.insert_one(sample_kg)
    
    # Check if main_graph exists in the collection
    main_graph = knowledge_graphs_collection.find_one({"name": "main_graph"})
    print(f"Is main_graph found in collection: {main_graph is not None}")
    
    if main_graph:
        # Check if nodes and links are present
        has_nodes = 'nodes' in main_graph and len(main_graph['nodes']) > 0
        has_links = 'links' in main_graph and len(main_graph['links']) > 0
        has_relationships = 'relationships' in main_graph and len(main_graph['relationships']) > 0
        
        print(f"Has nodes: {has_nodes} (count: {len(main_graph['nodes']) if has_nodes else 0})")
        print(f"Has links: {has_links} (count: {len(main_graph['links']) if has_links else 0})")
        print(f"Has relationships: {has_relationships} (count: {len(main_graph['relationships']) if has_relationships else 0})")
        
        # Check if there's valid data for visualization based on criteria in interactive_graph()
        has_valid_data = (main_graph and 
                         'nodes' in main_graph and len(main_graph['nodes']) > 0 and
                         ('links' in main_graph and len(main_graph['links']) > 0 or
                          'relationships' in main_graph and len(main_graph['relationships']) > 0))
        
        print(f"Has valid data for display: {has_valid_data}")
        
        # Format the data for D3.js visualization
        print("\nFormatting graph data for D3.js visualization...")
        formatted_data = format_graph_data(main_graph)
        
        if formatted_data:
            print(f"Formatted data has {len(formatted_data['nodes'])} nodes and {len(formatted_data['links'])} links")
            
            # Debug: Print some sample nodes and links
            if formatted_data['nodes']:
                print("\nSample node:")
                print(json.dumps(formatted_data['nodes'][0], indent=2))
            
            if formatted_data['links']:
                print("\nSample link:")
                print(json.dumps(formatted_data['links'][0], indent=2))
            
            # Simulate D3.js filtering logic that might be causing empty visualization
            print("\nSimulating D3.js filtering logic...")
            
            # Count connections for each node
            node_connections = {}
            
            # Initialize connection counter for each node
            for node in formatted_data['nodes']:
                node_connections[node['id']] = 0
            
            # Count connections for each node based on links
            for link in formatted_data['links']:
                source_id = link['source']
                target_id = link['target']
                
                if source_id in node_connections:
                    node_connections[source_id] += 1
                if target_id in node_connections:
                    node_connections[target_id] += 1
            
            # Print connection counts for all nodes
            print("\nNode connection counts:")
            for node_id, count in node_connections.items():
                print(f"  {node_id}: {count} connections")
            
            # Filter nodes with at least 2 edges (connections) - this is the D3.js filtering logic
            print("\nFiltering nodes with fewer than 2 connections (as done in D3.js)...")
            connected_nodes = [node for node in formatted_data['nodes'] if node_connections[node['id']] >= 2]
            
            # Create a set of node IDs we're rendering
            node_ids = {n['id'] for n in connected_nodes}
            
            # Filter links to only include those connecting our rendered nodes
            relevant_links = [l for l in formatted_data['links'] 
                             if l['source'] in node_ids and l['target'] in node_ids]
            
            # Print filtered results
            print(f"\nAfter filtering: {len(connected_nodes)} nodes and {len(relevant_links)} links remain")
            
            if connected_nodes:
                print("\nNodes that remain after filtering:")
                for node in connected_nodes:
                    print(f"  {node['id']}: {node['name']} ({node_connections[node['id']]} connections)")
            else:
                print("\nWARNING: No nodes remain after filtering! This explains the empty visualization.")
                print("Suggestion: Modify the visualization to show nodes with 1+ connections instead of 2+")
        else:
            print("Error: Failed to format graph data")
    else:
        print("Error: main_graph not found in collection")

if __name__ == "__main__":
    debug_kg_visualization()
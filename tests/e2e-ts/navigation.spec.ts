import { test, expect } from '@playwright/test';

test.describe('Navigation Tests', () => {
  test('homepage loads correctly', async ({ page }) => {
    await page.goto('/');
    await expect(page).toHaveTitle('Moatless');
    
    // Check for basic page elements
    await expect(page.locator('header')).toBeVisible();
    await expect(page.locator('footer')).toBeVisible();
  });

  test('search page loads correctly', async ({ page }) => {
    await page.goto('/search');
    await expect(page).toHaveTitle('Moatless - Search');
  });

  test('knowledge graph page loads correctly', async ({ page }) => {
    await page.goto('/graph');
    await expect(page.locator('#graph-container')).toBeVisible();
  });
});

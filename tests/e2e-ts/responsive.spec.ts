import { test, expect } from '@playwright/test';

test.describe('Responsive Design Tests', () => {
  test('site is responsive on different screen sizes', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/');
    // Check that mobile menu is visible
    await expect(page.locator('header')).toBeVisible();
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1280, height: 800 });
    await page.goto('/');
    // Check that desktop navigation is visible
    await expect(page.locator('header')).toBeVisible();
  });
  
  test('mobile navigation menu works', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/');
    
    // Check that mobile navigation works
    // This is a placeholder - update with actual selectors for your mobile menu
    const mobileMenuButton = page.locator('.mobile-menu-button');
    if (await mobileMenuButton.isVisible()) {
      await mobileMenuButton.click();
      await expect(page.locator('.mobile-menu')).toBeVisible();
    }
  });
});

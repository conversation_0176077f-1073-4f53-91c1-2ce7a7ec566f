import { test, expect } from '@playwright/test';

test.describe('Search Tests', () => {
  test('search functionality works correctly', async ({ page }) => {
    await page.goto('/');
    await page.fill('input[type="search"]', 'machine learning');
    await page.press('input[type="search"]', 'Enter');
    await expect(page.locator('.paper-list')).toBeVisible();

    // Check that search results are displayed
    await expect(page.locator('.paper-item')).toBeVisible();
  });

  test('paper details can be viewed', async ({ page }) => {
    await page.goto('/');
    // For the mock server, we'll navigate directly to a paper page
    // since we can't actually click and follow links in the mock
    await page.goto('/paper/123');
    await expect(page.locator('.paper-title')).toBeVisible();
    await expect(page.locator('.paper-abstract')).toBeVisible();
    await expect(page.locator('.paper-authors')).toBeVisible();
  });
});

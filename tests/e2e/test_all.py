"""
Combined test file for running all Playwright tests with pytest
"""
import pytest
from playwright.sync_api import Page, expect

# Navigation tests
def test_homepage(page: Page):
    """Test that the homepage loads correctly"""
    page.goto("/")
    expect(page).to_have_title("Moatless")

    # Check for basic page elements
    expect(page.locator('header')).to_be_visible()
    expect(page.locator('footer')).to_be_visible()

def test_search_page(page: Page):
    """Test that the search page loads correctly"""
    page.goto("/search")
    expect(page).to_have_title("Moatless - Search")

def test_graph_page(page: Page):
    """Test that the knowledge graph page loads correctly"""
    page.goto('/graph')
    expect(page.locator('#graph-container')).to_be_visible()

# Search tests
def test_search_functionality(page: Page):
    """Test that search works correctly"""
    page.goto("/")
    page.fill('input[type="search"]', 'machine learning')
    page.press('input[type="search"]', 'Enter')
    expect(page.locator('.paper-list')).to_be_visible()

    # Check that search results are displayed
    expect(page.locator('.paper-item')).to_be_visible()

def test_paper_details(page: Page):
    """Test that paper details can be viewed"""
    page.goto('/')
    # For the mock server, we'll navigate directly to a paper page
    # since we can't actually click and follow links in the mock
    page.goto('/paper/123')
    expect(page.locator('.paper-title')).to_be_visible()
    expect(page.locator('.paper-abstract')).to_be_visible()
    expect(page.locator('.paper-authors')).to_be_visible()

# Responsive design tests
def test_responsive_design(page: Page):
    """Test that the site is responsive on different screen sizes"""
    # Test mobile viewport
    page.set_viewport_size({"width": 375, "height": 667})
    page.goto("/")
    # Check that mobile menu is visible
    expect(page.locator('header')).to_be_visible()

    # Test desktop viewport
    page.set_viewport_size({"width": 1280, "height": 800})
    page.goto("/")
    # Check that desktop navigation is visible
    expect(page.locator('header')).to_be_visible()

def test_mobile_navigation(page: Page):
    """Test mobile navigation menu"""
    # Set mobile viewport
    page.set_viewport_size({"width": 375, "height": 667})
    page.goto("/")

    # Check that mobile navigation works
    # This is a placeholder - update with actual selectors for your mobile menu
    if page.locator('.mobile-menu-button').count() > 0:
        page.click('.mobile-menu-button')
        expect(page.locator('.mobile-menu')).to_be_visible()

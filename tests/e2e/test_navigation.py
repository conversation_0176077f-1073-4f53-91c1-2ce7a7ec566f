"""
Tests for basic navigation and page structure
"""
import pytest
from playwright.sync_api import Page, expect

def test_homepage(page: Page):
    """Test that the homepage loads correctly"""
    page.goto("/")
    expect(page).to_have_title("Moatless")
    
    # Check for basic page elements
    expect(page.locator('header')).to_be_visible()
    expect(page.locator('footer')).to_be_visible()

def test_search_page(page: Page):
    """Test that the search page loads correctly"""
    page.goto("/search")
    expect(page).to_have_title("Moatless - Search")

def test_graph_page(page: Page):
    """Test that the knowledge graph page loads correctly"""
    page.goto('/graph')
    expect(page.locator('#graph-container')).to_be_visible()

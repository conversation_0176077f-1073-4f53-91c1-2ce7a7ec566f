"""
Tests for responsive design
"""
import pytest
from playwright.sync_api import Page, expect

def test_responsive_design(page: Page):
    """Test that the site is responsive on different screen sizes"""
    # Test mobile viewport
    page.set_viewport_size({"width": 375, "height": 667})
    page.goto("/")
    # Check that mobile menu is visible
    expect(page.locator('header')).to_be_visible()
    
    # Test desktop viewport
    page.set_viewport_size({"width": 1280, "height": 800})
    page.goto("/")
    # Check that desktop navigation is visible
    expect(page.locator('header')).to_be_visible()
    
def test_mobile_navigation(page: Page):
    """Test mobile navigation menu"""
    # Set mobile viewport
    page.set_viewport_size({"width": 375, "height": 667})
    page.goto("/")
    
    # Check that mobile navigation works
    # This is a placeholder - update with actual selectors for your mobile menu
    if page.locator('.mobile-menu-button').is_visible():
        page.click('.mobile-menu-button')
        expect(page.locator('.mobile-menu')).to_be_visible()

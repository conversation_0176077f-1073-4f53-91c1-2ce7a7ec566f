"""
Tests for search functionality
"""
import pytest
from playwright.sync_api import Page, expect

def test_search_functionality(page: Page):
    """Test that search works correctly"""
    page.goto("/")
    page.fill('input[type="search"]', 'machine learning')
    page.press('input[type="search"]', 'Enter')
    expect(page.locator('.paper-list')).to_be_visible()

    # Check that search results are displayed
    expect(page.locator('.paper-item')).to_be_visible()

def test_paper_details(page: Page):
    """Test that paper details can be viewed"""
    page.goto('/')
    # For the mock server, we'll navigate directly to a paper page
    # since we can't actually click and follow links in the mock
    page.goto('/paper/123')
    expect(page.locator('.paper-title')).to_be_visible()
    expect(page.locator('.paper-abstract')).to_be_visible()
    expect(page.locator('.paper-authors')).to_be_visible()

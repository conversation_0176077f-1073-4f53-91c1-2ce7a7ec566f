import sqlite3

def fetch_and_print_raw_tags():
    # Connect to your SQLite database
    conn = sqlite3.connect('data/summaries.db')
    cursor = conn.cursor()

    # Execute the SQL query to fetch all entries from your table
    cursor.execute('SELECT * FROM summaries')

    # Fetch all results
    all_entries = cursor.fetchall()

    # Print the raw tags and their types
    for row in all_entries:
        tags = row[8] if row[8] else []  # Adjust index based on your table structure
        print(f"Raw tags: {tags} (Type: {type(tags)})")

    # Close the connection
    conn.close()

# Run the function to inspect the raw tags
fetch_and_print_raw_tags()
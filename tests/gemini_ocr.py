from pdf2image import convert_from_path
from PIL import Image
import io
import base64
import os 
import aiohttp

from openai import OpenAI

import asyncio

from dotenv import load_dotenv, find_dotenv
load_dotenv(find_dotenv())

client = OpenAI(
    api_key=os.getenv("GEMINI_API_KEY"), 
    base_url=os.getenv("GEMINI_BASE_URL")
    )

def ocr_chunking():
    gemini_ocr_chunking = f"""\
    OCR the following page into Markdown. Tables should be formatted as HTML. 
    Do not sorround your output with triple backticks.

    Chunk the document into sections of roughly 250 - 1000 words. Our goal is 
    to identify parts of the page with same semantic theme. These chunks will 
    be embedded and used in a RAG pipeline. 

    Surround the chunks with <chunk> </chunk> html tags.
    """
    return gemini_ocr_chunking

async def process_pdf_content(client, abs_url: str) -> str:
    """
    Fetches PDF from arXiv, converts to images, processes with LLM and saves text output.
    
    Args:
        abs_url (str): The arXiv abstract URL
        
    Returns:
        str: Path to the saved text file or error message
    """
    try:
        # Convert abs URL to PDF URL
        paper_id = abs_url.split('/')[-1]
        pdf_url = abs_url.replace('/abs/', '/pdf/') + '.pdf'
        output_path = f"paper_texts/{paper_id}.txt"

        # Create output directory if it doesn't exist
        os.makedirs("paper_texts", exist_ok=True)
        
        # Download PDF
        timeout = aiohttp.ClientTimeout(total=30)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.get(pdf_url) as response:
                if response.status != 200:
                    return f"Error fetching PDF: {response.status}"
                
                # Save PDF temporarily
                pdf_content = await response.read()
                temp_pdf = f"temp_{paper_id}.pdf"
                with open(temp_pdf, 'wb') as f:
                    f.write(pdf_content)

        try:
            # Convert PDF pages to images
            images = convert_from_path(temp_pdf)
            full_text = []

            # Process each page
            for i, image in enumerate(images):
                # Convert PIL image to bytes and base64
                img_byte_arr = io.BytesIO()
                image.save(img_byte_arr, format='PNG')
                img_base64 = base64.b64encode(img_byte_arr.getvalue()).decode('utf-8')

                # Send to LLM API
                response = client.chat.completions.create(
                    messages=[{
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": ocr_chunking()
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{img_base64}"
                                }
                            }
                        ]
                    }],
                    model=os.getenv("GEMINI_MODEL")
                )

                # Add processed text to collection
                processed_text = response.choices[0].message.content.strip()
                full_text.append(f"--- Page {i+1} ---\n{processed_text}")

            # Save combined text to file
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write('\n\n'.join(full_text))

            return output_path

        finally:
            # Clean up temporary PDF file
            if os.path.exists(temp_pdf):
                os.remove(temp_pdf)
                
    except aiohttp.ClientError as e:
        return f"Network error: {str(e)}"
    except Exception as e:
        return f"Error processing PDF: {str(e)}"
    
if __name__ == "__main__":
    abs_url = "https://arxiv.org/abs/2502.14727"
    result = asyncio.run(process_pdf_content(client, abs_url))
    print(result)
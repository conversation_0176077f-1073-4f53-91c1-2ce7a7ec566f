import asyncio
import unittest
import sys
import os
from unittest.mock import patch, MagicMock

# Add parent directory to path to import module
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from arxiv_html_checker import (
    get_paper_data,
    check_html_availability,
    download_html_content,
    download_pdf_content,
    download_paper_content,
    get_full_paper_text
)

# Set up event loop for asyncio tests
def async_test(coro):
    def wrapper(*args, **kwargs):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(coro(*args, **kwargs))
        finally:
            loop.close()
            asyncio.set_event_loop(None)
    return wrapper


class MockResponse:
    def __init__(self, status, content=None):
        self.status = status
        self.content = content
    
    async def text(self):
        return self.content
    
    async def read(self):
        return self.content
        
    async def __aenter__(self):
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass


class TestArxivHtmlChecker(unittest.TestCase):
    """Test cases for the arxiv_html_checker module"""
    
    @async_test
    async def test_paper_id_extraction(self):
        """Test extraction of paper ID from URL"""
        test_cases = [
            ("https://arxiv.org/abs/2502.14727", "2502.14727"),
            ("https://arxiv.org/abs/1706.03762", "1706.03762"),
            ("https://arxiv.org/abs/2310.06825v1", "2310.06825v1"),
        ]
        
        for url, expected_id in test_cases:
            # Mock the check_html_availability to avoid actual network requests
            with patch('arxiv_html_checker.check_html_availability') as mock_check:
                mock_check.return_value = (False, 404)
                
                # Run the function and get results
                paper_data = await get_paper_data(url)
                
                # Check that the ID was extracted correctly
                self.assertEqual(paper_data['id'], expected_id)
    
    @patch('aiohttp.ClientSession.head')
    @async_test
    async def test_html_availability_check(self, mock_head):
        """Test checking HTML availability"""
        # Setup mock for a paper with HTML version
        mock_response_with_html = MagicMock()
        mock_response_with_html.status = 200
        mock_response_with_html.__aenter__.return_value = mock_response_with_html
        
        # Setup mock for a paper without HTML version
        mock_response_without_html = MagicMock()
        mock_response_without_html.status = 404
        mock_response_without_html.__aenter__.return_value = mock_response_without_html
        
        # Test case 1: Paper with HTML version
        mock_head.return_value = mock_response_with_html
        has_html, status = await check_html_availability("https://arxiv.org/html/1706.03762")
        self.assertTrue(has_html)
        self.assertEqual(status, 200)
        
        # Test case 2: Paper without HTML version
        mock_head.return_value = mock_response_without_html
        has_html, status = await check_html_availability("https://arxiv.org/html/2502.14727")
        self.assertFalse(has_html)
        self.assertEqual(status, 404)
    
    @patch('aiohttp.ClientSession.get')
    @async_test
    async def test_download_html_content(self, mock_get):
        """Test downloading HTML content"""
        # Setup mock for successful download
        mock_response_success = MockResponse(200, "<html>Paper content</html>")
        
        # Setup mock for failed download
        mock_response_failure = MockResponse(404)
        
        # Test case 1: Successful HTML download
        mock_get.return_value = mock_response_success
        content = await download_html_content("https://arxiv.org/html/1706.03762")
        self.assertEqual(content, "<html>Paper content</html>")
        
        # Test case 2: Failed HTML download
        mock_get.return_value = mock_response_failure
        content = await download_html_content("https://arxiv.org/html/2502.14727")
        self.assertIsNone(content)
    
    @patch('aiohttp.ClientSession.get')
    @async_test
    async def test_download_pdf_content(self, mock_get):
        """Test downloading PDF content"""
        # Setup mock for successful download
        pdf_bytes = b"%PDF-1.5\n..."
        mock_response_success = MockResponse(200, pdf_bytes)
        
        # Setup mock for failed download
        mock_response_failure = MockResponse(404)
        
        # Test case 1: Successful PDF download
        mock_get.return_value = mock_response_success
        content = await download_pdf_content("https://arxiv.org/pdf/1706.03762.pdf")
        self.assertEqual(content, pdf_bytes)
        
        # Test case 2: Failed PDF download
        mock_get.return_value = mock_response_failure
        content = await download_pdf_content("https://arxiv.org/pdf/2502.14727.pdf")
        self.assertIsNone(content)
    
    @patch('arxiv_html_checker.download_html_content')
    @patch('arxiv_html_checker.download_pdf_content')
    @async_test
    async def test_content_preference(self, mock_pdf, mock_html):
        """Test preference for HTML or PDF content"""
        # Setup mocks for successful downloads
        mock_html.return_value = "<html>Paper content</html>"
        mock_pdf.return_value = b"%PDF-1.5\n..."
        
        # Test data with HTML available
        paper_with_html = {
            'id': '1706.03762',
            'has_html': True,
            'html_url': 'https://arxiv.org/html/1706.03762',
            'pdf_url': 'https://arxiv.org/pdf/1706.03762.pdf'
        }
        
        # Test data without HTML available
        paper_without_html = {
            'id': '2502.14727',
            'has_html': False,
            'html_url': None,
            'pdf_url': 'https://arxiv.org/pdf/2502.14727.pdf'
        }
        
        # Test case 1: Prefer HTML and HTML available
        content = await download_paper_content(paper_with_html, prefer_html=True)
        self.assertEqual(content, "<html>Paper content</html>")
        mock_html.assert_called_once()
        mock_pdf.assert_not_called()
        
        # Reset mocks
        mock_html.reset_mock()
        mock_pdf.reset_mock()
        
        # Test case 2: Prefer PDF even when HTML available
        content = await download_paper_content(paper_with_html, prefer_html=False)
        self.assertEqual(content, b"%PDF-1.5\n...")
        mock_html.assert_not_called()
        mock_pdf.assert_called_once()
        
        # Reset mocks
        mock_html.reset_mock()
        mock_pdf.reset_mock()
        
        # Test case 3: HTML not available, falls back to PDF
        content = await download_paper_content(paper_without_html, prefer_html=True)
        self.assertEqual(content, b"%PDF-1.5\n...")
        mock_html.assert_not_called()
        mock_pdf.assert_called_once()
        
    @patch('arxiv_html_checker.extract_text_from_html')
    @patch('arxiv_html_checker.download_html_content')
    @patch('arxiv_html_checker.get_paper_data')
    @patch('os.path.exists')
    @async_test
    async def test_get_full_paper_text(self, mock_exists, mock_get_paper, mock_html_content, mock_extract_text):
        """Test full paper text extraction with HTML"""
        # Setup mocks
        mock_exists.return_value = False  # File doesn't exist yet
        
        # Mock paper data with HTML available
        mock_get_paper.return_value = {
            'id': '1706.03762',
            'has_html': True,
            'html_url': 'https://arxiv.org/html/1706.03762'
        }
        
        # Mock HTML content download
        mock_html_content.return_value = "<html>Paper content</html>"
        
        # Mock text extraction
        mock_extract_text.return_value = "Extracted paper text from HTML."
        
        # Temporarily mock save_paper_text to avoid actually writing files
        with patch('arxiv_html_checker.save_paper_text') as mock_save:
            mock_save.return_value = "paper_texts/1706.03762.txt"
            
            # Test full paper extraction
            text, used_html = await get_full_paper_text("https://arxiv.org/abs/1706.03762")
            
            # Verify correct flow and results
            self.assertEqual(text, "Extracted paper text from HTML.")
            self.assertTrue(used_html)
            mock_get_paper.assert_called_once()
            mock_html_content.assert_called_once()
            mock_extract_text.assert_called_once()
            mock_save.assert_called_once()


if __name__ == '__main__':
    unittest.main()
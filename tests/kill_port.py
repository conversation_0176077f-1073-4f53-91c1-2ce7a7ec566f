#!/usr/bin/env python3
"""
Helper script to kill processes using a specific port.
Useful when tests fail to start because the port is already in use.
"""

import os
import sys
import subprocess
import platform

def kill_process_on_port(port):
    """Kill any process using the specified port"""
    system = platform.system()
    
    try:
        if system == 'Darwin':  # macOS
            # Find PID using the port
            cmd = f"lsof -i tcp:{port} -t"
            result = subprocess.check_output(cmd, shell=True).decode('utf-8').strip()
            
            if result:
                pids = result.split('\n')
                for pid in pids:
                    print(f"Killing process {pid} using port {port}")
                    os.system(f"kill -9 {pid}")
                return True
            else:
                print(f"No process found using port {port}")
                return False
                
        elif system == 'Linux':
            # Find PID using the port on Linux
            cmd = f"fuser -n tcp {port} 2>/dev/null"
            result = subprocess.check_output(cmd, shell=True).decode('utf-8').strip()
            
            if result:
                pids = result.split()
                for pid in pids:
                    print(f"Killing process {pid} using port {port}")
                    os.system(f"kill -9 {pid}")
                return True
            else:
                print(f"No process found using port {port}")
                return False
                
        elif system == 'Windows':
            # Find PID using the port on Windows
            cmd = f"netstat -ano | findstr :{port}"
            result = subprocess.check_output(cmd, shell=True).decode('utf-8').strip()
            
            if result:
                lines = result.split('\n')
                for line in lines:
                    if 'LISTENING' in line:
                        pid = line.split()[-1]
                        print(f"Killing process {pid} using port {port}")
                        os.system(f"taskkill /F /PID {pid}")
                return True
            else:
                print(f"No process found using port {port}")
                return False
        else:
            print(f"Unsupported operating system: {system}")
            return False
            
    except subprocess.CalledProcessError:
        print(f"No process found using port {port}")
        return False
    except Exception as e:
        print(f"Error killing process on port {port}: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python kill_port.py PORT_NUMBER")
        sys.exit(1)
        
    try:
        port = int(sys.argv[1])
        success = kill_process_on_port(port)
        if success:
            print(f"Successfully killed processes on port {port}")
        else:
            print(f"No processes were killed on port {port}")
    except ValueError:
        print(f"Invalid port number: {sys.argv[1]}")
        sys.exit(1)

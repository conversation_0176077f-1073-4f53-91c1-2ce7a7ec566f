#!/usr/bin/env python3
"""
Mock server for Playwright tests.
This provides a simple HTTP server that mimics the behavior of the Moatless application
for testing purposes when the actual application is not available.
"""

from http.server import HTTPServer, SimpleHTTPRequestHandler
import threading
import sys
import signal
import time

class Mock<PERSON>oatlessHandler(SimpleHTTPRequestHandler):
    """Custom handler for mocking Moatless application responses"""

    def do_GET(self):
        """Handle GET requests with mock responses"""
        if self.path == '/' or self.path == '/index.html':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            html = """
            <!DOCTYPE html>
            <html>
                <head><title>Moatless</title></head>
                <body>
                    <header>Mock Header</header>
                    <h1>Moatless Test Page</h1>
                    <div class="paper-list">
                        <div class="paper-item">Sample Paper</div>
                    </div>
                    <input type="search" placeholder="Search papers...">
                    <footer><PERSON>ck Footer</footer>
                </body>
            </html>
            """
            self.wfile.write(html.encode())
        elif self.path == '/search':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            html = """
            <!DOCTYPE html>
            <html>
                <head><title>Moatless - Search</title></head>
                <body>
                    <header>Mock Header</header>
                    <h1>Search Page</h1>
                    <div class="search-container">
                        <input type="search" placeholder="Search papers...">
                    </div>
                    <footer>Mock Footer</footer>
                </body>
            </html>
            """
            self.wfile.write(html.encode())
        elif self.path == '/graph':
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            html = """
            <!DOCTYPE html>
            <html>
                <head><title>Moatless - Knowledge Graph</title></head>
                <body>
                    <header>Mock Header</header>
                    <h1>Knowledge Graph</h1>
                    <div id="graph-container">Mock Graph</div>
                    <footer>Mock Footer</footer>
                </body>
            </html>
            """
            self.wfile.write(html.encode())
        elif self.path.startswith('/paper/'):
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            html = """
            <!DOCTYPE html>
            <html>
                <head><title>Moatless - Paper Details</title></head>
                <body>
                    <header>Mock Header</header>
                    <div class="paper-title">Sample Paper Title</div>
                    <div class="paper-authors">Author 1, Author 2</div>
                    <div class="paper-abstract">This is a sample abstract for testing purposes.</div>
                    <footer>Mock Footer</footer>
                </body>
            </html>
            """
            self.wfile.write(html.encode())
        else:
            super().do_GET()

def run_server(port=8000, max_attempts=5):
    """Run the mock server on the specified port"""
    for attempt in range(max_attempts):
        try:
            server = HTTPServer(('localhost', port), MockMoatlessHandler)
            print(f"Mock Moatless server running at http://localhost:{port}")
            print("Press Ctrl+C to stop")

            def signal_handler(sig, frame):
                print("\nShutting down server...")
                server.shutdown()
                server.server_close()
                sys.exit(0)

            signal.signal(signal.SIGINT, signal_handler)

            try:
                server.serve_forever()
            except KeyboardInterrupt:
                pass
            return True
        except OSError as e:
            if e.errno == 98 or e.errno == 48:  # Address already in use
                print(f"Port {port} is already in use, trying port {port + 1}")
                port += 1
                if attempt == max_attempts - 1:
                    print(f"Failed to find an available port after {max_attempts} attempts")
                    return False
            else:
                print(f"Error starting server: {e}")
                return False

if __name__ == "__main__":
    port = 8000
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print(f"Invalid port number: {sys.argv[1]}")
            sys.exit(1)

    success = run_server(port)
    if not success:
        sys.exit(1)

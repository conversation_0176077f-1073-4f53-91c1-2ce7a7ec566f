"""
<PERSON><PERSON><PERSON> to directly test the Notion integration without mocking
"""
from dotenv import load_dotenv
import os
from notion_client import Client
from datetime import datetime

def check_notion_database():
    """Query the Notion database directly without mocking"""
    # Load environment variables
    load_dotenv()
    
    # Get credentials from environment
    api_key = os.getenv("NOTION_API_KEY")
    database_id = os.getenv("NOTION_DATABASE_ID")
    
    # Check if credentials are available
    if not api_key or not database_id:
        print("Error: Missing NOTION_API_KEY or NOTION_DATABASE_ID in .env file")
        return False
        
    # Remove any query parameters from database_id if present
    if "?" in database_id:
        database_id = database_id.split("?")[0]
    
    print(f"API Key: {api_key[:4]}...{api_key[-4:]}")
    print(f"Database ID: {database_id}")
    
    try:
        # Initialize Notion client
        notion = Client(auth=api_key)
        
        # Test the connection by retrieving database metadata
        db_info = notion.databases.retrieve(database_id=database_id)
        print("Successfully connected to Notion database!")
        print(f"Database title: {db_info['title'][0]['plain_text'] if db_info.get('title') and db_info['title'] else 'Untitled'}")
        print(f"Database URL: https://www.notion.so/{database_id}")
        
        # Query all pages in the database
        pages_response = notion.databases.query(database_id=database_id)
        results = pages_response.get("results", [])
        
        print(f"\nFound {len(results)} pages in the database:")
        
        # Display each page
        for i, page in enumerate(results):
            # Get page ID
            page_id = page.get("id", "Unknown ID")
            
            # Extract title
            title = "Untitled"
            try:
                title_property = page.get("properties", {}).get("Title", {})
                if title_property and title_property.get("title"):
                    title_elements = title_property["title"]
                    if title_elements:
                        title = title_elements[0].get("plain_text", "Untitled")
            except Exception as e:
                print(f"Error extracting title: {e}")
            
            # Extract URL
            url = "No URL"
            try:
                url_property = page.get("properties", {}).get("URL", {})
                if url_property and "url" in url_property:
                    url = url_property["url"] or "No URL"
            except Exception as e:
                print(f"Error extracting URL: {e}")
            
            print(f"  {i+1}. {title} | {url} | {page_id}")
        
        if not results:
            print("  No pages found in database")
        
        # Get database properties to understand what's available
        print("\nRetrieving database properties...")
        properties = db_info.get("properties", {})
        print(f"Available properties: {list(properties.keys())}")
        
        # Show property details for debugging
        for prop_name, prop_details in properties.items():
            prop_type = prop_details.get("type", "unknown")
            print(f"  - {prop_name}: {prop_type}")
        
        # Add new properties if needed
        print("\nChecking if database needs new properties...")
        new_properties = {}
        db_needs_update = False
        
        # Check for URL property
        url_property_exists = False
        for prop_name, prop_details in properties.items():
            if prop_details.get("type") == "url":
                url_property_exists = True
                break
                
        if not url_property_exists:
            new_properties["URL"] = {"url": {}}
            db_needs_update = True
            print("Adding URL property to database")
        
        # Check for Date property
        date_property_exists = False
        for prop_name, prop_details in properties.items():
            if prop_details.get("type") == "date":
                date_property_exists = True
                break
                
        if not date_property_exists:
            new_properties["Date Added"] = {"date": {}}
            db_needs_update = True
            print("Adding Date Added property to database")
        
        # Update database if needed
        if db_needs_update:
            try:
                notion.databases.update(
                    database_id=database_id,
                    properties=new_properties
                )
                print("Database schema updated successfully")
                
                # Retrieve updated database properties
                db_info = notion.databases.retrieve(database_id=database_id)
                properties = db_info.get("properties", {})
                print("Updated properties:", list(properties.keys()))
            except Exception as e:
                print(f"Error updating database schema: {e}")
        
        # Add a test page
        print("\nAdding a test page...")
        current_date = datetime.now().strftime('%Y-%m-%d')
        test_title = "Test Page from Direct API Check"
        test_url = "https://example.com/direct-test"
        
        # Format for easy copying into Substack
        linked_title = f"{test_title} [{test_url}]"
        
        # Prepare properties based on what's available in the database
        page_properties = {}
        
        # Look for a title property - different databases might name this differently
        title_property_name = None
        for prop_name, prop_details in properties.items():
            if prop_details.get("type") == "title":
                title_property_name = prop_name
                break
        
        if title_property_name:
            page_properties[title_property_name] = {
                "title": [
                    {
                        "text": {
                            "content": linked_title
                        }
                    }
                ]
            }
        else:
            print("Warning: No title property found in the database")
            
        # Look for a URL property
        url_property_name = None
        for prop_name, prop_details in properties.items():
            if prop_details.get("type") == "url":
                url_property_name = prop_name
                break
                
        if url_property_name:
            page_properties[url_property_name] = {
                "url": test_url
            }
        
        # Look for a date property
        date_property_name = None
        for prop_name, prop_details in properties.items():
            if prop_details.get("type") == "date":
                date_property_name = prop_name
                break
                
        if date_property_name:
            page_properties[date_property_name] = {
                "date": {
                    "start": current_date
                }
            }
            
        print(f"Creating page with properties: {page_properties}")
        
        new_page = notion.pages.create(
            parent={"database_id": database_id},
            properties=page_properties
        )
        
        print(f"Successfully created test page with ID: {new_page['id']}")
        
        # Query again to show the new page
        pages_response = notion.databases.query(database_id=database_id)
        results = pages_response.get("results", [])
        
        print(f"\nAfter adding test page, found {len(results)} pages in the database:")
        for i, page in enumerate(results):
            # Extract title
            title = "Untitled"
            try:
                title_property = page.get("properties", {}).get("Title", {})
                if title_property and title_property.get("title"):
                    title_elements = title_property["title"]
                    if title_elements:
                        title = title_elements[0].get("plain_text", "Untitled")
            except Exception as e:
                print(f"Error extracting title: {e}")
                
            print(f"  {i+1}. {title}")
        
        return True
        
    except Exception as e:
        print(f"Error testing Notion integration: {e}")
        return False

if __name__ == "__main__":
    check_notion_database()
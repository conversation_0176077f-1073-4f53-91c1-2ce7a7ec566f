import pymongo
import numpy as np
from scipy.spatial.distance import cdist
import os
import sys
import json
import datetime
from datetime import timedel<PERSON>
# Add the project root to the path to import from src
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from src.db.mongo import setup_mongo_client, setup_collections, load_json_data_to_mock

# Note: The 'openai' library is mentioned in the prompt for accessing an OpenAI-compatible
# endpoint for Gemini. However, this script assumes the embeddings are *already computed*
# and stored in MongoDB. Therefore, the 'openai' library is NOT directly needed
# for the recommendation logic itself, only for the prior step of generating
# and storing the embeddings (which is outside the scope of this specific script).

# --- Configuration ---

# MongoDB Configuration
PAPERS_COLLECTION = "summaries"  # Collection with paper details (abs_url, abstract, embedding)
SAVED_PAPERS_COLLECTION = "saved_papers"  # Collection with user saved papers (user_id, abs_url)
DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "data", "mongodb")

# Recommendation Configuration
DEFAULT_TOP_N = 10  # Default number of recommendations to return
RECENCY_BOOST_SAME_DAY = 3.0  # Multiplier for papers from the same day as reference date
RECENCY_BOOST_1_DAY = 2.0  # Multiplier for papers from 1 day before reference date
RECENCY_BOOST_2_DAY = 1.5  # Multiplier for papers from 2 days before reference date
RECENCY_BOOST_3_DAY = 1.2  # Multiplier for papers from 3 days before reference date
RECENCY_DAYS = 3  # Maximum number of days to consider for any boost
USE_REFERENCE_DATE = True  # Use the latest paper date as a reference rather than today's date

# --- MongoDB Connection ---

def get_db_connection(local_mode=True):
    """Establishes connection to MongoDB using the project's connection utilities.
    
    Args:
        local_mode (bool): Whether to use local in-memory MongoDB
        
    Returns:
        dict: Dictionary containing MongoDB collections
    """
    try:
        client = setup_mongo_client(local_mode=local_mode)
        print("MongoDB connection successful.")
        collections = setup_collections(client)
        return collections
    except Exception as e:
        print(f"Error connecting to MongoDB: {e}")
        raise

# --- Recommendation Logic ---

def recommend_papers(user_id: str, collections: dict, top_n: int = DEFAULT_TOP_N) -> list:
    """
    Generates paper recommendations for a user based on their saved papers' embeddings.

    Args:
        user_id: The ID of the user for whom to generate recommendations.
        collections: Dictionary containing MongoDB collections.
        top_n: The maximum number of recommendations to return.

    Returns:
        A list of recommended paper abs_urls, sorted by relevance.
    """
    papers_coll = collections[PAPERS_COLLECTION]
    saved_papers_coll = collections[SAVED_PAPERS_COLLECTION]

    # 1. Identify User's Saved Papers
    try:
        saved_paper_docs = list(saved_papers_coll.find({'user_id': user_id}, {'abs_url': 1, '_id': 0}))
        saved_urls = [doc['abs_url'] for doc in saved_paper_docs]

        if not saved_urls:
            print(f"User {user_id} has no saved papers. Cannot generate recommendations.")
            return []

        print(f"Successfully retrieved {len(saved_urls)} saved papers for user {user_id}.")

    except Exception as e:
        print(f"Error fetching saved papers for user {user_id}: {e}")
        return []

    # 2. Retrieve Embeddings for Saved Papers
    try:
        # Fetch papers ensuring the embedding field exists and is not empty/null
        saved_papers_with_embeddings = list(papers_coll.find(
            {'abs_url': {'$in': saved_urls}, 'embedding': {'$exists': True, '$ne': None, '$not': {'$size': 0}}},
            {'embedding': 1, 'abs_url': 1}
        ))

        if not saved_papers_with_embeddings:
            print(f"Could not find valid embeddings for any of the saved papers for user {user_id}.")
            return []

        saved_embeddings = [doc['embedding'] for doc in saved_papers_with_embeddings]
        print(f"Retrieved embeddings for {len(saved_embeddings)} saved papers.")

    except Exception as e:
        print(f"Error fetching embeddings for saved papers: {e}")
        return []

    # 3. Calculate User Profile Vector (Average Embedding)
    # Gemini embeddings are numerical vectors representing semantic meaning
    user_profile_vector = np.mean(np.array(saved_embeddings), axis=0)
    if user_profile_vector is None or user_profile_vector.size == 0:
         print(f"Could not calculate user profile vector for user {user_id}.")
         return []
    print(f"Calculated user profile vector (shape: {user_profile_vector.shape}).")


    # 4. Retrieve Embeddings for Candidate Papers (Papers NOT saved by the user)
    try:
        # Fetch candidate papers ensuring the embedding field exists and is not empty/null
        # Include published date for recency calculation
        candidate_docs_cursor = papers_coll.find(
            {'abs_url': {'$nin': saved_urls}, 'embedding': {'$exists': True, '$ne': None, '$not': {'$size': 0}}},
            {'abs_url': 1, 'embedding': 1, 'published': 1, 'updated': 1}
        )

        # Process candidates efficiently (avoid loading all into memory if very large)
        candidate_ids = []
        candidate_embeddings_list = []
        candidate_dates = []  # Store dates for recency calculation
        
        # Get today's date for recency calculation
        today = datetime.date.today()
        
        for doc in candidate_docs_cursor:
            # Basic validation of embedding structure (e.g., ensure it's a list/vector)
            if isinstance(doc.get('embedding'), list) and len(doc['embedding']) > 0:
                candidate_ids.append(doc['abs_url'])
                candidate_embeddings_list.append(doc['embedding'])
                
                # Store the date (use updated date if available, otherwise published date)
                paper_date = None
                if 'updated' in doc and doc['updated']:
                    paper_date = doc['updated']
                elif 'published' in doc and doc['published']:
                    paper_date = doc['published']
                    
                # Default to today if no date is available
                if not paper_date:
                    paper_date = today.strftime("%Y-%m-%d")
                    
                candidate_dates.append(paper_date)
                
        if not candidate_ids:
            print("No candidate papers found for recommendation.")
            return []

        candidate_embeddings = np.array(candidate_embeddings_list)
        print(f"Found {len(candidate_ids)} candidate papers with valid embeddings.")

    except Exception as e:
        print(f"Error fetching candidate papers: {e}")
        return []

    # 5. Calculate Similarity (Cosine Similarity)
    try:
        # Reshape user profile vector for cdist (needs 2D array)
        user_profile_2d = user_profile_vector.reshape(1, -1)

        # Calculate cosine distance (1 - similarity)
        # cdist calculates the distance between each candidate and the user profile
        distances = cdist(candidate_embeddings, user_profile_2d, 'cosine')

        # Convert distances to similarities (similarity = 1 - distance)
        similarities = 1 - distances.flatten() # flatten converts the result to a 1D array

        if similarities.size != len(candidate_ids):
             print("Mismatch between number of similarities and candidate IDs. Aborting.")
             return []

        print(f"Calculated similarities for {len(similarities)} candidates.")

        # 5B. Apply recency boost to similarity scores
        today = datetime.date.today()
        boosted_similarities = []
        
        # Store both the original and boosted similarities for reporting
        similarity_info = []
        
        # Parse all paper dates for finding the reference date
        parsed_dates = []
        for date_str in candidate_dates:
            try:
                paper_date = datetime.datetime.strptime(date_str, "%Y-%m-%d").date()
                parsed_dates.append(paper_date)
            except ValueError:
                # Skip unparseable dates
                pass
        
        # Find the most recent date from the papers as reference if enabled
        reference_date = today
        if USE_REFERENCE_DATE and parsed_dates:
            reference_date = max(parsed_dates)
            print(f"Using reference date: {reference_date} instead of today ({today})")
        
        for i, similarity in enumerate(similarities):
            paper_date_str = candidate_dates[i]
            try:
                # Parse the date string into a datetime object
                paper_date = datetime.datetime.strptime(paper_date_str, "%Y-%m-%d").date()
                
                # Calculate days difference from reference date
                days_old = (reference_date - paper_date).days
                
                # Apply granular recency boost with specific multiplier for each day
                recency_multiplier = 1.0  # Default multiplier (no boost)
                
                if days_old <= 0:  # Published on reference date or newer
                    recency_multiplier = RECENCY_BOOST_SAME_DAY
                elif days_old == 1:  # Published 1 day before reference date
                    recency_multiplier = RECENCY_BOOST_1_DAY
                elif days_old == 2:  # Published 2 days before reference date
                    recency_multiplier = RECENCY_BOOST_2_DAY
                elif days_old == 3:  # Published 3 days before reference date
                    recency_multiplier = RECENCY_BOOST_3_DAY
                
                # Apply boost to similarity score
                boosted_similarity = similarity * recency_multiplier
                
                # Cap at 1.0 (100% similarity)
                boosted_similarity = min(boosted_similarity, 1.0)
                
                boosted_similarities.append(boosted_similarity)
                similarity_info.append({
                    'abs_url': candidate_ids[i],
                    'original_similarity': similarity,
                    'boosted_similarity': boosted_similarity,
                    'date': paper_date_str,
                    'days_old': days_old,
                    'recency_multiplier': recency_multiplier,
                    'reference_date': reference_date.strftime("%Y-%m-%d")
                })
                
            except ValueError:
                # If date parsing fails, use the original similarity
                boosted_similarities.append(similarity)
                similarity_info.append({
                    'abs_url': candidate_ids[i],
                    'original_similarity': similarity,
                    'boosted_similarity': similarity,
                    'date': paper_date_str,
                    'days_old': None,
                    'recency_multiplier': 1.0,
                    'reference_date': reference_date.strftime("%Y-%m-%d")
                })
        
        print(f"Applied recency boost to similarity scores.")

    except Exception as e:
        print(f"Error calculating similarities: {e}")
        import traceback
        traceback.print_exc()
        return []

    # 6. Rank and Select Recommendations
    # Combine candidate IDs with their similarity scores
    results = list(zip(candidate_ids, boosted_similarities, similarity_info))

    # Sort by boosted similarity score in descending order
    results.sort(key=lambda x: x[1], reverse=True)

    # Select the top N paper IDs with their scores and info
    top_results = results[:top_n]
    recommendations_with_scores = []
    
    for paper_url, boosted_score, info in top_results:
        # Round scores to 2 decimal places
        original_score = round(info['original_similarity'] * 100, 2)
        boosted_score = round(boosted_score * 100, 2)
        days_old = info['days_old']
        
        recommendations_with_scores.append((
            paper_url, 
            boosted_score,
            {
                'original_score': original_score,
                'date': info['date'],
                'days_old': days_old,
                'recency_multiplier': round(info['recency_multiplier'], 2)
            }
        ))
    
    # Just the URLs for compatibility with other code
    recommendations = [paper_url for paper_url, _, _ in recommendations_with_scores]

    print(f"Generated {len(recommendations)} recommendations.")

    # 7. Return Results with scores
    return recommendations_with_scores

# --- Add embeddings to papers (since they're not in the sample data) ---
def add_embeddings_to_papers(collections, embedding_dim=1536):
    """
    Add synthetic embeddings to papers in the summaries collection.
    This is needed because the sample data doesn't include embeddings.
    
    Args:
        collections: Dictionary with MongoDB collections
        embedding_dim: Dimension of embeddings to generate
    """
    # Get all papers without embeddings
    papers_to_update = list(collections[PAPERS_COLLECTION].find({'embedding': {'$exists': False}}))
    update_count = 0
    
    # Bulk update with synthetic embeddings using abs_url as key (which is more reliable than _id)
    for paper in papers_to_update:
        # Generate a random embedding vector with the specified dimension
        embedding = np.random.rand(embedding_dim).tolist()
        
        # Update the paper document with the embedding using abs_url instead of _id
        if 'abs_url' in paper:
            collections[PAPERS_COLLECTION].update_one(
                {'abs_url': paper['abs_url']},
                {'$set': {'embedding': embedding}}
            )
            update_count += 1
    
    print(f"Added synthetic embeddings to {update_count} papers")

# --- Example Usage ---

if __name__ == "__main__":
    # Establish DB connection with local mode
    try:
        # Connect to in-memory MongoDB
        client = setup_mongo_client(local_mode=True)
        
        # Load real sample data from files
        print("\n--- Loading real sample data from disk ---")
        load_json_data_to_mock(client, DATA_DIR)
        
        # Setup collections
        collections = setup_collections(client)
        
        # Add synthetic embeddings to papers (since they're not in the sample data)
        add_embeddings_to_papers(collections)
        
        # Print some stats about the loaded data
        print(f"Loaded {collections[PAPERS_COLLECTION].count_documents({})} papers")
        print(f"Loaded {collections[SAVED_PAPERS_COLLECTION].count_documents({})} saved papers")
        
        # Get unique user IDs who have saved papers
        saved_papers_users = collections[SAVED_PAPERS_COLLECTION].distinct("user_id")
        print(f"Found {len(saved_papers_users)} unique users who have saved papers in the database")
        
        # Print the first few saved papers for debugging
        print("\n=== DEBUG: First 3 saved papers ===")
        for idx, paper in enumerate(collections[SAVED_PAPERS_COLLECTION].find().limit(3)):
            print(f"Paper {idx+1}:")
            for key, value in paper.items():
                if key != 'abstract':  # Skip long abstract for readability
                    print(f"  {key}: {value}")
            print()
        
        if saved_papers_users:
            # Select a user who has saved multiple papers
            target_user = saved_papers_users[0]  # Using the first user
            saved_count = collections[SAVED_PAPERS_COLLECTION].count_documents({"user_id": target_user})
            print(f"User {target_user} has saved {saved_count} papers")
            
            if saved_count >= 2:  # Need at least 2 saved papers to calculate a meaningful user profile
                print(f"\n--- Generating recommendations for {target_user} ---")
                recommended_items = recommend_papers(target_user, collections, top_n=5)
                
                if recommended_items:
                    # Get the reference date if it exists in the info dictionary
                    reference_date = None
                    if recommended_items and len(recommended_items[0]) > 2:
                        info = recommended_items[0][2]
                        if isinstance(info, dict) and 'reference_date' in info:
                            reference_date = info['reference_date']
                    
                    reference_msg = f" (with recency boost, reference date: {reference_date})" if reference_date else " (with recency boost)"
                    print(f"\nTop 5 recommended papers for {target_user}{reference_msg}:")
                    recommended_urls = [url for url, _, _ in recommended_items]
                    
                    for i, (paper_url, boosted_score, info) in enumerate(recommended_items):
                        days_old = info['days_old']
                        date = info['date']
                        original_score = info['original_score']
                        recency_multiplier = info['recency_multiplier']
                        
                        recency_info = f"Date: {date}"
                        if days_old is not None:
                            if days_old <= 0:
                                recency_info += " (Today!)"
                            elif days_old == 1:
                                recency_info += " (Yesterday)"
                            else:
                                recency_info += f" ({days_old} days ago)"
                        
                        print(f"{i+1}. {paper_url}")
                        print(f"   {recency_info}")
                        print(f"   Base similarity: {original_score}% → Boosted: {boosted_score}% (×{recency_multiplier})")
                    
                    # Fetch details of recommended papers
                    recommended_papers_details = collections[PAPERS_COLLECTION].find(
                        {'abs_url': {'$in': recommended_urls}}
                    )
                    print("\nDetails of recommended papers:")
                    details_map = {}
                    for doc in recommended_papers_details:
                        if 'abs_url' in doc:
                            details_map[doc['abs_url']] = doc
                    
                    # Print in recommended order
                    for paper_url, boosted_score, info in recommended_items:
                        detail = details_map.get(paper_url)
                        if detail:
                            date = info['date']
                            days_old = info['days_old']
                            original_score = info['original_score']
                            recency_multiplier = info['recency_multiplier']
                            
                            print(f"- Title: {detail.get('title', 'N/A')}")
                            tags = detail.get('tags', [])
                            if tags:
                                print(f"  Tags: {', '.join(tags)}")
                            
                            # Recency information
                            print(f"  Date: {date}", end="")
                            if days_old is not None:
                                if days_old <= 0:
                                    print(" (Today!)")
                                elif days_old == 1:
                                    print(" (Yesterday)")
                                else:
                                    print(f" ({days_old} days ago)")
                            else:
                                print("")
                                
                            # Similarity scores
                            print(f"  Base Similarity: {original_score}% → Boosted: {boosted_score}% (×{recency_multiplier})")
                            print(f"  Abstract: {detail.get('abstract', 'N/A')[:100]}...") # Show first 100 chars
                        else:
                            print(f"- URL: {paper_url} (Details not found)")
                            
                # --- Test case for user with no saved papers ---
                target_user_new = "user_without_papers"
                print(f"\n--- Generating recommendations for {target_user_new} ---")
                recommended_items_new = recommend_papers(target_user_new, collections, top_n=5)
                if not recommended_items_new:
                    print(f"No recommendations generated for {target_user_new} (as expected).")
            else:
                print(f"User {target_user} has fewer than 2 saved papers. Cannot generate meaningful recommendations.")
        else:
            print("No users with saved papers found in the dataset.")
            
    except Exception as e:
        print(f"An error occurred during the process: {e}")
        import traceback
        traceback.print_exc()
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Simple script to run the daily stats tests locally.
This allows testing without having to install pytest or other test runners.
"""

import asyncio
import sys
import os
from dotenv import load_dotenv, find_dotenv

# Load environment variables from .env file
load_dotenv(find_dotenv())

# Import environment variables
MONGO_URL_LOCAL = os.getenv("MONGO_URL_LOCAL")

# Set args.local to True to use in-memory MongoDB for testing
import argparse
parser = argparse.ArgumentParser(description='Run Moatless app for testing')
parser.add_argument('--local', action='store_true', help='Use in-memory MongoDB for local testing')
args = parser.parse_args()
args.local = True  # Always use in-memory MongoDB for testing

# Set environ variable to indicate testing mode
os.environ["TESTING"] = "1"

# Import the test class
from test_daily_stats import TestDailyStats

async def run_tests():
    print("Running daily statistics tests...\n")
    
    # Setup test instance
    test = TestDailyStats()
    
    # Test 1: Calculate daily stats for the previous day
    print("Test 1: Calculating statistics for the previous day...")
    await test.setup_method()
    try:
        await test.test_update_daily_stats()
        print("✅ Test 1 passed: Statistics calculated correctly for the previous day\n")
    except AssertionError as e:
        print(f"❌ Test 1 failed: {e}\n")
        return False
    
    # Test 2: Don't recalculate if stats already exist
    print("Test 2: Checking that existing statistics aren't recalculated...")
    await test.setup_method()
    try:
        await test.test_existing_stats_not_recalculated()
        print("✅ Test 2 passed: Existing statistics are preserved\n")
    except AssertionError as e:
        print(f"❌ Test 2 failed: {e}\n")
        return False
    
    print("All tests passed! The daily statistics for the previous day are working correctly.")
    return True

if __name__ == "__main__":
    success = asyncio.run(run_tests())
    sys.exit(0 if success else 1)
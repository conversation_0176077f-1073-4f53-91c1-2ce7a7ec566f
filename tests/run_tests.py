#!/usr/bin/env python3
"""
Helper script to run all tests with proper setup and cleanup.
This script:
1. Kills any processes using port 8000
2. Starts the mock server
3. Runs the tests
4. Cleans up
"""

import os
import sys
import subprocess
import time
import argparse
from multiprocessing import Process

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import the kill_port and mock_server modules
from tests.kill_port import kill_process_on_port
from tests.mock_server import run_server

def start_mock_server(port=8000):
    """Start the mock server in a separate process"""
    server_process = Process(target=run_server, args=(port,))
    server_process.daemon = True
    server_process.start()
    return server_process

def run_playwright_tests(test_file=None):
    """Run the Playwright tests using npx playwright"""
    if test_file and test_file.endswith('.ts'):
        # For TypeScript tests, use npx playwright
        cmd = "npx playwright test"
        if test_file:
            cmd += f" {test_file}"
    else:
        # For Python tests, use pytest
        cmd = "python -m pytest"
        if test_file:
            cmd += f" {test_file}"
        else:
            cmd += " tests/e2e"

    print(f"Running Playwright tests: {cmd}")
    return subprocess.call(cmd, shell=True)

def run_pytest_tests(test_file=None):
    """Run the pytest tests"""
    cmd = "python -m pytest"
    if test_file:
        cmd += f" {test_file}"
    else:
        cmd += " tests/e2e"

    print(f"Running pytest tests: {cmd}")
    return subprocess.call(cmd, shell=True)

def run_typescript_tests(test_file=None):
    """Run the TypeScript Playwright tests"""
    cmd = "npx playwright test"
    if test_file:
        cmd += f" {test_file}"

    print(f"Running TypeScript Playwright tests: {cmd}")
    return subprocess.call(cmd, shell=True)

def main():
    parser = argparse.ArgumentParser(description="Run tests with proper setup and cleanup")
    parser.add_argument("--port", type=int, default=8000, help="Port to use for the mock server")
    parser.add_argument("--test-file", help="Specific test file to run")
    parser.add_argument("--playwright", action="store_true", help="Run Python Playwright tests")
    parser.add_argument("--typescript", action="store_true", help="Run TypeScript Playwright tests")
    parser.add_argument("--pytest", action="store_true", help="Run pytest tests")
    args = parser.parse_args()

    # If none is specified, run all
    if not args.playwright and not args.pytest and not args.typescript:
        args.playwright = True
        args.pytest = True
        args.typescript = True

    # Kill any processes using the port
    print(f"Checking for processes using port {args.port}...")
    kill_process_on_port(args.port)

    # Start the mock server if running pytest tests
    server_process = None
    if args.pytest:
        print(f"Starting mock server on port {args.port}...")
        server_process = start_mock_server(args.port)
        # Give the server a moment to start
        time.sleep(1)

    try:
        # Run the tests
        exit_code = 0

        if args.typescript:
            typescript_exit = run_typescript_tests(args.test_file)
            exit_code = max(exit_code, typescript_exit)

        if args.playwright:
            playwright_exit = run_playwright_tests(args.test_file)
            exit_code = max(exit_code, playwright_exit)

        if args.pytest:
            pytest_exit = run_pytest_tests(args.test_file)
            exit_code = max(exit_code, pytest_exit)

        return exit_code
    finally:
        # Clean up
        if server_process:
            print("Stopping mock server...")
            server_process.terminate()
            server_process.join(timeout=2)
            if server_process.is_alive():
                print("Server didn't terminate gracefully, killing...")
                server_process.kill()

if __name__ == "__main__":
    sys.exit(main())

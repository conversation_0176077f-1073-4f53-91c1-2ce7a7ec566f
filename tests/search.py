
from fasthtml.common import *
import sqlite3

#app,rt = fast_app(default_hdrs=True) # use the default_hdrs for the search bar

# Database setup
#db = database('data/summaries.db')
#summaries = db.t.summaries

#@rt("/")
#async def get():
#    return Body(
#        Search(
#            Input(
#                type="search",
#                name="search",
#                placeholder='Enter search term',
#                hx_post='/search',
#                hx_trigger='input changed delay:500ms, search',
#                hx_target='#search-results', 
#                hx_indicator='.htmx-indicator'),   
#               Button("Search")),
#        cls='container')

#@rt("/search")
def search_database(database_path, search_term, table_name, column_name):
    # Connect to the database
    conn = sqlite3.connect(database_path)
    cursor = conn.cursor()

    # Create the SQL query
    query = f"SELECT * FROM {table_name} WHERE {column_name} LIKE ?"

    # Execute the query
    cursor.execute(query, ('%' + search_term + '%',))

    # Fetch all the results
    results = cursor.fetchall()

    # Close the connection
    conn.close()

    return results

if __name__ == "__main__":

    # Example usage
    db_path = 'data/summaries.db'
    search_term = 'RAG'
    table_name = 'summaries'
    column_name = 'summary'

    results = search_database(db_path, search_term, table_name, column_name)

    for row in results:
        print(row)

#serve()
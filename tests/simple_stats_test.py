#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
A completely standalone test script for testing the daily stats functionality.
This script implements a simplified version of the update_daily_stats function
and tests it using a mock database.
"""

import asyncio
import json
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv, find_dotenv

# Load environment variables from .env file
load_dotenv(find_dotenv())

# Import environment variables
MONGO_URL_LOCAL = os.getenv("MONGO_URL_LOCAL")

# Create our implementation of the daily stats function for testing
async def update_daily_stats(previous_day=None, summaries_collection=None, full_texts_collection=None, daily_stats_collection=None):
    """Simplified version of update_daily_stats for testing"""
    try:
        # Get previous day's date in YYYY-MM-DD format if not provided
        if previous_day is None:
            previous_day = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        
        # Check if we already have stats for the previous day
        existing_stats = daily_stats_collection.find_one({'date': previous_day})
        if existing_stats:
            print(f"Daily stats for {previous_day} already exist - skipping recalculation")
            return existing_stats
            
        print(f"Calculating daily paper statistics for {previous_day}...")
        
        # Count papers published on the previous day
        papers_previous_day = summaries_collection.count_documents({
            'published': previous_day
        })
        
        # Get papers with full text available
        papers_with_full_text = list(summaries_collection.find(
            {'has_full_text': True, 'published': previous_day},
            {'abs_url': 1}
        ))
        
        # Initialize counters
        total_token_count = 0
        html_count = 0
        pdf_count = 0
        abstract_count = 0
        token_lengths = []
        
        # Process each paper to get token counts and source info
        for paper in papers_with_full_text:
            paper_id = paper['abs_url'].split('/')[-1]
            
            # Get full text document
            full_text_doc = full_texts_collection.find_one({'paper_id': paper_id})
            if not full_text_doc or 'text' not in full_text_doc:
                continue
                
            # Get the source and text
            source = full_text_doc.get('source', 'unknown')
            text = full_text_doc.get('text', '')
            
            # Count tokens (approximation: words * 1.3)
            token_count = len(text.split()) * 1.3
            
            # Update counters
            total_token_count += token_count
            token_lengths.append(token_count)
            
            # Update source counters
            if source == 'html':
                html_count += 1
            elif source == 'pdf':
                pdf_count += 1
            elif source == 'abstract':
                abstract_count += 1
        
        # Calculate average token length
        avg_token_length = 0
        if token_lengths:
            avg_token_length = sum(token_lengths) / len(token_lengths)
        
        # Calculate PDF/HTML ratio
        pdf_html_ratio = 0
        if html_count > 0:
            pdf_html_ratio = pdf_count / html_count
        
        # Create stats document
        stats = {
            'date': previous_day,
            'paper_count': papers_previous_day,
            'papers_with_full_text': len(papers_with_full_text),
            'avg_token_length': round(avg_token_length, 2),
            'total_token_count': round(total_token_count, 2),
            'html_count': html_count,
            'pdf_count': pdf_count,
            'abstract_count': abstract_count,
            'pdf_html_ratio': round(pdf_html_ratio, 3),
            'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # Save to database
        daily_stats_collection.update_one(
            {'date': previous_day},
            {'$set': stats},
            upsert=True
        )
        
        print(f"Daily stats updated for {previous_day}: {papers_previous_day} papers, {len(papers_with_full_text)} with full text")
        return stats
        
    except Exception as e:
        print(f"Error updating daily stats: {e}")
        import traceback
        traceback.print_exc()
        return None

# Create a simple mock for MongoDB collections
class MockCollection:
    def __init__(self, name):
        self.name = name
        self.data = []
        self.indexes = []
    
    def insert_one(self, document):
        # Generate a unique ID if not provided
        if '_id' not in document:
            document['_id'] = len(self.data) + 1
        self.data.append(document)
        return type('obj', (object,), {'inserted_id': document['_id']})
    
    def find_one(self, query):
        for doc in self.data:
            match = True
            for key, value in query.items():
                if key not in doc or doc[key] != value:
                    match = False
                    break
            if match:
                return doc
        return None
    
    def find(self, query=None, sort=None, limit=None, **kwargs):
        query = query or {}
        results = []
        
        # Basic filtering
        for doc in self.data:
            match = True
            for key, value in query.items():
                if key in doc:
                    if doc[key] != value:
                        match = False
                        break
                else:
                    match = False
                    break
            if match:
                results.append(doc)
        
        return results
    
    def count_documents(self, query=None):
        query = query or {}
        count = 0
        
        for doc in self.data:
            match = True
            for key, value in query.items():
                if key in doc:
                    if doc[key] != value:
                        match = False
                        break
                else:
                    match = False
                    break
            if match:
                count += 1
                
        return count
    
    def delete_many(self, query=None):
        # For simplicity, just clear the entire collection
        self.data = []
        return type('obj', (object,), {'deleted_count': 0})
    
    def update_one(self, query, update, upsert=False):
        doc = self.find_one(query)
        
        if doc:
            # Apply updates
            if '$set' in update:
                for key, value in update['$set'].items():
                    doc[key] = value
            return type('obj', (object,), {
                'modified_count': 1,
                'matched_count': 1,
                'upserted_id': None
            })
        elif upsert:
            # Create new document with query fields + update fields
            new_doc = query.copy()
            if '$set' in update:
                for key, value in update['$set'].items():
                    new_doc[key] = value
            self.insert_one(new_doc)
            return type('obj', (object,), {
                'modified_count': 0,
                'matched_count': 0,
                'upserted_id': new_doc['_id']
            })
        else:
            return type('obj', (object,), {
                'modified_count': 0,
                'matched_count': 0,
                'upserted_id': None
            })

# Create mock collections
summaries_collection = MockCollection('summaries')
full_texts_collection = MockCollection('full_texts')
daily_stats_collection = MockCollection('daily_stats')

class StatsTest:
    def __init__(self):
        self.previous_day = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        self.two_days_ago = (datetime.now() - timedelta(days=2)).strftime('%Y-%m-%d')
    
    def setup(self):
        """Setup test data"""
        # Clear collections
        summaries_collection.delete_many({})
        full_texts_collection.delete_many({})
        daily_stats_collection.delete_many({})
        
        # Create sample papers for the previous day
        sample_papers = [
            {
                'abs_url': 'https://arxiv.org/abs/example1',
                'pdf_url': 'https://arxiv.org/pdf/example1.pdf',
                'title': 'Sample Paper 1',
                'authors': ['Author 1', 'Author 2'],
                'updated': self.previous_day,
                'published': self.previous_day,
                'abstract': 'This is a sample abstract for paper 1',
                'summary': 'This is a sample summary for paper 1',
                'entities': [('Entity 1', 'Type A'), ('Entity 2', 'Type B')],
                'tags': ['Tag 1', 'Tag 2'],
                'has_full_text': True
            },
            {
                'abs_url': 'https://arxiv.org/abs/example2',
                'pdf_url': 'https://arxiv.org/pdf/example2.pdf',
                'title': 'Sample Paper 2',
                'authors': ['Author 3', 'Author 4'],
                'updated': self.previous_day,
                'published': self.previous_day,
                'abstract': 'This is a sample abstract for paper 2',
                'summary': 'This is a sample summary for paper 2',
                'entities': [('Entity 3', 'Type A'), ('Entity 4', 'Type C')],
                'tags': ['Tag 3', 'Tag 4'],
                'has_full_text': True
            },
            {
                'abs_url': 'https://arxiv.org/abs/example3',
                'pdf_url': 'https://arxiv.org/pdf/example3.pdf',
                'title': 'Sample Paper 3',
                'authors': ['Author 5'],
                'updated': self.previous_day,
                'published': self.previous_day,
                'abstract': 'This is a sample abstract for paper 3',
                'summary': 'This is a sample summary for paper 3',
                'entities': [('Entity 5', 'Type B')],
                'tags': ['Tag 5'],
                'has_full_text': False  # No full text for this paper
            },
            # Add a paper from two days ago (should not be included in stats)
            {
                'abs_url': 'https://arxiv.org/abs/example4',
                'pdf_url': 'https://arxiv.org/pdf/example4.pdf',
                'title': 'Sample Paper 4',
                'authors': ['Author 6'],
                'updated': self.two_days_ago,
                'published': self.two_days_ago,
                'abstract': 'This is a sample abstract for paper 4',
                'summary': 'This is a sample summary for paper 4',
                'entities': [('Entity 6', 'Type A')],
                'tags': ['Tag 6'],
                'has_full_text': True
            }
        ]
        
        # Insert papers
        for paper in sample_papers:
            summaries_collection.insert_one(paper)
        
        # Create sample full texts
        full_texts = [
            {
                'paper_id': 'example1',
                'text': 'This is sample full text for Paper 1. It has enough words to make a reasonable token count for testing purposes. This represents an HTML extraction source.',
                'source': 'html',
                'updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            },
            {
                'paper_id': 'example2',
                'text': 'This is sample full text for Paper 2. It also has enough words for token counting, but this one represents a PDF extraction source.',
                'source': 'pdf',
                'updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            },
            {
                'paper_id': 'example4',  # From two days ago
                'text': 'Full text for Paper 4. This should not be included in the previous day statistics.',
                'source': 'html',
                'updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        ]
        
        # Insert full texts
        for full_text in full_texts:
            full_texts_collection.insert_one(full_text)
    
    async def test_calculate_stats(self):
        """Test calculating statistics for the previous day"""
        print("Test 1: Calculating statistics for the previous day...")
        
        try:
            # Run the update_daily_stats function
            stats = await update_daily_stats(
                previous_day=self.previous_day,
                summaries_collection=summaries_collection,
                full_texts_collection=full_texts_collection,
                daily_stats_collection=daily_stats_collection
            )
            
            # Check that stats were returned
            if stats is None:
                raise AssertionError("Stats were not returned")
            
            # Verify the date is correct (previous day)
            if stats['date'] != self.previous_day:
                raise AssertionError(f"Date is incorrect: expected {self.previous_day}, got {stats['date']}")
            
            # Verify paper count is correct (3 papers from previous day)
            if stats['paper_count'] != 3:
                raise AssertionError(f"Paper count is incorrect: expected 3, got {stats['paper_count']}")
            
            # Verify papers with full text count is correct (2 papers with has_full_text = True)
            if stats['papers_with_full_text'] != 2:
                raise AssertionError(f"Papers with full text count is incorrect: expected 2, got {stats['papers_with_full_text']}")
            
            # Verify source counts
            if stats['html_count'] != 1:
                raise AssertionError(f"HTML count is incorrect: expected 1, got {stats['html_count']}")
            if stats['pdf_count'] != 1:
                raise AssertionError(f"PDF count is incorrect: expected 1, got {stats['pdf_count']}")
            if stats['abstract_count'] != 0:
                raise AssertionError(f"Abstract count is incorrect: expected 0, got {stats['abstract_count']}")
            
            # Verify PDF/HTML ratio
            if stats['pdf_html_ratio'] != 1.0:
                raise AssertionError(f"PDF/HTML ratio is incorrect: expected 1.0, got {stats['pdf_html_ratio']}")
            
            # Check that the stats were saved to the database
            saved_stats = daily_stats_collection.find_one({'date': self.previous_day})
            if saved_stats is None:
                raise AssertionError("Stats were not saved to the database")
            if saved_stats['paper_count'] != 3:
                raise AssertionError(f"Saved paper count is incorrect: expected 3, got {saved_stats['paper_count']}")
            if saved_stats['papers_with_full_text'] != 2:
                raise AssertionError(f"Saved papers with full text count is incorrect: expected 2, got {saved_stats['papers_with_full_text']}")
            
            print("✅ Test 1 passed: Statistics calculated correctly for the previous day\n")
            return True
        except AssertionError as e:
            print(f"❌ Test 1 failed: {e}\n")
            return False
    
    async def test_existing_stats(self):
        """Test that stats aren't recalculated if they already exist"""
        print("Test 2: Checking that existing statistics aren't recalculated...")
        
        try:
            # First create existing stats
            existing_stats = {
                'date': self.previous_day,
                'paper_count': 10,  # Deliberately wrong to test that it's not recalculated
                'papers_with_full_text': 5,
                'avg_token_length': 150.0,
                'total_token_count': 750.0,
                'html_count': 3,
                'pdf_count': 2,
                'abstract_count': 0,
                'pdf_html_ratio': 0.667,
                'updated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # Insert into database
            daily_stats_collection.insert_one(existing_stats)
            
            # Run update_daily_stats
            stats = await update_daily_stats(
                previous_day=self.previous_day,
                summaries_collection=summaries_collection,
                full_texts_collection=full_texts_collection,
                daily_stats_collection=daily_stats_collection
            )
            
            # Verify that the existing stats were returned without recalculation
            if stats['paper_count'] != 10:
                raise AssertionError(f"Paper count was recalculated: expected 10, got {stats['paper_count']}")
            if stats['papers_with_full_text'] != 5:
                raise AssertionError(f"Papers with full text count was recalculated: expected 5, got {stats['papers_with_full_text']}")
            
            # Verify database still has the original values
            saved_stats = daily_stats_collection.find_one({'date': self.previous_day})
            if saved_stats['paper_count'] != 10:
                raise AssertionError(f"Saved paper count was changed: expected 10, got {saved_stats['paper_count']}")
            if saved_stats['pdf_html_ratio'] != 0.667:
                raise AssertionError(f"Saved PDF/HTML ratio was changed: expected 0.667, got {saved_stats['pdf_html_ratio']}")
            
            print("✅ Test 2 passed: Existing statistics are preserved\n")
            return True
        except AssertionError as e:
            print(f"❌ Test 2 failed: {e}\n")
            return False

async def run_tests():
    print("Running daily statistics tests...\n")
    
    # Create and setup test instance
    test = StatsTest()
    
    # Test 1: Calculate daily stats for the previous day
    test.setup()
    test1_success = await test.test_calculate_stats()
    
    # Test 2: Don't recalculate if stats already exist
    test.setup()
    test2_success = await test.test_existing_stats()
    
    # Final results
    if test1_success and test2_success:
        print("All tests passed! The daily statistics for the previous day are working correctly.")
        return True
    else:
        print("Some tests failed. Please review the issues.")
        return False

if __name__ == "__main__":
    success = asyncio.run(run_tests())
    import sys
    sys.exit(0 if success else 1)
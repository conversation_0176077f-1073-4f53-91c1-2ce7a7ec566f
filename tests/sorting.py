from fasthtml.common import *
from datetime import datetime
import sqlite3
from pydantic import BaseModel
from typing import List
import json  # Import json to handle list conversion

class SummaryModel(BaseModel):
    abs_url: str
    pdf_url: str
    title: str
    authors: List[str]           # Assuming authors are stored as comma-separated strings
    updated: str
    published: str
    abstract: str
    summary: str
    tags: List[str]              # Assuming tags are stored as comma-separated strings

# Database setup
db = database('data/summaries.db')
summaries = db.t.summaries
latest_summaries = summaries(order_by='-published', limit=100)

print(latest_summaries[0]["published"])

def to_dataclass(results):
    models: List[SummaryModel] = []
    for row in results:
        # Assume that authors and tags are stored as comma-separated strings in the database
        authors = row[3].split(',') if row[3] else []  # Adjust index based on your table structure
        # Assuming tags are stored as a string that looks like a JSON array
        tags_str = row[8] if row[8] else ''  # Adjust index based on your table structure
        
        # Clean the tags string and convert it to a list
        if tags_str:
            # Strip whitespace and handle common issues
            cleaned_tags_str = tags_str.strip()
            
            # Attempt to load the JSON array
            try:
                # Remove any extra brackets or quotes if necessary
                if cleaned_tags_str.startswith('[') and cleaned_tags_str.endswith(']'):
                    tags = json.loads(cleaned_tags_str)
                else:
                    # If the string is malformed, log it and set to empty
                    print(f"Malformed tags string: {cleaned_tags_str}")
                    tags = []
            except json.JSONDecodeError as e:
                print(f"JSON decode error: {e} for string: {cleaned_tags_str}")
                tags = []  # Default to an empty list if there's an error
        else:
            tags = []  # Default to an empty list if tags_str is empty
        
        model_instance = SummaryModel(
            abs_url=row[0],
            pdf_url=row[1],
            title=row[2],
            authors=authors,
            updated=row[4],
            published=row[5],
            abstract=row[6],
            summary=row[7],
            tags=tags
        )
        models.append(model_instance)
    return models

# Connect to your SQLite database
conn = sqlite3.connect('data/summaries.db')
cursor = conn.cursor()

# Execute the SQL query to fetch the latest entry based on the datestring column
cursor.execute('''
    SELECT * FROM summaries
    ORDER BY DATE(published) DESC
    LIMIT 100
''')

# Fetch all results
all_entries = cursor.fetchall()

models = to_dataclass(all_entries)

# Print the latest entry
print(models[0].published)
print(models[0].tags[0])

# Close the connection
conn.close()
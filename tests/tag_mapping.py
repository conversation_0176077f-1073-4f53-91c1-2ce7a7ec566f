tag_mapping = {
    "Security": "tag-security",
    "Agents": "tag-agents",
    "Finance": "tag-finance",
    "Tabular": "tag-tabular",
    "Audio": "tag-audio", 
    "Vision": "tag-vision",
    "Time Series": "tag-time-series",
    "Education": "tag-education",
    "Chemistry": "tag-chemistry",
    "Physics": "tag-physics",
    "Biology": "tag-biology",
    "Psychology": "tag-psychology",
    "Sociology": "tag-sociology", 
    "Law": "tag-law", 
    "Reinforcement Learning": "tag-reinforcement-learning", 
    "Reasoning": "tag-reasoning", 
    "Tool-Use": "tag-tool-use", 
    "AI Ethics": "tag-ai-ethics", 
    "Coding": "tag-coding", 
    "Efficiency": "tag-efficiency", 
    "Alignment": "tag-alignment", 
    "Prompt Engineering": "tag-prompt-engineering", 
    "Robotics": "tag-robotics",
    "Graphs": "tag-graphs", 
    "Explainable AI": "tag-explainable-ai",
    "RAG": "tag-rag"
}

if __name__ == "__main__":
    print(*((tag, tag_mapping[tag]) for tag in ["Graphs", "Explainable AI", "Coding"]))

from openai import OpenAI
import asyncio
import json

client = OpenAI(api_key="***************************************************") #api_key=os.getenv("OPENAI_API_KEY")

tagging_sp = """You are a helpful assistant with world-class expertise in AI research and application.
Your role is to classify research papers into categories based on their abstracts better than anyone else in the world."""
async def tagging_papers(text):
    chat_completion = client.chat.completions.create(
        messages=[{
            "role": "system",
            "content": tagging_sp
        }, {
            "role": "user",
            "content": f"""
            Use the following topics and only the following topics: "Vision", "Audio", "Tabular", "Health", "Finance", "Education", "Chemistry", "Physics", "Psychology", "Law", "Biology", 
            "Agents", "Reasoning", "Tool-Use", "AI Ethics", "Coding", "RAG", "Efficiency", "Security", "Alignment", "Prompt Engineering", "Robotics", "Graphs", "Explainable AI".

            You are allowed to use multiple topics for a single paper. Never choose a topic that you're not asbolutely certain about or you'll catch fire and potentially die.
            If none of these topics fits a description, output an empty json.

            Description: {text}

            Topic:
            """
        }],
        model="gpt-4o-mini",
        response_format={ "type":"json_object" },
        temperature=0.0
    )

    tags = chat_completion.choices[0].message.content.strip()
    return json.loads(tags)["topics"]

abstract = """In the rapidly evolving landscape of Natural Language Processing (NLP) and text generation, the emergence of Retrieval Augmented Generation (RAG) presents 
a promising avenue for improving the quality and reliability of generated text by leveraging information retrieved from user specified database. Benchmarking is essential 
to evaluate and compare the performance of the different RAG configurations in terms of retriever and generator, providing insights into their effectiveness, scalability, 
and suitability for the specific domain and applications. In this paper, we present a comprehensive framework to generate a domain relevant RAG benchmark. Our framework is
 based on automatic question-answer generation with Human (domain experts)-AI Large Language Model (LLM) teaming. As a case study, we demonstrate the framework by 
 introducing PermitQA, a first-of-its-kind benchmark on the wind siting and permitting domain which comprises of multiple scientific documents/reports related to 
 environmental impact of wind energy projects. Our framework systematically evaluates RAG performance using diverse metrics and multiple question types with varying 
 complexity level. We also demonstrate the performance of different models on our benchmark."""
async def main():
    tags = await tagging_papers(abstract)
    return tags

if __name__ == "__main__":
    print(asyncio.run(main()))
#!/usr/bin/env python3
"""
Tests for the entity dictionary functionality.
"""

import os
import sys
import unittest
import tempfile
import json
from datetime import datetime

# Add the parent directory to the path so we can import from src
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.utils.entity_dictionary import EntityDictionaryManager, normalize_text

class TestEntityDictionary(unittest.TestCase):
    """Tests for the EntityDictionaryManager class"""
    
    def setUp(self):
        """Create a temporary dictionary file for testing"""
        self.temp_dir = tempfile.TemporaryDirectory()
        self.dict_path = os.path.join(self.temp_dir.name, "test_entity_dictionary.json")
        self.manager = EntityDictionaryManager(self.dict_path)
    
    def tearDown(self):
        """Clean up temporary files"""
        self.temp_dir.cleanup()
    
    def test_save_and_load_dictionary(self):
        """Test saving and loading the dictionary from disk"""
        # Add some test entities
        self.manager.entity_dict = {
            "test entity": {
                "canonical_id": "test-entity-123",
                "canonical_name": "Test Entity",
                "entity_type": "Test"
            },
            "another entity": {
                "canonical_id": "another-entity-456",
                "canonical_name": "Another Entity",
                "entity_type": "Test"
            }
        }
        
        # Save the dictionary
        self.manager.save_dictionary()
        
        # Check that the file exists
        self.assertTrue(os.path.exists(self.dict_path))
        
        # Load the dictionary into a new manager
        new_manager = EntityDictionaryManager(self.dict_path)
        
        # Check that the entities were loaded correctly
        self.assertEqual(len(new_manager.entity_dict), 2)
        self.assertEqual(new_manager.entity_dict["test entity"]["canonical_id"], "test-entity-123")
        self.assertEqual(new_manager.entity_dict["another entity"]["canonical_name"], "Another Entity")
    
    def test_lookup_entity(self):
        """Test looking up entities in the dictionary"""
        # Add some test entities
        self.manager.entity_dict = {
            "bert": {
                "canonical_id": "bert-model",
                "canonical_name": "BERT",
                "entity_type": "Model"
            },
            "gpt3": {
                "canonical_id": "gpt-3",
                "canonical_name": "GPT-3",
                "entity_type": "Model"
            }
        }
        
        # Test exact lookups
        bert_result = self.manager.lookup_entity("bert")
        self.assertIsNotNone(bert_result)
        self.assertEqual(bert_result["canonical_name"], "BERT")
        
        # Test case-insensitive lookups
        gpt_result = self.manager.lookup_entity("GPT3")
        self.assertIsNotNone(gpt_result)
        self.assertEqual(gpt_result["canonical_id"], "gpt-3")
        
        # Test normalized lookups
        bert_variant = self.manager.lookup_entity("BERT.")
        self.assertIsNotNone(bert_variant)
        self.assertEqual(bert_variant["canonical_id"], "bert-model")
        
        # Test non-existent entity
        non_existent = self.manager.lookup_entity("T5")
        self.assertIsNone(non_existent)
    
    def test_normalize_text(self):
        """Test text normalization for entity matching"""
        # Test basic normalization
        self.assertEqual(normalize_text("BERT"), "bert")
        self.assertEqual(normalize_text("GPT-3"), "gpt3")
        
        # Test whitespace handling
        self.assertEqual(normalize_text("  Transformer  Model  "), "transformer model")
        
        # Test punctuation removal
        self.assertEqual(normalize_text("U-Net!"), "unet")
        self.assertEqual(normalize_text("CLIP (Contrastive Language-Image Pre-training)"), "clip contrastive languageimage pretraining")
        
        # Test compound entity names
        self.assertEqual(normalize_text("Neural-Symbolic VQA"), "neuralsymbolic vqa")

if __name__ == "__main__":
    unittest.main()
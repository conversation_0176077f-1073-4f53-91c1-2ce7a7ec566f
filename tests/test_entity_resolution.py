#!/usr/bin/env python3
"""
Test script for entity resolution with real data from local MongoDB JSON files
"""

import os
import sys
import asyncio
from openai import OpenAI
from dotenv import load_dotenv

# Add the current directory to the path so we can import from src
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import necessary modules
from src.db.mongo import setup_mongo_client, setup_collections, create_indexes, load_json_data_to_mock
from src.services.entity_resolution import EntityResolutionService

async def run_test():
    """Test entity resolution with real data from local MongoDB JSON files"""
    print("\n======== TESTING ENTITY RESOLUTION ========")
    
    # Setup MongoDB client in local mode
    mongo_client = setup_mongo_client(local_mode=True)
    
    # Load JSON data from local files
    load_json_data_to_mock(mongo_client)
    
    # Setup collections and indexes
    collections = setup_collections(mongo_client)
    create_indexes(collections)
    
    # Count the loaded data
    summaries_count = collections['summaries'].count_documents({})
    entities_count = collections.get('entities', collections['summaries']).count_documents({})
    entity_resolution_count = collections['entity_resolution'].count_documents({})
    print(f"Loaded {summaries_count} summaries")
    print(f"Loaded {entities_count} entities")
    print(f"Loaded {entity_resolution_count} resolved entities")
    
    # Initialize entity resolution service with mock OpenAI client
    mock_client = OpenAI(
        api_key="test_key",
        base_url="https://fake-url.com",
    )
    
    # Create a simple mock for the OpenAI client
    mock_client.beta = type('obj', (), {})
    mock_client.beta.chat = type('obj', (), {})
    mock_client.beta.chat.completions = type('obj', (), {
        'parse': lambda **kwargs: type('obj', (), {
            'choices': [
                type('obj', (), {
                    'message': type('obj', (), {
                        'content': '{"resolved_entities": [{"canonical_id": "deep_learning", "canonical_name": "Deep Learning", "variant_ids": ["deep learning"], "entity_type": "Methods & Techniques", "description": "A machine learning technique"}]}'
                    })
                })
            ]
        })
    })
    
    # Also mock chat completions
    mock_client.chat = type('obj', (), {})
    mock_client.chat.completions = type('obj', (), {
        'create': lambda **kwargs: type('obj', (), {
            'choices': [
                type('obj', (), {
                    'message': type('obj', (), {
                        'content': '{"resolved_entities": [{"canonical_id": "deep_learning", "canonical_name": "Deep Learning", "variant_ids": ["deep learning"], "entity_type": "Methods & Techniques", "description": "A machine learning technique"}]}'
                    })
                })
            ]
        })
    })
    
    entity_service = EntityResolutionService(collections, mock_client)
    
    # Run the entity resolution process
    print("Starting entity resolution process...")
    result = await entity_service.resolve_entities(force_update=True)
    
    print("\n======== ENTITY RESOLUTION TEST COMPLETE ========")
    print(f"Status: {result.status}")
    print(f"Message: {result.message}")
    
    if result.updated is not None:
        print(f"Updated entities: {result.updated}")
    if result.new is not None:
        print(f"New entities: {result.new}")
    if result.total is not None:
        print(f"Total entities processed: {result.total}")
    if result.entities:
        print(f"Entities returned: {len(result.entities)}")
        entity_samples = result.entities[:10] if len(result.entities) > 10 else result.entities
        for entity in entity_samples:
            print(f"  - {entity.canonical_name} ({entity.entity_type})")
        
        if len(result.entities) > 10:
            print(f"  ... and {len(result.entities) - 10} more")
    
    return result

if __name__ == "__main__":
    # Load environment variables
    load_dotenv()
    
    # Set local mode environment variable
    os.environ['LOCAL_MODE'] = 'true'
    
    # Run the test
    asyncio.run(run_test())
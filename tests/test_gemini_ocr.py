import asyncio
import unittest
import sys
import os
from unittest.mock import patch, MagicMock, mock_open

# Add parent directory to path to import module
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from gemini_ocr import get_full_paper_text

# Set up event loop for asyncio tests
def async_test(coro):
    def wrapper(*args, **kwargs):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(coro(*args, **kwargs))
        finally:
            loop.close()
            asyncio.set_event_loop(None)
    return wrapper


class TestGeminiOCR(unittest.TestCase):
    """Test cases for the gemini_ocr module - focusing on high level functionality"""

    @patch('gemini_ocr.process_pdf_content')
    @async_test
    async def test_get_full_paper_text_success(self, mock_process):
        """Test get_full_paper_text function with successful processing"""
        # Mock successful processing with sufficient content length
        mock_process.return_value = ("Processed paper text with more than 100 characters. This text is long enough to pass the length check in the get_full_paper_text function.", True)
        
        # Create a mock client
        mock_client = MagicMock()
        
        # Call the function
        text, success = await get_full_paper_text(mock_client, "https://arxiv.org/abs/1706.03762")
        
        # Check results - content should pass through directly
        self.assertTrue("Processed paper text" in text)
        self.assertTrue(success)
        mock_process.assert_called_once()

    @patch('gemini_ocr.process_pdf_content')
    @async_test
    async def test_get_full_paper_text_failure(self, mock_process):
        """Test get_full_paper_text function with unsuccessful processing"""
        # Mock unsuccessful processing
        mock_process.return_value = ("Error processing", False)
        
        # Create a mock client
        mock_client = MagicMock()
        
        # Call the function
        text, success = await get_full_paper_text(mock_client, "https://arxiv.org/abs/1706.03762")
        
        # Check results - should indicate failure
        self.assertTrue("Failed to extract content" in text)
        self.assertFalse(success)
        mock_process.assert_called_once()


if __name__ == '__main__':
    unittest.main()
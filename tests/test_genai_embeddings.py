#!/usr/bin/env python3
import unittest
import os
import numpy as np
from unittest.mock import patch, MagicMock
from dotenv import load_dotenv
import google.generativeai as genai

# Load environment variables
load_dotenv()

class TestGenaiEmbeddings(unittest.TestCase):
    
    def setUp(self):
        # Get API key from environment
        api_key = os.getenv("GEMINI_API_KEY")
        
        if not api_key:
            self.skipTest("GEMINI_API_KEY environment variable not set")
        
        # Configure the genai library
        genai.configure(api_key=api_key)
        
        # Test text for embeddings
        self.test_text = "This is a test document for embedding generation using Google's Gemini API."
        self.model = "models/embedding-001"  # Text embedding model
    
    def test_embedding_creation(self):
        """Test that we can create embeddings with the Google Genai API"""
        # Create embedding using the embeddings function directly
        result = genai.embed_content(
            model=self.model,
            content=self.test_text,
            task_type="RETRIEVAL_DOCUMENT"
        )
        
        # Check that we got a result
        self.assertIsNotNone(result)
        
        # Access the embeddings
        embedding = result["embedding"]
        
        # Check that the embedding is a list or numpy array
        self.assertTrue(isinstance(embedding, list))
        
        # Convert to numpy array
        embedding = np.array(embedding)
        
        # Check the embedding dimensions
        self.assertGreater(embedding.shape[0], 0)
        
        # Check that values are within expected range (typically normalized)
        self.assertTrue(-1.5 <= embedding.min() <= 1.5)
        self.assertTrue(-1.5 <= embedding.max() <= 1.5)
    
    def test_embedding_consistency(self):
        """Test that the same text produces consistent embeddings"""
        # Create embedding twice for the same text
        result1 = genai.embed_content(
            model=self.model,
            content=self.test_text,
            task_type="RETRIEVAL_DOCUMENT"
        )
        
        result2 = genai.embed_content(
            model=self.model,
            content=self.test_text,
            task_type="RETRIEVAL_DOCUMENT"
        )
        
        # Convert to numpy arrays
        embedding1 = np.array(result1["embedding"])
        embedding2 = np.array(result2["embedding"])
        
        # Calculate cosine similarity
        # Normalize the vectors
        embedding1_norm = embedding1 / np.linalg.norm(embedding1)
        embedding2_norm = embedding2 / np.linalg.norm(embedding2)
        
        # Calculate cosine similarity
        similarity = np.dot(embedding1_norm, embedding2_norm)
        
        # Same text should have almost perfect similarity
        self.assertGreater(similarity, 0.99)
    
    def test_embedding_similarity(self):
        """Test that similar texts have similar embeddings"""
        # Create two similar texts
        text1 = "Machine learning algorithms can automatically learn and improve from experience."
        text2 = "ML algorithms learn automatically and get better with experience."
        
        # Create embeddings
        result1 = genai.embed_content(
            model=self.model,
            content=text1,
            task_type="RETRIEVAL_DOCUMENT"
        )
        result2 = genai.embed_content(
            model=self.model,
            content=text2,
            task_type="RETRIEVAL_DOCUMENT"
        )
        
        # Convert to numpy arrays
        embedding1 = np.array(result1["embedding"])
        embedding2 = np.array(result2["embedding"])
        
        # Calculate cosine similarity
        # Normalize the vectors
        embedding1_norm = embedding1 / np.linalg.norm(embedding1)
        embedding2_norm = embedding2 / np.linalg.norm(embedding2)
        
        # Calculate cosine similarity
        similarity = np.dot(embedding1_norm, embedding2_norm)
        
        # Similar texts should have similarity > 0.7
        self.assertGreater(similarity, 0.7)
        
        # Now test with dissimilar texts
        text3 = "The quick brown fox jumps over the lazy dog."
        result3 = genai.embed_content(
            model=self.model,
            content=text3,
            task_type="RETRIEVAL_DOCUMENT"
        )
        embedding3 = np.array(result3["embedding"])
        embedding3_norm = embedding3 / np.linalg.norm(embedding3)
        
        # Calculate cosine similarity with first text
        dissimilar_similarity = np.dot(embedding1_norm, embedding3_norm)
        
        # Dissimilar texts should have lower similarity than similar texts
        self.assertLess(dissimilar_similarity, similarity)

    @patch('google.generativeai.embed_content')
    def test_embedding_mocked(self, mock_embed_content):
        """Test embeddings with a mocked API to avoid actual API calls"""
        # Create a mock response
        mock_response = {
            "embedding": [0.1] * 768  # Typical embedding length
        }
        mock_embed_content.return_value = mock_response
        
        # Call the function with our text
        result = genai.embed_content(
            model=self.model,
            content=self.test_text,
            task_type="RETRIEVAL_DOCUMENT"
        )
        
        # Verify the mock was called with correct parameters
        mock_embed_content.assert_called_once_with(
            model=self.model,
            content=self.test_text,
            task_type="RETRIEVAL_DOCUMENT"
        )
        
        # Check we got the mock response
        self.assertEqual(result, mock_response)
        
        # Verify the embedding has the expected length
        self.assertEqual(len(result["embedding"]), 768)


if __name__ == "__main__":
    unittest.main()
#!/usr/bin/env python3
import unittest
import json
from typing import List, Dict, Any
from pydantic import BaseModel, Field, ValidationError

# Revised Data Model for the Knowledge Graph
class EntityNode(BaseModel):
    id: str
    label: str = Field(default="")  # Reflects the dot 'label' property
    properties: Dict[str, Any] = Field(default_factory=dict)

class NodeReference(BaseModel):
    id: str

class RelationEdge(BaseModel):
    start: NodeReference
    end: NodeReference
    label: str  # Represents the relationship label (e.g., "prone_to", "integrates", etc.)
    properties: Dict[str, Any] = Field(default_factory=dict)

class KnowledgeGraphData(BaseModel):
    raw_dot: str
    nodes: List[EntityNode]
    relationships: List[RelationEdge]

# Simulated API call that validates the payload against the data model.
def simulate_api_call(payload: dict):
    try:
        # Validate payload using the Pydantic model.
        kg_data = KnowledgeGraphData(**payload)
        print("API call successful. Parsed data:")
        # Use model_dump() for obtaining a dict, then pretty print using json.dumps.
        print(json.dumps(kg_data.model_dump(), indent=2))
        return kg_data
    except ValidationError as e:
        print("API call failed with validation error:")
        print(e)
        raise

# Unit tests to verify suggestions for avoiding bad request errors.
class TestKnowledgeGraphAPI(unittest.TestCase):

    def test_valid_payload(self):
        # This payload contains all required fields and should pass validation.
        valid_payload = {
            "raw_dot": "digraph KnowledgeGraph { A -> B [label='connects']; }",
            "nodes": [
                {"id": "A", "label": "Node A", "properties": {}},
                {"id": "B", "label": "Node B", "properties": {}}
            ],
            "relationships": [
                {"start": {"id": "A"}, "end": {"id": "B"}, "label": "connects", "properties": {}}
            ]
        }
        kg_data = simulate_api_call(valid_payload)
        self.assertEqual(kg_data.nodes[0].id, "A")
        self.assertEqual(kg_data.relationships[0].label, "connects")

    def test_missing_required_field(self):
        # This payload is missing the required 'raw_dot' field.
        invalid_payload = {
            "nodes": [
                {"id": "A", "label": "Node A", "properties": {}}
            ],
            "relationships": []
        }
        with self.assertRaises(ValidationError):
            simulate_api_call(invalid_payload)

    def test_invalid_type(self):
        # This payload has an incorrect type for 'raw_dot' (int instead of str),
        # simulating a type conversion issue.
        invalid_payload = {
            "raw_dot": 123,  # Incorrect type: should be a string.
            "nodes": [
                {"id": "A", "label": "Node A", "properties": {}}
            ],
            "relationships": []
        }
        with self.assertRaises(ValidationError):
            simulate_api_call(invalid_payload)

if __name__ == '__main__':
    unittest.main()


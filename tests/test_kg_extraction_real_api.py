import os
import sys
import json
import pytest
import asyncio
from openai import OpenAI

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.services.nlp import extract_knowledge_graph
from src.models.kg import KnowledgeGraphData, NodeReference
from prompts import kg_prompt

from dotenv import load_dotenv, find_dotenv

# Load environment variables
load_dotenv(find_dotenv())

@pytest.mark.asyncio
async def test_extract_knowledge_graph_real_api():
    # Sample text for testing
    test_text = """
    Transformers have revolutionized natural language processing by introducing an attention mechanism 
    that allows models to focus on different parts of the input sequence. 
    BERT, developed by Google in 2018, is a bidirectional transformer model pre-trained on a large corpus 
    of text data using masked language modeling. GPT (Generative Pre-trained Transformer), 
    developed by OpenAI, is an autoregressive model that predicts the next token in a sequence.
    BERT is primarily used for understanding tasks like classification and question answering,
    while GPT excels at generation tasks like text completion and summarization.
    """
    
    openai_client = OpenAI(
    api_key=os.getenv("GEMINI_API_KEY"), 
    base_url=os.getenv("GEMINI_BASE_URL")
    )
    
    # Call the function to extract knowledge graph from src.services.nlp
    kg_result = await extract_knowledge_graph(test_text, openai_client)
    
    # Verify the structure of the returned knowledge graph
    assert isinstance(kg_result, KnowledgeGraphData)
    assert hasattr(kg_result, 'raw_dot')
    assert hasattr(kg_result, 'nodes')
    assert hasattr(kg_result, 'relationships')
    
    # Check that we have some nodes and relationships
    assert len(kg_result.nodes) > 0
    assert len(kg_result.relationships) > 0
    
    # Verify node structure
    for node in kg_result.nodes:
        assert hasattr(node, 'id')
        assert hasattr(node, 'label')
        assert hasattr(node, 'properties')
        assert isinstance(node.label, str)
        assert isinstance(node.properties, dict)
    
    # Verify relationship structure
    for rel in kg_result.relationships:
        assert hasattr(rel, 'start')
        assert hasattr(rel, 'end')
        assert hasattr(rel, 'label')
        assert hasattr(rel, 'properties')
        assert isinstance(rel.start, NodeReference)
        assert isinstance(rel.end, NodeReference)
        assert isinstance(rel.properties, dict)
    
    # Additional checks for expected content based on test text
    node_ids = [node.id for node in kg_result.nodes]
    node_labels = [node.label for node in kg_result.nodes]
    rel_labels = [rel.label for rel in kg_result.relationships]
    
    # Check for expected entities in the nodes
    expected_entities = ["Transformers", "BERT", "GPT", "Google", "OpenAI"]
    for entity in expected_entities:
        assert any(entity.lower() in node_id.lower() for node_id in node_ids) or \
               any(entity.lower() in label.lower() for label in node_labels)
    
    # Print the knowledge graph for debugging
    print("Knowledge Graph Nodes:", len(kg_result.nodes))
    print("Knowledge Graph Relationships:", len(kg_result.relationships))
    print("Raw DOT Graph length:", len(kg_result.raw_dot))
    
    return kg_result

if __name__ == "__main__":
    # Run the test directly when script is executed
    asyncio.run(test_extract_knowledge_graph_real_api())
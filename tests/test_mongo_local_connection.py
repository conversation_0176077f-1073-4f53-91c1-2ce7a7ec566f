import os
import unittest
import sys
from pymongo import MongoClient
from pymongo.errors import ServerSelectionTimeoutError

# Add parent directory to path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from src.db.mongo import setup_mongo_client, setup_collections


class TestMongoLocalConnection(unittest.TestCase):
    """Test MongoDB local connection using MONGO_URL_LOCAL environment variable."""
    
    def setUp(self):
        """Set up environment for test."""
        # Store original environment variable values
        self.original_mongo_url_local = os.environ.get("MONGO_URL_LOCAL")
        
        # Set test environment variables
        if "MONGO_URL_LOCAL" in os.environ:
            # Use existing MONGO_URL_LOCAL for test
            print(f"Using existing MONGO_URL_LOCAL: {os.environ['MONGO_URL_LOCAL']}")
        else:
            # For testing purposes, if no MONGO_URL_LOCAL is set, use a default
            test_url = "mongodb://localhost:27017"
            os.environ["MONGO_URL_LOCAL"] = test_url
            print(f"Setting test MONGO_URL_LOCAL: {test_url}")
        
    def tearDown(self):
        """Clean up after tests."""
        if self.original_mongo_url_local:
            os.environ["MONGO_URL_LOCAL"] = self.original_mongo_url_local
        else:
            if "MONGO_URL_LOCAL" in os.environ:
                del os.environ["MONGO_URL_LOCAL"]

    def test_mongo_connection(self):
        """Test connection to MongoDB using MONGO_URL_LOCAL."""
        try:
            # Try to connect to MongoDB
            mongo_client = MongoClient(
                os.environ["MONGO_URL_LOCAL"],
                connectTimeoutMS=5000,  # Lower timeout for tests
                serverSelectionTimeoutMS=5000,
                socketTimeoutMS=10000
            )
            
            # Test connection with ping
            result = mongo_client.admin.command('ping')
            self.assertEqual(result.get('ok'), 1.0, "MongoDB ping failed")
            print("Successfully connected to MongoDB at", os.environ["MONGO_URL_LOCAL"])
            
            # Test creating a test document
            test_db = mongo_client["test_db"]
            test_collection = test_db["test_collection"]
            
            # Insert a test document
            test_id = test_collection.insert_one({"test": "data"}).inserted_id
            self.assertIsNotNone(test_id, "Failed to insert test document")
            
            # Find the test document
            test_doc = test_collection.find_one({"test": "data"})
            self.assertIsNotNone(test_doc, "Failed to find test document")
            
            # Clean up
            test_collection.delete_one({"_id": test_id})
            
        except ServerSelectionTimeoutError:
            self.fail(f"Could not connect to MongoDB at {os.environ['MONGO_URL_LOCAL']}. Make sure MongoDB is running.")
        except Exception as e:
            self.fail(f"Unexpected error: {str(e)}")

    def test_mongo_client_setup(self):
        """Test setup_mongo_client function with local connection."""
        try:
            # Use our module's function to set up the client with local_mode=True
            mongo_client = setup_mongo_client(local_mode=True)
            self.assertIsNotNone(mongo_client, "Failed to create MongoDB client")
            
            # Test the connection
            result = mongo_client.admin.command('ping')
            self.assertEqual(result.get('ok'), 1.0, "MongoDB ping failed")
            
            # Test collections setup
            collections = setup_collections(mongo_client)
            self.assertIsInstance(collections, dict, "Collections should be a dictionary")
            self.assertIn('summaries', collections, "Missing 'summaries' collection")
            self.assertIn('users', collections, "Missing 'users' collection")
            self.assertIn('daily_stats', collections, "Missing 'daily_stats' collection")
            
        except Exception as e:
            self.fail(f"Error testing setup_mongo_client: {str(e)}")


if __name__ == "__main__":
    unittest.main()
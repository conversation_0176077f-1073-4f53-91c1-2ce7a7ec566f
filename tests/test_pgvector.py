import unittest
import tempfile
import os
import numpy as np
import psycopg2
from psycopg2.errors import DuplicateDatabase
from sqlalchemy import create_engine, Column, Integer, String, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from pgvector.sqlalchemy import Vector

class TestPgVector(unittest.TestCase):
    
    def setUp(self):
        # Create a temporary database
        self.test_db = "test_pgvector_db"
        self.conn_string = f"postgresql://postgres:postgres@localhost:5432/{self.test_db}"
        
        # Connect to postgres to create test database
        try:
            conn = psycopg2.connect("postgresql://postgres:postgres@localhost:5432/postgres")
            conn.autocommit = True
            with conn.cursor() as cursor:
                cursor.execute(f"CREATE DATABASE {self.test_db}")
                
            # Close connection to postgres
            conn.close()
        except DuplicateDatabase:
            # Database already exists
            pass
        
        # Connect to test database
        self.engine = create_engine(self.conn_string)
        
        # Create pgvector extension
        with self.engine.connect() as connection:
            connection.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))
            connection.commit()
        
        # Create model
        Base = declarative_base()
        
        class Document(Base):
            __tablename__ = "documents"
            
            id = Column(Integer, primary_key=True)
            content = Column(String)
            embedding = Column(Vector(384))  # 384-dimensional vector
            
        self.Document = Document
        Base.metadata.create_all(self.engine)
        
        # Create session
        Session = sessionmaker(bind=self.engine)
        self.session = Session()
    
    def test_store_numpy_vector(self):
        # Create a random vector
        vector = np.random.rand(384).astype(np.float32)
        
        # Store in database
        doc = self.Document(content="Test document", embedding=vector)
        self.session.add(doc)
        self.session.commit()
        
        # Retrieve from database
        retrieved_doc = self.session.query(self.Document).first()
        
        # Check if content matches
        self.assertEqual(retrieved_doc.content, "Test document")
        
        # Check if embedding was stored correctly
        retrieved_vector = np.array(retrieved_doc.embedding)
        np.testing.assert_almost_equal(vector, retrieved_vector, decimal=5)
    
    def tearDown(self):
        # Close the session
        self.session.close()
        
        # Drop the test database
        self.engine.dispose()
        
        # Connect to postgres to drop test database
        conn = psycopg2.connect("postgresql://postgres:postgres@localhost:5432/postgres")
        conn.autocommit = True
        with conn.cursor() as cursor:
            cursor.execute(f"DROP DATABASE IF EXISTS {self.test_db}")
        conn.close()

if __name__ == "__main__":
    unittest.main()
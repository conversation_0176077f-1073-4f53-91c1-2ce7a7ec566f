import unittest
import numpy as np
import chromadb
from chromadb.config import Settings

class TestInMemoryVectorDB(unittest.TestCase):
    
    def setUp(self):
        # Create in-memory ChromaDB client
        self.chroma_client = chromadb.Client(Settings(
            chroma_db_impl="duckdb+parquet",
            persist_directory=":memory:"
        ))
        
        # Create a collection
        self.collection = self.chroma_client.create_collection(name="test_collection")
    
    def test_store_numpy_vector(self):
        # Create a random vector
        vector = np.random.rand(384).astype(np.float32)
        
        # Store in database
        self.collection.add(
            embeddings=[vector.tolist()],
            documents=["Test document"],
            ids=["doc1"]
        )
        
        # Query to retrieve the vector
        results = self.collection.get(ids=["doc1"], include=["embeddings", "documents"])
        
        # Check if content matches
        self.assertEqual(results["documents"][0], "Test document")
        
        # Check if embedding was stored correctly
        retrieved_vector = np.array(results["embeddings"][0])
        np.testing.assert_array_almost_equal(vector, retrieved_vector)
    
    def tearDown(self):
        # Delete the collection
        self.chroma_client.delete_collection("test_collection")

if __name__ == "__main__":
    unittest.main()
import os
import sys
import pytest
import asyncio
import logging
from openai import OpenAI
from dotenv import load_dotenv, find_dotenv
from io import StringIO

# Add parent directory to path to fix imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the function to test
from src.services.sota_extraction import extract_sota
from src.models.paper import SOTAResult

# Suppress print output during tests to hide expected validation error messages
class SuppressOutput:
    def __init__(self):
        self.original_stdout = None
        self.suppressed_stdout = StringIO()
    
    def __enter__(self):
        self.original_stdout = sys.stdout
        sys.stdout = self.suppressed_stdout
        return self.suppressed_stdout
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        sys.stdout = self.original_stdout

# Load environment variables
load_dotenv(find_dotenv())

# Initialize OpenAI client
client = OpenAI(
    api_key=os.getenv("GEMINI_API_KEY"),
    base_url=os.getenv("GEMINI_BASE_URL")
)

@pytest.fixture
def sample_text():
    """Fixture to load a sample paper abstract with SOTA claims"""
    with open(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'test', 'sample_abstract.txt'), 'r') as f:
        return f.read()

@pytest.mark.asyncio
async def test_extract_sota_real_api(sample_text):
    """Test the extract_sota function using the real Gemini API"""
    # Ensure environment variables are set
    assert os.getenv("GEMINI_API_KEY"), "GEMINI_API_KEY environment variable not set"
    assert os.getenv("GEMINI_BASE_URL"), "GEMINI_BASE_URL environment variable not set"
    assert os.getenv("GEMINI_MODEL"), "GEMINI_MODEL environment variable not set"
    
    # Call the function
    result = await extract_sota(sample_text, client)
    
    # Verify the result
    assert result is not None, "extract_sota should return a result"
    assert isinstance(result, SOTAResult), "Result should be a SOTAResult instance"
    
    # Verify the expected data is present
    sota = result.sota
    assert sota.benchmark, "Benchmark should be extracted"
    assert sota.previous_method, "Previous SOTA method should be extracted"
    assert sota.previous_value > 0, "Previous SOTA value should be a positive number"
    assert sota.proposed_method, "Proposed method should be extracted"
    assert sota.proposed_value > 0, "Proposed method value should be a positive number"
    assert sota.absolute_gain > 0, "Absolute gain should be a positive number"
    assert sota.relative_gain_percent > 0, "Relative gain should be a positive percentage"
    
    # Verify expected values for this specific test case
    assert "ImageNet" in sota.benchmark or "COCO" in sota.benchmark, "Expected benchmark not found"
    assert "VisualBERT" in sota.previous_method, "Expected previous SOTA method not found"
    assert "DreamVision" in sota.proposed_method, "Expected proposed method not found"
    
    # Print the extracted SOTA data for debugging
    print("\nExtracted SOTA data:")
    print(f"Benchmark: {sota.benchmark}")
    print(f"Previous SOTA: {sota.previous_method} - {sota.previous_metric}: {sota.previous_value}")
    print(f"Proposed method: {sota.proposed_method} - {sota.proposed_metric}: {sota.proposed_value}")
    print(f"Absolute gain: {sota.absolute_gain}")
    print(f"Relative gain: {sota.relative_gain_percent}%")

@pytest.mark.asyncio
async def test_extract_sota_with_empty_text():
    """Test the extract_sota function with empty text"""
    # Use context manager to suppress expected validation error messages
    with SuppressOutput():
        # Empty text should be handled properly
        result = await extract_sota("", client)
    
    # We expect None for empty input
    assert result is None, "extract_sota should return None for empty text"

@pytest.mark.asyncio
async def test_extract_sota_with_non_sota_text():
    """Test the extract_sota function with text that doesn't contain SOTA claims"""
    # This text has no SOTA claims
    non_sota_text = """
    This paper discusses theoretical implications of transformers in NLP applications.
    We explore the mathematical foundations behind attention mechanisms and their relationship
    to earlier recurrent neural network approaches.
    """
    
    # Use context manager to suppress expected validation error messages
    with SuppressOutput():
        # For text without SOTA claims, the API might return invalid data that fails validation
        # The function should catch these errors and return None
        result = await extract_sota(non_sota_text, client)
    
    assert result is None, "extract_sota should return None for text without SOTA claims"

if __name__ == "__main__":
    # When running directly, execute the test with asyncio
    sample_text_content = ""
    with open(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'test', 'sample_abstract.txt'), 'r') as f:
        sample_text_content = f.read()
    
    print("Running test_extract_sota_real_api...")
    result = asyncio.run(test_extract_sota_real_api(sample_text_content))
    print("Test completed")
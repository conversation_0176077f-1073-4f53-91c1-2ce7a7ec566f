import os
import sys
import json
from pydantic import BaseModel
from dotenv import load_dotenv, find_dotenv
from openai import OpenAI
import pytest
from openai import OpenAI
from unittest.mock import Mock
from src.services.sota_extraction import SOTAResult

@pytest.fixture
def sample_paper_text():
    return """Our model achieves 90.2% accuracy on ImageNet, 
    improving upon the previous SOTA of 88.5% achieved by ViT."""

@pytest.mark.asyncio
async def test_sota_extraction_with_real_text(sample_paper_text):
    """Integration test using real text - only run in CI with credentials"""
    pytest.skip("Skipping integration test - run with --runslow to include")
    client = OpenAI(
        api_key="test-key",
        base_url="test-url"
    )
    result = await extract_sota(sample_paper_text, client)
    assert isinstance(result, SOTAResult)
    print(f"Extracted SOTA: {result}")

# Load environment variables
load_dotenv(find_dotenv())

# Initialize OpenAI client for Gemini API
client = OpenAI(
    api_key=os.getenv("GEMINI_API_KEY"),
    base_url=os.getenv("GEMINI_BASE_URL")
)

class SOTAMethod(BaseModel):
    method: str
    metric: str
    value: float

class SOTA(BaseModel):
    benchmark: str
    table_nr: int = 0
    previous_sota: SOTAMethod
    proposed_method: SOTAMethod
    absolute_gain: float
    relative_gain_percent: float

class SOTAResult(BaseModel):
    sota: SOTA

def sota_extraction(text_input):
    sota_extraction = f"""\
    {text_input}
    Extract the following information from the text above:
    
    sota:
      benchmark: str  # The name of the benchmark dataset or task
      table_nr: int   # The table number where the results are reported (optional)
      previous_sota:
        method: str   # The name of the previous state-of-the-art method
        metric: str   # The evaluation metric used (e.g., accuracy, F1, BLEU)
        value: float  # The performance value of the previous SOTA
      proposed_method:
        method: str   # The name of the method proposed in this paper
        metric: str   # The evaluation metric used (should match previous_sota)
        value: float  # The performance value of the proposed method
      absolute_gain: float  # The absolute numerical improvement
      relative_gain_percent: float  # The percentage improvement
    """
    return sota_extraction

def extract_sota(text):
    print("Extracting SOTA...")
    print(f"Using model: {os.getenv('GEMINI_MODEL')}")
    
    chat_completion = client.beta.chat.completions.parse(
        model=os.getenv("GEMINI_MODEL"),
        messages=[{
            "role": "system",
            "content": """You are a helpful assistant with world-class expertise in AI research and application.
            Your role is to extract SOTA improvements from research papers better than anyone else in the world.
            You only need to focus on the most important benchmark - the one that is most relevant to advancing the field.
            
            Return your response in this JSON structure:
            {
              "sota": {
                "benchmark": "string",
                "table_nr": number,
                "previous_sota": {
                  "method": "string",
                  "metric": "string",
                  "value": number
                },
                "proposed_method": {
                  "method": "string", 
                  "metric": "string",
                  "value": number
                },
                "absolute_gain": number,
                "relative_gain_percent": number
              }
            }
            """
        }, {
            "role": "user",
            "content": sota_extraction(text)
        }],
        response_format=SOTAResult,
    )

    print("API call completed")
    response_content = chat_completion.choices[0].message.content
    print(f"Raw API response: {response_content}")
    
    try:
        # Handle case where response is a string that needs to be parsed
        if isinstance(response_content, str):
            print("Response is a string, parsing as JSON")
            parsed_json = json.loads(response_content)
        else:
            print("Response is already parsed")
            parsed_json = response_content
            
        print(f"Parsed JSON: {parsed_json}")
        print(f"Parsed JSON type: {type(parsed_json)}")
        
        # Let's print the keys in case it's a dict
        if isinstance(parsed_json, dict):
            print(f"JSON keys: {list(parsed_json.keys())}")
            
            # If the model returns old format fields directly, transform them
            if 'sota' in parsed_json:
                sota_obj = parsed_json['sota']
                
                # Check if we need to transform the old structure to the new one
                if isinstance(sota_obj, dict) and not isinstance(sota_obj.get('previous_sota'), dict):
                    # Handle legacy fields
                    if 'most_important_benchmark' in sota_obj:
                        sota_obj['benchmark'] = sota_obj.pop('most_important_benchmark')
                    
                    # Create nested structures
                    previous_sota = {
                        'method': sota_obj.pop('previous_sota', ''),
                        'metric': sota_obj.pop('metric', ''),
                        'value': sota_obj.pop('previous_sota_value', 0.0)
                    }
                    
                    proposed_method = {
                        'method': sota_obj.pop('proposed_method', ''),
                        'metric': previous_sota['metric'],  # Use same metric
                        'value': sota_obj.pop('proposed_method_value', 0.0)
                    }
                    
                    # Add these new nested objects
                    sota_obj['previous_sota'] = previous_sota
                    sota_obj['proposed_method'] = proposed_method
                    
                    # Update the original parsed JSON
                    parsed_json['sota'] = sota_obj
                    print(f"Transformed JSON: {parsed_json}")
        
        sota_result = SOTAResult(**parsed_json)
        print("Successfully created SOTAResult")
        return sota_result
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON response: {e}")
        print(f"Raw response: {response_content}")
        return None
    except Exception as e:
        print(f"Error creating SOTAResult: {e}")
        if 'parsed_json' in locals():
            print(f"Parsed JSON: {parsed_json}")
            print(f"Parsed JSON type: {type(parsed_json)}")
            if isinstance(parsed_json, dict):
                print(f"JSON keys: {list(parsed_json.keys())}")
        else:
            print(f"JSON not parsed yet")
        return None

if __name__ == "__main__":
    # Use the sample abstract
    with open(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'test', 'sample_abstract.txt'), 'r') as f:
        text = f.read()
    
    print("Sample text:")
    print(text[:200] + "..." if len(text) > 200 else text)
    
    result = extract_sota(text)
    if result:
        print("\nExtracted SOTA result:")
        print(f"Benchmark: {result.sota.benchmark}")
        print(f"Previous SOTA: {result.sota.previous_sota.method} - {result.sota.previous_sota.metric}: {result.sota.previous_sota.value}")
        print(f"Proposed method: {result.sota.proposed_method.method} - {result.sota.proposed_method.metric}: {result.sota.proposed_method.value}")
        print(f"Gain: {result.sota.absolute_gain} ({result.sota.relative_gain_percent}%)")
    else:
        print("Failed to extract SOTA result")

import os 
from openai import OpenAI
from pydantic import BaseModel
from typing import List
import json

from dotenv import load_dotenv, find_dotenv
load_dotenv(find_dotenv())

client = OpenAI(
    api_key=os.getenv("GEMINI_API_KEY"), 
    base_url=os.getenv("GEMINI_BASE_URL")
    )

class SOTA(BaseModel):
    most_important_benchmark: str
    table_nr: int
    metric: str
    previous_sota: str
    previous_sota_value: float
    proposed_method: str
    proposed_method_value: float
    absolute_gain: float
    relative_gain_percent: float

class SOTAResult(BaseModel):
    sota: SOTA

def sota_extraction(text_input):
    sota_extraction = f"""\
    {text_input}
    Extract the following information from the text above:

    most_important_benchmark: str
    table_nr: int
    metric: str
    previous_sota: str
    previous_sota_value: float
    proposed_method: str
    proposed_method_value: float
    absolute_gain: float
    relative_gain_percent: float
    """
    return sota_extraction

def extract_sota(text):
    chat_completion = client.beta.chat.completions.parse(
    model= os.getenv("GEMINI_MODEL"),
    messages=[{
            "role": "system",
            "content": """You are a helpful assistant with world-class expertise in AI research and application.
            Your role is to extract SOTA improvements from research papers better than anyone else in the world.
            You only need to focus on the most important benchmark - the one that is most relevant to advancing the field.
            """
        }, {
            "role": "user",
            "content": sota_extraction(text)
        }],
    response_format=SOTAResult,
    )

    response_content = chat_completion.choices[0].message.content
    print(f"Raw API response: {response_content}")
    try:
        # Handle case where response is a string that needs to be parsed
        if isinstance(response_content, str):
            parsed_json = json.loads(response_content)
        else:
            parsed_json = response_content
            
        print(f"Parsed JSON: {parsed_json}")
        print(f"Parsed JSON type: {type(parsed_json)}")
        
        # Let's print the keys in case it's a dict
        if isinstance(parsed_json, dict):
            print(f"JSON keys: {list(parsed_json.keys())}")
            
            # Check if the JSON doesn't have a 'sota' key but has all the SOTA fields directly
            if 'sota' not in parsed_json and 'most_important_benchmark' in parsed_json:
                print("Wrapping direct SOTA fields in a 'sota' object")
                parsed_json = {'sota': parsed_json}
                print(f"Wrapped JSON: {parsed_json}")
        
        sota_result = SOTAResult(**parsed_json)
        return sota_result
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON response: {e}")
        print(f"Raw response: {response_content}")
        return None
    except Exception as e:
        print(f"Error creating SOTAResult: {e}")
        if 'parsed_json' in locals():
            print(f"Parsed JSON: {parsed_json}")
            print(f"Parsed JSON type: {type(parsed_json)}")
            if isinstance(parsed_json, dict):
                print(f"JSON keys: {list(parsed_json.keys())}")
        else:
            print(f"JSON not parsed yet")
        return None

if __name__ == "__main__":
    with open('/home/<USER>/Development/Websites/fastmoatless/paper_texts/2502.14727.txt', 'r') as f:
        text = f.read()
    result = extract_sota(text)
    print(result)